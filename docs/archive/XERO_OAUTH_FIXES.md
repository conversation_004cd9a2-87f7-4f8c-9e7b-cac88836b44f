# Xero OAuth2 Authorization Script Fixes

## Issues Fixed

### 1. **Redirect URI Mismatch**
- **Problem**: Configuration had `/auth/xero/callback` but API routes used `/api/auth/xero/callback`
- **Fix**: Updated `config.yml` and ensured consistency across all configuration files
- **Files Changed**: `config.yml`, `.env`

### 2. **Improper OAuth2 Token Exchange**
- **Problem**: Using undefined `oauth2.fetch_token()` method from Xero SDK
- **Fix**: Implemented proper OAuth2 flow using `requests-oauthlib.OAuth2Session`
- **Files Changed**: `mcx3d_finance/auth/xero_oauth.py`

### 3. **Missing Dependencies**
- **Problem**: Required OAuth2 and configuration dependencies not in requirements
- **Fix**: Added `requests-oauthlib` and `pydantic-settings`
- **Files Changed**: `requirements.txt`

### 4. **Incomplete Error Handling**
- **Problem**: Poor error messages and handling in auth flow
- **Fix**: Enhanced error handling with detailed troubleshooting
- **Files Changed**: `mcx3d_finance/api/auth_routes.py`, `xero_auth_helper.py`

### 5. **Configuration Validation**
- **Problem**: No validation of OAuth configuration before attempting auth
- **Fix**: Added comprehensive configuration validation
- **Files Changed**: `xero_auth_helper.py`

## Key Changes Made

### OAuth Implementation (`mcx3d_finance/auth/xero_oauth.py`)
```python
# Before: Undefined method
token = self.api_client.oauth2.fetch_token(...)

# After: Proper OAuth2Session usage
oauth2_session = OAuth2Session(
    client_id=self.config["client_id"],
    redirect_uri=self.config["redirect_uri"],
    state=state
)
token = oauth2_session.fetch_token(
    token_url="https://identity.xero.com/connect/token",
    authorization_response=authorization_response_url,
    client_secret=self.config["client_secret"]
)
```

### Configuration Updates
- **Redirect URI**: `http://localhost:8000/api/auth/xero/callback`
- **Scopes**: Added `offline_access` for token refresh capability
- **Validation**: Pre-flight validation of all required parameters

### Enhanced Helper Script (`xero_auth_helper.py`)
- Configuration validation before authorization
- Comprehensive troubleshooting guide
- Better error messages and handling
- Step-by-step user instructions

## Required Xero App Configuration

In your Xero Developer Console (https://developer.xero.com/app/manage):

1. **App Type**: Web App
2. **Redirect URIs**: 
   - `http://localhost:8000/api/auth/xero/callback`
3. **Scopes**: 
   - Accounting Transactions
   - Accounting Contacts  
   - Accounting Reports
   - Accounting Settings
   - Offline Access (for token refresh)

## Environment Variables Required

```bash
# Xero OAuth Configuration
XERO_CLIENT_ID=your_actual_client_id
XERO_CLIENT_SECRET=your_actual_client_secret
XERO_REDIRECT_URI=http://localhost:8000/api/auth/xero/callback
XERO_SCOPES=accounting.transactions accounting.contacts accounting.reports.read accounting.settings offline_access
XERO_WEBHOOK_KEY=your_webhook_key

# Other required variables
DATABASE_URL=postgresql://user:password@localhost:5432/mcx3d_db
REDIS_URL=redis://localhost:6379/0
SECRET_KEY=your_secret_key_minimum_32_characters
ENCRYPTION_KEY=your_base64_encoded_encryption_key
```

## Testing the Fix

### 1. Update Dependencies
```bash
# Install new dependencies
pip install -r requirements.txt

# Or with Docker
docker-compose build web
```

### 2. Validate Configuration
```bash
# Run the helper script - it will validate configuration first
python xero_auth_helper.py
```

### 3. Test Full OAuth Flow
```bash
# Start the application
docker-compose up

# In another terminal, run the auth helper
python xero_auth_helper.py

# Or test via API endpoints
curl http://localhost:8000/api/auth/xero/login
```

## OAuth Flow Steps (Fixed)

1. **Authorization URL Generation**:
   - Uses `OAuth2Session` for proper URL generation
   - Includes all required parameters (client_id, redirect_uri, scope, state)
   - Validates configuration before generation

2. **User Authorization**:
   - User redirected to Xero login page
   - Selects organization and grants permissions
   - Redirected back to callback URL with authorization code

3. **Token Exchange**:
   - Validates state parameter (CSRF protection)
   - Extracts authorization code from callback URL
   - Exchanges code for access/refresh tokens using proper OAuth2 endpoint

4. **Token Storage**:
   - Retrieves tenant information using access token
   - Stores encrypted tokens in database
   - Creates/updates organization record

## Common Issues & Solutions

### Issue: "Invalid redirect URI"
**Solution**: Ensure your Xero app redirect URI exactly matches:
`http://localhost:8000/api/auth/xero/callback`

### Issue: "Invalid client credentials"
**Solution**: Verify your XERO_CLIENT_ID and XERO_CLIENT_SECRET in `.env`

### Issue: "Invalid state parameter"
**Solution**: State tokens expire in 10 minutes. Generate a new auth URL.

### Issue: "Scope not authorized"
**Solution**: Check your Xero app has all required scopes enabled in the developer console.

## API Endpoints

- **GET** `/api/auth/xero/login` - Initiates OAuth flow
- **GET** `/api/auth/xero/callback` - Handles OAuth callback

## Testing Checklist

- [ ] Configuration validation passes
- [ ] Authorization URL generates correctly
- [ ] Browser opens to Xero login page
- [ ] After login, redirects to correct callback URL
- [ ] Token exchange completes successfully
- [ ] Organization saved to database
- [ ] Token refresh works (test after 30 minutes)

## Files Modified

1. `mcx3d_finance/auth/xero_oauth.py` - Fixed OAuth implementation
2. `mcx3d_finance/api/auth_routes.py` - Enhanced error handling
3. `xero_auth_helper.py` - Added validation and troubleshooting
4. `config.yml` - Fixed redirect URI and scopes
5. `.env` - Added offline_access scope
6. `requirements.txt` - Added missing dependencies

The OAuth flow should now work correctly with proper error handling and user guidance.