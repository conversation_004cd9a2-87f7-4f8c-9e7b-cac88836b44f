"""
Parallel version of Xero Data Storage Service

Extends the base XeroDataStorageService with parallel processing capabilities
using ThreadPoolExecutor to sync accounts, contacts, invoices, and transactions concurrently.
"""

import logging
from typing import Dict, List, Any, Optional, Set, Tuple
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from sqlalchemy import and_
import json
from concurrent.futures import ThreadPoolExecutor, as_completed, Future
import threading
from collections import defaultdict

from .xero_data_storage import XeroDataStorageService
from ..db.session import SessionLocal
from ..db.models import (
    Organization, Account, Contact, Transaction, 
    Invoice, BankTransaction, SyncStatus
)

logger = logging.getLogger(__name__)


class ParallelXeroDataStorageService(XeroDataStorageService):
    """Extended storage service with parallel processing capabilities."""
    
    def __init__(self, db: Session, optimize: bool = True, max_workers: int = 4):
        super().__init__(db, optimize)
        self.max_workers = max_workers
        self._progress_lock = threading.Lock()
        self._progress_data = {
            "accounts": 0,
            "contacts": 0,
            "invoices": 0,
            "transactions": 0,
            "total": 0
        }
        # Thread-safe storage stats
        self._stats_lock = threading.Lock()
        self._thread_local = threading.local()
    
    def store_all_data_parallel(
        self,
        organization_id: int,
        imported_data: Dict[str, Any],
        progress_callback=None
    ) -> Dict[str, Any]:
        """
        Store all imported data in the database using parallel processing.
        
        This method processes accounts, contacts, invoices, and bank transactions
        concurrently using ThreadPoolExecutor for improved performance.
        
        Args:
            organization_id: Organization ID
            imported_data: Data from XeroDataImportService
            progress_callback: Optional callback for progress updates
            
        Returns:
            Dict with storage results and statistics
        """
        try:
            logger.info(f"Starting parallel data storage for organization {organization_id}")
            
            # Update organization details first (single-threaded)
            self._update_progress(progress_callback, 5, "Updating organization details...")
            org_details = imported_data.get("organization", {})
            if org_details:
                self._update_organization(organization_id, org_details)
            
            # Prepare entity data
            entities = {
                "accounts": imported_data.get("accounts", []),
                "contacts": imported_data.get("contacts", []),
                "invoices": imported_data.get("invoices", []),
                "transactions": imported_data.get("transactions", [])
            }
            
            # Calculate total items for progress tracking
            total_items = sum(len(items) for items in entities.values())
            self._progress_data["total"] = total_items
            
            # If optimization is enabled, preload caches
            if self.optimize:
                self._preload_caches_parallel(organization_id, entities)
            
            # Create thread pool and submit tasks
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures: Dict[Future, str] = {}
                
                # Submit accounts task
                if entities["accounts"]:
                    future = executor.submit(
                        self._store_accounts_thread_safe,
                        organization_id,
                        entities["accounts"],
                        progress_callback
                    )
                    futures[future] = "accounts"
                
                # Submit contacts task
                if entities["contacts"]:
                    future = executor.submit(
                        self._store_contacts_thread_safe,
                        organization_id,
                        entities["contacts"],
                        progress_callback
                    )
                    futures[future] = "contacts"
                
                # Submit invoices task
                if entities["invoices"]:
                    future = executor.submit(
                        self._store_invoices_thread_safe,
                        organization_id,
                        entities["invoices"],
                        progress_callback
                    )
                    futures[future] = "invoices"
                
                # Submit bank transactions task
                if entities["transactions"]:
                    future = executor.submit(
                        self._store_bank_transactions_thread_safe,
                        organization_id,
                        entities["transactions"],
                        progress_callback
                    )
                    futures[future] = "transactions"
                
                # Process completed futures
                errors = []
                for future in as_completed(futures):
                    entity_type = futures[future]
                    try:
                        result = future.result()
                        logger.info(f"Completed parallel storage for {entity_type}")
                    except Exception as e:
                        logger.error(f"Error in parallel storage for {entity_type}: {e}")
                        errors.append(f"{entity_type}: {str(e)}")
            
            # Finalize and commit
            self._update_progress(progress_callback, 95, "Finalizing data storage...")
            
            # Update sync status
            if errors:
                self._update_sync_status(organization_id, "completed_with_errors", "; ".join(errors))
            else:
                self._update_sync_status(organization_id, "completed")
            
            self._update_progress(progress_callback, 100, "Storage completed!")
            
            return {
                "success": len(errors) == 0,
                "stats": self.storage_stats,
                "errors": errors,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error during parallel data storage: {e}")
            self._update_sync_status(organization_id, "failed", str(e))
            return {
                "success": False,
                "error": str(e),
                "stats": self.storage_stats,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    def _preload_caches_parallel(self, organization_id: int, entities: Dict[str, List]) -> None:
        """Preload caches for all entity types in parallel."""
        if not self.optimize:
            return
        
        with ThreadPoolExecutor(max_workers=2) as executor:
            futures = []
            
            # Preload contacts for invoices
            if entities["invoices"]:
                xero_contact_ids = {
                    inv.get("contact_id") 
                    for inv in entities["invoices"] 
                    if inv.get("contact_id")
                }
                future = executor.submit(self._preload_contacts, organization_id, xero_contact_ids)
                futures.append(future)
            
            # Preload accounts for transactions
            if entities["transactions"]:
                xero_account_ids = {
                    trans.get("account_id") 
                    for trans in entities["transactions"] 
                    if trans.get("account_id")
                }
                future = executor.submit(self._preload_accounts, organization_id, xero_account_ids)
                futures.append(future)
            
            # Wait for all preloading to complete
            for future in futures:
                future.result()
    
    def _get_thread_session(self) -> Session:
        """Get a thread-local database session."""
        if not hasattr(self._thread_local, 'session'):
            self._thread_local.session = SessionLocal()
        return self._thread_local.session
    
    def _close_thread_session(self):
        """Close the thread-local session if it exists."""
        if hasattr(self._thread_local, 'session'):
            self._thread_local.session.close()
            delattr(self._thread_local, 'session')
    
    def _update_progress_parallel(self, entity_type: str, processed: int, total: int, 
                                 progress_callback=None, message: str = None):
        """Update progress for parallel operations in a thread-safe manner."""
        with self._progress_lock:
            self._progress_data[entity_type] = processed
            
            # Calculate overall progress
            total_processed = sum(
                self._progress_data[key] 
                for key in ["accounts", "contacts", "invoices", "transactions"]
            )
            
            if self._progress_data["total"] > 0:
                progress_percent = int((total_processed / self._progress_data["total"]) * 85) + 10
            else:
                progress_percent = 10
            
            if not message:
                message = f"Processing {entity_type}: {processed}/{total}"
            
            self._update_progress(progress_callback, progress_percent, message)
    
    def _update_stats_thread_safe(self, entity_type: str, action: str, count: int = 1):
        """Update storage stats in a thread-safe manner."""
        with self._stats_lock:
            self.storage_stats[entity_type][action] += count
    
    def _store_accounts_thread_safe(self, organization_id: int, accounts: List[Dict[str, Any]], 
                                   progress_callback=None) -> bool:
        """Store accounts with thread-safe database operations."""
        session = self._get_thread_session()
        try:
            total = len(accounts)
            for idx, account_data in enumerate(accounts):
                try:
                    xero_id = account_data.get("xero_account_id")
                    if not xero_id:
                        continue
                    
                    # Convert date fields
                    if 'updated_date_utc' in account_data:
                        account_data['updated_date_utc'] = self._parse_xero_date(account_data['updated_date_utc'])
                    
                    # Check if account exists
                    existing = session.query(Account).filter_by(
                        xero_account_id=xero_id,
                        organization_id=organization_id
                    ).first()
                    
                    if existing:
                        # Update existing account
                        for key, value in account_data.items():
                            if hasattr(existing, key) and key not in ['id', 'created_at']:
                                setattr(existing, key, value)
                        existing.updated_at = datetime.now(timezone.utc)
                        self._update_stats_thread_safe("accounts", "updated")
                    else:
                        # Create new account
                        valid_fields = {k: v for k, v in account_data.items() 
                                      if hasattr(Account, k) or k == 'organization_id'}
                        account = Account(
                            organization_id=organization_id,
                            **valid_fields
                        )
                        session.add(account)
                        self._update_stats_thread_safe("accounts", "created")
                    
                    # Update progress
                    if idx % 10 == 0 or idx == total - 1:
                        self._update_progress_parallel("accounts", idx + 1, total, progress_callback)
                        
                except Exception as e:
                    logger.error(f"Error storing account {account_data.get('name')}: {e}")
                    self._update_stats_thread_safe("accounts", "skipped")
            
            # Commit this thread's changes
            session.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error in thread storing accounts: {e}")
            session.rollback()
            raise
        finally:
            self._close_thread_session()
    
    def _store_contacts_thread_safe(self, organization_id: int, contacts: List[Dict[str, Any]], 
                                   progress_callback=None) -> bool:
        """Store contacts with thread-safe database operations."""
        session = self._get_thread_session()
        try:
            total = len(contacts)
            for idx, contact_data in enumerate(contacts):
                try:
                    xero_id = contact_data.get("xero_contact_id")
                    if not xero_id:
                        continue
                    
                    # Convert date fields
                    if 'updated_date_utc' in contact_data:
                        contact_data['updated_date_utc'] = self._parse_xero_date(contact_data['updated_date_utc'])
                    
                    # Check if contact exists
                    existing = session.query(Contact).filter_by(
                        xero_contact_id=xero_id,
                        organization_id=organization_id
                    ).first()
                    
                    if existing:
                        # Update existing contact
                        for key, value in contact_data.items():
                            if hasattr(existing, key) and key not in ['id', 'created_at']:
                                setattr(existing, key, value)
                        existing.updated_at = datetime.now(timezone.utc)
                        self._update_stats_thread_safe("contacts", "updated")
                    else:
                        # Create new contact
                        valid_fields = {k: v for k, v in contact_data.items() 
                                      if hasattr(Contact, k) or k == 'organization_id'}
                        contact = Contact(
                            organization_id=organization_id,
                            **valid_fields
                        )
                        session.add(contact)
                        self._update_stats_thread_safe("contacts", "created")
                    
                    # Update progress
                    if idx % 10 == 0 or idx == total - 1:
                        self._update_progress_parallel("contacts", idx + 1, total, progress_callback)
                        
                except Exception as e:
                    logger.error(f"Error storing contact {contact_data.get('name')}: {e}")
                    self._update_stats_thread_safe("contacts", "skipped")
            
            # Commit this thread's changes
            session.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error in thread storing contacts: {e}")
            session.rollback()
            raise
        finally:
            self._close_thread_session()
    
    def _store_invoices_thread_safe(self, organization_id: int, invoices: List[Dict[str, Any]], 
                                   progress_callback=None) -> bool:
        """Store invoices with thread-safe database operations."""
        session = self._get_thread_session()
        try:
            total = len(invoices)
            
            # Use cached contact lookups if available
            for idx, invoice_data in enumerate(invoices):
                try:
                    xero_id = invoice_data.get("xero_invoice_id")
                    if not xero_id:
                        continue
                    
                    # Use cached contact lookup or query database
                    xero_contact_id = invoice_data.get("contact_id")
                    if xero_contact_id:
                        if self.optimize and self._contact_cache:
                            invoice_data["contact_id"] = self._contact_cache.get(xero_contact_id)
                        else:
                            contact = session.query(Contact).filter_by(
                                xero_contact_id=xero_contact_id,
                                organization_id=organization_id
                            ).first()
                            invoice_data["contact_id"] = contact.id if contact else None
                    
                    # Convert date fields
                    for date_field in ['date', 'due_date', 'expected_payment_date', 
                                     'planned_payment_date', 'updated_date_utc']:
                        if date_field in invoice_data:
                            invoice_data[date_field] = self._parse_xero_date(invoice_data[date_field])
                    
                    # Check if invoice exists
                    existing = session.query(Invoice).filter_by(
                        xero_invoice_id=xero_id,
                        organization_id=organization_id
                    ).first()
                    
                    if existing:
                        # Update existing invoice
                        for key, value in invoice_data.items():
                            if hasattr(existing, key) and key not in ['id', 'created_at']:
                                setattr(existing, key, value)
                        existing.updated_at = datetime.now(timezone.utc)
                        self._update_stats_thread_safe("invoices", "updated")
                    else:
                        # Create new invoice
                        invoice = Invoice(
                            organization_id=organization_id,
                            **invoice_data
                        )
                        session.add(invoice)
                        self._update_stats_thread_safe("invoices", "created")
                    
                    # Update progress
                    if idx % 10 == 0 or idx == total - 1:
                        self._update_progress_parallel("invoices", idx + 1, total, progress_callback)
                        
                except Exception as e:
                    logger.error(f"Error storing invoice {invoice_data.get('invoice_number')}: {e}")
                    self._update_stats_thread_safe("invoices", "skipped")
            
            # Commit this thread's changes
            session.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error in thread storing invoices: {e}")
            session.rollback()
            raise
        finally:
            self._close_thread_session()
    
    def _store_bank_transactions_thread_safe(self, organization_id: int, 
                                           transactions: List[Dict[str, Any]], 
                                           progress_callback=None) -> bool:
        """Store bank transactions with thread-safe database operations."""
        session = self._get_thread_session()
        try:
            total = len(transactions)
            
            for idx, trans_data in enumerate(transactions):
                try:
                    xero_id = trans_data.get("xero_transaction_id")
                    if not xero_id:
                        continue
                    
                    # Convert contact ID if present
                    xero_contact_id = trans_data.get("contact_id")
                    if xero_contact_id:
                        if self.optimize and self._contact_cache:
                            trans_data["contact_id"] = self._contact_cache.get(xero_contact_id)
                        else:
                            contact = session.query(Contact).filter_by(
                                xero_contact_id=xero_contact_id,
                                organization_id=organization_id
                            ).first()
                            trans_data["contact_id"] = contact.id if contact else None
                    
                    # Convert date fields
                    if 'date' in trans_data:
                        trans_data['date'] = self._parse_xero_date(trans_data['date'])
                    if 'updated_date_utc' in trans_data:
                        trans_data['updated_date_utc'] = self._parse_xero_date(trans_data['updated_date_utc'])
                    
                    # Check if transaction exists
                    existing = session.query(BankTransaction).filter_by(
                        xero_transaction_id=xero_id,
                        organization_id=organization_id
                    ).first()
                    
                    if existing:
                        # Update existing transaction
                        for key, value in trans_data.items():
                            if hasattr(existing, key) and key not in ['id', 'created_at']:
                                setattr(existing, key, value)
                        existing.updated_at = datetime.now(timezone.utc)
                        self._update_stats_thread_safe("bank_transactions", "updated")
                    else:
                        # Create new transaction
                        valid_fields = {k: v for k, v in trans_data.items() 
                                      if hasattr(BankTransaction, k) or k == 'organization_id'}
                        transaction = BankTransaction(
                            organization_id=organization_id,
                            **valid_fields
                        )
                        session.add(transaction)
                        self._update_stats_thread_safe("bank_transactions", "created")
                    
                    # Update progress
                    if idx % 10 == 0 or idx == total - 1:
                        self._update_progress_parallel("transactions", idx + 1, total, progress_callback)
                        
                except Exception as e:
                    logger.error(f"Error storing bank transaction: {e}")
                    self._update_stats_thread_safe("bank_transactions", "skipped")
            
            # Commit this thread's changes
            session.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error in thread storing bank transactions: {e}")
            session.rollback()
            raise
        finally:
            self._close_thread_session()
    
    def store_all_data(
        self,
        organization_id: int,
        imported_data: Dict[str, Any],
        progress_callback=None,
        use_parallel: bool = True
    ) -> Dict[str, Any]:
        """
        Store all imported data with optional parallel processing.
        
        Args:
            organization_id: Organization ID
            imported_data: Data from XeroDataImportService
            progress_callback: Optional callback for progress updates
            use_parallel: Whether to use parallel processing (default: True)
            
        Returns:
            Dict with storage results and statistics
        """
        if use_parallel:
            return self.store_all_data_parallel(
                organization_id, imported_data, progress_callback
            )
        else:
            # Fall back to sequential processing
            return super().store_all_data(
                organization_id, imported_data, progress_callback
            )