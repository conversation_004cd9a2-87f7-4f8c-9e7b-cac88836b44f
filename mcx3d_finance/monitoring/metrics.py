"""
Prometheus Metrics Collection for MCX3D Financial Platform

Provides comprehensive business KPI monitoring, performance metrics, and system
health metrics for production monitoring and alerting.
"""

import time
import functools
from typing import Dict, Any, Optional, Callable
from datetime import datetime
from prometheus_client import (
    Counter, Histogram, Gauge, Summary, Info,
    start_http_server, generate_latest, CONTENT_TYPE_LATEST
)
import logging

logger = logging.getLogger(__name__)

# Business Metrics - Core Financial Operations
REPORT_GENERATION_COUNT = Counter(
    'mcx3d_reports_generated_total',
    'Total number of financial reports generated',
    ['report_type', 'output_format', 'status', 'organization_id', 'complexity']
)

VALUATION_PROCESSING_TIME = Histogram(
    'mcx3d_valuation_processing_seconds',
    'Time spent processing valuations',
    ['model_type', 'complexity', 'data_source', 'organization_id'],
    buckets=(0.1, 0.5, 1.0, 2.5, 5.0, 10.0, 30.0, 60.0, 120.0, 300.0, float('inf'))
)

VALUATION_COUNT = Counter(
    'mcx3d_valuations_completed_total',
    'Total number of valuations completed',
    ['model_type', 'status', 'organization_id', 'currency']
)

VALUATION_AMOUNT = Histogram(
    'mcx3d_valuation_amount_usd',
    'Valuation amounts in USD',
    ['model_type', 'organization_id', 'industry'],
    buckets=(1000, 10000, 100000, 1000000, 10000000, 100000000, 1000000000, float('inf'))
)

# System Performance Metrics
ACTIVE_REPORT_JOBS = Gauge(
    'mcx3d_active_report_jobs',
    'Currently active report generation jobs'
)

DATABASE_CONNECTION_POOL = Gauge(
    'mcx3d_db_connections_active',
    'Active database connections'
)

DATABASE_QUERY_TIME = Histogram(
    'mcx3d_db_query_duration_seconds',
    'Database query execution time',
    ['operation', 'table', 'status'],
    buckets=(0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, float('inf'))
)

# API Performance Metrics
HTTP_REQUEST_COUNT = Counter(
    'mcx3d_http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status_code']
)

HTTP_REQUEST_DURATION = Histogram(
    'mcx3d_http_request_duration_seconds',
    'HTTP request duration',
    ['method', 'endpoint'],
    buckets=(0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1.0, 2.5, 5.0, 7.5, 10.0, float('inf'))
)

# User Activity Metrics
USER_ACTIVITY_COUNT = Counter(
    'mcx3d_user_activities_total',
    'Total user activities',
    ['user_id', 'action', 'resource']
)

ACTIVE_USERS = Gauge(
    'mcx3d_active_users_current',
    'Currently active users'
)

USER_SESSION_DURATION = Histogram(
    'mcx3d_user_session_duration_seconds',
    'User session duration',
    ['user_type'],
    buckets=(60, 300, 900, 1800, 3600, 7200, 14400, 28800, float('inf'))
)

# Financial Data Quality Metrics
DATA_VALIDATION_COUNT = Counter(
    'mcx3d_data_validations_total',
    'Data validation results',
    ['validation_type', 'status', 'data_source']
)

DATA_QUALITY_SCORE = Gauge(
    'mcx3d_data_quality_score',
    'Data quality score (0-100)',
    ['data_source', 'organization_id']
)

# Integration Metrics
XERO_API_CALLS = Counter(
    'mcx3d_xero_api_calls_total',
    'Xero API calls',
    ['endpoint', 'status_code', 'organization_id']
)

XERO_SYNC_DURATION = Histogram(
    'mcx3d_xero_sync_duration_seconds',
    'Xero data synchronization duration',
    ['sync_type', 'organization_id'],
    buckets=(1, 5, 10, 30, 60, 120, 300, 600, 1200, float('inf'))
)

XERO_RECORDS_SYNCED = Counter(
    'mcx3d_xero_records_synced_total',
    'Total records synced from Xero',
    ['record_type', 'organization_id', 'status']
)

# Error and Alert Metrics
ERROR_COUNT = Counter(
    'mcx3d_errors_total',
    'Total application errors',
    ['error_type', 'component', 'severity']
)

ALERT_COUNT = Counter(
    'mcx3d_alerts_fired_total',
    'Total alerts fired',
    ['alert_type', 'severity', 'component']
)

# Resource Utilization Metrics
MEMORY_USAGE_BYTES = Gauge(
    'mcx3d_memory_usage_bytes',
    'Memory usage in bytes',
    ['component']
)

CPU_USAGE_PERCENT = Gauge(
    'mcx3d_cpu_usage_percent',
    'CPU usage percentage',
    ['component']
)

DISK_USAGE_BYTES = Gauge(
    'mcx3d_disk_usage_bytes',
    'Disk usage in bytes',
    ['mount_point']
)

# File and Report Metrics
FILE_SIZE_BYTES = Histogram(
    'mcx3d_generated_file_size_bytes',
    'Size of generated files',
    ['file_type', 'format'],
    buckets=(1024, 10240, 102400, 1048576, 10485760, 104857600, float('inf'))
)

REPORT_COMPLEXITY_SCORE = Histogram(
    'mcx3d_report_complexity_score',
    'Report complexity scoring',
    ['report_type'],
    buckets=(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, float('inf'))
)

# System Health Metrics
HEALTH_CHECK_DURATION = Histogram(
    'mcx3d_health_check_duration_seconds',
    'Health check execution time',
    ['check_type', 'component'],
    buckets=(0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, float('inf'))
)

COMPONENT_STATUS = Gauge(
    'mcx3d_component_status',
    'Component health status (1=healthy, 0=unhealthy)',
    ['component', 'check_type']
)

# Celery-Specific Metrics (Module Level)
CELERY_WORKER_COUNT = Gauge(
    'mcx3d_celery_workers_active',
    'Number of active Celery workers'
)

CELERY_QUEUE_LENGTH = Gauge(
    'mcx3d_celery_queue_length_total',
    'Total length of all Celery queues',
    ['queue_name']
)

CELERY_FAILED_TASKS = Gauge(
    'mcx3d_celery_failed_tasks_total',
    'Total number of failed Celery tasks'
)

CELERY_ACTIVE_TASKS = Gauge(
    'mcx3d_celery_active_tasks',
    'Number of currently active Celery tasks'
)

# External Service Metrics
EXTERNAL_SERVICE_STATUS = Gauge(
    'mcx3d_external_service_status',
    'External service health status (1=healthy, 0=unhealthy)',
    ['service_name', 'endpoint']
)

EXTERNAL_SERVICE_RESPONSE_TIME = Histogram(
    'mcx3d_external_service_response_seconds',
    'External service response time',
    ['service_name', 'endpoint'],
    buckets=(0.1, 0.5, 1.0, 2.5, 5.0, 10.0, 30.0, float('inf'))
)


def monitor_performance(metric_name: str = None, labels: Dict[str, str] = None):
    """
    Decorator to automatically monitor function performance with Prometheus metrics.
    
    Args:
        metric_name: Optional custom metric name
        labels: Optional additional labels for metrics
    
    Usage:
        @monitor_performance('report_generation')
        def generate_report(...):
            ...
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            function_name = metric_name or func.__name__
            additional_labels = labels or {}
            
            # Increment active jobs counter
            ACTIVE_REPORT_JOBS.inc()
            
            try:
                result = func(*args, **kwargs)
                
                # Record successful execution
                if 'report_type' in kwargs and 'output_format' in kwargs:
                    REPORT_GENERATION_COUNT.labels(
                        report_type=kwargs.get('report_type', 'unknown'),
                        output_format=kwargs.get('output_format', 'unknown'),
                        status='success',
                        organization_id=kwargs.get('organization_id', 'unknown'),
                        complexity=kwargs.get('complexity', 'standard')
                    ).inc()
                
                return result
                
            except Exception as e:
                # Record error
                ERROR_COUNT.labels(
                    error_type=type(e).__name__,
                    component=function_name,
                    severity='error'
                ).inc()
                
                if 'report_type' in kwargs and 'output_format' in kwargs:
                    REPORT_GENERATION_COUNT.labels(
                        report_type=kwargs.get('report_type', 'unknown'),
                        output_format=kwargs.get('output_format', 'unknown'),
                        status='error',
                        organization_id=kwargs.get('organization_id', 'unknown'),
                        complexity=kwargs.get('complexity', 'standard')
                    ).inc()
                
                raise
                
            finally:
                # Record processing time and decrement active jobs
                duration = time.time() - start_time
                
                if 'model_type' in kwargs:
                    VALUATION_PROCESSING_TIME.labels(
                        model_type=kwargs.get('model_type', 'unknown'),
                        complexity=kwargs.get('complexity', 'standard'),
                        data_source=kwargs.get('data_source', 'unknown'),
                        organization_id=kwargs.get('organization_id', 'unknown')
                    ).observe(duration)
                
                ACTIVE_REPORT_JOBS.dec()
                
        return wrapper
    return decorator


def monitor_business_event(event_type: str, **labels):
    """
    Record business events with custom metrics.
    
    Args:
        event_type: Type of business event
        **labels: Event labels for metric dimensions
    """
    timestamp = datetime.utcnow()
    
    if event_type == 'valuation_completed':
        VALUATION_COUNT.labels(
            model_type=labels.get('model_type', 'unknown'),
            status='success',
            organization_id=labels.get('organization_id', 'unknown'),
            currency=labels.get('currency', 'USD')
        ).inc()
        
        if 'valuation_amount' in labels:
            VALUATION_AMOUNT.labels(
                model_type=labels.get('model_type', 'unknown'),
                organization_id=labels.get('organization_id', 'unknown'),
                industry=labels.get('industry', 'unknown')
            ).observe(labels['valuation_amount'])
    
    elif event_type == 'user_activity':
        USER_ACTIVITY_COUNT.labels(
            user_id=labels.get('user_id', 'anonymous'),
            action=labels.get('action', 'unknown'),
            resource=labels.get('resource', 'unknown')
        ).inc()
    
    elif event_type == 'data_validation':
        DATA_VALIDATION_COUNT.labels(
            validation_type=labels.get('validation_type', 'unknown'),
            status=labels.get('status', 'unknown'),
            data_source=labels.get('data_source', 'unknown')
        ).inc()
    
    elif event_type == 'xero_sync':
        XERO_RECORDS_SYNCED.labels(
            record_type=labels.get('record_type', 'unknown'),
            organization_id=labels.get('organization_id', 'unknown'),
            status=labels.get('status', 'success')
        ).inc()


def record_file_metrics(file_path: str, file_type: str, format_type: str):
    """
    Record file-related metrics.
    
    Args:
        file_path: Path to the generated file
        file_type: Type of file (report, chart, export)
        format_type: File format (pdf, excel, csv, etc.)
    """
    try:
        import os
        file_size = os.path.getsize(file_path)
        FILE_SIZE_BYTES.labels(
            file_type=file_type,
            format=format_type
        ).observe(file_size)
    except Exception as e:
        logger.warning(f"Failed to record file metrics: {e}")


def update_system_metrics(component: str, memory_bytes: int, cpu_percent: float):
    """
    Update system resource metrics.
    
    Args:
        component: Component name
        memory_bytes: Memory usage in bytes
        cpu_percent: CPU usage percentage
    """
    MEMORY_USAGE_BYTES.labels(component=component).set(memory_bytes)
    CPU_USAGE_PERCENT.labels(component=component).set(cpu_percent)


def update_health_status(component: str, check_type: str, is_healthy: bool, duration: float):
    """
    Update component health status metrics.
    
    Args:
        component: Component name
        check_type: Type of health check
        is_healthy: Whether component is healthy
        duration: Check duration in seconds
    """
    COMPONENT_STATUS.labels(
        component=component,
        check_type=check_type
    ).set(1 if is_healthy else 0)
    
    HEALTH_CHECK_DURATION.labels(
        check_type=check_type,
        component=component
    ).observe(duration)


def update_celery_metrics(worker_count: int, active_tasks: int, queue_length: int, failed_tasks: int):
    """
    Update Celery-specific metrics with real data.
    
    Args:
        worker_count: Number of active workers
        active_tasks: Number of currently active tasks
        queue_length: Total queue length across all queues
        failed_tasks: Number of failed tasks
    """
    try:
        # Update existing gauges and counters with real data
        ACTIVE_REPORT_JOBS.set(active_tasks)
        
        # Update Celery-specific metrics
        CELERY_WORKER_COUNT.set(worker_count)
        CELERY_ACTIVE_TASKS.set(active_tasks)
        CELERY_QUEUE_LENGTH.labels(queue_name='total').set(queue_length)
        CELERY_FAILED_TASKS.set(failed_tasks)
        
        logger.debug(f"Updated Celery metrics: workers={worker_count}, active={active_tasks}, queue={queue_length}, failed={failed_tasks}")
        
    except Exception as e:
        logger.error(f"Failed to update Celery metrics: {e}")


def record_celery_queue_metrics(queue_metrics: Dict[str, Any]):
    """
    Record detailed Celery queue metrics.
    
    Args:
        queue_metrics: Dictionary containing queue metrics
    """
    try:
        # Record per-queue metrics
        for queue_name, queue_length in queue_metrics.get('queues', {}).items():
            CELERY_QUEUE_LENGTH.labels(queue_name=queue_name).set(queue_length)
        
        # Record total metrics
        total_length = queue_metrics.get('total_queue_length', 0)
        failed_tasks = queue_metrics.get('failed_tasks', 0)
        
        CELERY_QUEUE_LENGTH.labels(queue_name='total').set(total_length)
        CELERY_FAILED_TASKS.set(failed_tasks)
        
        logger.debug(f"Recorded queue metrics: total_length={total_length}, failed_tasks={failed_tasks}")
            
    except Exception as e:
        logger.error(f"Failed to record Celery queue metrics: {e}")


def record_external_service_metrics(service_name: str, endpoint: str, is_healthy: bool, response_time_seconds: float):
    """
    Record external service health metrics.
    
    Args:
        service_name: Name of the external service
        endpoint: Service endpoint
        is_healthy: Whether the service is healthy
        response_time_seconds: Response time in seconds
    """
    try:
        EXTERNAL_SERVICE_STATUS.labels(
            service_name=service_name,
            endpoint=endpoint
        ).set(1 if is_healthy else 0)
        
        EXTERNAL_SERVICE_RESPONSE_TIME.labels(
            service_name=service_name,
            endpoint=endpoint
        ).observe(response_time_seconds)
        
        logger.debug(f"Recorded external service metrics: {service_name} ({endpoint}) - healthy={is_healthy}, time={response_time_seconds}s")
        
    except Exception as e:
        logger.error(f"Failed to record external service metrics: {e}")


def record_api_metrics(method: str, endpoint: str, status_code: int, duration: float):
    """
    Record API request metrics.
    
    Args:
        method: HTTP method
        endpoint: API endpoint
        status_code: HTTP status code
        duration: Request duration in seconds
    """
    HTTP_REQUEST_COUNT.labels(
        method=method,
        endpoint=endpoint,
        status_code=str(status_code)
    ).inc()
    
    HTTP_REQUEST_DURATION.labels(
        method=method,
        endpoint=endpoint
    ).observe(duration)


def start_metrics_server(port: int = 8001):
    """
    Start Prometheus metrics server.
    
    Args:
        port: Port to serve metrics on
    """
    try:
        start_http_server(port)
        logger.info(f"Metrics server started on port {port}")
    except Exception as e:
        logger.error(f"Failed to start metrics server: {e}")


def get_metrics_data():
    """
    Get current metrics data in Prometheus format.
    
    Returns:
        Metrics data as string
    """
    return generate_latest()


# Initialize metrics collection
def initialize_metrics():
    """Initialize metrics collection system."""
    logger.info("Initializing Prometheus metrics collection")
    
    # Set initial values for gauges
    ACTIVE_REPORT_JOBS.set(0)
    ACTIVE_USERS.set(0)
    
    logger.info("Metrics collection initialized successfully")