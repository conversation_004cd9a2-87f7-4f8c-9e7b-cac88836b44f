# MCX3D Finance - Essential Development Commands

## Application Startup

### Docker Development Environment
```bash
# Start all services (recommended)
docker-compose up --build

# Start specific services
docker-compose up web db redis

# View logs
docker-compose logs -f web
docker-compose logs -f celery
```

### Manual Python Development
```bash
# Start FastAPI development server
python -m uvicorn mcx3d_finance.main:app --reload --host 0.0.0.0 --port 8000

# Start with monitoring
python mcx3d_finance/main_with_monitoring.py

# Start Celery worker
celery -A mcx3d_finance.tasks.celery_app worker --loglevel=info
```

## Code Quality & Formatting

### Automated Formatting
```bash
# Format code with Black (88 character limit)
black mcx3d_finance/ tests/

# Sort imports with isort
isort mcx3d_finance/ tests/

# Remove unused imports
autoflake --remove-all-unused-imports --recursive --in-place mcx3d_finance/
```

### Code Quality Checks
```bash
# Lint with flake8
flake8 mcx3d_finance/ tests/

# Static type checking
mypy mcx3d_finance/

# Advanced code analysis
pylint mcx3d_finance/

# Security vulnerability scan
bandit -r mcx3d_finance/
```

### Pre-commit Hooks
```bash
# Install pre-commit hooks
pre-commit install

# Run all hooks manually
pre-commit run --all-files

# Run specific hook
pre-commit run black --all-files
```

## Testing

### Test Execution
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=mcx3d_finance --cov-report=html

# Run specific test categories
pytest -m e2e                    # End-to-end tests
pytest tests/core/               # Core business logic
pytest tests/security/           # Security tests
pytest tests/integration/        # Integration tests

# Run tests in parallel
pytest -n auto                   # Auto-detect CPU cores
pytest -n 4                      # Use 4 parallel workers

# Performance benchmarks
pytest --benchmark-only
```

### Docker Testing
```bash
# Run tests in Docker container
docker-compose exec web pytest

# Run with coverage in Docker
docker-compose exec web pytest --cov=mcx3d_finance
```

## Database Management

### Alembic Migrations
```bash
# Generate new migration
docker-compose exec web alembic revision --autogenerate -m "Description"

# Apply migrations
docker-compose exec web alembic upgrade head

# Rollback migration
docker-compose exec web alembic downgrade -1

# View migration history
docker-compose exec web alembic history
```

### Database Operations
```bash
# Connect to PostgreSQL
docker-compose exec db psql -U postgres -d mcx3d_db

# Backup database
docker-compose exec db pg_dump -U postgres mcx3d_db > backup.sql

# Restore database
cat backup.sql | docker-compose exec -T db psql -U postgres -d mcx3d_db
```

## CLI Operations

### Report Generation
```bash
# Income statement
docker-compose exec web python -m mcx3d_finance.cli.main generate income-statement --organization-id 1 --period 2024-Q1 --format pdf

# Balance sheet
docker-compose exec web python -m mcx3d_finance.cli.main generate balance-sheet --organization-id 1 --date 2024-03-31 --format excel

# Cash flow statement
docker-compose exec web python -m mcx3d_finance.cli.main generate cash-flow --organization-id 1 --period 2024-Q1 --format html
```

### Valuation Models
```bash
# DCF valuation
docker-compose exec web python -m mcx3d_finance.cli.main valuate dcf --config config/dcf_example.json

# Multiples valuation
docker-compose exec web python -m mcx3d_finance.cli.main valuate multiples --config config/multiples_example.json
```

### Data Synchronization
```bash
# Sync Xero data
docker-compose exec web python -m mcx3d_finance.cli.main sync xero --org-id 1

# Incremental sync
docker-compose exec web python -m mcx3d_finance.cli.main sync xero --org-id 1 --incremental
```

## Monitoring & Health Checks

### Application Health
```bash
# Basic health check
curl http://localhost:8000/health/

# Detailed health check
curl http://localhost:8000/health/detailed

# Comprehensive health check
curl http://localhost:8000/health/comprehensive

# Monitoring status
curl http://localhost:8000/monitoring/status
```

### Metrics & Monitoring
```bash
# Prometheus metrics
curl http://localhost:8001/metrics

# Business dashboard
curl http://localhost:8000/monitoring/business-dashboard

# Test alert system
curl -X POST http://localhost:8000/monitoring/test-alert \
  -H "Content-Type: application/json" \
  -d '{"severity": "info", "title": "Test Alert", "message": "Testing alert system"}'
```

## Development Utilities

### Redis Operations
```bash
# Connect to Redis
docker-compose exec redis redis-cli

# Monitor Redis commands
docker-compose exec redis redis-cli monitor

# Check Redis info
docker-compose exec redis redis-cli info
```

### Log Monitoring
```bash
# Follow application logs
docker-compose logs -f web

# Follow Celery worker logs
docker-compose logs -f celery

# Follow all service logs
docker-compose logs -f
```

## System Commands (macOS/Darwin)

### File Operations
```bash
# List files with details
ls -la

# Find files
find . -name "*.py" -type f

# Search in files
grep -r "search_term" --include="*.py" .

# Directory navigation
cd /path/to/directory
pwd
```

### Process Management
```bash
# List processes
ps aux | grep python

# Kill process
kill -9 <pid>

# System information
top
df -h
```

## Task Completion Checklist

When completing development tasks:

1. **Code Quality**: `black . && isort . && flake8 . && mypy mcx3d_finance/`
2. **Security**: `bandit -r mcx3d_finance/`
3. **Testing**: `pytest --cov=mcx3d_finance`
4. **Pre-commit**: `pre-commit run --all-files`
5. **Documentation**: Update relevant documentation
6. **Migration**: Generate database migrations if needed