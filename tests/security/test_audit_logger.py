"""
Tests for the AuditLogger utility.
"""
import pytest
import json
from mcx3d_finance.utils.audit_logger import <PERSON>t<PERSON>og<PERSON>, AuditEventType, AuditSeverity

@pytest.mark.asyncio
class TestAuditLogger:
    """
    Test suite for the AuditLogger.
    """

    async def test_log_event(self, audit_logger: AuditLogger):
        """
        Test that events are logged correctly.
        """
        event_id = await audit_logger.log_event(
            event_type=AuditEventType.LOGIN_SUCCESS,
            action="login",
            outcome="success",
            user_id="1",
        )

        assert event_id is not None

        # Check that the event was written to the log file
        with open(audit_logger.audit_handler.baseFilename, "r") as f:
            log_entry = json.loads(f.readline())
            assert log_entry["event_id"] == event_id
            assert log_entry["event_type"] == AuditEventType.LOGIN_SUCCESS.value

    async def test_tamper_detection(self, audit_logger: AuditLogger):
        """
        Test that the integrity hash is correctly calculated and that
        any tampering with the log is detected.
        """
        event_id = await audit_logger.log_event(
            event_type=AuditEventType.LOGIN_SUCCESS,
            action="login",
            outcome="success",
            user_id="1",
        )

        # Read the log entry
        with open(audit_logger.audit_handler.baseFilename, "r") as f:
            log_entry = json.loads(f.readline())

        # Verify the integrity hash
        integrity_hash = log_entry["metadata"]["integrity_hash"]
        del log_entry["metadata"]["integrity_hash"]
        
        # Re-calculate the hash
        from mcx3d_finance.utils.audit_logger import AuditEvent
        event = AuditEvent(**log_entry)
        expected_hash = audit_logger._calculate_integrity_hash(event)

        assert integrity_hash == expected_hash

    async def test_event_correlation(self, audit_logger: AuditLogger):
        """
        Test that related events (e.g., multiple failed logins) are
        correctly correlated.
        """
        user_id = "2"

        # Simulate 4 failed logins
        for _ in range(4):
            await audit_logger.log_event(
                event_type=AuditEventType.LOGIN_FAILURE,
                action="login",
                outcome="failure",
                user_id=user_id,
            )

        # The 5th failed login should trigger an account locked event
        await audit_logger.log_event(
            event_type=AuditEventType.LOGIN_FAILURE,
            action="login",
            outcome="failure",
            user_id=user_id,
        )

        # Check for the account locked event
        with open(audit_logger.audit_handler.baseFilename, "r") as f:
            logs = f.readlines()
            assert any(
                AuditEventType.ACCOUNT_LOCKED.value in log for log in logs
            )

    async def test_compliance_report_generation(self, audit_logger: AuditLogger):
        """
        Test the generation of compliance reports.
        """
        from datetime import datetime

        report = await audit_logger.generate_compliance_report(
            start_date=datetime(2023, 1, 1), end_date=datetime(2023, 1, 31)
        )

        assert report["compliance_status"] == "compliant"
