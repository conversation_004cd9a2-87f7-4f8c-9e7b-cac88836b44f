"""
Business rule validation for the MCX3D financial system.

Provides validation for domain-specific business logic and financial constraints.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Tuple, Optional, Union

from mcx3d_finance.exceptions import (
    BusinessRuleValidationError,
    FinancialDataError,
    ValidationError
)

logger = logging.getLogger(__name__)


class BusinessRuleValidator:
    """
    Validator for business-specific rules and financial constraints.
    
    Enforces domain knowledge and business logic validation for financial operations.
    """
    
    def __init__(self):
        """Initialize the business rule validator."""
        self.validation_errors = []
        self.validation_warnings = []
    
    def validate_financial_statement_rules(
        self,
        financial_data: Dict[str, Any],
        statement_type: str
    ) -> Tuple[bool, List[str], List[str]]:
        """
        Validate business rules for financial statements.
        
        Args:
            financial_data: Financial statement data
            statement_type: Type of statement (balance_sheet, income_statement, cash_flow)
            
        Returns:
            Tuple of (is_valid, errors, warnings)
        """
        try:
            self.validation_errors = []
            self.validation_warnings = []
            
            if statement_type == 'balance_sheet':
                self._validate_balance_sheet_rules(financial_data)
            elif statement_type == 'income_statement':
                self._validate_income_statement_rules(financial_data)
            elif statement_type == 'cash_flow':
                self._validate_cash_flow_rules(financial_data)
            
            # Common rules for all statements
            self._validate_accounting_period_rules(financial_data)
            self._validate_materiality_rules(financial_data)
            
            is_valid = len(self.validation_errors) == 0
            
            if self.validation_errors:
                logger.warning(f"Financial statement business rules validation failed with {len(self.validation_errors)} errors")
            
            return is_valid, self.validation_errors, self.validation_warnings
            
        except Exception as e:
            raise BusinessRuleValidationError(
                f"Business rule validation failed: {e}",
                rule_name="financial_statement_validation"
            )
    
    def validate_valuation_rules(
        self,
        valuation_data: Dict[str, Any],
        valuation_method: str
    ) -> Tuple[bool, List[str], List[str]]:
        """
        Validate business rules for valuation models.
        
        Args:
            valuation_data: Valuation input data
            valuation_method: Valuation method (DCF, multiples, etc.)
            
        Returns:
            Tuple of (is_valid, errors, warnings)
        """
        try:
            self.validation_errors = []
            self.validation_warnings = []
            
            if valuation_method.upper() == 'DCF':
                self._validate_dcf_business_rules(valuation_data)
            elif valuation_method.upper() == 'MULTIPLES':
                self._validate_multiples_business_rules(valuation_data)
            elif valuation_method.upper() == 'SAAS':
                self._validate_saas_business_rules(valuation_data)
            
            # Common valuation rules
            self._validate_general_valuation_rules(valuation_data)
            
            is_valid = len(self.validation_errors) == 0
            
            return is_valid, self.validation_errors, self.validation_warnings
            
        except Exception as e:
            raise BusinessRuleValidationError(
                f"Valuation business rule validation failed: {e}",
                rule_name=f"{valuation_method}_validation"
            )
    
    def validate_saas_business_rules(
        self,
        saas_data: Dict[str, Any]
    ) -> Tuple[bool, List[str], List[str]]:
        """
        Validate SaaS-specific business rules.
        
        Args:
            saas_data: SaaS metrics and data
            
        Returns:
            Tuple of (is_valid, errors, warnings)
        """
        try:
            self.validation_errors = []
            self.validation_warnings = []
            
            # Core SaaS metrics validation
            self._validate_saas_metrics_consistency(saas_data)
            self._validate_saas_unit_economics(saas_data)
            self._validate_saas_growth_patterns(saas_data)
            self._validate_saas_cohort_analysis(saas_data)
            
            is_valid = len(self.validation_errors) == 0
            
            return is_valid, self.validation_errors, self.validation_warnings
            
        except Exception as e:
            raise BusinessRuleValidationError(
                f"SaaS business rule validation failed: {e}",
                rule_name="saas_business_rules"
            )
    
    def _validate_balance_sheet_rules(self, data: Dict[str, Any]):
        """Validate balance sheet business rules."""
        # Fundamental accounting equation: Assets = Liabilities + Equity
        assets = data.get('total_assets', 0)
        liabilities = data.get('total_liabilities', 0)
        equity = data.get('total_equity', 0)
        
        if assets and liabilities and equity:
            balance_difference = abs(assets - (liabilities + equity))
            tolerance = max(assets * 0.01, 1000)  # 1% or $1,000, whichever is larger
            
            if balance_difference > tolerance:
                self.validation_errors.append(
                    f"Balance sheet does not balance: Assets ({assets:,.2f}) ≠ "
                    f"Liabilities + Equity ({liabilities + equity:,.2f}). "
                    f"Difference: {balance_difference:,.2f}"
                )
        
        # Current assets should include liquid items
        current_assets = data.get('current_assets', 0)
        cash = data.get('cash', 0)
        
        if current_assets > 0 and cash == 0:
            self.validation_warnings.append(
                "Current assets reported but no cash balance. Verify cash and cash equivalents are included."
            )
        
        # Debt-to-equity ratio reasonableness
        total_debt = data.get('total_debt', 0)
        if equity > 0 and total_debt > 0:
            debt_to_equity = total_debt / equity
            if debt_to_equity > 5:
                self.validation_warnings.append(
                    f"High debt-to-equity ratio: {debt_to_equity:.2f}. "
                    "Consider if this level of leverage is sustainable."
                )
        
        # Working capital analysis
        current_liabilities = data.get('current_liabilities', 0)
        if current_assets > 0 and current_liabilities > 0:
            current_ratio = current_assets / current_liabilities
            if current_ratio < 1.0:
                self.validation_warnings.append(
                    f"Current ratio below 1.0 ({current_ratio:.2f}). "
                    "This may indicate potential liquidity issues."
                )
    
    def _validate_income_statement_rules(self, data: Dict[str, Any]):
        """Validate income statement business rules."""
        # Revenue recognition principles
        revenue = data.get('total_revenue', 0)
        if revenue < 0:
            self.validation_errors.append(
                "Negative revenue is generally not acceptable under standard accounting principles."
            )
        
        # Gross margin analysis
        cost_of_goods_sold = data.get('cost_of_goods_sold', 0)
        if revenue > 0 and cost_of_goods_sold >= 0:
            gross_margin = (revenue - cost_of_goods_sold) / revenue
            if gross_margin < 0:
                self.validation_warnings.append(
                    f"Negative gross margin ({gross_margin:.1%}). "
                    "Verify pricing strategy and cost structure."
                )
            elif gross_margin > 0.95:
                self.validation_warnings.append(
                    f"Unusually high gross margin ({gross_margin:.1%}). "
                    "Verify cost allocation and revenue recognition."
                )
        
        # Operating expenses reasonableness
        operating_expenses = data.get('operating_expenses', 0)
        if revenue > 0 and operating_expenses > 0:
            opex_ratio = operating_expenses / revenue
            if opex_ratio > 2.0:
                self.validation_warnings.append(
                    f"Operating expenses are {opex_ratio:.1f}x revenue. "
                    "This may indicate unsustainable cost structure."
                )
        
        # Tax rate reasonableness
        pretax_income = data.get('pretax_income', 0)
        tax_expense = data.get('tax_expense', 0)
        
        if pretax_income > 0 and tax_expense >= 0:
            effective_tax_rate = tax_expense / pretax_income
            if effective_tax_rate > 0.60:
                self.validation_warnings.append(
                    f"Effective tax rate appears high ({effective_tax_rate:.1%}). "
                    "Verify tax calculations and jurisdiction."
                )
        elif pretax_income < 0 and tax_expense > 0:
            self.validation_warnings.append(
                "Tax expense with pretax loss. Verify tax benefit recognition or timing differences."
            )
    
    def _validate_cash_flow_rules(self, data: Dict[str, Any]):
        """Validate cash flow statement business rules."""
        operating_cf = data.get('operating_cash_flow', 0)
        investing_cf = data.get('investing_cash_flow', 0)
        financing_cf = data.get('financing_cash_flow', 0)
        
        # Net cash flow consistency
        net_cash_flow = operating_cf + investing_cf + financing_cf
        reported_net_change = data.get('net_change_in_cash', 0)
        
        if reported_net_change and abs(net_cash_flow - reported_net_change) > 100:
            self.validation_errors.append(
                f"Cash flow components ({net_cash_flow:,.2f}) do not equal "
                f"reported net change in cash ({reported_net_change:,.2f})"
            )
        
        # Operating cash flow vs net income
        net_income = data.get('net_income', 0)
        if net_income != 0 and operating_cf != 0:
            cf_to_ni_ratio = operating_cf / net_income
            if abs(cf_to_ni_ratio) > 3:
                self.validation_warnings.append(
                    f"Large discrepancy between operating cash flow and net income "
                    f"(ratio: {cf_to_ni_ratio:.2f}). Verify working capital changes."
                )
        
        # Free cash flow calculation
        capex = data.get('capital_expenditures', 0)
        if operating_cf and capex:
            free_cash_flow = operating_cf - abs(capex)  # capex is usually negative
            if free_cash_flow < 0:
                self.validation_warnings.append(
                    f"Negative free cash flow ({free_cash_flow:,.2f}). "
                    "This may indicate high capital requirements or operating challenges."
                )
    
    def _validate_accounting_period_rules(self, data: Dict[str, Any]):
        """Validate accounting period and date rules."""
        period_start = data.get('period_start')
        period_end = data.get('period_end')
        
        if period_start and period_end:
            try:
                start_date = datetime.fromisoformat(period_start.replace('Z', '+00:00'))
                end_date = datetime.fromisoformat(period_end.replace('Z', '+00:00'))
                
                period_length = (end_date - start_date).days
                
                # Validate period length
                if period_length < 28:  # Less than 4 weeks
                    self.validation_warnings.append(
                        f"Short accounting period ({period_length} days). "
                        "Verify this is intentional and not a data error."
                    )
                elif period_length > 400:  # More than 13 months
                    self.validation_warnings.append(
                        f"Long accounting period ({period_length} days). "
                        "Verify period dates are correct."
                    )
                
                # Check for future dates
                if end_date > datetime.now():
                    future_days = (end_date - datetime.now()).days
                    if future_days > 5:  # Allow small buffer for different time zones
                        self.validation_warnings.append(
                            f"Period end date is {future_days} days in the future. "
                            "Verify this represents actual historical data."
                        )
                        
            except ValueError:
                self.validation_errors.append("Invalid date format in period dates")
    
    def _validate_materiality_rules(self, data: Dict[str, Any]):
        """Validate materiality and significant figures."""
        # Check for implausibly precise numbers
        revenue = data.get('total_revenue', 0)
        if revenue > 1000000:  # For companies with >$1M revenue
            # Check if revenue ends in exactly 00000 (suspiciously round)
            if revenue % 100000 == 0 and revenue > 0:
                self.validation_warnings.append(
                    f"Revenue appears rounded to nearest $100K ({revenue:,.0f}). "
                    "Verify precision is appropriate."
                )
    
    def _validate_dcf_business_rules(self, data: Dict[str, Any]):
        """Validate DCF-specific business rules."""
        # Terminal value reasonableness
        projections = data.get('projections', [])
        terminal_growth_rate = data.get('terminal_growth_rate', 0)
        
        if projections and terminal_growth_rate:
            # Terminal growth should generally not exceed GDP growth
            if terminal_growth_rate > 0.06:  # 6% is aggressive for long-term GDP growth
                self.validation_warnings.append(
                    f"Terminal growth rate ({terminal_growth_rate:.1%}) exceeds "
                    "typical long-term GDP growth expectations."
                )
            
            # Check for declining business with positive terminal growth
            if len(projections) >= 2:
                final_year_growth = ((projections[-1].get('revenue', 0) - 
                                    projections[-2].get('revenue', 1)) / 
                                   projections[-2].get('revenue', 1))
                
                if final_year_growth < -0.05 and terminal_growth_rate > 0.02:
                    self.validation_warnings.append(
                        "Declining revenue in final projection year with positive terminal growth. "
                        "Consider if terminal growth assumption is appropriate."
                    )
    
    def _validate_multiples_business_rules(self, data: Dict[str, Any]):
        """Validate multiples valuation business rules."""
        comparable_companies = data.get('comparable_companies', [])
        
        if len(comparable_companies) < 3:
            self.validation_warnings.append(
                f"Only {len(comparable_companies)} comparable companies. "
                "Consider expanding the peer group for more reliable multiples."
            )
        
        # Check for outlier multiples
        multiples = data.get('multiples', {})
        for multiple_type, value in multiples.items():
            if isinstance(value, (int, float)):
                if multiple_type == 'pe_ratio' and value > 100:
                    self.validation_warnings.append(
                        f"Very high P/E ratio ({value:.1f}). "
                        "Consider if this is appropriate for the business stage."
                    )
                elif multiple_type == 'ev_revenue' and value > 20:
                    self.validation_warnings.append(
                        f"Very high EV/Revenue multiple ({value:.1f}). "
                        "Verify this is justified by growth and margins."
                    )
    
    def _validate_saas_metrics_consistency(self, data: Dict[str, Any]):
        """Validate SaaS metrics for internal consistency."""
        arr = data.get('arr', 0)
        mrr = data.get('mrr', 0)
        
        # ARR should be approximately MRR * 12
        if arr and mrr:
            expected_arr = mrr * 12
            variance = abs(arr - expected_arr) / expected_arr
            
            if variance > 0.10:  # More than 10% variance
                self.validation_warnings.append(
                    f"ARR ({arr:,.0f}) and MRR ({mrr:,.0f}) inconsistency. "
                    f"Expected ARR ≈ {expected_arr:,.0f}"
                )
        
        # Customer metrics consistency
        total_customers = data.get('total_customers', 0)
        new_customers = data.get('new_customers', 0)
        churned_customers = data.get('churned_customers', 0)
        
        if total_customers and new_customers and churned_customers:
            net_new_customers = new_customers - churned_customers
            # This is a simplified check - in practice you'd need historical data
            if abs(net_new_customers) > total_customers * 0.5:
                self.validation_warnings.append(
                    "Large customer count change relative to base. "
                    "Verify customer acquisition and churn calculations."
                )
    
    def _validate_saas_unit_economics(self, data: Dict[str, Any]):
        """Validate SaaS unit economics business rules."""
        cac = data.get('customer_acquisition_cost', 0)
        ltv = data.get('customer_lifetime_value', 0)
        
        if cac > 0 and ltv > 0:
            ltv_cac_ratio = ltv / cac
            
            # LTV:CAC ratio benchmarks
            if ltv_cac_ratio < 1:
                self.validation_errors.append(
                    f"LTV:CAC ratio below 1.0 ({ltv_cac_ratio:.2f}). "
                    "Unit economics are unsustainable."
                )
            elif ltv_cac_ratio < 3:
                self.validation_warnings.append(
                    f"LTV:CAC ratio below 3.0 ({ltv_cac_ratio:.2f}). "
                    "Unit economics may be marginal."
                )
            elif ltv_cac_ratio > 15:
                self.validation_warnings.append(
                    f"LTV:CAC ratio very high ({ltv_cac_ratio:.2f}). "
                    "Consider if there's room for increased customer acquisition investment."
                )
        
        # Payback period
        monthly_revenue_per_customer = data.get('arpu', 0)  # Average Revenue Per User
        if cac > 0 and monthly_revenue_per_customer > 0:
            payback_months = cac / monthly_revenue_per_customer
            
            if payback_months > 24:
                self.validation_warnings.append(
                    f"CAC payback period is {payback_months:.1f} months. "
                    "Consider strategies to reduce CAC or increase ARPU."
                )
    
    def _validate_saas_growth_patterns(self, data: Dict[str, Any]):
        """Validate SaaS growth patterns and trends."""
        growth_rate = data.get('revenue_growth_rate', 0)
        churn_rate = data.get('churn_rate', 0)
        
        # High growth with high churn is problematic
        if growth_rate > 0.5 and churn_rate > 0.1:  # 50% growth with 10% churn
            self.validation_warnings.append(
                f"High growth ({growth_rate:.1%}) with high churn ({churn_rate:.1%}). "
                "Focus on retention to sustain growth."
            )
        
        # Net Revenue Retention (NRR)
        nrr = data.get('net_revenue_retention', 0)
        if nrr > 0:
            if nrr < 0.9:  # Less than 90%
                self.validation_warnings.append(
                    f"Net Revenue Retention below 90% ({nrr:.1%}). "
                    "This indicates significant customer downgrades or churn."
                )
            elif nrr > 1.3:  # Greater than 130%
                self.validation_warnings.append(
                    f"Net Revenue Retention above 130% ({nrr:.1%}). "
                    "Verify expansion revenue calculations."
                )
    
    def _validate_saas_cohort_analysis(self, data: Dict[str, Any]):
        """Validate SaaS cohort analysis if provided."""
        cohort_data = data.get('cohort_analysis', {})
        
        if cohort_data:
            # Check for cohort retention patterns
            retention_rates = cohort_data.get('retention_rates', [])
            
            if retention_rates:
                # Retention should generally decline over time
                for i in range(1, len(retention_rates)):
                    if retention_rates[i] > retention_rates[i-1] * 1.05:  # 5% tolerance
                        self.validation_warnings.append(
                            f"Cohort retention rate increases in month {i+1}. "
                            "Verify this pattern is accurate."
                        )
    
    def _validate_general_valuation_rules(self, data: Dict[str, Any]):
        """Validate general valuation business rules."""
        # Valuation date should not be too far in the future
        valuation_date = data.get('valuation_date')
        if valuation_date:
            try:
                val_date = datetime.fromisoformat(valuation_date.replace('Z', '+00:00'))
                if val_date > datetime.now() + timedelta(days=30):
                    self.validation_warnings.append(
                        "Valuation date is more than 30 days in the future. "
                        "Verify this is appropriate for the analysis."
                    )
            except ValueError:
                self.validation_errors.append("Invalid valuation date format")
        
        # Check for negative enterprise value
        enterprise_value = data.get('enterprise_value', 0)
        if enterprise_value < 0:
            self.validation_warnings.append(
                f"Negative enterprise value ({enterprise_value:,.2f}). "
                "This may indicate distressed situation or calculation error."
            )