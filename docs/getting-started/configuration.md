# MCX3D Financial System - Configuration Guide

## Overview

This guide covers all configuration options and customization parameters for the MCX3D Financial System Phase 1 components.

---

## Environment Configuration

### Required Environment Variables

Create a `.env` file in your project root:

```bash
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/mcx3d_finance
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# Application Settings
APP_ENV=development  # development, staging, production
LOG_LEVEL=INFO       # DEBUG, INFO, WARNING, ERROR
SECRET_KEY=your-secret-key-here

# Financial Calculation Defaults
DEFAULT_RISK_FREE_RATE=0.045
DEFAULT_MARKET_RISK_PREMIUM=0.065
DEFAULT_TAX_RATE=0.25
DEFAULT_TERMINAL_GROWTH=0.025

# Performance Settings
CACHE_TTL=3600  # Cache time-to-live in seconds
MAX_WORKERS=4   # For parallel processing
```

### Optional Environment Variables

```bash
# Xero Integration (for Phase 2)
XERO_CLIENT_ID=your_xero_client_id
XERO_CLIENT_SECRET=your_xero_client_secret
XERO_REDIRECT_URI=http://localhost:8000/auth/callback

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password

# Monitoring & Analytics
SENTRY_DSN=your_sentry_dsn
ANALYTICS_ENABLED=true
```

---

## Configuration Files

### Main Configuration (`config/settings.yaml`)

```yaml
# Application Settings
app:
  name: "MCX3D Financial System"
  version: "1.0.0"
  debug: false
  timezone: "UTC"

# Database Settings
database:
  pool_size: 20
  max_overflow: 30
  pool_timeout: 30
  pool_recycle: 3600
  echo: false  # Set to true for SQL debugging

# Cache Settings
cache:
  default_ttl: 3600
  max_memory: "256mb"
  eviction_policy: "allkeys-lru"

# Financial Calculation Settings
financial:
  default_currency: "USD"
  decimal_precision: 2
  rounding_method: "ROUND_HALF_UP"
  
  # Default assumptions
  assumptions:
    risk_free_rate: 0.045
    market_risk_premium: 0.065
    tax_rate: 0.25
    terminal_growth_rate: 0.025
    beta: 1.0
    
  # Cash flow settings
  cash_flow:
    default_method: "indirect"
    include_comparative: true
    amounts_in: "actual"  # actual, thousands, millions
    
  # DCF settings
  dcf:
    projection_years: 5
    monte_carlo_simulations: 10000
    sensitivity_steps: 5
    
    # Sensitivity ranges
    sensitivity:
      discount_rate: [-0.02, -0.01, 0.00, 0.01, 0.02]
      terminal_growth: [-0.01, -0.005, 0.00, 0.005, 0.01]
      
    # Volatility assumptions for Monte Carlo
    volatility:
      revenue: 0.15
      expenses: 0.10
      discount_rate: 0.02
      terminal_growth: 0.005
      
  # Multiples valuation settings
  multiples:
    outlier_threshold: 2.0  # Z-score threshold
    confidence_weights: true
    default_multiples:
      - "ev_revenue"
      - "ev_ebitda"
      - "pe_ratio"
      - "price_to_book"
      - "ev_fcf"
      
    # Default weights (if not specified)
    default_weights:
      ev_revenue: 0.25
      ev_ebitda: 0.30
      pe_ratio: 0.25
      ev_fcf: 0.20

# SaaS KPI Settings
saas:
  # Industry benchmarks
  benchmarks:
    growth_rate:
      excellent: 25
      good: 15
      fair: 10
    churn_rate:
      excellent: 2
      good: 5
      fair: 10
    ltv_cac_ratio:
      excellent: 7
      good: 5
      fair: 3
    nrr:
      excellent: 120
      good: 110
      fair: 100
    gross_margin:
      excellent: 85
      good: 75
      fair: 65
    rule_of_40:
      excellent: 60
      good: 50
      fair: 40
      
  # Health score weights
  health_score_weights:
    revenue: 0.25
    customer: 0.25
    unit_economics: 0.25
    efficiency: 0.25

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/mcx3d_finance.log"
  max_size: "10MB"
  backup_count: 5
  
  # Component-specific logging
  loggers:
    cash_flow: "INFO"
    dcf: "INFO"
    multiples: "INFO"
    saas_kpis: "INFO"
    database: "WARNING"
    cache: "WARNING"

# Performance Settings
performance:
  max_workers: 4
  chunk_size: 1000
  timeout: 30
  
  # Memory limits
  memory:
    max_heap: "1GB"
    gc_threshold: 0.8
    
  # Query optimization
  database:
    query_timeout: 30
    batch_size: 500
    connection_timeout: 10
```

### Industry Benchmarks (`config/benchmarks.yaml`)

```yaml
# SaaS Industry Benchmarks by Company Stage
saas_benchmarks:
  early_stage:  # < $1M ARR
    growth_rate: 100
    churn_rate: 15
    ltv_cac_ratio: 3
    gross_margin: 70
    
  growth_stage:  # $1M - $10M ARR
    growth_rate: 50
    churn_rate: 10
    ltv_cac_ratio: 5
    gross_margin: 75
    
  scale_stage:  # $10M+ ARR
    growth_rate: 25
    churn_rate: 5
    ltv_cac_ratio: 7
    gross_margin: 80

# Industry Multiples by Sector
multiples_benchmarks:
  saas:
    ev_revenue: [3.0, 8.0]  # [min, max]
    ev_ebitda: [10.0, 25.0]
    pe_ratio: [20.0, 40.0]
    
  fintech:
    ev_revenue: [2.5, 6.0]
    ev_ebitda: [8.0, 20.0]
    pe_ratio: [15.0, 35.0]
    
  healthcare_tech:
    ev_revenue: [4.0, 10.0]
    ev_ebitda: [12.0, 30.0]
    pe_ratio: [25.0, 50.0]
```

---

## Customization Options

### Custom Financial Assumptions

```python
# Override default assumptions
from mcx3d_finance.core.valuation.dcf import DCFValuation

dcf = DCFValuation()
dcf.risk_free_rate = 0.035  # 3.5% risk-free rate
dcf.market_risk_premium = 0.070  # 7% market risk premium

# Custom WACC calculation
custom_wacc = dcf.calculate_wacc(
    market_value_equity=*********,
    market_value_debt=20000000,
    cost_of_equity=0.12,
    cost_of_debt=0.05,
    tax_rate=0.21  # Corporate tax rate
)
```

### Custom SaaS Benchmarks

```python
from mcx3d_finance.core.metrics.saas_kpis import SaaSKPICalculator

calculator = SaaSKPICalculator()

# Override industry benchmarks
custom_benchmarks = {
    "mrr_growth_rate": {"excellent": 30, "good": 20, "fair": 15},
    "customer_churn_rate": {"excellent": 1, "good": 3, "fair": 7},
    "ltv_cac_ratio": {"excellent": 10, "good": 7, "fair": 5},
}

# Use in calculation
result = calculator.calculate_comprehensive_kpis(
    organization_id=1,
    period_start=datetime(2024, 1, 1),
    period_end=datetime(2024, 12, 31)
)
```

### Custom Multiples Weights

```python
from mcx3d_finance.core.valuation.multiples import MultiplesValuation

multiples = MultiplesValuation()

# Custom weights for different multiples
custom_weights = {
    "ev_revenue": 0.40,    # Higher weight on revenue multiple
    "ev_ebitda": 0.35,     # Standard weight
    "pe_ratio": 0.15,      # Lower weight on P/E
    "ev_fcf": 0.10,        # Lower weight on FCF multiple
}

result = multiples.calculate_comprehensive_multiples_valuation(
    target_metrics=target_metrics,
    comparable_companies=comparables,
    weights=custom_weights
)
```

---

## Database Configuration

### PostgreSQL Optimization

```sql
-- Recommended PostgreSQL settings for MCX3D Finance
-- Add to postgresql.conf

shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# Connection settings
max_connections = 100
shared_preload_libraries = 'pg_stat_statements'

# Logging
log_statement = 'mod'
log_min_duration_statement = 1000  # Log slow queries
```

### Required Indexes

```sql
-- Essential indexes for performance
CREATE INDEX idx_transactions_org_date ON transactions(organization_id, date);
CREATE INDEX idx_transactions_account ON transactions(account_id);
CREATE INDEX idx_accounts_org_type ON accounts(organization_id, type);
CREATE INDEX idx_customers_org_signup ON customers(organization_id, signup_date);
CREATE INDEX idx_subscriptions_customer ON subscriptions(customer_id);
```

---

## Monitoring & Alerting

### Health Check Endpoints

```python
# Add to your FastAPI app
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow(),
        "version": "1.0.0",
        "components": {
            "database": "healthy",
            "cache": "healthy",
            "calculations": "healthy"
        }
    }
```

### Performance Monitoring

```python
# Custom metrics collection
import time
from functools import wraps

def monitor_performance(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        execution_time = time.time() - start_time
        
        # Log performance metrics
        logger.info(f"{func.__name__} executed in {execution_time:.2f}s")
        
        return result
    return wrapper
```

---

## Security Configuration

### API Security

```yaml
# Security settings
security:
  api_key_required: true
  rate_limiting:
    requests_per_minute: 100
    burst_limit: 20
    
  cors:
    allowed_origins: ["http://localhost:3000", "https://yourdomain.com"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE"]
    allowed_headers: ["*"]
    
  authentication:
    jwt_secret: "your-jwt-secret"
    token_expiry: 3600  # 1 hour
    
  encryption:
    algorithm: "AES-256-GCM"
    key_rotation_days: 90
```

### Data Protection

```python
# Sensitive data handling
from cryptography.fernet import Fernet

class DataProtection:
    def __init__(self, key: bytes):
        self.cipher = Fernet(key)
    
    def encrypt_financial_data(self, data: dict) -> str:
        """Encrypt sensitive financial data."""
        json_data = json.dumps(data)
        return self.cipher.encrypt(json_data.encode()).decode()
    
    def decrypt_financial_data(self, encrypted_data: str) -> dict:
        """Decrypt sensitive financial data."""
        decrypted = self.cipher.decrypt(encrypted_data.encode())
        return json.loads(decrypted.decode())
```

---

## Deployment Configuration

### Docker Configuration

```dockerfile
# Production Dockerfile optimizations
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health')"

# Run application
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "main:app"]
```

### Kubernetes Configuration

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mcx3d-finance
spec:
  replicas: 3
  selector:
    matchLabels:
      app: mcx3d-finance
  template:
    metadata:
      labels:
        app: mcx3d-finance
    spec:
      containers:
      - name: mcx3d-finance
        image: mcx3d-finance:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: mcx3d-secrets
              key: database-url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

This configuration guide provides comprehensive setup options for all Phase 1 components. Adjust settings based on your specific deployment environment and requirements.
