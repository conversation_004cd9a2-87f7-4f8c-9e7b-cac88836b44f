# Xero Authorization Steps

## Current Status
- ✅ Xero credentials added to .env
- ✅ Organization created (ID: 1)
- ❌ OAuth authorization needed before data sync

## Required Steps

### 1. Fix the Server Issue
There's an indentation error in `mcx3d_finance/api/auth.py` at line 579 that needs to be fixed first.

### 2. Complete OAuth Authorization
Once the server is running, you need to:

1. Visit this URL in your browser:
   ```
   http://localhost:8000/api/auth/xero/authorize?organization_id=1
   ```

2. Log in to Xero and authorize the app

3. You'll be redirected back and the authorization will be saved

### 3. Then Run Data Sync
After authorization is complete:
```bash
source venv39/bin/activate
python -m mcx3d_finance.cli.main sync xero --org-id 1 --show-progress
```

## Alternative: Manual Token Setup
If you have an existing Xero access token, you can manually add it to the database, but OAuth flow is recommended for proper token refresh handling.

## Note
The sync command will fail with "Organization not found or Xero token is missing" until the OAuth authorization is completed, as the organization currently has no Xero token stored.