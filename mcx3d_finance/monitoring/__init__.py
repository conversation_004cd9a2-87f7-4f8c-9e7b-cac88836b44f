"""
MCX3D Financial System - Production Monitoring Package

This package provides comprehensive production monitoring, observability, and alerting
capabilities for the MCX3D financial valuation platform.

Components:
- structured_logger: Correlation IDs and business event logging
- metrics: Prometheus metrics collection for business KPIs
- alerting: Multi-channel alerting system with escalation
- business_intelligence: Financial KPI monitoring and anomaly detection
- health_checker: Comprehensive system health monitoring
- audit_trails: Financial transaction and user activity logging
"""

from .structured_logger import (
    StructuredLogger, 
    get_correlation_id,
    main_logger,
    api_logger,
    reporting_logger,
    valuation_logger,
    integration_logger,
    auth_logger
)
from .metrics import (
    REPORT_GENERATION_COUNT,
    VALUATION_PROCESSING_TIME,
    ACTIVE_REPORT_JOBS,
    DATABASE_CONNECTION_POOL,
    monitor_performance,
    monitor_business_event,
    initialize_metrics,
    start_metrics_server
)
from .alerting import (
    AlertManager, 
    AlertSeverity,
    alert_manager,
    anomaly_detector,
    alert_system_error,
    alert_business_anomaly,
    alert_performance_degradation
)
from .business_intelligence import (
    BusinessIntelligenceCollector, 
    FinancialKPI,
    TrendDirection,
    BusinessAnomalyAlert
)
from .health_checker import HealthChecker, HealthStatus
from .audit_trails import (
    AuditTrailManager,
    AuditRecord,
    AuditEventType,
    AuditSeverity,
    audit_manager,
    audit_financial_transaction,
    audit_data_change,
    audit_user_login,
    audit_report_creation,
    audit_valuation
)

__all__ = [
    # Structured Logging
    "StructuredLogger",
    "get_correlation_id",
    "main_logger",
    "api_logger", 
    "reporting_logger",
    "valuation_logger",
    "integration_logger",
    "auth_logger",
    
    # Metrics
    "REPORT_GENERATION_COUNT",
    "VALUATION_PROCESSING_TIME", 
    "ACTIVE_REPORT_JOBS",
    "DATABASE_CONNECTION_POOL",
    "monitor_performance",
    "monitor_business_event",
    "initialize_metrics",
    "start_metrics_server",
    
    # Alerting
    "AlertManager",
    "AlertSeverity",
    "alert_manager",
    "anomaly_detector",
    "alert_system_error",
    "alert_business_anomaly", 
    "alert_performance_degradation",
    
    # Business Intelligence
    "BusinessIntelligenceCollector",
    "FinancialKPI",
    "TrendDirection",
    "BusinessAnomalyAlert",
    
    # Health Checking
    "HealthChecker",
    "HealthStatus",
    
    # Audit Trails
    "AuditTrailManager",
    "AuditRecord",
    "AuditEventType",
    "AuditSeverity", 
    "audit_manager",
    "audit_financial_transaction",
    "audit_data_change",
    "audit_user_login",
    "audit_report_creation",
    "audit_valuation"
]

__version__ = "1.0.0"