# Custom Claude Code Commands

This directory contains custom slash commands for this project.

## Available Commands

### `/cleanup` - Intelligent Project Cleanup & Organization

**Purpose**: Systematically clean up code repositories by removing AI artifacts, organizing misplaced files, and maintaining proper project structure across different project types.

**Usage Examples**:
```bash
/cleanup                    # Full intelligent cleanup with safe defaults
/cleanup --dry-run          # Preview changes without executing
/cleanup --type artifacts   # Only remove AI artifacts
/cleanup --type organization # Only fix file organization
/cleanup --aggressive       # More thorough cleanup
/cleanup root               # Focus on root directory only
```

**Key Features**:
- ✅ **Multi-Project Support**: Detects Python, Node.js, and hybrid projects
- ✅ **AI Artifact Detection**: Identifies demo files, temporary scripts, issue docs
- ✅ **Safe File Organization**: Moves files to conventional locations
- ✅ **Progress Tracking**: Uses TodoWrite for transparent progress
- ✅ **Comprehensive Reporting**: Detailed summary of all changes
- ✅ **Rollback Support**: Provides undo instructions for all operations

**Safety Features**:
- Never deletes legitimate project files
- Provides clear logging of all operations
- Respects .gitignore patterns
- Creates rollback information
- Stops immediately if operations seem unsafe

## How to Use

1. **Direct Usage**: Type `/cleanup` in Claude Code to run the command
2. **With Options**: Add arguments like `/cleanup --dry-run` to preview changes
3. **Help**: The command provides built-in guidance and error handling

## Command Development

To create additional custom commands:
1. Create a new `.md` file in this directory
2. The filename becomes the command name (e.g., `deploy.md` → `/deploy`)
3. Use `$ARGUMENTS` placeholder for dynamic arguments
4. Follow the established pattern for comprehensive, safe operations

## Integration

These commands are:
- **Version Controlled**: Checked into Git for team sharing
- **Project Specific**: Tailored to this project's structure and needs
- **Cross-Platform**: Work on macOS, Linux, and Windows
- **Extensible**: Easy to modify and enhance over time