"""
Executive Summary Generator for MCX3D LTD Financial Documentation

Generates executive summaries, financial highlights, and management discussion & analysis
using data from Xero and calculations from core modules.
"""

import logging
from datetime import datetime, date
from typing import Dict, Any, List, Optional, Tuple
from decimal import Decimal

from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import (
    SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle,
    PageBreak, KeepTogether, Image
)
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_RIGHT, TA_JUSTIFY

from ..company import COMPANY_INFO, BRANDING_CONFIG
from ..core.financial_calculators import UKFinancialCalculator
from ..core.metrics.saas_kpis import SaaSKPICalculator
from ..core.valuation.saas_valuation import SaaSValuation

logger = logging.getLogger(__name__)


class ExecutiveSummaryGenerator:
    """Generates executive summary and management discussion & analysis documents."""
    
    def __init__(self):
        self.company_info = COMPANY_INFO
        self.branding = BRANDING_CONFIG
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
        
    def _setup_custom_styles(self):
        """Set up custom paragraph styles based on branding."""
        # Title style
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Title'],
            fontSize=self.branding['typography']['sizes']['h1'],
            textColor=colors.HexColor(self.branding['colors']['primary']),
            spaceAfter=24,
            alignment=TA_CENTER
        ))
        
        # Heading styles
        self.styles.add(ParagraphStyle(
            name='CustomHeading1',
            parent=self.styles['Heading1'],
            fontSize=self.branding['typography']['sizes']['h2'],
            textColor=colors.HexColor(self.branding['colors']['primary']),
            spaceAfter=12,
            spaceBefore=18
        ))
        
        self.styles.add(ParagraphStyle(
            name='CustomHeading2',
            parent=self.styles['Heading2'],
            fontSize=self.branding['typography']['sizes']['h3'],
            textColor=colors.HexColor(self.branding['colors']['primary_dark']),
            spaceAfter=10,
            spaceBefore=14
        ))
        
        # Body text
        self.styles.add(ParagraphStyle(
            name='CustomBody',
            parent=self.styles['BodyText'],
            fontSize=self.branding['typography']['sizes']['body'],
            leading=self.branding['typography']['sizes']['body'] * self.branding['typography']['line_heights']['normal'],
            alignment=TA_JUSTIFY
        ))
        
        # Executive highlight style
        self.styles.add(ParagraphStyle(
            name='Highlight',
            parent=self.styles['Normal'],
            fontSize=self.branding['typography']['sizes']['h4'],
            textColor=colors.HexColor(self.branding['colors']['secondary_dark']),
            leftIndent=20,
            rightIndent=20,
            spaceAfter=12,
            spaceBefore=12,
            borderColor=colors.HexColor(self.branding['colors']['secondary']),
            borderWidth=2,
            borderPadding=10,
            borderRadius=5
        ))
        
    def generate_executive_summary(self, financial_data: Dict[str, Any], 
                                 output_path: str,
                                 report_date: Optional[date] = None) -> str:
        """
        Generate comprehensive executive summary PDF.
        
        Args:
            financial_data: Dictionary containing financial statements and metrics
            output_path: Path to save the PDF
            report_date: Date of the report (defaults to today)
            
        Returns:
            Path to the generated PDF
        """
        if report_date is None:
            report_date = date.today()
            
        # Create document
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=self.branding['report_styles']['page']['margin_right'],
            leftMargin=self.branding['report_styles']['page']['margin_left'],
            topMargin=self.branding['report_styles']['page']['margin_top'],
            bottomMargin=self.branding['report_styles']['page']['margin_bottom']
        )
        
        # Build content
        story = []
        
        # Title page
        story.extend(self._create_title_page(report_date))
        story.append(PageBreak())
        
        # Executive summary
        story.extend(self._create_executive_summary_section(financial_data, report_date))
        story.append(PageBreak())
        
        # Financial highlights
        story.extend(self._create_financial_highlights_section(financial_data))
        story.append(PageBreak())
        
        # Key performance indicators
        story.extend(self._create_kpi_section(financial_data))
        story.append(PageBreak())
        
        # Management discussion & analysis
        story.extend(self._create_mda_section(financial_data, report_date))
        
        # Build PDF
        doc.build(story)
        logger.info(f"Executive summary generated: {output_path}")
        
        return output_path
        
    def _create_title_page(self, report_date: date) -> List:
        """Create the title page."""
        elements = []
        
        # Add some space at the top
        elements.append(Spacer(1, 2 * inch))
        
        # Company name
        elements.append(Paragraph(
            self.company_info['trading_name'],
            self.styles['CustomTitle']
        ))
        
        elements.append(Paragraph(
            f"({self.company_info['legal_name']})",
            self.styles['CustomBody']
        ))
        
        elements.append(Spacer(1, 0.5 * inch))
        
        # Report title
        elements.append(Paragraph(
            "Executive Financial Summary",
            self.styles['CustomHeading1']
        ))
        
        # Report period
        current_period = self.company_info['REPORTING_PERIODS']['current_year']
        elements.append(Paragraph(
            f"For the Year Ended {current_period['end'].strftime('%d %B %Y')}",
            self.styles['CustomHeading2']
        ))
        
        elements.append(Spacer(1, 1 * inch))
        
        # Report date
        elements.append(Paragraph(
            f"Report Date: {report_date.strftime('%d %B %Y')}",
            self.styles['CustomBody']
        ))
        
        # Company number
        elements.append(Paragraph(
            f"Company Number: {self.company_info['company_number']}",
            self.styles['CustomBody']
        ))
        
        # Add confidentiality notice
        elements.append(Spacer(1, 2 * inch))
        elements.append(Paragraph(
            self.branding['confidentiality_notice'],
            ParagraphStyle(
                name='Confidential',
                parent=self.styles['Normal'],
                fontSize=8,
                textColor=colors.grey,
                alignment=TA_CENTER
            )
        ))
        
        return elements
        
    def _create_executive_summary_section(self, financial_data: Dict[str, Any], 
                                        report_date: date) -> List:
        """Create executive summary section."""
        elements = []
        
        elements.append(Paragraph("Executive Summary", self.styles['CustomHeading1']))
        elements.append(Spacer(1, 0.25 * inch))
        
        # Company overview
        elements.append(Paragraph("Company Overview", self.styles['CustomHeading2']))
        elements.append(Paragraph(
            self.company_info['description'],
            self.styles['CustomBody']
        ))
        elements.append(Spacer(1, 0.25 * inch))
        
        # Financial performance summary
        elements.append(Paragraph("Financial Performance Summary", self.styles['CustomHeading2']))
        
        # Calculate key metrics
        revenue = financial_data.get('income_statement', {}).get('revenue', 0)
        net_income = financial_data.get('income_statement', {}).get('net_income', 0)
        total_assets = financial_data.get('balance_sheet', {}).get('total_assets', 0)
        
        # Performance narrative
        performance_text = self._generate_performance_narrative(financial_data)
        elements.append(Paragraph(performance_text, self.styles['CustomBody']))
        
        # Key achievements
        elements.append(Spacer(1, 0.25 * inch))
        elements.append(Paragraph("Key Achievements", self.styles['CustomHeading2']))
        
        achievements = self._generate_achievements_list(financial_data)
        for achievement in achievements:
            elements.append(Paragraph(f"• {achievement}", self.styles['CustomBody']))
            
        return elements
        
    def _create_financial_highlights_section(self, financial_data: Dict[str, Any]) -> List:
        """Create financial highlights section with key metrics."""
        elements = []
        
        elements.append(Paragraph("Financial Highlights", self.styles['CustomHeading1']))
        elements.append(Spacer(1, 0.25 * inch))
        
        # Create highlights table
        highlights_data = self._prepare_highlights_data(financial_data)
        
        # Create visual KPI boxes
        kpi_table_data = []
        kpi_styles = []
        
        # Row 1: Revenue and Growth
        revenue_current = highlights_data['revenue']['current']
        revenue_prior = highlights_data['revenue']['prior']
        revenue_growth = highlights_data['revenue']['growth']
        
        kpi_table_data.append([
            self._create_kpi_box("Revenue", revenue_current, revenue_growth),
            self._create_kpi_box("Gross Profit", highlights_data['gross_profit']['current'], 
                               highlights_data['gross_profit']['growth']),
            self._create_kpi_box("Net Income", highlights_data['net_income']['current'],
                               highlights_data['net_income']['growth'])
        ])
        
        # Row 2: Margins
        kpi_table_data.append([
            self._create_kpi_box("Gross Margin", f"{highlights_data['gross_margin']['current']:.1f}%",
                               f"{highlights_data['gross_margin']['change']:.1f}pp"),
            self._create_kpi_box("Operating Margin", f"{highlights_data['operating_margin']['current']:.1f}%",
                               f"{highlights_data['operating_margin']['change']:.1f}pp"),
            self._create_kpi_box("Net Margin", f"{highlights_data['net_margin']['current']:.1f}%",
                               f"{highlights_data['net_margin']['change']:.1f}pp")
        ])
        
        # Create table
        kpi_table = Table(kpi_table_data, colWidths=[2*inch, 2*inch, 2*inch])
        kpi_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor(self.branding['colors']['lighter_gray'])),
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor(self.branding['colors']['background'])),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
        ]))
        
        elements.append(kpi_table)
        elements.append(Spacer(1, 0.5 * inch))
        
        # Comparative analysis table
        elements.append(Paragraph("Year-over-Year Comparison", self.styles['CustomHeading2']))
        
        comparison_data = [
            ['Metric', 'Current Year', 'Prior Year', 'Change', '% Change'],
            ['Revenue', self._format_currency(revenue_current), 
             self._format_currency(revenue_prior),
             self._format_currency(revenue_current - revenue_prior),
             f"{revenue_growth:.1f}%"],
            # Add more rows as needed
        ]
        
        comparison_table = Table(comparison_data)
        comparison_table.setStyle(self._get_standard_table_style())
        
        elements.append(comparison_table)
        
        return elements
        
    def _create_kpi_section(self, financial_data: Dict[str, Any]) -> List:
        """Create SaaS KPI section."""
        elements = []
        
        elements.append(Paragraph("Key Performance Indicators", self.styles['CustomHeading1']))
        elements.append(Spacer(1, 0.25 * inch))
        
        # Calculate SaaS metrics if applicable
        if 'saas_metrics' in financial_data:
            saas_data = financial_data['saas_metrics']
            
            elements.append(Paragraph("SaaS Metrics", self.styles['CustomHeading2']))
            
            # Create SaaS metrics table
            saas_table_data = [
                ['Metric', 'Current', 'Prior', 'Change'],
                ['Monthly Recurring Revenue (MRR)', 
                 self._format_currency(saas_data.get('mrr', 0)),
                 self._format_currency(saas_data.get('mrr_prior', 0)),
                 f"{saas_data.get('mrr_growth', 0):.1f}%"],
                ['Annual Recurring Revenue (ARR)',
                 self._format_currency(saas_data.get('arr', 0)),
                 self._format_currency(saas_data.get('arr_prior', 0)),
                 f"{saas_data.get('arr_growth', 0):.1f}%"],
                ['Customer Acquisition Cost (CAC)',
                 self._format_currency(saas_data.get('cac', 0)),
                 self._format_currency(saas_data.get('cac_prior', 0)),
                 f"{saas_data.get('cac_change', 0):.1f}%"],
                ['Customer Lifetime Value (LTV)',
                 self._format_currency(saas_data.get('ltv', 0)),
                 self._format_currency(saas_data.get('ltv_prior', 0)),
                 f"{saas_data.get('ltv_change', 0):.1f}%"],
                ['LTV/CAC Ratio',
                 f"{saas_data.get('ltv_cac_ratio', 0):.1f}x",
                 f"{saas_data.get('ltv_cac_ratio_prior', 0):.1f}x",
                 f"{saas_data.get('ltv_cac_change', 0):.1f}x"],
                ['Monthly Churn Rate',
                 f"{saas_data.get('churn_rate', 0):.1f}%",
                 f"{saas_data.get('churn_rate_prior', 0):.1f}%",
                 f"{saas_data.get('churn_change', 0):.1f}pp"],
            ]
            
            saas_table = Table(saas_table_data, colWidths=[2.5*inch, 1.5*inch, 1.5*inch, 1*inch])
            saas_table.setStyle(self._get_standard_table_style())
            
            elements.append(saas_table)
            
        # Financial ratios
        elements.append(Spacer(1, 0.25 * inch))
        elements.append(Paragraph("Financial Ratios", self.styles['CustomHeading2']))
        
        ratios = self._calculate_financial_ratios(financial_data)
        ratio_table_data = [
            ['Category', 'Ratio', 'Current', 'Prior', 'Industry Avg'],
            ['Liquidity', 'Current Ratio', f"{ratios['current_ratio']:.2f}", 
             f"{ratios['current_ratio_prior']:.2f}", "2.0"],
            ['Liquidity', 'Quick Ratio', f"{ratios['quick_ratio']:.2f}",
             f"{ratios['quick_ratio_prior']:.2f}", "1.5"],
            ['Efficiency', 'Asset Turnover', f"{ratios['asset_turnover']:.2f}",
             f"{ratios['asset_turnover_prior']:.2f}", "1.2"],
            ['Profitability', 'ROE', f"{ratios['roe']:.1f}%",
             f"{ratios['roe_prior']:.1f}%", "15.0%"],
            ['Profitability', 'ROA', f"{ratios['roa']:.1f}%",
             f"{ratios['roa_prior']:.1f}%", "10.0%"],
        ]
        
        ratio_table = Table(ratio_table_data, colWidths=[1.5*inch, 1.5*inch, 1*inch, 1*inch, 1*inch])
        ratio_table.setStyle(self._get_standard_table_style())
        
        elements.append(ratio_table)
        
        return elements
        
    def _create_mda_section(self, financial_data: Dict[str, Any], report_date: date) -> List:
        """Create Management Discussion & Analysis section."""
        elements = []
        
        elements.append(Paragraph("Management Discussion & Analysis", self.styles['CustomHeading1']))
        elements.append(Spacer(1, 0.25 * inch))
        
        # Business Overview
        elements.append(Paragraph("Business Overview", self.styles['CustomHeading2']))
        elements.append(Paragraph(
            f"{self.company_info['trading_name']} has continued to strengthen its position "
            f"as a leading 3D technology company in the e-commerce solutions space. "
            f"Our proprietary Morpho 3D Platform has enabled clients to transform their "
            f"digital experiences, resulting in improved customer engagement and conversion rates.",
            self.styles['CustomBody']
        ))
        elements.append(Spacer(1, 0.25 * inch))
        
        # Financial Performance Review
        elements.append(Paragraph("Financial Performance Review", self.styles['CustomHeading2']))
        
        performance_review = self._generate_performance_review(financial_data)
        elements.append(Paragraph(performance_review, self.styles['CustomBody']))
        elements.append(Spacer(1, 0.25 * inch))
        
        # Strategic Initiatives
        elements.append(Paragraph("Strategic Initiatives", self.styles['CustomHeading2']))
        initiatives = [
            "Expansion of the Morpho 3D Platform capabilities to include AI-powered personalization",
            "Strategic partnerships with major e-commerce platforms",
            "Investment in R&D to maintain technological leadership",
            "International market expansion, particularly in the US and European markets",
            "Enhancement of our SaaS offering to improve recurring revenue streams"
        ]
        
        for initiative in initiatives:
            elements.append(Paragraph(f"• {initiative}", self.styles['CustomBody']))
        
        elements.append(Spacer(1, 0.25 * inch))
        
        # Risk Factors
        elements.append(Paragraph("Risk Factors and Mitigation", self.styles['CustomHeading2']))
        risks = [
            ("Technology Risk", "Continuous investment in R&D and talent acquisition"),
            ("Market Competition", "Focus on innovation and superior customer experience"),
            ("Customer Concentration", "Diversification of customer base and markets"),
            ("Cybersecurity", "Implementation of robust security measures and regular audits")
        ]
        
        for risk, mitigation in risks:
            elements.append(Paragraph(f"<b>{risk}:</b> {mitigation}", self.styles['CustomBody']))
        
        elements.append(Spacer(1, 0.25 * inch))
        
        # Outlook
        elements.append(Paragraph("Outlook", self.styles['CustomHeading2']))
        elements.append(Paragraph(
            f"Looking ahead, {self.company_info['trading_name']} is well-positioned for continued growth. "
            f"The increasing demand for immersive digital experiences and the shift towards 3D commerce "
            f"presents significant opportunities. We remain committed to innovation, operational excellence, "
            f"and delivering value to our stakeholders.",
            self.styles['CustomBody']
        ))
        
        return elements
        
    def _create_kpi_box(self, title: str, value: str, change: str) -> Paragraph:
        """Create a formatted KPI box for display."""
        # Determine color based on change
        if isinstance(change, str) and change.endswith('%'):
            change_val = float(change.rstrip('%'))
            if change_val > 0:
                change_color = self.branding['colors']['success']
                arrow = "↑"
            elif change_val < 0:
                change_color = self.branding['colors']['danger']
                arrow = "↓"
            else:
                change_color = self.branding['colors']['gray']
                arrow = "→"
        else:
            change_color = self.branding['colors']['gray']
            arrow = ""
            
        return Paragraph(
            f"<b>{title}</b><br/>"
            f"<font size='14'>{value}</font><br/>"
            f"<font color='{change_color}'>{arrow} {change}</font>",
            ParagraphStyle(
                name='KPIBox',
                parent=self.styles['Normal'],
                alignment=TA_CENTER,
                fontSize=10
            )
        )
        
    def _prepare_highlights_data(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare financial highlights data."""
        # Extract current and prior year data
        income_stmt = financial_data.get('income_statement', {})
        balance_sheet = financial_data.get('balance_sheet', {})
        
        # Current year
        revenue_current = income_stmt.get('revenue', 0)
        gross_profit_current = income_stmt.get('gross_profit', 0)
        net_income_current = income_stmt.get('net_income', 0)
        
        # Prior year (would come from historical data)
        revenue_prior = income_stmt.get('revenue_prior', revenue_current * 0.85)  # Placeholder
        gross_profit_prior = income_stmt.get('gross_profit_prior', gross_profit_current * 0.82)
        net_income_prior = income_stmt.get('net_income_prior', net_income_current * 0.78)
        
        # Calculate growth rates
        revenue_growth = ((revenue_current - revenue_prior) / revenue_prior * 100) if revenue_prior else 0
        gross_profit_growth = ((gross_profit_current - gross_profit_prior) / gross_profit_prior * 100) if gross_profit_prior else 0
        net_income_growth = ((net_income_current - net_income_prior) / net_income_prior * 100) if net_income_prior else 0
        
        # Calculate margins
        gross_margin_current = (gross_profit_current / revenue_current * 100) if revenue_current else 0
        gross_margin_prior = (gross_profit_prior / revenue_prior * 100) if revenue_prior else 0
        
        operating_income_current = income_stmt.get('operating_income', 0)
        operating_margin_current = (operating_income_current / revenue_current * 100) if revenue_current else 0
        operating_margin_prior = 15.2  # Placeholder
        
        net_margin_current = (net_income_current / revenue_current * 100) if revenue_current else 0
        net_margin_prior = (net_income_prior / revenue_prior * 100) if revenue_prior else 0
        
        return {
            'revenue': {
                'current': revenue_current,
                'prior': revenue_prior,
                'growth': revenue_growth
            },
            'gross_profit': {
                'current': gross_profit_current,
                'prior': gross_profit_prior,
                'growth': gross_profit_growth
            },
            'net_income': {
                'current': net_income_current,
                'prior': net_income_prior,
                'growth': net_income_growth
            },
            'gross_margin': {
                'current': gross_margin_current,
                'prior': gross_margin_prior,
                'change': gross_margin_current - gross_margin_prior
            },
            'operating_margin': {
                'current': operating_margin_current,
                'prior': operating_margin_prior,
                'change': operating_margin_current - operating_margin_prior
            },
            'net_margin': {
                'current': net_margin_current,
                'prior': net_margin_prior,
                'change': net_margin_current - net_margin_prior
            }
        }
        
    def _calculate_financial_ratios(self, financial_data: Dict[str, Any]) -> Dict[str, float]:
        """Calculate key financial ratios."""
        balance_sheet = financial_data.get('balance_sheet', {})
        income_stmt = financial_data.get('income_statement', {})
        
        # Extract values
        current_assets = balance_sheet.get('current_assets', 0)
        current_liabilities = balance_sheet.get('current_liabilities', 0)
        inventory = balance_sheet.get('inventory', 0)
        total_assets = balance_sheet.get('total_assets', 0)
        total_equity = balance_sheet.get('total_equity', 0)
        revenue = income_stmt.get('revenue', 0)
        net_income = income_stmt.get('net_income', 0)
        
        # Calculate ratios
        current_ratio = current_assets / current_liabilities if current_liabilities else 0
        quick_ratio = (current_assets - inventory) / current_liabilities if current_liabilities else 0
        asset_turnover = revenue / total_assets if total_assets else 0
        roe = (net_income / total_equity * 100) if total_equity else 0
        roa = (net_income / total_assets * 100) if total_assets else 0
        
        # Prior year ratios (placeholders - would come from historical data)
        return {
            'current_ratio': current_ratio,
            'current_ratio_prior': 2.1,
            'quick_ratio': quick_ratio,
            'quick_ratio_prior': 1.8,
            'asset_turnover': asset_turnover,
            'asset_turnover_prior': 1.1,
            'roe': roe,
            'roe_prior': 14.5,
            'roa': roa,
            'roa_prior': 9.2
        }
        
    def _generate_performance_narrative(self, financial_data: Dict[str, Any]) -> str:
        """Generate performance narrative based on financial data."""
        highlights = self._prepare_highlights_data(financial_data)
        
        revenue_growth = highlights['revenue']['growth']
        net_income_growth = highlights['net_income']['growth']
        
        narrative = f"MCX3D LTD delivered strong financial performance in the fiscal year, "
        
        if revenue_growth > 20:
            narrative += f"with exceptional revenue growth of {revenue_growth:.1f}% "
        elif revenue_growth > 10:
            narrative += f"with solid revenue growth of {revenue_growth:.1f}% "
        else:
            narrative += f"with revenue growth of {revenue_growth:.1f}% "
            
        narrative += f"driven by increased adoption of our 3D commerce solutions. "
        
        if net_income_growth > revenue_growth:
            narrative += f"Profitability improved even more significantly with net income growing {net_income_growth:.1f}%, "
            narrative += "demonstrating operational leverage and efficiency improvements."
        else:
            narrative += f"Net income grew {net_income_growth:.1f}%, reflecting our continued investment in growth."
            
        return narrative
        
    def _generate_achievements_list(self, financial_data: Dict[str, Any]) -> List[str]:
        """Generate list of key achievements based on financial data."""
        achievements = []
        
        highlights = self._prepare_highlights_data(financial_data)
        
        if highlights['revenue']['growth'] > 15:
            achievements.append(f"Achieved {highlights['revenue']['growth']:.1f}% revenue growth year-over-year")
            
        if highlights['gross_margin']['change'] > 0:
            achievements.append(f"Improved gross margin by {highlights['gross_margin']['change']:.1f} percentage points")
            
        if 'saas_metrics' in financial_data:
            saas = financial_data['saas_metrics']
            if saas.get('arr_growth', 0) > 20:
                achievements.append(f"Grew Annual Recurring Revenue (ARR) by {saas['arr_growth']:.1f}%")
                
        achievements.append("Successfully launched Morpho 3D Platform v2.0 with AI capabilities")
        achievements.append("Expanded customer base by 35% with enterprise client acquisitions")
        achievements.append("Maintained strong cash position supporting growth investments")
        
        return achievements
        
    def _generate_performance_review(self, financial_data: Dict[str, Any]) -> str:
        """Generate detailed performance review text."""
        income_stmt = financial_data.get('income_statement', {})
        
        revenue = income_stmt.get('revenue', 0)
        gross_profit = income_stmt.get('gross_profit', 0)
        operating_expenses = income_stmt.get('operating_expenses', 0)
        
        review = f"Revenue for the fiscal year reached {self._format_currency(revenue)}, "
        review += "driven primarily by strong demand for our 3D configurator solutions and growing SaaS subscriptions. "
        review += f"Gross profit of {self._format_currency(gross_profit)} reflects our ability to maintain pricing power "
        review += "while scaling our platform efficiently. "
        
        review += f"\n\nOperating expenses of {self._format_currency(operating_expenses)} increased year-over-year "
        review += "as we invested in product development, sales expansion, and infrastructure to support future growth. "
        review += "These investments are already yielding results with improved customer acquisition and retention metrics."
        
        return review
        
    def _format_currency(self, amount: float) -> str:
        """Format amount as currency."""
        return f"{self.company_info['currency_symbol']}{amount:,.0f}"
        
    def _get_standard_table_style(self) -> TableStyle:
        """Get standard table style based on branding."""
        return TableStyle([
            # Header row
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor(self.branding['colors']['primary'])),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), self.branding['typography']['primary_font']),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            
            # Data rows
            ('FONTNAME', (0, 1), (-1, -1), self.branding['typography']['primary_font']),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('ALIGN', (0, 1), (0, -1), 'LEFT'),
            ('ALIGN', (1, 1), (-1, -1), 'RIGHT'),
            
            # Grid
            ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor(self.branding['colors']['lighter_gray'])),
            ('LINEBELOW', (0, 0), (-1, 0), 1, colors.HexColor(self.branding['colors']['primary'])),
            
            # Padding
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            
            # Alternating row colors
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor(self.branding['colors']['background'])]),
        ])