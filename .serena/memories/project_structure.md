# MCX3D Finance - Project Structure

## Core Directory Layout
```
mcx3d_finance/
├── api/           # REST API endpoints and middleware
├── auth/          # Authentication (Xero OAuth)
├── cli/           # Command-line interface
├── core/          # Business logic & calculations
├── db/            # Database models & sessions
├── exceptions/    # Custom exception hierarchy
├── integrations/  # External API clients (Xero)
├── monitoring/    # Metrics, logging, health checks
├── reporting/     # Multi-format report generation
├── tasks/         # Background processing (Celery)
├── utils/         # Utility functions and helpers
└── validation/    # Data validation logic
```

## Key Configuration Files
- `config.yml`: Application settings
- `alembic.ini`: Database migrations
- `pytest.ini`: Test configuration (coverage ≥85%)
- `pyproject.toml`: Python project metadata
- `.pre-commit-config.yaml`: Pre-commit hooks

## Test Structure
```
tests/
├── conftest.py         # Global test fixtures
├── security/           # Security-focused tests
├── integration/        # Integration tests
├── core/              # Core business logic tests
├── auth/              # Authentication tests
├── e2e/               # End-to-end tests
├── performance/       # Performance benchmarks
└── production/        # Production validation
```