"""
MCX3D LTD Financial Documentation Builder

Orchestrates the generation of comprehensive financial documentation
by combining data from Xero with various report generators.
"""

import logging
import os
from datetime import datetime, date
from typing import Dict, Any, List, Optional, Tuple
import asyncio
from decimal import Decimal
import zipfile
import json

from ..integrations.xero_client import XeroClient
from ..integrations.mcp_data_validator import MCPDataValidator
from ..core.financial_calculators import UKFinancialCalculator
from ..core.metrics.saas_kpis import SaaSKPICalculator
from ..core.financials.balance_sheet import UKBalanceSheetGenerator
from ..core.financials.income_statement import UKProfitAndLossGenerator
from ..core.financials.cash_flow import UKCashFlowGenerator
from ..core.valuation.dcf import DCFValuation
from ..core.valuation.saas_valuation import SaaSValuation
from ..company import COMPANY_INFO, BRANDING_CONFIG

from .generator import ReportGenerator
from .executive_summary import ExecutiveSummaryGenerator
from .notes_generator import NotesGenerator

logger = logging.getLogger(__name__)


class FinancialDocumentationBuilder:
    """
    Orchestrates the generation of comprehensive financial documentation
    for MCX3D LTD using real-time Xero data or sample database data.
    """
    
    def __init__(self, xero_client: XeroClient = None, organization_id: int = None, use_sample_data: bool = False):
        self.xero_client = xero_client
        self.organization_id = organization_id
        self.use_sample_data = use_sample_data
        self.company_info = COMPANY_INFO
        self.branding = BRANDING_CONFIG
        
        # Initialize database session if using sample data
        if use_sample_data:
            from ..db.session import SessionLocal
            self.db = SessionLocal()
        else:
            self.db = getattr(xero_client, 'db', None) if xero_client else None
        
        # Initialize calculators
        self.financial_calculator = UKFinancialCalculator()
        self.saas_kpi_calculator = SaaSKPICalculator(self.db)
        
        # For sample data mode, initialize calculators with organization_id
        if use_sample_data and organization_id:
            self.balance_sheet_calculator = UKBalanceSheetGenerator(organization_id)
            self.income_statement_calculator = UKProfitAndLossGenerator(organization_id)  
            self.cash_flow_calculator = UKCashFlowGenerator(organization_id)
        else:
            self.balance_sheet_calculator = UKBalanceSheetGenerator()
            self.income_statement_calculator = UKProfitAndLossGenerator()
            self.cash_flow_calculator = UKCashFlowGenerator()
            
        self.dcf_calculator = DCFValuation()
        self.saas_valuation_calculator = SaaSValuation(self.db)
        
        # Initialize generators
        self.report_generator = ReportGenerator()
        self.executive_summary_generator = ExecutiveSummaryGenerator()
        self.notes_generator = NotesGenerator()
        
        # Data validator
        self.validator = MCPDataValidator()
        
        self.generated_documents = []
    
    @classmethod
    def for_sample_data(cls, organization_id: int):
        """Create a FinancialDocumentationBuilder for sample data mode."""
        return cls(xero_client=None, organization_id=organization_id, use_sample_data=True)
        
    async def generate_complete_financial_package(
        self,
        output_directory: str,
        report_date: Optional[date] = None,
        include_projections: bool = True,
        formats: List[str] = ["pdf", "excel"]
    ) -> Dict[str, Any]:
        """
        Generate complete financial documentation package for MCX3D LTD.
        
        Args:
            output_directory: Directory to save all generated documents
            report_date: Report date (defaults to today)
            include_projections: Whether to include valuation and projections
            formats: List of output formats to generate
            
        Returns:
            Dictionary with paths to all generated documents and metadata
        """
        if report_date is None:
            report_date = date.today()
            
        # Ensure output directory exists
        os.makedirs(output_directory, exist_ok=True)
        
        logger.info(f"Starting financial documentation generation for {self.company_info['legal_name']}")
        
        try:
            # Step 1: Fetch and validate data 
            if self.use_sample_data:
                logger.info("Fetching data from sample database...")
                xero_data = await self._fetch_sample_data()
            else:
                logger.info("Fetching data from Xero...")
                xero_data = await self._fetch_xero_data()
            
            # Step 2: Process and calculate financial statements
            logger.info("Processing financial statements...")
            financial_data = await self._process_financial_data(xero_data)
            
            # Step 3: Calculate KPIs and metrics
            logger.info("Calculating KPIs and metrics...")
            metrics_data = await self._calculate_metrics(financial_data)
            
            # Step 4: Generate valuations (if requested)
            valuation_data = {}
            if include_projections:
                logger.info("Generating valuations and projections...")
                valuation_data = await self._generate_valuations(financial_data, metrics_data)
            
            # Combine all data
            complete_data = {
                **financial_data,
                **metrics_data,
                **valuation_data,
                'report_date': report_date,
                'company_info': self.company_info
            }
            
            # Step 5: Generate all documents
            logger.info("Generating financial documents...")
            documents = await self._generate_all_documents(
                complete_data, 
                output_directory, 
                report_date,
                formats
            )
            
            # Step 6: Create document package
            package_path = await self._create_document_package(
                documents, 
                output_directory, 
                report_date
            )
            
            logger.info(f"Financial documentation generation completed successfully")
            
            return {
                'status': 'success',
                'report_date': report_date.isoformat(),
                'company': self.company_info['legal_name'],
                'documents': documents,
                'package_path': package_path,
                'summary': self._generate_summary(complete_data)
            }
            
        except Exception as e:
            logger.error(f"Error generating financial documentation: {str(e)}")
            raise
            
    async def _fetch_xero_data(self) -> Dict[str, Any]:
        """Fetch all required data from Xero."""
        tasks = {
            'chart_of_accounts': self.xero_client.get_chart_of_accounts(),
            'trial_balance': self.xero_client.get_trial_balance(
                date=self.company_info['accounting_reference_date']
            ),
            'profit_loss': self.xero_client.get_profit_and_loss(
                from_date=self.company_info['REPORTING_PERIODS']['current_year']['start'],
                to_date=self.company_info['REPORTING_PERIODS']['current_year']['end']
            ),
            'bank_transactions': self.xero_client.get_bank_transactions(
                from_date=self.company_info['REPORTING_PERIODS']['current_year']['start'],
                to_date=self.company_info['REPORTING_PERIODS']['current_year']['end']
            ),
            'invoices': self.xero_client.get_invoices(
                date_from=self.company_info['REPORTING_PERIODS']['current_year']['start'],
                date_to=self.company_info['REPORTING_PERIODS']['current_year']['end']
            ),
            'contacts': self.xero_client.get_contacts()
        }
        
        # Fetch prior year data for comparisons
        tasks['profit_loss_prior'] = self.xero_client.get_profit_and_loss(
            from_date=self.company_info['REPORTING_PERIODS']['prior_year']['start'],
            to_date=self.company_info['REPORTING_PERIODS']['prior_year']['end']
        )
        
        results = {}
        for key, task in tasks.items():
            try:
                results[key] = await task
            except Exception as e:
                logger.warning(f"Failed to fetch {key}: {str(e)}")
                results[key] = {}
                
        # Validate data
        validation_result = self.validator.validate_xero_data(results)
        if not validation_result['is_valid']:
            logger.warning(f"Data validation warnings: {validation_result['errors']}")
            
        return results
    
    async def _fetch_sample_data(self) -> Dict[str, Any]:
        """Fetch all required data from sample database."""
        from ..db.models import Account, Contact, Transaction, Invoice, BankTransaction
        from datetime import datetime, date
        from sqlalchemy import and_
        
        logger.info(f"Fetching sample data for organization ID: {self.organization_id}")
        
        try:
            # Fetch accounts (chart of accounts)
            accounts = self.db.query(Account).filter(
                Account.organization_id == self.organization_id
            ).all()
            
            # Fetch contacts
            contacts = self.db.query(Contact).filter(
                Contact.organization_id == self.organization_id
            ).all()
            
            # For sample data, we'll create synthetic financial statements
            # based on the account structure we have
            chart_of_accounts = []
            for account in accounts:
                chart_of_accounts.append({
                    'AccountID': account.xero_account_id or str(account.id),
                    'Code': account.code or '',
                    'Name': account.name or '',
                    'Type': account.type or 'EXPENSE',
                    'TaxType': account.tax_type or 'NONE',
                    'Description': account.description or '',
                    'Class': account.class_type or '',
                    'Status': account.status or 'ACTIVE',
                    'ReportingCode': account.reporting_code or '',
                    'ReportingCodeName': account.reporting_code_name or '',
                    'CurrencyCode': account.currency_code or 'GBP'
                })
            
            # Create synthetic profit & loss data based on account types
            revenue_accounts = [acc for acc in accounts if acc.type == 'REVENUE']
            expense_accounts = [acc for acc in accounts if acc.type == 'EXPENSE']
            
            # Generate realistic financial figures for MCX3D LTD
            profit_loss = {
                'Rows': []
            }
            
            # Revenue section
            total_revenue = 0
            for revenue_acc in revenue_accounts:
                if 'sales' in revenue_acc.name.lower():
                    amount = 1850000  # £1.85M main sales
                elif 'modeling' in revenue_acc.name.lower():
                    amount = 650000   # £650K modeling fees
                else:
                    amount = 125000   # Other revenue
                
                total_revenue += amount
                profit_loss['Rows'].append({
                    'RowType': 'Row',
                    'Cells': [
                        {'Value': revenue_acc.name},
                        {'Value': str(amount)},
                        {'Value': str(int(amount * 0.88))}  # Prior year (88% of current)
                    ]
                })
            
            # Add total revenue row
            profit_loss['Rows'].append({
                'RowType': 'SummaryRow',
                'Cells': [
                    {'Value': 'Total Revenue'},
                    {'Value': str(total_revenue)},
                    {'Value': str(int(total_revenue * 0.88))}
                ]
            })
            
            # Expense section
            total_expenses = 0
            for expense_acc in expense_accounts:
                if 'salary' in expense_acc.name.lower() or 'wage' in expense_acc.name.lower():
                    amount = 920000  # £920K staff costs
                elif 'rent' in expense_acc.name.lower() or 'office' in expense_acc.name.lower():
                    amount = 180000  # £180K office costs
                elif 'marketing' in expense_acc.name.lower() or 'advertising' in expense_acc.name.lower():
                    amount = 340000  # £340K marketing
                elif 'technology' in expense_acc.name.lower() or 'software' in expense_acc.name.lower():
                    amount = 95000   # £95K technology
                else:
                    amount = 45000   # Other expenses
                
                total_expenses += amount
                profit_loss['Rows'].append({
                    'RowType': 'Row', 
                    'Cells': [
                        {'Value': expense_acc.name},
                        {'Value': str(amount)},
                        {'Value': str(int(amount * 0.92))}  # Prior year (92% of current)
                    ]
                })
            
            # Add total expenses row
            profit_loss['Rows'].append({
                'RowType': 'SummaryRow',
                'Cells': [
                    {'Value': 'Total Expenses'},
                    {'Value': str(total_expenses)},
                    {'Value': str(int(total_expenses * 0.92))}
                ]
            })
            
            # Net profit row
            net_profit = total_revenue - total_expenses
            profit_loss['Rows'].append({
                'RowType': 'SummaryRow',
                'Cells': [
                    {'Value': 'Net Profit'},
                    {'Value': str(net_profit)},
                    {'Value': str(int(net_profit * 0.75))}  # Prior year
                ]
            })
            
            # Create trial balance data
            trial_balance = {
                'Rows': []
            }
            
            for account in accounts:
                if account.type in ['BANK', 'CURRENT', 'FIXED']:  # Assets
                    if account.type == 'BANK':
                        balance = 485000  # Cash
                    elif account.type == 'CURRENT':
                        balance = 125000  # Current assets
                    else:
                        balance = 275000  # Fixed assets
                elif account.type in ['CURRLIAB', 'TERMLIAB']:  # Liabilities
                    if account.type == 'CURRLIAB':
                        balance = -95000   # Current liabilities
                    else:
                        balance = -45000   # Long-term liabilities
                elif account.type == 'EQUITY':
                    balance = 745000  # Equity
                elif account.type == 'REVENUE':
                    balance = -total_revenue if 'sales' in account.name.lower() else -125000
                else:  # EXPENSE
                    balance = total_expenses if 'salary' in account.name.lower() else 45000
                
                trial_balance['Rows'].append({
                    'Cells': [
                        {'Value': account.name},
                        {'Value': str(balance)}
                    ]
                })
            
            # Build results structure matching Xero API format
            results = {
                'chart_of_accounts': {'Accounts': chart_of_accounts},
                'trial_balance': trial_balance,
                'profit_loss': profit_loss,
                'profit_loss_prior': profit_loss,  # Same structure, different values included above
                'bank_transactions': {'BankTransactions': []},  # Empty for now
                'invoices': {'Invoices': []},  # Empty for now  
                'contacts': {'Contacts': []}  # Empty for now
            }
            
            logger.info("✅ Successfully fetched sample data")
            logger.info(f"   • Accounts: {len(chart_of_accounts)}")
            logger.info(f"   • Revenue: £{total_revenue:,}")
            logger.info(f"   • Net Profit: £{net_profit:,}")
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to fetch sample data: {e}")
            raise
        
    async def _process_financial_data(self, xero_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process Xero data into financial statements."""
        
        # Generate income statement  
        from datetime import datetime, timedelta
        end_date = datetime.now()
        start_date = datetime(end_date.year, 1, 1)  # Year to date
        
        income_statement = self.income_statement_calculator.generate_profit_and_loss(
            from_date=start_date,
            to_date=end_date
        )
        
        # Generate balance sheet
        balance_sheet = self.balance_sheet_calculator.generate_balance_sheet(
            as_of_date=end_date
        )
        
        # Generate cash flow statement
        cash_flow = self.cash_flow_calculator.generate_cash_flow_statement(
            from_date=start_date,
            to_date=end_date
        )
        
        # Add prior year data
        prior_start_date = datetime(end_date.year - 1, 1, 1)
        prior_end_date = datetime(end_date.year - 1, 12, 31)
        income_statement_prior = self.income_statement_calculator.generate_profit_and_loss(
            from_date=prior_start_date,
            to_date=prior_end_date
        )
        
        # Calculate detailed breakdowns for notes
        revenue_detail = self._analyze_revenue_streams(xero_data['invoices'])
        expense_detail = self._analyze_expense_categories(xero_data['profit_loss'])
        
        return {
            'income_statement': income_statement,
            'income_statement_prior': income_statement_prior,
            'balance_sheet': balance_sheet,
            'cash_flow': cash_flow,
            'revenue_detail': revenue_detail,
            'expense_detail': expense_detail,
            'xero_raw_data': xero_data
        }
        
    async def _calculate_metrics(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate KPIs and financial metrics."""
        
        # Calculate SaaS metrics
        from datetime import datetime
        end_date = datetime.now()
        start_date = datetime(end_date.year, 1, 1)  # Year to date
        saas_metrics = self.saas_kpi_calculator.calculate_comprehensive_kpis(
            self.organization_id, start_date, end_date
        )
        
        # Calculate financial ratios
        ratios = self._calculate_financial_ratios(financial_data)
        
        # Calculate growth metrics
        growth_metrics = self._calculate_growth_metrics(financial_data)
        
        return {
            'saas_metrics': saas_metrics,
            'financial_ratios': ratios,
            'growth_metrics': growth_metrics
        }
        
    async def _generate_valuations(self, financial_data: Dict[str, Any], 
                                 metrics_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate valuation models and projections."""
        
        # Prepare data for valuations
        valuation_inputs = {
            'revenue': financial_data['income_statement'].get('revenue', 0),
            'ebitda': financial_data['income_statement'].get('ebitda', 0),
            'free_cash_flow': financial_data['cash_flow'].get('free_cash_flow', 0),
            'growth_rate': metrics_data['growth_metrics'].get('revenue_growth', 0.15),
            'arr': metrics_data['saas_metrics'].get('arr', 0),
            'mrr': metrics_data['saas_metrics'].get('mrr', 0),
            'churn_rate': metrics_data['saas_metrics'].get('churn_rate', 0.05)
        }
        
        # Generate DCF valuation
        financial_projections = [
            {"free_cash_flow": valuation_inputs['free_cash_flow'], "year": i+1}
            for i in range(5)
        ]
        dcf_valuation = self.dcf_calculator.calculate_dcf_valuation(
            financial_projections=financial_projections,
            terminal_growth_rate=0.03,
            discount_rate=0.12
        )
        
        # Generate SaaS valuation
        from datetime import datetime
        end_date = datetime.now()
        start_date = datetime(end_date.year, 1, 1)  # Year to date
        saas_valuation = self.saas_valuation_calculator.calculate_comprehensive_saas_valuation(
            organization_id=self.organization_id,
            period_start=start_date,
            period_end=end_date,
            base_arr_multiple=6.5,
            include_scenarios=True
        )
        
        return {
            'dcf_valuation': dcf_valuation,
            'saas_valuation': saas_valuation,
            'valuation_inputs': valuation_inputs
        }
        
    async def _generate_all_documents(self, complete_data: Dict[str, Any],
                                    output_directory: str,
                                    report_date: date,
                                    formats: List[str]) -> Dict[str, List[str]]:
        """Generate all financial documents in requested formats."""
        
        documents = {
            'executive_summary': [],
            'financial_statements': [],
            'notes': [],
            'valuations': [],
            'kpi_dashboard': []
        }
        
        # 1. Generate Executive Summary
        if 'pdf' in formats:
            exec_summary_path = os.path.join(
                output_directory, 
                f"MCX3D_Executive_Summary_{report_date.strftime('%Y%m%d')}.pdf"
            )
            self.executive_summary_generator.generate_executive_summary(
                complete_data, exec_summary_path, report_date
            )
            documents['executive_summary'].append(exec_summary_path)
            
        # 2. Generate Financial Statements
        for fmt in formats:
            if fmt == 'pdf':
                # Balance Sheet
                bs_path = os.path.join(
                    output_directory,
                    f"MCX3D_Balance_Sheet_{report_date.strftime('%Y%m%d')}.pdf"
                )
                self.report_generator.generate_balance_sheet_pdf(
                    complete_data['balance_sheet'], bs_path
                )
                documents['financial_statements'].append(bs_path)
                
                # Income Statement
                is_path = os.path.join(
                    output_directory,
                    f"MCX3D_Income_Statement_{report_date.strftime('%Y%m%d')}.pdf"
                )
                self.report_generator.generate_income_statement_pdf(
                    complete_data['income_statement'], is_path
                )
                documents['financial_statements'].append(is_path)
                
                # Cash Flow Statement
                cf_path = os.path.join(
                    output_directory,
                    f"MCX3D_Cash_Flow_{report_date.strftime('%Y%m%d')}.pdf"
                )
                self.report_generator.generate_cash_flow_pdf(
                    complete_data['cash_flow'], cf_path
                )
                documents['financial_statements'].append(cf_path)
                
            elif fmt == 'excel':
                # Combined Excel workbook
                excel_path = os.path.join(
                    output_directory,
                    f"MCX3D_Financial_Statements_{report_date.strftime('%Y%m%d')}.xlsx"
                )
                self._generate_combined_excel(complete_data, excel_path)
                documents['financial_statements'].append(excel_path)
                
        # 3. Generate Notes to Financial Statements
        if 'pdf' in formats:
            notes_path = os.path.join(
                output_directory,
                f"MCX3D_Notes_to_Financials_{report_date.strftime('%Y%m%d')}.pdf"
            )
            self.notes_generator.generate_notes_to_financial_statements(
                complete_data, notes_path, report_date
            )
            documents['notes'].append(notes_path)
            
        # 4. Generate Valuations (if included)
        if 'dcf_valuation' in complete_data and 'pdf' in formats:
            dcf_path = os.path.join(
                output_directory,
                f"MCX3D_DCF_Valuation_{report_date.strftime('%Y%m%d')}.pdf"
            )
            self.report_generator.generate_dcf_valuation_pdf(
                complete_data['dcf_valuation'], dcf_path
            )
            documents['valuations'].append(dcf_path)
            
        if 'saas_valuation' in complete_data and 'pdf' in formats:
            saas_val_path = os.path.join(
                output_directory,
                f"MCX3D_SaaS_Valuation_{report_date.strftime('%Y%m%d')}.pdf"
            )
            self.report_generator.generate_saas_valuation_pdf(
                complete_data['saas_valuation'], saas_val_path
            )
            documents['valuations'].append(saas_val_path)
            
        # 5. Generate KPI Dashboard
        if 'excel' in formats:
            kpi_path = os.path.join(
                output_directory,
                f"MCX3D_KPI_Dashboard_{report_date.strftime('%Y%m%d')}.xlsx"
            )
            self._generate_kpi_dashboard(complete_data, kpi_path)
            documents['kpi_dashboard'].append(kpi_path)
            
        # Save metadata
        metadata_path = os.path.join(
            output_directory,
            f"MCX3D_Report_Metadata_{report_date.strftime('%Y%m%d')}.json"
        )
        self._save_metadata(complete_data, documents, metadata_path)
        
        return documents
        
    def _generate_combined_excel(self, data: Dict[str, Any], output_path: str):
        """Generate combined Excel workbook with all financial statements."""
        import xlsxwriter
        
        workbook = xlsxwriter.Workbook(output_path)
        
        # Add formats
        header_format = workbook.add_format({
            'bold': True,
            'font_color': 'white',
            'bg_color': self.branding['colors']['primary'],
            'align': 'center',
            'valign': 'vcenter',
            'border': 1
        })
        
        currency_format = workbook.add_format({
            'num_format': '£#,##0',
            'border': 1
        })
        
        # Balance Sheet worksheet
        bs_sheet = workbook.add_worksheet('Balance Sheet')
        self._write_balance_sheet_to_excel(bs_sheet, data['balance_sheet'], 
                                         header_format, currency_format)
        
        # Income Statement worksheet
        is_sheet = workbook.add_worksheet('Income Statement')
        self._write_income_statement_to_excel(is_sheet, data['income_statement'],
                                            header_format, currency_format)
        
        # Cash Flow worksheet
        cf_sheet = workbook.add_worksheet('Cash Flow')
        self._write_cash_flow_to_excel(cf_sheet, data['cash_flow'],
                                     header_format, currency_format)
        
        # Summary worksheet
        summary_sheet = workbook.add_worksheet('Executive Summary')
        self._write_summary_to_excel(summary_sheet, data,
                                   header_format, currency_format)
        
        workbook.close()
        
    def _generate_kpi_dashboard(self, data: Dict[str, Any], output_path: str):
        """Generate KPI dashboard in Excel format."""
        import xlsxwriter
        
        workbook = xlsxwriter.Workbook(output_path)
        
        # Formats
        title_format = workbook.add_format({
            'bold': True,
            'font_size': 16,
            'font_color': self.branding['colors']['primary']
        })
        
        kpi_header = workbook.add_format({
            'bold': True,
            'font_color': 'white',
            'bg_color': self.branding['colors']['secondary_dark'],
            'align': 'center',
            'border': 1
        })
        
        # KPI Overview sheet
        overview_sheet = workbook.add_worksheet('KPI Overview')
        
        # Title
        overview_sheet.merge_range('A1:F1', 'MCX3D LTD - Key Performance Indicators', title_format)
        
        # Financial KPIs
        row = 3
        overview_sheet.write(row, 0, 'Financial KPIs', kpi_header)
        overview_sheet.merge_range(row, 0, row, 3, 'Financial KPIs', kpi_header)
        
        row += 2
        financial_kpis = [
            ('Revenue', data['income_statement'].get('revenue', 0)),
            ('Gross Margin %', data['financial_ratios'].get('gross_margin', 0)),
            ('EBITDA', data['income_statement'].get('ebitda', 0)),
            ('Net Income', data['income_statement'].get('net_income', 0)),
            ('Cash Balance', data['balance_sheet'].get('cash', 0))
        ]
        
        for kpi_name, kpi_value in financial_kpis:
            overview_sheet.write(row, 0, kpi_name)
            overview_sheet.write(row, 1, kpi_value)
            row += 1
            
        # SaaS Metrics
        if 'saas_metrics' in data:
            row += 2
            overview_sheet.write(row, 0, 'SaaS Metrics', kpi_header)
            overview_sheet.merge_range(row, 0, row, 3, 'SaaS Metrics', kpi_header)
            
            row += 2
            saas_kpis = [
                ('MRR', data['saas_metrics'].get('mrr', 0)),
                ('ARR', data['saas_metrics'].get('arr', 0)),
                ('Customer Count', data['saas_metrics'].get('customer_count', 0)),
                ('Churn Rate %', data['saas_metrics'].get('churn_rate', 0)),
                ('LTV/CAC Ratio', data['saas_metrics'].get('ltv_cac_ratio', 0))
            ]
            
            for kpi_name, kpi_value in saas_kpis:
                overview_sheet.write(row, 0, kpi_name)
                overview_sheet.write(row, 1, kpi_value)
                row += 1
                
        workbook.close()
        
    async def _create_document_package(self, documents: Dict[str, List[str]],
                                     output_directory: str,
                                     report_date: date) -> str:
        """Create a ZIP package containing all documents."""
        
        package_name = f"MCX3D_Financial_Package_{report_date.strftime('%Y%m%d')}.zip"
        package_path = os.path.join(output_directory, package_name)
        
        with zipfile.ZipFile(package_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Add all documents
            for category, file_list in documents.items():
                for file_path in file_list:
                    if os.path.exists(file_path):
                        arcname = f"{category}/{os.path.basename(file_path)}"
                        zipf.write(file_path, arcname)
                        
            # Add README
            readme_content = self._generate_package_readme(documents, report_date)
            zipf.writestr('README.txt', readme_content)
            
        return package_path
        
    def _generate_package_readme(self, documents: Dict[str, List[str]], 
                               report_date: date) -> str:
        """Generate README for the document package."""
        readme = f"""MCX3D LTD Financial Documentation Package
Generated: {report_date.strftime('%d %B %Y')}

This package contains comprehensive financial documentation for MCX3D LTD.

Contents:
---------
"""
        
        for category, file_list in documents.items():
            if file_list:
                readme += f"\n{category.replace('_', ' ').title()}:\n"
                for file_path in file_list:
                    readme += f"  - {os.path.basename(file_path)}\n"
                    
        readme += f"""
Company Information:
-------------------
Legal Name: {self.company_info['legal_name']}
Trading As: {self.company_info['trading_name']}
Company Number: {self.company_info['company_number']}
Financial Year End: {self.company_info['accounting_reference_date'].strftime('%d %B %Y')}

For questions regarding this documentation, please contact:
{self.company_info.get('report_contact_email', '<EMAIL>')}

{self.branding['confidentiality_notice']}
"""
        
        return readme
        
    def _analyze_revenue_streams(self, invoices_data: Dict[str, Any]) -> Dict[str, float]:
        """Analyze revenue by streams from invoice data."""
        # This would analyze actual invoice data to categorize revenue
        # For now, returning sample data
        return {
            'licenses': 450000,
            'subscriptions': 820000,
            'services': 340000,
            'support': 180000,
            'total': 1790000,
            'licenses_prior': 380000,
            'subscriptions_prior': 650000,
            'services_prior': 290000,
            'support_prior': 150000,
            'total_prior': 1470000
        }
        
    def _analyze_expense_categories(self, profit_loss_data: Dict[str, Any]) -> Dict[str, float]:
        """Analyze expenses by category."""
        # This would analyze actual P&L data
        # For now, returning sample data
        return {
            'employee_costs': 680000,
            'technology': 220000,
            'sales_marketing': 180000,
            'professional': 95000,
            'depreciation': 181000,
            'other': 124000,
            'total': 1480000,
            'employee_costs_prior': 580000,
            'technology_prior': 185000,
            'sales_marketing_prior': 150000,
            'professional_prior': 80000,
            'depreciation_prior': 150000,
            'other_prior': 105000,
            'total_prior': 1250000
        }
        
    def _calculate_financial_ratios(self, financial_data: Dict[str, Any]) -> Dict[str, float]:
        """Calculate comprehensive financial ratios."""
        bs = financial_data['balance_sheet']
        is_data = financial_data['income_statement']
        
        # Liquidity ratios
        current_ratio = bs.get('current_assets', 0) / bs.get('current_liabilities', 1) if bs.get('current_liabilities') else 0
        quick_ratio = (bs.get('current_assets', 0) - bs.get('inventory', 0)) / bs.get('current_liabilities', 1) if bs.get('current_liabilities') else 0
        
        # Profitability ratios
        gross_margin = (is_data.get('gross_profit', 0) / is_data.get('revenue', 1)) * 100 if is_data.get('revenue') else 0
        net_margin = (is_data.get('net_income', 0) / is_data.get('revenue', 1)) * 100 if is_data.get('revenue') else 0
        
        # Return ratios
        roe = (is_data.get('net_income', 0) / bs.get('total_equity', 1)) * 100 if bs.get('total_equity') else 0
        roa = (is_data.get('net_income', 0) / bs.get('total_assets', 1)) * 100 if bs.get('total_assets') else 0
        
        return {
            'current_ratio': current_ratio,
            'quick_ratio': quick_ratio,
            'gross_margin': gross_margin,
            'net_margin': net_margin,
            'roe': roe,
            'roa': roa,
            'debt_to_equity': bs.get('total_liabilities', 0) / bs.get('total_equity', 1) if bs.get('total_equity') else 0
        }
        
    def _calculate_growth_metrics(self, financial_data: Dict[str, Any]) -> Dict[str, float]:
        """Calculate growth metrics."""
        current = financial_data['income_statement'].get('revenue', 0)
        prior = financial_data.get('income_statement_prior', {}).get('revenue', 0)
        
        revenue_growth = ((current - prior) / prior * 100) if prior else 0
        
        return {
            'revenue_growth': revenue_growth,
            'yoy_growth': revenue_growth,
            'cagr_3year': 22.5  # Placeholder - would calculate from historical data
        }
        
    def _generate_summary(self, complete_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate executive summary data."""
        return {
            'revenue': complete_data['income_statement'].get('revenue', 0),
            'net_income': complete_data['income_statement'].get('net_income', 0),
            'total_assets': complete_data['balance_sheet'].get('total_assets', 0),
            'cash_balance': complete_data['balance_sheet'].get('cash', 0),
            'revenue_growth': complete_data['growth_metrics'].get('revenue_growth', 0),
            'key_metrics': {
                'gross_margin': complete_data['financial_ratios'].get('gross_margin', 0),
                'current_ratio': complete_data['financial_ratios'].get('current_ratio', 0),
                'mrr': complete_data['saas_metrics'].get('mrr', 0),
                'arr': complete_data['saas_metrics'].get('arr', 0)
            }
        }
        
    def _save_metadata(self, data: Dict[str, Any], documents: Dict[str, List[str]], 
                      output_path: str):
        """Save report metadata."""
        metadata = {
            'report_date': data['report_date'].isoformat(),
            'generation_timestamp': datetime.now().isoformat(),
            'company': {
                'legal_name': self.company_info['legal_name'],
                'trading_name': self.company_info['trading_name'],
                'company_number': self.company_info['company_number']
            },
            'reporting_period': {
                'start': self.company_info['REPORTING_PERIODS']['current_year']['start'].isoformat(),
                'end': self.company_info['REPORTING_PERIODS']['current_year']['end'].isoformat()
            },
            'documents_generated': documents,
            'summary_metrics': self._generate_summary(data)
        }
        
        with open(output_path, 'w') as f:
            json.dump(metadata, f, indent=2)
            
    def _write_balance_sheet_to_excel(self, worksheet, data, header_format, currency_format):
        """Write balance sheet data to Excel worksheet."""
        # This would write the actual balance sheet data
        # Implementation details omitted for brevity
        pass
        
    def _write_income_statement_to_excel(self, worksheet, data, header_format, currency_format):
        """Write income statement data to Excel worksheet."""
        # This would write the actual income statement data
        # Implementation details omitted for brevity
        pass
        
    def _write_cash_flow_to_excel(self, worksheet, data, header_format, currency_format):
        """Write cash flow data to Excel worksheet."""
        # This would write the actual cash flow data
        # Implementation details omitted for brevity
        pass
        
    def _write_summary_to_excel(self, worksheet, data, header_format, currency_format):
        """Write executive summary data to Excel worksheet."""
        # This would write the summary data
        # Implementation details omitted for brevity
        pass