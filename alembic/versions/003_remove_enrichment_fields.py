"""Remove enrichment fields

Revision ID: 003_remove_enrichment
Revises: 002_add_xero_fields
Create Date: 2025-01-22 15:10:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '003_remove_enrichment'
down_revision = '002_add_xero_fields'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Drop enrichment columns from contacts table
    op.drop_column('contacts', 'business_profile')
    op.drop_column('contacts', 'enrichment_metadata')


def downgrade() -> None:
    # Add back enrichment columns
    op.add_column('contacts', sa.Column('business_profile', postgresql.JSON(), nullable=True))
    op.add_column('contacts', sa.Column('enrichment_metadata', postgresql.JSON(), nullable=True))