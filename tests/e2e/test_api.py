import os
import requests
import pytest
from dotenv import load_dotenv

load_dotenv()

API_BASE_URL = os.getenv("API_BASE_URL", "http://127.0.0.1:8000")
API_KEY = os.getenv("API_KEY")


@pytest.mark.e2e
def test_get_income_statement_success():
    """
    Tests successful retrieval of the income statement.
    """
    headers = {"Authorization": f"Bearer {API_KEY}"}
    params = {"start_date": "2023-01-01", "end_date": "2023-12-31", "period": "month"}
    response = requests.get(
        f"{API_BASE_URL}/reports/income-statement", headers=headers, params=params
    )
    assert response.status_code == 200
    assert "revenue" in response.json()
    assert "cost_of_goods_sold" in response.json()
    assert "gross_profit" in response.json()


@pytest.mark.e2e
def test_get_income_statement_invalid_params():
    """
    Tests retrieval of the income statement with invalid parameters.
    """
    headers = {"Authorization": f"Bearer {API_KEY}"}
    params = {
        "start_date": "2023-12-31",
        "end_date": "2023-01-01",  # Invalid date range
        "period": "month",
    }
    response = requests.get(
        f"{API_BASE_URL}/reports/income-statement", headers=headers, params=params
    )
    assert response.status_code == 422  # Unprocessable Entity
