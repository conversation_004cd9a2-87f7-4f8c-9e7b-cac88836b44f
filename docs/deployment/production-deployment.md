# Production Deployment Guide

This guide provides comprehensive instructions for deploying MCX3D Financials v2 to production environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Infrastructure Requirements](#infrastructure-requirements)
3. [Security Configuration](#security-configuration)
4. [Deployment Steps](#deployment-steps)
5. [Post-Deployment Verification](#post-deployment-verification)
6. [Monitoring and Maintenance](#monitoring-and-maintenance)
7. [Troubleshooting](#troubleshooting)

## Prerequisites

### Required Tools
- Docker Engine 20.10+ with Docker Swarm mode
- Docker Compose 2.0+
- PostgreSQL client tools
- SSL certificates (Let's Encrypt recommended)
- Domain name with DNS access

### Access Requirements
- SSH access to production servers
- Docker Hub or private registry access
- Database backup storage access
- Monitoring system access

## Infrastructure Requirements

### Minimum Hardware Requirements

#### Single Server Deployment
- **CPU**: 4 cores (8 recommended)
- **RAM**: 8GB (16GB recommended)
- **Storage**: 100GB SSD (200GB recommended)
- **Network**: 100Mbps (1Gbps recommended)

#### Multi-Server Deployment
- **Web/App Servers**: 2+ servers with 4 cores, 8GB RAM each
- **Database Server**: Dedicated server with 8 cores, 16GB RAM, 500GB SSD
- **Redis Server**: 2 cores, 4GB RAM
- **Load Balancer**: Nginx or cloud provider LB

### Software Requirements
```bash
# Operating System
Ubuntu 20.04 LTS or later
CentOS 8 or later
Amazon Linux 2

# Docker Installation
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Docker Swarm Initialization
docker swarm init --advertise-addr <MANAGER-IP>
```

## Security Configuration

### 1. SSL/TLS Setup

#### Using Let's Encrypt
```bash
# Install certbot
sudo apt-get update
sudo apt-get install certbot

# Generate certificates
sudo certbot certonly --standalone -d mcx3d.com -d www.mcx3d.com

# Copy certificates to nginx directory
sudo cp /etc/letsencrypt/live/mcx3d.com/fullchain.pem nginx/ssl/
sudo cp /etc/letsencrypt/live/mcx3d.com/privkey.pem nginx/ssl/
sudo cp /etc/letsencrypt/live/mcx3d.com/chain.pem nginx/ssl/
```

### 2. Docker Secrets Management

```bash
# Create secrets
./scripts/deployment/manage_secrets.sh create

# Required secrets:
# - db_password: PostgreSQL password
# - secret_key: Django/FastAPI secret key
# - encryption_key: Data encryption key
# - xero_client_id: Xero OAuth client ID
# - xero_client_secret: Xero OAuth client secret
```

### 3. Environment Variables

Create production `.env` file:
```bash
# Environment
ENVIRONMENT=production
DEBUG=false

# Database
DB_HOST=db
DB_PORT=5432
DB_NAME=mcx3d_finance_prod
DB_USER=mcx3d_user

# Redis
REDIS_PASSWORD=<secure-password>

# Security
SESSION_COOKIE_SECURE=true
CSRF_COOKIE_SECURE=true
OAUTH2_REDIRECT_HTTPS=true
CORS_ORIGINS=https://mcx3d.com,https://app.mcx3d.com

# Performance
RATE_LIMIT_ENABLED=true
ENABLE_QUERY_CACHE=true
ENABLE_RESPONSE_COMPRESSION=true
```

### 4. Firewall Configuration

```bash
# Allow only necessary ports
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP (redirects to HTTPS)
sudo ufw allow 443/tcp   # HTTPS
sudo ufw allow 2377/tcp  # Docker Swarm management
sudo ufw allow 7946/tcp  # Docker Swarm discovery
sudo ufw allow 7946/udp  # Docker Swarm discovery
sudo ufw allow 4789/udp  # Docker overlay network
sudo ufw enable
```

## Deployment Steps

### 1. Build and Push Images

```bash
# Build production images
docker build -t mcx3d/financials:latest .
docker build -t mcx3d/nginx:latest ./nginx

# Push to registry
docker push mcx3d/financials:latest
docker push mcx3d/nginx:latest
```

### 2. Database Setup

```bash
# Create production database
docker-compose -f docker-compose.production.yml run --rm web python -m mcx3d_finance.db.init_db

# Run migrations
docker-compose -f docker-compose.production.yml run --rm web alembic upgrade head

# Create admin user
docker-compose -f docker-compose.production.yml run --rm web mcx3d-finance user create-admin
```

### 3. Deploy with Docker Swarm

```bash
# Deploy stack
docker stack deploy -c docker-compose.production.yml mcx3d

# Verify services
docker service ls
docker service ps mcx3d_web
docker service ps mcx3d_nginx
```

### 4. Scale Services

```bash
# Scale web service
docker service scale mcx3d_web=3

# Scale celery workers
docker service scale mcx3d_celery=2
```

## Post-Deployment Verification

### 1. Health Checks

```bash
# Basic health check
curl https://mcx3d.com/health/

# Comprehensive health check
curl https://mcx3d.com/health/comprehensive

# Performance metrics
curl https://mcx3d.com/health/performance
```

### 2. SSL Verification

```bash
# Check SSL certificate
openssl s_client -connect mcx3d.com:443 -servername mcx3d.com

# Test SSL configuration
curl -I https://mcx3d.com
```

### 3. Application Testing

```bash
# Test authentication
curl -X POST https://mcx3d.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# Test API endpoints
curl -H "Authorization: Bearer <token>" \
  https://mcx3d.com/api/organizations
```

### 4. Performance Testing

```bash
# Load testing with Apache Bench
ab -n 1000 -c 50 https://mcx3d.com/health/

# Monitor response times
while true; do
  time curl -s https://mcx3d.com/health/ > /dev/null
  sleep 5
done
```

## Monitoring and Maintenance

### 1. Log Management

```bash
# View logs
docker service logs mcx3d_web
docker service logs mcx3d_nginx

# Follow logs
docker service logs -f mcx3d_web

# Export logs
docker service logs mcx3d_web > web_logs.txt
```

### 2. Backup Strategy

#### Database Backups
```bash
# Automated daily backups
0 2 * * * docker exec mcx3d_db pg_dump -U mcx3d_user mcx3d_finance_prod | gzip > /backups/db_$(date +\%Y\%m\%d).sql.gz

# Manual backup
docker exec mcx3d_db pg_dump -U mcx3d_user mcx3d_finance_prod > backup.sql
```

#### Application Backups
```bash
# Backup uploaded files
tar -czf uploads_backup.tar.gz /app/uploads/

# Backup configuration
tar -czf config_backup.tar.gz .env docker-compose.production.yml
```

### 3. Monitoring Setup

#### Prometheus Configuration
```yaml
scrape_configs:
  - job_name: 'mcx3d_financials'
    static_configs:
      - targets: ['mcx3d.com:8000']
    metrics_path: '/api/metrics'
```

#### Alert Rules
```yaml
groups:
  - name: mcx3d_alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: High error rate detected
          
      - alert: HighMemoryUsage
        expr: memory_usage_percent > 80
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: High memory usage detected
```

### 4. Updates and Maintenance

#### Rolling Updates
```bash
# Update service with new image
docker service update --image mcx3d/financials:v2.1.0 mcx3d_web

# Update with rollback on failure
docker service update \
  --image mcx3d/financials:v2.1.0 \
  --update-failure-action rollback \
  --update-max-failure-ratio 0.25 \
  mcx3d_web
```

#### Maintenance Mode
```bash
# Enable maintenance mode
docker service update --env-add MAINTENANCE_MODE=true mcx3d_web

# Disable maintenance mode
docker service update --env-rm MAINTENANCE_MODE mcx3d_web
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Issues
```bash
# Check database connectivity
docker exec mcx3d_web pg_isready -h db -U mcx3d_user

# Check connection pool status
curl https://mcx3d.com/health/detailed | jq '.components.database'
```

#### 2. Memory Issues
```bash
# Check memory usage
docker stats

# Increase memory limits
docker service update --limit-memory 4G mcx3d_web
```

#### 3. Performance Issues
```bash
# Check cache status
curl https://mcx3d.com/health/performance | jq '.cache'

# Clear cache if needed
docker exec mcx3d_web mcx3d-finance cache clear
```

#### 4. SSL Certificate Issues
```bash
# Renew certificates
docker exec mcx3d_nginx certbot renew

# Reload nginx
docker exec mcx3d_nginx nginx -s reload
```

### Debug Mode

For detailed debugging:
```bash
# Enable debug logging
docker service update --env-add LOG_LEVEL=DEBUG mcx3d_web

# View debug logs
docker service logs mcx3d_web | grep DEBUG

# Disable debug logging
docker service update --env-add LOG_LEVEL=INFO mcx3d_web
```

### Recovery Procedures

#### Database Recovery
```bash
# Restore from backup
gunzip < /backups/db_20240725.sql.gz | docker exec -i mcx3d_db psql -U mcx3d_user mcx3d_finance_prod
```

#### Full System Recovery
```bash
# Stop services
docker stack rm mcx3d

# Restore database
# ... (see above)

# Redeploy
docker stack deploy -c docker-compose.production.yml mcx3d
```

## Performance Optimization

### 1. Database Optimization
```sql
-- Add indexes for common queries
CREATE INDEX idx_transactions_org_date ON transactions(organization_id, date);
CREATE INDEX idx_invoices_org_status ON invoices(organization_id, status);

-- Analyze tables
ANALYZE transactions;
ANALYZE invoices;
```

### 2. Redis Optimization
```bash
# Configure Redis persistence
docker exec mcx3d_redis redis-cli CONFIG SET save "900 1 300 10 60 10000"

# Set max memory policy
docker exec mcx3d_redis redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

### 3. Application Optimization
```bash
# Enable query caching
export ENABLE_QUERY_CACHE=true

# Increase worker processes
docker service update --env-add WORKERS=4 mcx3d_web
```

## Security Best Practices

1. **Regular Updates**
   - Update base images monthly
   - Apply security patches immediately
   - Monitor CVE databases

2. **Access Control**
   - Use strong passwords
   - Enable 2FA for admin accounts
   - Regularly rotate secrets

3. **Network Security**
   - Use VPN for admin access
   - Implement rate limiting
   - Enable WAF if available

4. **Audit Logging**
   - Log all admin actions
   - Monitor failed login attempts
   - Regular security audits

## Conclusion

Following this guide ensures a secure, performant, and maintainable production deployment of MCX3D Financials v2. Regular monitoring and maintenance are essential for optimal performance and security.