#!/bin/bash

# MCX3D Finance - Xero Sync Examples

echo "MCX3D Finance - Xero Sync Examples"
echo "=================================="
echo ""

# 1. Full sync (synchronous mode with progress)
echo "1. Running full sync for organization ID 2 (synchronous with progress):"
echo "   $ mcx3d-finance sync xero --org-id 2"
echo ""

# 2. Full sync (asynchronous mode)
echo "2. Running full sync asynchronously:"
echo "   $ mcx3d-finance sync xero --org-id 2 --async-mode"
echo ""

# 3. Incremental sync
echo "3. Running incremental sync (only recent changes):"
echo "   $ mcx3d-finance sync xero --org-id 2 --incremental"
echo ""

# 4. Check async task status
echo "4. Checking status of async sync task:"
echo "   $ mcx3d-finance sync status <task-id>"
echo ""

# 5. Full sync without progress bar
echo "5. Running full sync without progress display:"
echo "   $ mcx3d-finance sync xero --org-id 2 --no-show-progress"
echo ""

# 6. Debug mode
echo "6. Running sync with debug logging:"
echo "   $ mcx3d-finance --debug sync xero --org-id 2"
echo ""

# Example output format:
echo "Example output:"
echo "--------------"
echo "Starting Xero sync for organization: 2"
echo "Mode: Full sync"
echo "Processing: Synchronous"
echo ""
echo "Starting synchronous sync..."
echo "Syncing data  [####################################]  100%"
echo ""
echo "✅ Sync completed successfully!"
echo ""
echo "Import Statistics:"
echo "  accounts: 107/107 imported, 0 errors"
echo "  contacts: 374/374 imported, 0 errors"
echo "  invoices: 120/120 imported, 0 errors"
echo "  transactions: 89/89 imported, 0 errors"
echo ""
echo "Storage Statistics:"
echo "  accounts: 107 created, 0 updated, 0 skipped"
echo "  contacts: 374 created, 0 updated, 0 skipped"
echo "  invoices: 120 created, 0 updated, 0 skipped"
echo "  bank_transactions: 89 created, 0 updated, 0 skipped"