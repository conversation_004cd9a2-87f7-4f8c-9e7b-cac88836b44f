# MCX3D Financials Environment Configuration
# Copy this file to .env and update with your actual values
# IMPORTANT: Never commit this file to version control!

# ==================== SECURITY NOTICE ====================
# This application requires proper security configuration.
# Failure to set secure values will result in startup errors.
# Use 'python -m mcx3d_finance.utils.generate_keys' to generate secure keys.
# ========================================================

# Database Configuration
# REQUIRED: Set a secure database URL with strong credentials
# Format: postgresql://username:password@host:port/database
# Example: ***********************************************************/mcx3d_finance
DATABASE_URL=

# Redis Configuration
# Used for caching and session storage
REDIS_URL=redis://localhost:6379/0

# Xero OAuth Configuration
# Obtain these from your Xero app at https://developer.xero.com/myapps
XERO_CLIENT_ID=
XERO_CLIENT_SECRET=
# For production, use HTTPS redirect URI
XERO_REDIRECT_URI=http://localhost:8000/api/auth/xero/callback
XERO_SCOPES=accounting.transactions accounting.contacts accounting.reports.read accounting.settings
# Optional: Set webhook key for Xero webhooks
XERO_WEBHOOK_KEY=

# Xero Authentication Tokens (for CLI usage)
# These are needed when using the CLI to generate financial documents
XERO_ACCESS_TOKEN=
XERO_REFRESH_TOKEN=
XERO_TENANT_ID=

# ==================== CRITICAL SECURITY SETTINGS ====================
# These MUST be set with secure values. No defaults are provided.

# JWT Secret Key
# REQUIRED: Generate with 'python -m mcx3d_finance.utils.generate_keys --type jwt'
# Must be at least 32 characters with mixed case, numbers, and special characters
# NEVER use default or example values in production!
SECRET_KEY=

# Encryption Key for sensitive data (OAuth tokens, etc.)
# REQUIRED: Generate with 'python -m mcx3d_finance.utils.generate_keys --type fernet'
# This is a base64-encoded Fernet key for AES-256 encryption
ENCRYPTION_KEY=

# JWT Configuration
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=15  # Reduced for security (use refresh tokens)

# ==================== SESSION MANAGEMENT ====================
# Controls how user sessions are managed
REFRESH_TOKEN_EXPIRE_DAYS=30
MAX_SESSIONS_PER_USER=5
SESSION_IDLE_TIMEOUT_MINUTES=60

# ==================== ACCOUNT SECURITY ====================
# Account lockout settings to prevent brute force attacks
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=30

# Multi-Factor Authentication (MFA)
MFA_CODE_VALIDITY_MINUTES=5
MFA_ISSUER_NAME=MCX3D Finance

# ==================== RATE LIMITING ====================
# Protect against abuse and DDoS attacks
RATE_LIMIT_DEFAULT=100  # requests per minute for general API
RATE_LIMIT_AUTH=5      # login attempts per 5 minutes

# ==================== AUDIT & MONITORING ====================
# Security audit logging configuration
AUDIT_LOG_FILE=/var/log/mcx3d_audit.log
AUDIT_RETENTION_DAYS=365
ENABLE_AUDIT_ENCRYPTION=true

# ==================== DATA PROTECTION ====================
# Field-level encryption and data retention
ENABLE_FIELD_ENCRYPTION=true
KEY_ROTATION_DAYS=90
DATA_RETENTION_DAYS=2555  # 7 years for financial data

# Application Configuration
# SECURITY: Set DEBUG=False in production!
DEBUG=False
LOG_LEVEL=INFO
APP_NAME=MCX3D Finance
APP_VERSION=2.0.0
ENVIRONMENT=production

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# CORS Configuration (comma-separated origins)
# SECURITY: Only allow trusted origins in production
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Legacy Rate Limiting (deprecated - use new rate limit settings above)
# RATE_LIMIT_PER_MINUTE=100
# RATE_LIMIT_PER_HOUR=1000

# Email Configuration (for notifications)
# SECURITY: Use app-specific passwords, never your main password
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
EMAIL_FROM=<EMAIL>

# Monitoring (optional but recommended for security)
SENTRY_DSN=
DATADOG_API_KEY=

# Testing (only used in test environment)
# SECURITY: Use different credentials for test database
TEST_DATABASE_URL=
TEST_REDIS_URL=redis://localhost:6379/1

# ==================== SECURITY BEST PRACTICES ====================
# 1. Generate unique keys for each environment (dev, staging, prod)
# 2. Rotate keys periodically (recommended: every 90 days)
# 3. Use a secure key management system in production (e.g., AWS KMS, HashiCorp Vault)
# 4. Enable HTTPS for all production endpoints
# 5. Monitor failed authentication attempts
# 6. Implement IP allowlisting for sensitive operations
# 7. Regular security audits: python -m mcx3d_finance.utils.security_audit
# 8. Enable MFA for all administrative accounts
# 9. Use Redis with password protection in production
# 10. Set up security headers (CSP, HSTS, X-Frame-Options)
# 11. Implement GDPR compliance features if handling EU data
# 12. Regular dependency updates: pip-audit or safety check
# 13. Use separate database credentials for read-only operations
# 14. Enable database SSL/TLS connections in production
# 15. Implement API versioning for backward compatibility
# ================================================================

# ==================== ADDITIONAL SECURITY DEPENDENCIES ====================
# Install these packages for enhanced security:
# pip install redis[hiredis]  # Faster Redis client
# pip install pyotp          # For MFA/2FA support
# pip install email-validator # For email validation
# pip install bleach         # For input sanitization
# pip install cryptography   # For field-level encryption
# =======================================================================