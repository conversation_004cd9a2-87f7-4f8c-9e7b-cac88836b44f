"""
Centralized error handling middleware for consistent API error responses.

This middleware ensures all API errors are returned in a standardized format
with proper logging, correlation IDs, and user-friendly messages.
"""
import logging
import uuid
from datetime import datetime
from typing import Any, Dict, Optional

from fastapi import Request, Response
from fastapi.exceptions import HTTPException, RequestValidationError
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.status import HTTP_500_INTERNAL_SERVER_ERROR

from mcx3d_finance.api.schemas import ErrorResponse
from mcx3d_finance.exceptions.auth import (
    InvalidCredentialsError,
    AccountLockedError,
    MFARequiredError,
    AuthenticationError,
)
from mcx3d_finance.exceptions.integration import (
    XeroIntegrationError,
)

logger = logging.getLogger(__name__)


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """
    Centralized error handling middleware that catches all exceptions
    and returns standardized error responses.
    """

    async def dispatch(self, request: Request, call_next):
        """Process the request and handle any exceptions."""
        # Generate correlation ID for this request
        correlation_id = str(uuid.uuid4())
        request.state.correlation_id = correlation_id

        try:
            response = await call_next(request)
            return response
        except Exception as exc:
            return await self._handle_exception(request, exc, correlation_id)

    async def _handle_exception(
        self, request: Request, exc: Exception, correlation_id: str
    ) -> JSONResponse:
        """Handle different types of exceptions and return appropriate responses."""
        
        # Log the error with correlation ID for debugging
        logger.error(
            f"API Error [{correlation_id}] {request.method} {request.url}: {str(exc)}",
            exc_info=True,
            extra={
                "correlation_id": correlation_id,
                "method": request.method,
                "url": str(request.url),
                "user_agent": request.headers.get("user-agent"),
            }
        )

        error_response, status_code = self._create_error_response(exc, correlation_id)
        
        return JSONResponse(
            status_code=status_code,
            content=error_response.dict(),
            headers={"X-Request-ID": correlation_id}
        )

    def _create_error_response(
        self, exc: Exception, correlation_id: str
    ) -> tuple[ErrorResponse, int]:
        """Create standardized error response based on exception type."""
        
        timestamp = datetime.utcnow()

        # Handle HTTPException (FastAPI's standard HTTP errors)
        if isinstance(exc, HTTPException):
            return (
                ErrorResponse(
                    error=self._get_error_type(exc.status_code),
                    detail=exc.detail,
                    timestamp=timestamp,
                    request_id=correlation_id,
                ),
                exc.status_code,
            )

        # Handle Pydantic validation errors
        if isinstance(exc, RequestValidationError):
            error_detail = self._format_validation_error(exc)
            return (
                ErrorResponse(
                    error="Validation Error",
                    detail=error_detail,
                    timestamp=timestamp,
                    request_id=correlation_id,
                ),
                422,
            )

        # Handle custom authentication errors
        if isinstance(exc, InvalidCredentialsError):
            return (
                ErrorResponse(
                    error="Authentication Failed",
                    detail="Invalid username or password",
                    timestamp=timestamp,
                    request_id=correlation_id,
                ),
                401,
            )

        if isinstance(exc, AccountLockedError):
            return (
                ErrorResponse(
                    error="Account Locked",
                    detail="Account is temporarily locked due to multiple failed login attempts",
                    timestamp=timestamp,
                    request_id=correlation_id,
                ),
                423,
            )

        if isinstance(exc, MFARequiredError):
            return (
                ErrorResponse(
                    error="MFA Required",
                    detail="Multi-factor authentication is required to complete login",
                    timestamp=timestamp,
                    request_id=correlation_id,
                ),
                403,
            )

        if isinstance(exc, AuthenticationError):
            return (
                ErrorResponse(
                    error="Authentication Error",
                    detail=str(exc),
                    timestamp=timestamp,
                    request_id=correlation_id,
                ),
                401,
            )

        # Handle Xero integration errors
        if isinstance(exc, XeroIntegrationError):
            # Determine if this is an auth-specific error based on the message
            error_msg = str(exc).lower()
            if any(term in error_msg for term in ['auth', 'token', 'unauthorized', 'forbidden']):
                return (
                    ErrorResponse(
                        error="Xero Authentication Error",
                        detail="Failed to authenticate with Xero. Please reconnect your account.",
                        timestamp=timestamp,
                        request_id=correlation_id,
                    ),
                    401,
                )
            else:
                return (
                    ErrorResponse(
                        error="Xero Integration Error",
                        detail=f"Xero API error: {str(exc)}",
                        timestamp=timestamp,
                        request_id=correlation_id,
                    ),
                    502,
                )

        # Handle any other unexpected errors
        return (
            ErrorResponse(
                error="Internal Server Error",
                detail="An unexpected error occurred. Please try again later.",
                timestamp=timestamp,
                request_id=correlation_id,
            ),
            HTTP_500_INTERNAL_SERVER_ERROR,
        )

    def _get_error_type(self, status_code: int) -> str:
        """Get human-readable error type from HTTP status code."""
        error_types = {
            400: "Bad Request",
            401: "Unauthorized",
            403: "Forbidden",
            404: "Not Found",
            405: "Method Not Allowed",
            409: "Conflict",
            422: "Validation Error",
            423: "Locked",
            429: "Too Many Requests",
            500: "Internal Server Error",
            502: "Bad Gateway",
            503: "Service Unavailable",
            504: "Gateway Timeout",
        }
        return error_types.get(status_code, "Error")

    def _format_validation_error(self, exc: RequestValidationError) -> str:
        """Format Pydantic validation errors into user-friendly messages."""
        errors = []
        for error in exc.errors():
            field = " -> ".join(str(loc) for loc in error["loc"])
            message = error["msg"]
            errors.append(f"{field}: {message}")
        
        if len(errors) == 1:
            return f"Validation failed: {errors[0]}"
        else:
            return f"Validation failed for {len(errors)} fields: {'; '.join(errors)}"


def get_correlation_id(request: Request) -> Optional[str]:
    """Get the correlation ID for the current request."""
    return getattr(request.state, 'correlation_id', None)