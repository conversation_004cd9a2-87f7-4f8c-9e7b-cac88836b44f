"""
FastAPI routes for Xero data import operations.
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from celery import current_app as celery_app

from .auth_middleware import get_current_user
from ..db.session import get_db
from ..db.models import Organization, SyncStatus
from ..integrations.xero_data_import import XeroDataImportService
from ..integrations.mcp_bridge import MCPXeroBridge
from ..tasks.sync_tasks import sync_xero_data
from .schemas import (
    XeroImportRequest, 
    XeroImportResponse, 
    XeroSyncStatusRequest,
    XeroAuthStatusResponse,
    SyncStatusResponse,
    ErrorResponse
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/xero", tags=["xero"])


@router.post("/import", response_model=XeroImportResponse)
async def import_xero_data(
    request: XeroImportRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Import data from Xero for the specified organization.
    
    This endpoint can perform either a full import or incremental sync:
    - Full import: Downloads all data from Xero
    - Incremental sync: Only downloads changes since last sync
    """
    try:
        # Verify organization exists and user has access
        organization = db.query(Organization).filter(
            Organization.id == request.organization_id
        ).first()
        
        if not organization:
            raise HTTPException(
                status_code=404, 
                detail=f"Organization {request.organization_id} not found"
            )
        
        # Check if user has access to this organization (implement your auth logic)
        # For now, we'll allow any authenticated user to access any organization
        
        logger.info(f"Starting Xero import for organization {request.organization_id}")
        
        # Check if there's already an import in progress
        sync_status = db.query(SyncStatus).filter(
            SyncStatus.organization_id == request.organization_id
        ).first()
        
        if sync_status and sync_status.sync_status == "running":
            raise HTTPException(
                status_code=409,
                detail="Import already in progress for this organization"
            )
        
        # Update sync status to running
        if not sync_status:
            sync_status = SyncStatus(
                organization_id=request.organization_id,
                sync_status="running",
                started_at=datetime.now(timezone.utc)
            )
            db.add(sync_status)
        else:
            sync_status.sync_status = "running"
            sync_status.started_at = datetime.now(timezone.utc)
            sync_status.error_message = None
        
        db.commit()
        
        if request.incremental and sync_status.last_successful_sync:
            # Incremental sync - only import changes since last sync
            logger.info(f"Starting incremental sync since {sync_status.last_successful_sync}")
            background_tasks.add_task(
                run_incremental_import,
                request.organization_id,
                sync_status.last_successful_sync
            )
        else:
            # Full import
            logger.info("Starting full data import")
            background_tasks.add_task(
                run_full_import,
                request.organization_id,
                request.force_refresh
            )
        
        return XeroImportResponse(
            success=True,
            organization_id=request.organization_id,
            import_stats={},  # Will be populated by background task
            processed_data_summary={},
            timestamp=datetime.now(timezone.utc)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting Xero import: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/auth-status/{organization_id}", response_model=XeroAuthStatusResponse)
async def get_xero_auth_status(
    organization_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Check the Xero authentication status for an organization."""
    try:
        # Verify organization exists
        organization = db.query(Organization).filter(
            Organization.id == organization_id
        ).first()
        
        if not organization:
            raise HTTPException(
                status_code=404,
                detail=f"Organization {organization_id} not found"
            )
        
        # Check authentication status using MCP bridge
        bridge = MCPXeroBridge(organization_id)
        tokens = bridge._get_organization_tokens()
        
        if not tokens:
            return XeroAuthStatusResponse(
                organization_id=organization_id,
                is_authenticated=False,
                expires_at=None,
                scopes=[],
                tenant_name=None
            )
        
        # Get expiration time from tokens
        expires_at = None
        if 'expires_at' in tokens:
            expires_at = datetime.fromtimestamp(tokens['expires_at'], tz=timezone.utc)
        
        # Get organization info
        try:
            org_details = bridge.get_organization_details()
            tenant_name = org_details.get('Name', 'Unknown')
        except:
            tenant_name = None
        
        return XeroAuthStatusResponse(
            organization_id=organization_id,
            is_authenticated=True,
            expires_at=expires_at,
            scopes=tokens.get('scope', '').split(' ') if tokens.get('scope') else [],
            tenant_name=tenant_name
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking Xero auth status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sync-status/{organization_id}", response_model=SyncStatusResponse)
async def get_sync_status(
    organization_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get the current sync status for an organization."""
    try:
        # Verify organization exists
        organization = db.query(Organization).filter(
            Organization.id == organization_id
        ).first()
        
        if not organization:
            raise HTTPException(
                status_code=404,
                detail=f"Organization {organization_id} not found"
            )
        
        # Get sync status
        sync_status = db.query(SyncStatus).filter(
            SyncStatus.organization_id == organization_id
        ).first()
        
        if not sync_status:
            return SyncStatusResponse(
                organization_id=organization_id,
                sync_status="never_synced",
                last_sync_at=None,
                next_sync_at=None,
                records_synced={},
                errors=[]
            )
        
        # Calculate next sync time (e.g., 24 hours from last successful sync)
        next_sync_at = None
        if sync_status.last_successful_sync:
            next_sync_at = sync_status.last_successful_sync + timedelta(hours=24)
        
        return SyncStatusResponse(
            organization_id=organization_id,
            sync_status=sync_status.sync_status,
            last_sync_at=sync_status.last_successful_sync,
            next_sync_at=next_sync_at,
            records_synced=sync_status.records_processed or {},
            errors=[sync_status.error_message] if sync_status.error_message else []
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting sync status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sync/{organization_id}", response_model=Dict[str, Any])
async def trigger_sync(
    organization_id: int,
    background_tasks: BackgroundTasks,
    force_full: bool = False,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Trigger a sync using the Celery task system.
    This is the recommended way to handle long-running imports.
    """
    try:
        # Verify organization exists
        organization = db.query(Organization).filter(
            Organization.id == organization_id
        ).first()
        
        if not organization:
            raise HTTPException(
                status_code=404,
                detail=f"Organization {organization_id} not found"
            )
        
        # Check if sync is already running
        sync_status = db.query(SyncStatus).filter(
            SyncStatus.organization_id == organization_id
        ).first()
        
        if sync_status and sync_status.sync_status == "running":
            raise HTTPException(
                status_code=409,
                detail="Sync already in progress for this organization"
            )
        
        # Queue the sync task
        task = sync_xero_data.delay(organization_id, not force_full)  # incremental = not force_full
        
        return {
            "task_id": task.id,
            "organization_id": organization_id,
            "status": "queued",
            "message": "Sync task has been queued and will start shortly"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error triggering sync: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/task-status/{task_id}")
async def get_task_status(
    task_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get the status of a Celery task."""
    try:
        result = celery_app.AsyncResult(task_id)
        
        return {
            "task_id": task_id,
            "status": result.status,
            "result": result.result if result.ready() else None,
            "info": result.info
        }
        
    except Exception as e:
        logger.error(f"Error getting task status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Background task functions
async def run_full_import(organization_id: int, force_refresh: bool = False):
    """Run a full data import in the background."""
    import_service = XeroDataImportService(organization_id)
    
    try:
        logger.info(f"Starting full import for organization {organization_id}")
        result = import_service.import_all_data()
        
        # Update sync status
        db = next(get_db())
        sync_status = db.query(SyncStatus).filter(
            SyncStatus.organization_id == organization_id
        ).first()
        
        if result["success"]:
            sync_status.sync_status = "completed"
            sync_status.last_successful_sync = datetime.now(timezone.utc)
            sync_status.records_processed = result["stats"]
            sync_status.error_message = None
            logger.info(f"Full import completed for organization {organization_id}")
        else:
            sync_status.sync_status = "failed"
            sync_status.error_message = result.get("error", "Unknown error")
            logger.error(f"Full import failed for organization {organization_id}: {result.get('error')}")
        
        db.commit()
        db.close()
        
    except Exception as e:
        logger.error(f"Error in full import: {e}")
        # Update sync status to failed
        db = next(get_db())
        sync_status = db.query(SyncStatus).filter(
            SyncStatus.organization_id == organization_id
        ).first()
        if sync_status:
            sync_status.sync_status = "failed"
            sync_status.error_message = str(e)
            db.commit()
        db.close()


async def run_incremental_import(organization_id: int, since_date: datetime):
    """Run an incremental import in the background."""
    import_service = XeroDataImportService(organization_id)
    
    try:
        logger.info(f"Starting incremental import for organization {organization_id} since {since_date}")
        result = import_service.import_incremental_data(since_date)
        
        # Update sync status
        db = next(get_db())
        sync_status = db.query(SyncStatus).filter(
            SyncStatus.organization_id == organization_id
        ).first()
        
        if result["success"]:
            sync_status.sync_status = "completed"
            sync_status.last_successful_sync = datetime.now(timezone.utc)
            sync_status.records_processed = result["stats"]
            sync_status.error_message = None
            logger.info(f"Incremental import completed for organization {organization_id}")
        else:
            sync_status.sync_status = "failed"
            sync_status.error_message = result.get("error", "Unknown error")
            logger.error(f"Incremental import failed for organization {organization_id}: {result.get('error')}")
        
        db.commit()
        db.close()
        
    except Exception as e:
        logger.error(f"Error in incremental import: {e}")
        # Update sync status to failed
        db = next(get_db())
        sync_status = db.query(SyncStatus).filter(
            SyncStatus.organization_id == organization_id
        ).first()
        if sync_status:
            sync_status.sync_status = "failed"
            sync_status.error_message = str(e)
            db.commit()
        db.close()