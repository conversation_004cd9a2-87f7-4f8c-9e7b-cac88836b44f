"""
Comprehensive tests for configuration validation system.

Tests all aspects of the configuration management system including:
- Environment-specific configuration loading
- Configuration validation rules
- Security validations
- Environment-specific requirements
- Configuration health checks
"""

import pytest
import tempfile
import os
import yaml
from unittest.mock import patch, MagicMock
from pathlib import Path

from mcx3d_finance.core.config import (
    detect_environment,
    load_config,
    get_database_url,
    get_xero_config,
    get_security_config,
    validate_startup_config,
    get_config_health_status,
    settings
)
from mcx3d_finance.validation.config_validator import ConfigurationValidator
from mcx3d_finance.exceptions import ConfigurationValidationError

class TestEnvironmentDetection:
    """Test environment detection functionality."""
    
    def test_environment_from_env_variable(self):
        """Test environment detection from environment variables."""
        with patch.dict(os.environ, {'ENVIRONMENT': 'staging'}):
            assert detect_environment() == 'staging'
        
        with patch.dict(os.environ, {'ENV': 'development'}):
            assert detect_environment() == 'development'
    
    def test_environment_from_deployment_indicators(self):
        """Test environment detection from deployment indicators."""
        with patch.dict(os.environ, {'KUBERNETES_SERVICE_HOST': 'kubernetes'}):
            assert detect_environment() == 'production'
        
        with patch.dict(os.environ, {'DOCKER_CONTAINER': 'true'}):
            assert detect_environment() == 'staging'
    
    def test_environment_from_git_presence(self):
        """Test environment detection from git repository."""
        with patch('os.path.exists') as mock_exists:
            mock_exists.return_value = True
            assert detect_environment() == 'development'
    
    def test_environment_default_production(self):
        """Test that production is the default environment."""
        with patch.dict(os.environ, {}, clear=True):
            with patch('os.path.exists', return_value=False):
                assert detect_environment() == 'production'

class TestConfigurationLoading:
    """Test configuration file loading."""
    
    def test_load_environment_specific_config(self):
        """Test loading environment-specific configuration."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create environment-specific config
            config_dir = Path(temp_dir) / 'config'
            config_dir.mkdir()
            
            env_config = {
                'environment': 'testing',
                'database': {'pool_size': 5},
                'security': {'access_token_expire_minutes': 60}
            }
            
            config_file = config_dir / 'testing.yml'
            with open(config_file, 'w') as f:
                yaml.dump(env_config, f)
            
            # Change working directory temporarily
            original_cwd = os.getcwd()
            try:
                os.chdir(temp_dir)
                with patch('mcx3d_finance.core.config.detect_environment', return_value='testing'):
                    config = load_config()
                    assert config['environment'] == 'testing'
                    assert config['database']['pool_size'] == 5
            finally:
                os.chdir(original_cwd)
    
    def test_load_fallback_config(self):
        """Test loading fallback configuration when environment-specific file doesn't exist."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create main config.yml
            config_data = {
                'environment': 'production',
                'database': {'pool_size': 10}
            }
            
            config_file = Path(temp_dir) / 'config.yml'
            with open(config_file, 'w') as f:
                yaml.dump(config_data, f)
            
            original_cwd = os.getcwd()
            try:
                os.chdir(temp_dir)
                config = load_config()
                assert config['environment'] == 'production'
            finally:
                os.chdir(original_cwd)
    
    def test_load_config_file_not_found(self):
        """Test behavior when no configuration file is found."""
        with tempfile.TemporaryDirectory() as temp_dir:
            original_cwd = os.getcwd()
            try:
                os.chdir(temp_dir)
                config = load_config()
                assert config == {}
            finally:
                os.chdir(original_cwd)

class TestDatabaseConfiguration:
    """Test database configuration validation."""
    
    def test_get_database_url_from_environment(self):
        """Test getting database URL from environment variable."""
        test_url = "************************************/database"
        with patch.object(settings, 'database_url', test_url):
            assert get_database_url() == test_url
    
    def test_get_database_url_test_mode(self):
        """Test database URL in test mode."""
        with patch.dict('sys.modules', {'pytest': MagicMock()}):
            url = get_database_url()
            assert url.startswith('sqlite:///')
    
    def test_get_database_url_missing(self):
        """Test error when database URL is missing."""
        with patch.object(settings, 'database_url', None):
            with patch('mcx3d_finance.core.config.config', {}):
                with pytest.raises(ValueError, match="DATABASE_URL must be set"):
                    get_database_url()
    
    def test_get_database_url_insecure_default(self):
        """Test error when insecure database URL is detected."""
        insecure_url = "postgresql://user:password@localhost:5432/database"
        with patch.object(settings, 'database_url', insecure_url):
            with pytest.raises(ValueError, match="Insecure database URL detected"):
                get_database_url()

class TestXeroConfiguration:
    """Test Xero configuration validation."""
    
    def test_get_xero_config_valid(self):
        """Test getting valid Xero configuration."""
        with patch.object(settings, 'xero_client_id', 'valid_client_id'):
            with patch.object(settings, 'xero_client_secret', 'valid_client_secret'):
                config = get_xero_config()
                assert config['client_id'] == 'valid_client_id'
                assert config['client_secret'] == 'valid_client_secret'
    
    def test_get_xero_config_missing_client_id(self):
        """Test error when Xero client ID is missing."""
        with patch.object(settings, 'xero_client_id', None):
            with patch.object(settings, 'xero_client_secret', 'secret'):
                with patch('mcx3d_finance.core.config.config', {}):
                    with pytest.raises(ValueError, match="XERO_CLIENT_ID must be set"):
                        get_xero_config()
    
    def test_get_xero_config_placeholder_values(self):
        """Test error when placeholder values are detected."""
        with patch.object(settings, 'xero_client_id', 'YOUR_CLIENT_ID'):
            with patch.object(settings, 'xero_client_secret', 'secret'):
                with pytest.raises(ValueError, match="Placeholder value detected"):
                    get_xero_config()

class TestSecurityConfiguration:
    """Test security configuration validation."""
    
    def test_get_security_config_valid(self):
        """Test getting valid security configuration."""
        valid_key = "a" * 32  # 32-character key
        with patch.object(settings, 'secret_key', valid_key):
            config = get_security_config()
            assert config['secret_key'] == valid_key
    
    def test_get_security_config_missing_key(self):
        """Test error when secret key is missing."""
        with patch.object(settings, 'secret_key', None):
            with patch('mcx3d_finance.core.config.config', {}):
                with pytest.raises(ValueError, match="SECRET_KEY must be set"):
                    get_security_config()
    
    def test_get_security_config_short_key(self):
        """Test error when secret key is too short."""
        short_key = "short"
        with patch.object(settings, 'secret_key', short_key):
            with pytest.raises(ValueError, match="SECRET_KEY must be at least 32 characters"):
                get_security_config()
    
    def test_get_security_config_insecure_key(self):
        """Test error when insecure secret key is detected."""
        insecure_key = "your-secret-key-here-minimum-32-characters-long"
        with patch.object(settings, 'secret_key', insecure_key):
            with pytest.raises(ValueError, match="Insecure SECRET_KEY detected"):
                get_security_config()

class TestConfigurationValidator:
    """Test the ConfigurationValidator class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = ConfigurationValidator()
    
    def test_validate_valid_configuration(self):
        """Test validation of a valid configuration."""
        config = {
            'database': {
                'pool_size': 10,
                'timeout': 30
            },
            'security': {
                'access_token_expire_minutes': 15,
                'max_login_attempts': 5
            },
            'environment': 'production'
        }
        
        is_valid, errors, warnings = self.validator.validate_configuration(config)
        assert is_valid
        assert len(errors) == 0
    
    def test_validate_invalid_field_types(self):
        """Test validation of invalid field types."""
        config = {
            'database': {
                'pool_size': 'invalid',  # Should be int
                'timeout': 30
            }
        }
        
        is_valid, errors, warnings = self.validator.validate_configuration(config)
        assert not is_valid
        assert any('Invalid type' in error for error in errors)
    
    def test_validate_out_of_range_values(self):
        """Test validation of out-of-range values."""
        config = {
            'database': {
                'pool_size': 1000,  # Too high
                'timeout': 0        # Too low
            }
        }
        
        is_valid, errors, warnings = self.validator.validate_configuration(config)
        assert not is_valid
        assert any('must be at most' in error for error in errors)
        assert any('must be at least' in error for error in errors)
    
    def test_validate_invalid_choices(self):
        """Test validation of invalid choice values."""
        config = {
            'logging': {
                'level': 'INVALID_LEVEL'
            }
        }
        
        is_valid, errors, warnings = self.validator.validate_configuration(config)
        assert not is_valid
        assert any('Must be one of' in error for error in errors)
    
    def test_validate_secret_key(self):
        """Test secret key validation."""
        # Test valid key
        self.validator._validate_secret_key('test.key', 'a' * 32)
        assert len(self.validator.validation_errors) == 0
        
        # Test short key
        self.validator.validation_errors = []
        with pytest.raises(ValueError, match="at least 32 characters"):
            self.validator._validate_secret_key('test.key', 'short')
        
        # Test insecure key
        with pytest.raises(ValueError, match="Insecure secret key"):
            self.validator._validate_secret_key('test.key', 'changeme' + 'a' * 25)
    
    def test_validate_encryption_key(self):
        """Test encryption key validation."""
        # Test valid Fernet key
        valid_key = "ZmDfcOSzI0ZwUOiMBWHPrYKnCybKIkb8d7C_F3cF5Xs="
        self.validator._validate_encryption_key('test.key', valid_key)
        assert len(self.validator.validation_errors) == 0
        
        # Test invalid key
        with pytest.raises(ValueError, match="Invalid Fernet"):
            self.validator._validate_encryption_key('test.key', 'invalid_key')
    
    def test_validate_xero_credentials(self):
        """Test Xero credential validation."""
        # Test placeholder detection
        with pytest.raises(ValueError, match="Placeholder"):
            self.validator._validate_xero_client_id('test.id', 'YOUR_CLIENT_ID')
        
        with pytest.raises(ValueError, match="Placeholder"):
            self.validator._validate_xero_client_secret('test.secret', 'YOUR_CLIENT_SECRET')
    
    def test_validate_cors_origins(self):
        """Test CORS origins validation."""
        # Test wildcard warning
        self.validator._validate_cors_origins('test.cors', ['*'])
        assert any('Wildcard CORS origin' in warning for warning in self.validator.validation_warnings)
        
        # Test invalid origin format
        self.validator.validation_errors = []
        self.validator._validate_cors_origins('test.cors', ['invalid-origin'])
        assert any('Invalid CORS origin format' in error for error in self.validator.validation_errors)
    
    def test_validate_environment_specific_production(self):
        """Test production-specific validation."""
        config = {
            'environment': 'production',
            'security': {'debug': True},  # Should trigger error
            'ssl': {'enforce_https': False},  # Should trigger warning
            'monitoring': {'enable_alerting': False}  # Should trigger warning
        }
        
        self.validator._validate_environment_specific_config(config)
        
        # Check for production-specific errors and warnings
        assert any('Debug mode must be disabled' in error for error in self.validator.validation_errors)
        assert any('HTTPS enforcement' in warning for warning in self.validator.validation_warnings)
        assert any('Alerting should be enabled' in warning for warning in self.validator.validation_warnings)

class TestStartupValidation:
    """Test application startup validation."""
    
    @patch('mcx3d_finance.core.config.get_database_url')
    @patch('mcx3d_finance.core.config.get_xero_config')
    @patch('mcx3d_finance.core.config.get_security_config')
    def test_validate_startup_config_success(self, mock_security, mock_xero, mock_db):
        """Test successful startup configuration validation."""
        # Mock valid configurations
        mock_db.return_value = "********************************/db"
        mock_xero.return_value = {
            'client_id': 'valid_id',
            'client_secret': 'valid_secret',
            'redirect_uri': 'https://example.com/callback',
            'scopes': 'accounting.transactions'
        }
        mock_security.return_value = {
            'secret_key': 'a' * 32,
            'access_token_expire_minutes': 15
        }
        
        # Should not raise an exception
        validate_startup_config()
    
    @patch('mcx3d_finance.core.config.get_database_url')
    def test_validate_startup_config_failure(self, mock_db):
        """Test startup configuration validation failure."""
        # Mock configuration error
        mock_db.side_effect = ValueError("Database configuration error")
        
        with pytest.raises(ValueError, match="Invalid configuration detected"):
            validate_startup_config()

class TestConfigurationHealthCheck:
    """Test configuration health check functionality."""
    
    @patch('mcx3d_finance.core.config.get_database_url')
    @patch('mcx3d_finance.core.config.get_xero_config')
    @patch('mcx3d_finance.core.config.get_security_config')
    def test_config_health_status_healthy(self, mock_security, mock_xero, mock_db):
        """Test healthy configuration status."""
        # Mock successful configurations
        mock_db.return_value = "********************************/db"
        mock_xero.return_value = {'client_id': 'valid', 'client_secret': 'valid'}
        mock_security.return_value = {'secret_key': 'a' * 32}
        
        status = get_config_health_status()
        assert status['status'] == 'healthy'
        assert status['checks_passed'] is True
        assert len(status['issues']) == 0
    
    @patch('mcx3d_finance.core.config.get_database_url')
    def test_config_health_status_unhealthy(self, mock_db):
        """Test unhealthy configuration status."""
        # Mock configuration error
        mock_db.side_effect = ValueError("Database error")
        
        status = get_config_health_status()
        assert status['status'] == 'unhealthy'
        assert status['checks_passed'] is False
        assert len(status['issues']) > 0
        assert any('Database configuration' in issue for issue in status['issues'])
    
    def test_config_health_status_with_warnings(self):
        """Test configuration status with warnings."""
        with patch('mcx3d_finance.core.config.detect_environment', return_value='production'):
            with patch('os.path.exists', return_value=False):  # No prod config file
                status = get_config_health_status()
                assert len(status.get('warnings', [])) > 0

class TestConfigurationFileValidation:
    """Test configuration file validation."""
    
    def test_validate_yaml_file(self):
        """Test YAML configuration file validation."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yml', delete=False) as f:
            config_data = {
                'database': {'pool_size': 10},
                'security': {'access_token_expire_minutes': 15}
            }
            yaml.dump(config_data, f)
            temp_file = f.name
        
        try:
            validator = ConfigurationValidator()
            is_valid, errors, warnings = validator.validate_file_configuration(temp_file)
            assert is_valid
            assert len(errors) == 0
        finally:
            os.unlink(temp_file)
    
    def test_validate_invalid_yaml_file(self):
        """Test validation of invalid YAML file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yml', delete=False) as f:
            f.write("invalid: yaml: content: [")  # Invalid YAML
            temp_file = f.name
        
        try:
            validator = ConfigurationValidator()
            with pytest.raises(ConfigurationValidationError, match="Invalid configuration file format"):
                validator.validate_file_configuration(temp_file)
        finally:
            os.unlink(temp_file)
    
    def test_validate_nonexistent_file(self):
        """Test validation of non-existent configuration file."""
        validator = ConfigurationValidator()
        with pytest.raises(ConfigurationValidationError, match="Configuration file not found"):
            validator.validate_file_configuration("/nonexistent/file.yml")

class TestEnvironmentVariableValidation:
    """Test environment variable configuration validation."""
    
    def test_validate_environment_variables(self):
        """Test validation of configuration from environment variables."""
        env_vars = {
            'MCX3D_DATABASE_POOL_SIZE': '10',
            'MCX3D_SECURITY_ACCESS_TOKEN_EXPIRE_MINUTES': '15'
        }
        
        with patch.dict(os.environ, env_vars):
            validator = ConfigurationValidator()
            is_valid, errors, warnings = validator.validate_environment_configuration()
            assert is_valid
    
    def test_validate_invalid_environment_variables(self):
        """Test validation of invalid environment variables."""
        env_vars = {
            'MCX3D_DATABASE_POOL_SIZE': 'invalid',  # Should be integer
        }
        
        with patch.dict(os.environ, env_vars):
            validator = ConfigurationValidator()
            is_valid, errors, warnings = validator.validate_environment_configuration()
            assert not is_valid
            assert len(errors) > 0

# Integration tests
class TestConfigurationIntegration:
    """Integration tests for the complete configuration system."""
    
    def test_full_configuration_lifecycle(self):
        """Test complete configuration loading and validation lifecycle."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create environment-specific configuration
            config_dir = Path(temp_dir) / 'config'
            config_dir.mkdir()
            
            config_data = {
                'environment': 'testing',
                'database': {
                    'pool_size': 5,
                    'timeout': 30
                },
                'security': {
                    'access_token_expire_minutes': 60,
                    'max_login_attempts': 10
                },
                'monitoring': {
                    'enable_metrics': True,
                    'health_check_interval_seconds': 30
                }
            }
            
            config_file = config_dir / 'testing.yml'
            with open(config_file, 'w') as f:
                yaml.dump(config_data, f)
            
            original_cwd = os.getcwd()
            try:
                os.chdir(temp_dir)
                
                # Test configuration loading
                with patch('mcx3d_finance.core.config.detect_environment', return_value='testing'):
                    config = load_config()
                    assert config['environment'] == 'testing'
                
                # Test configuration validation
                validator = ConfigurationValidator()
                is_valid, errors, warnings = validator.validate_configuration(config)
                assert is_valid
                assert len(errors) == 0
                
            finally:
                os.chdir(original_cwd)

# Performance tests
class TestConfigurationPerformance:
    """Performance tests for configuration operations."""
    
    def test_configuration_loading_performance(self):
        """Test that configuration loading is performant."""
        import time
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yml', delete=False) as f:
            config_data = {
                'database': {'pool_size': 10},
                'security': {'access_token_expire_minutes': 15},
                'performance': {'parallel_workers': 4}
            }
            yaml.dump(config_data, f)
            temp_file = f.name
        
        try:
            start_time = time.time()
            config = load_config(temp_file)
            load_time = time.time() - start_time
            
            # Configuration loading should be fast (< 100ms)
            assert load_time < 0.1
            assert config is not None
            
        finally:
            os.unlink(temp_file)
    
    def test_configuration_validation_performance(self):
        """Test that configuration validation is performant."""
        import time
        
        config = {
            'database': {'pool_size': 10, 'timeout': 30},
            'security': {'access_token_expire_minutes': 15, 'max_login_attempts': 5},
            'performance': {'parallel_workers': 4, 'memory_limit_mb': 1024},
            'monitoring': {'enable_metrics': True, 'health_check_interval_seconds': 60}
        }
        
        validator = ConfigurationValidator()
        
        start_time = time.time()
        is_valid, errors, warnings = validator.validate_configuration(config)
        validation_time = time.time() - start_time
        
        # Configuration validation should be fast (< 50ms)
        assert validation_time < 0.05
        assert is_valid