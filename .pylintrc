[MASTER]

# Use multiple processes to speed up Pylint
jobs=0

# <PERSON><PERSON> collected data for later comparisons
persistent=yes

# Specify a configuration file to load
#rcfile=

# When enabled, pylint would attempt to guess common misconfiguration and emit
# user-friendly hints instead of false-positive error messages
suggestion-mode=yes

# Allow loading of arbitrary C extensions. Extensions are imported into the
# active Python interpreter and may run arbitrary code.
unsafe-load-any-extension=no

# List of plugins (as comma separated values of python modules names) to load
load-plugins=

[MESSAGES CONTROL]

# Disable specific pylint warnings/errors
disable=raw-checker-failed,
        bad-inline-option,
        locally-disabled,
        file-ignored,
        suppressed-message,
        useless-suppression,
        deprecated-pragma,
        use-symbolic-message-instead,
        missing-module-docstring,
        missing-class-docstring,
        missing-function-docstring,
        too-few-public-methods,
        too-many-arguments,
        too-many-instance-attributes,
        too-many-local-variables,
        too-many-branches,
        too-many-statements,
        broad-except,
        consider-using-f-string

# Enable specific warnings
enable=c-extension-no-member

[REPORTS]

# Set the output format
output-format=text

# Tells whether to display a full report or only the messages
reports=no

# Python expression which should return a note less than 10 (10 being the best)
evaluation=10.0 - ((float(5 * error + warning + refactor + convention) / statement) * 10)

[REFACTORING]

# Maximum number of nested blocks for function / method body
max-nested-blocks=5

# Complete name of functions that never returns
never-returning-functions=sys.exit

[BASIC]

# Naming style matching correct argument names
argument-naming-style=snake_case

# Regular expression matching correct argument names
argument-rgx=[a-z_][a-z0-9_]{2,30}$

# Naming style matching correct attribute names
attr-naming-style=snake_case

# Regular expression matching correct attribute names
attr-rgx=[a-z_][a-z0-9_]{2,30}$

# Naming style matching correct class attribute names
class-attribute-naming-style=any

# Naming style matching correct class names
class-naming-style=PascalCase

# Regular expression matching correct class names
class-rgx=[A-Z_][a-zA-Z0-9]+$

# Naming style matching correct constant names
const-naming-style=UPPER_CASE

# Regular expression matching correct constant names
const-rgx=(([A-Z_][A-Z0-9_]*)|(__.*__))$

# Minimum line length for functions/classes that require docstrings
docstring-min-length=-1

# Naming style matching correct function names
function-naming-style=snake_case

# Regular expression matching correct function names
function-rgx=[a-z_][a-z0-9_]{2,30}$

# Good variable names which should always be accepted
good-names=i,j,k,ex,Run,_

# Include a hint for the correct naming format with invalid-name
include-naming-hint=no

# Naming style matching correct inline iteration names
inlinevar-naming-style=any

# Naming style matching correct method names
method-naming-style=snake_case

# Regular expression matching correct method names
method-rgx=[a-z_][a-z0-9_]{2,30}$

# Naming style matching correct module names
module-naming-style=snake_case

# Regular expression matching correct module names
module-rgx=(([a-z_][a-z0-9_]*)|([A-Z][a-zA-Z0-9]+))$

# Colon-delimited sets of names that determine each other's naming style when
# the name regexes allow several styles
name-group=

# Regular expression which should only match function or class names that do
# not require a docstring
no-docstring-rgx=^_

# List of decorators that produce properties
property-classes=abc.abstractproperty

# Naming style matching correct variable names
variable-naming-style=snake_case

# Regular expression matching correct variable names
variable-rgx=[a-z_][a-z0-9_]{2,30}$

[FORMAT]

# Expected format of line ending
expected-line-ending-format=

# Regexp for a line that is allowed to be longer than the limit
ignore-long-lines=^\s*(# )?<?https?://\S+>?$

# Number of spaces of indent required inside a hanging or continued line
indent-after-paren=4

# String used as indentation unit
indent-string='    '

# Maximum number of characters on a single line
max-line-length=88

# Maximum number of lines in a module
max-module-lines=1000

# List of optional constructs for which whitespace checking is disabled
no-space-check=trailing-comma,dict-separator

# Allow the body of a class to be on the same line as the declaration if body
# contains single statement
single-line-class-stmt=no

# Allow the body of an if to be on the same line as the test if there is no else
single-line-if-stmt=no

[LOGGING]

# Format style used to check logging format string
logging-format-style=old

# Logging modules to check that the string format arguments are in logging
# function parameter format
logging-modules=logging

[MISCELLANEOUS]

# List of note tags to take in consideration
notes=FIXME,XXX,TODO

[SIMILARITIES]

# Ignore comments when computing similarities
ignore-comments=yes

# Ignore docstrings when computing similarities
ignore-docstrings=yes

# Ignore imports when computing similarities
ignore-imports=no

# Minimum lines number of a similarity
min-similarity-lines=4

[SPELLING]

# Limits count of emitted suggestions by the spellchecker
max-spelling-suggestions=4

# Spelling dictionary name
spelling-dict=

# List of comma separated words that should not be checked
spelling-ignore-words=

# A path to a file that contains private dictionary
spelling-private-dict-file=

# Tells whether to store unknown words to indicated private dictionary
spelling-store-unknown-words=no

[TYPECHECK]

# List of decorators that produce context managers
contextmanager-decorators=contextlib.contextmanager

# List of members which are set dynamically and missed by pylint inference
generated-members=

# Tells whether missing members accessed in mixin class should be ignored
ignore-mixin-members=yes

# Tells whether to warn about missing members when the owner of the attribute
# is inferred to be None
ignore-none=yes

# This flag controls whether pylint should warn about no-member and similar
# checks whenever an opaque object is returned when inferring
ignore-on-opaque-inference=yes

# List of class names for which member attributes should not be checked
ignored-classes=optparse.Values,thread._local,_thread._local

# List of module names for which member attributes should not be checked
ignored-modules=

# Show a hint with the possible names when a member name was not found
missing-member-hint=yes

# The minimum edit distance a name should have in order to be considered a
# similar match for a missing member name
missing-member-hint-distance=1

# The total number of similar names that should be taken in consideration when
# showing a hint for a missing member
missing-member-max-choices=1

[VARIABLES]

# List of additional names supposed to be defined in builtins
additional-builtins=

# Tells whether unused global variables should be treated as a violation
allow-global-unused-variables=yes

# List of strings which can identify a callback function by name
callbacks=cb_,_cb

# A regular expression matching the name of dummy variables
dummy-variables-rgx=_+$|(_[a-zA-Z0-9_]*[a-zA-Z0-9]+?$)|dummy|^ignored_|^unused_

# Argument names that match this expression will be ignored
ignored-argument-names=_.*|^ignored_|^unused_

# Tells whether we should check for unused import in __init__ files
init-import=no

# List of qualified module names which can have objects that can redefine builtins
redefining-builtins-modules=six.moves,past.builtins,future.builtins,io

[CLASSES]

# List of method names used to declare (i.e. assign) instance attributes
defining-attr-methods=__init__,__new__,setUp

# List of member names, which should be excluded from the protected access warning
exclude-protected=_asdict,_fields,_replace,_source,_make

# List of valid names for the first argument in a class method
valid-classmethod-first-arg=cls

# List of valid names for the first argument in a metaclass class method
valid-metaclass-classmethod-first-arg=mcs

[DESIGN]

# Maximum number of arguments for function / method
max-args=5

# Maximum number of attributes for a class
max-attributes=7

# Maximum number of boolean expressions in a if statement
max-bool-expr=5

# Maximum number of branch for function / method body
max-branches=12

# Maximum number of locals for function / method body
max-locals=15

# Maximum number of parents for a class
max-parents=7

# Maximum number of public methods for a class
max-public-methods=20

# Maximum number of return statements in function / method body
max-returns=6

# Maximum number of statements in function / method body
max-statements=50

# Minimum number of public methods for a class
min-public-methods=2

[IMPORTS]

# Allow wildcard imports from modules that define __all__
allow-wildcard-with-all=no

# Analyse import fallback blocks
analyse-fallback-blocks=no

# Deprecated modules which should not be used
deprecated-modules=optparse,tkinter.tix

# Create a graph of external dependencies in the given file
ext-import-graph=

# Create a graph of every (i.e. internal and external) dependencies in the given file
import-graph=

# Create a graph of internal dependencies in the given file
int-import-graph=

# Force import order to follow PEP8 standards
known-standard-library=

# Force import order to follow PEP8 standards
known-third-party=

[EXCEPTIONS]

# Exceptions that will emit a warning when being caught
overgeneral-exceptions=Exception