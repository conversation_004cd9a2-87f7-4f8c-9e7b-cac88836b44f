"""
Graceful degradation strategies for financial report generation.

Provides intelligent fallback mechanisms when resource constraints or errors prevent
full-featured report generation, ensuring users always get meaningful output.
"""

import logging
from enum import Enum
from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Callable, Union
import json
import csv
import io
from pathlib import Path

from mcx3d_finance.exceptions import (
    ReportGenerationError,
    ReportOutputError,
    MCX3DResourceError,
    ReportMemoryError
)
from mcx3d_finance.utils.resource_monitor import get_resource_monitor, ResourceUsage

logger = logging.getLogger(__name__)


class ReportComplexity(Enum):
    """Report complexity levels for graceful degradation."""
    FULL = "full"  # Complete report with all features
    STANDARD = "standard"  # Standard report with essential features
    MINIMAL = "minimal"  # Basic report with core data only
    ESSENTIAL = "essential"  # Essential data only, no formatting
    EMERGENCY = "emergency"  # Raw data dump for critical situations


class OutputFormat(Enum):
    """Available output formats in priority order."""
    PDF = "pdf"
    EXCEL = "excel"
    HTML = "html"
    CSV = "csv"
    JSON = "json"
    TXT = "txt"


@dataclass
class DegradationStrategy:
    """Configuration for graceful degradation behavior."""
    
    # Resource thresholds for triggering degradation
    memory_threshold_mb: float = 256.0
    disk_threshold_mb: float = 500.0
    execution_time_threshold_seconds: float = 120.0
    
    # Complexity reduction preferences
    remove_charts_first: bool = True
    reduce_data_precision: bool = True
    simplify_formatting: bool = True
    enable_data_sampling: bool = True
    
    # Format fallback priority
    format_priority: List[str] = field(default_factory=lambda: ['pdf', 'excel', 'html', 'csv', 'json', 'txt'])
    
    # Complexity levels to attempt
    complexity_fallback: List[ReportComplexity] = field(
        default_factory=lambda: [
            ReportComplexity.FULL,
            ReportComplexity.STANDARD,
            ReportComplexity.MINIMAL,
            ReportComplexity.ESSENTIAL,
            ReportComplexity.EMERGENCY
        ]
    )
    
    # Emergency fallback settings
    enable_emergency_fallback: bool = True
    emergency_data_limit: int = 1000  # Maximum data points in emergency mode


class GracefulDegradationManager:
    """
    Manages graceful degradation strategies for report generation.
    
    Automatically adjusts report complexity and format based on available
    resources and error conditions to ensure users always receive output.
    """
    
    def __init__(self, strategy: Optional[DegradationStrategy] = None):
        """
        Initialize the graceful degradation manager.
        
        Args:
            strategy: Degradation strategy configuration
        """
        self.strategy = strategy or DegradationStrategy()
        self.resource_monitor = get_resource_monitor()
        self.degradation_log = []  # Track degradation decisions
    
    def execute_with_graceful_degradation(
        self,
        primary_generator: Callable,
        data: Dict[str, Any],
        output_path: str,
        requested_format: str = "pdf",
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute report generation with automatic graceful degradation.
        
        Args:
            primary_generator: Primary report generation function
            data: Report data
            output_path: Desired output path
            requested_format: Requested output format
            **kwargs: Additional arguments for generator
            
        Returns:
            Dictionary with generation results and degradation information
        """
        result = {
            'success': False,
            'output_path': None,
            'format': requested_format,
            'complexity': ReportComplexity.FULL,
            'degradation_applied': [],
            'warnings': [],
            'final_file_size_mb': 0,
            'generation_time_seconds': 0,
            'resource_usage': None
        }
        
        import time
        start_time = time.time()
        
        try:
            # Get initial resource usage
            initial_usage = self.resource_monitor.get_current_usage()
            result['resource_usage'] = initial_usage
            
            # Determine starting complexity level based on resources
            starting_complexity = self._assess_optimal_complexity(data, initial_usage)
            if starting_complexity != ReportComplexity.FULL:
                result['degradation_applied'].append(f"Started with {starting_complexity.value} complexity due to resource constraints")
            
            # Try each complexity level until success
            for complexity in self._get_complexity_sequence(starting_complexity):
                try:
                    logger.info(f"Attempting report generation with {complexity.value} complexity")
                    
                    # Adjust data based on complexity level
                    adjusted_data = self._adjust_data_for_complexity(data, complexity)
                    
                    # Try each format in priority order
                    formats_to_try = self._get_format_sequence(requested_format)
                    
                    for format_attempt in formats_to_try:
                        try:
                            # Adjust output path for format
                            format_output_path = self._adjust_path_for_format(output_path, format_attempt)
                            
                            # Monitor resources during generation
                            with self.resource_monitor.monitor_operation(
                                f"report_generation_{complexity.value}_{format_attempt}",
                                memory_limit_override=int(self.strategy.memory_threshold_mb * 2),
                                timeout_override=int(self.strategy.execution_time_threshold_seconds)
                            ):
                                # Execute the generator
                                generation_result = primary_generator(
                                    adjusted_data,
                                    format_output_path,
                                    format=format_attempt,
                                    complexity=complexity,
                                    **kwargs
                                )
                                
                                # Check if file was created successfully
                                if isinstance(generation_result, str):
                                    actual_output_path = generation_result
                                elif isinstance(generation_result, dict):
                                    actual_output_path = generation_result.get('output_path', format_output_path)
                                else:
                                    actual_output_path = format_output_path
                                
                                # Verify output file
                                if Path(actual_output_path).exists():
                                    file_size = Path(actual_output_path).stat().st_size / 1024 / 1024  # MB
                                    result.update({
                                        'success': True,
                                        'output_path': actual_output_path,
                                        'format': format_attempt,
                                        'complexity': complexity,
                                        'final_file_size_mb': file_size,
                                        'generation_time_seconds': time.time() - start_time
                                    })
                                    
                                    if format_attempt != requested_format:
                                        result['degradation_applied'].append(f"Format fallback: {requested_format} → {format_attempt}")
                                    
                                    logger.info(f"Report generated successfully: {actual_output_path} ({file_size:.2f}MB)")
                                    return result
                                
                        except (MCX3DResourceError, ReportMemoryError) as e:
                            logger.warning(f"Resource constraint with {format_attempt} format: {e}")
                            result['warnings'].append(f"Resource constraint with {format_attempt}: {str(e)}")
                            continue
                            
                        except Exception as e:
                            logger.warning(f"Error with {format_attempt} format: {e}")
                            result['warnings'].append(f"Error with {format_attempt}: {str(e)}")
                            continue
                    
                    # If all formats failed for this complexity, try next complexity
                    
                except Exception as e:
                    logger.warning(f"Error with {complexity.value} complexity: {e}")
                    result['warnings'].append(f"Error with {complexity.value} complexity: {str(e)}")
                    continue
            
            # If all attempts failed, try emergency fallback
            if self.strategy.enable_emergency_fallback:
                try:
                    logger.warning("All standard generation attempts failed, trying emergency fallback")
                    emergency_result = self._generate_emergency_fallback(data, output_path)
                    
                    result.update({
                        'success': True,
                        'output_path': emergency_result['path'],
                        'format': 'json',
                        'complexity': ReportComplexity.EMERGENCY,
                        'final_file_size_mb': emergency_result.get('size_mb', 0),
                        'generation_time_seconds': time.time() - start_time
                    })
                    result['degradation_applied'].append("Emergency fallback: raw data export")
                    result['warnings'].append("Used emergency fallback - review system resources")
                    
                    return result
                    
                except Exception as emergency_error:
                    logger.error(f"Even emergency fallback failed: {emergency_error}")
                    result['warnings'].append(f"Emergency fallback failed: {str(emergency_error)}")
            
            # Ultimate failure
            result.update({
                'success': False,
                'generation_time_seconds': time.time() - start_time
            })
            result['warnings'].append("All generation attempts failed - check system resources and data integrity")
            
            return result
            
        except Exception as e:
            logger.error(f"Graceful degradation manager failed: {e}")
            result.update({
                'success': False,
                'generation_time_seconds': time.time() - start_time
            })
            result['warnings'].append(f"Degradation manager error: {str(e)}")
            return result
    
    def _assess_optimal_complexity(self, data: Dict[str, Any], usage: ResourceUsage) -> ReportComplexity:
        """Assess optimal complexity level based on data size and resource usage."""
        try:
            # Estimate data complexity
            data_size = len(str(data))  # Rough estimate
            chart_count = self._estimate_chart_count(data)
            
            # Check resource constraints
            memory_constrained = usage.memory_mb > self.strategy.memory_threshold_mb
            disk_constrained = usage.disk_free_mb < self.strategy.disk_threshold_mb
            
            # Decide complexity level
            if memory_constrained or disk_constrained:
                if data_size > 100000 or chart_count > 10:  # Large dataset
                    return ReportComplexity.MINIMAL
                else:
                    return ReportComplexity.STANDARD
            elif data_size > 500000 or chart_count > 20:  # Very large dataset
                return ReportComplexity.STANDARD
            else:
                return ReportComplexity.FULL
                
        except Exception as e:
            logger.warning(f"Error assessing complexity: {e}")
            return ReportComplexity.STANDARD  # Safe default
    
    def _get_complexity_sequence(self, starting_complexity: ReportComplexity) -> List[ReportComplexity]:
        """Get sequence of complexity levels to try."""
        try:
            start_index = self.strategy.complexity_fallback.index(starting_complexity)
            return self.strategy.complexity_fallback[start_index:]
        except ValueError:
            return self.strategy.complexity_fallback
    
    def _get_format_sequence(self, requested_format: str) -> List[str]:
        """Get sequence of formats to try in priority order."""
        formats = []
        
        # Start with requested format if available
        if requested_format in self.strategy.format_priority:
            formats.append(requested_format)
        
        # Add other formats in priority order
        for fmt in self.strategy.format_priority:
            if fmt != requested_format:
                formats.append(fmt)
        
        return formats
    
    def _adjust_data_for_complexity(self, data: Dict[str, Any], complexity: ReportComplexity) -> Dict[str, Any]:
        """Adjust data based on complexity level."""
        if complexity == ReportComplexity.FULL:
            return data.copy()
        
        adjusted_data = data.copy()
        
        try:
            if complexity == ReportComplexity.STANDARD:
                # Remove some non-essential data
                adjusted_data = self._remove_non_essential_data(adjusted_data)
                
            elif complexity == ReportComplexity.MINIMAL:
                # Keep only core financial data
                adjusted_data = self._keep_core_data_only(adjusted_data)
                
            elif complexity == ReportComplexity.ESSENTIAL:
                # Keep only the most essential numbers
                adjusted_data = self._keep_essential_numbers(adjusted_data)
                
            elif complexity == ReportComplexity.EMERGENCY:
                # Minimal data for emergency export
                adjusted_data = self._prepare_emergency_data(adjusted_data)
            
            return adjusted_data
            
        except Exception as e:
            logger.warning(f"Error adjusting data for complexity {complexity.value}: {e}")
            return data  # Return original if adjustment fails
    
    def _remove_non_essential_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Remove non-essential data for standard complexity."""
        # Remove detailed breakdowns, keep summaries
        keys_to_remove = [
            'detailed_breakdown', 'subsidiary_data', 'monthly_detail',
            'comparative_analysis_detail', 'sensitivity_scenarios'
        ]
        
        for key in keys_to_remove:
            data.pop(key, None)
        
        # Simplify chart data
        if 'chart_data' in data:
            for chart_key in list(data['chart_data'].keys()):
                if 'detailed' in chart_key or 'breakdown' in chart_key:
                    data['chart_data'].pop(chart_key, None)
        
        return data
    
    def _keep_core_data_only(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Keep only core data for minimal complexity."""
        core_keys = [
            'header', 'total_revenue', 'total_expenses', 'net_income',
            'total_assets', 'total_liabilities', 'total_equity',
            'operating_cash_flow', 'valuation_summary'
        ]
        
        core_data = {}
        for key in core_keys:
            if key in data:
                core_data[key] = data[key]
        
        # Keep only summary charts
        if 'chart_data' in data:
            summary_charts = {k: v for k, v in data['chart_data'].items() 
                            if 'summary' in k.lower() or 'overview' in k.lower()}
            if summary_charts:
                core_data['chart_data'] = summary_charts
        
        return core_data
    
    def _keep_essential_numbers(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Keep only essential numbers for essential complexity."""
        essential_data = {}
        
        # Extract key financial metrics
        if 'header' in data:
            essential_data['header'] = data['header']
        
        # Core financial numbers
        financial_metrics = [
            'total_revenue', 'net_income', 'total_assets', 'total_equity',
            'enterprise_value', 'valuation'
        ]
        
        for metric in financial_metrics:
            if metric in data:
                essential_data[metric] = data[metric]
        
        return essential_data
    
    def _prepare_emergency_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare minimal data for emergency export."""
        emergency_data = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'emergency_export',
            'data_summary': {}
        }
        
        # Extract only the most critical values
        try:
            if 'header' in data and isinstance(data['header'], dict):
                emergency_data['company'] = data['header'].get('company_name', 'Unknown')
                emergency_data['date'] = data['header'].get('reporting_date', 'Unknown')
            
            # Flatten all numerical data
            flat_data = self._flatten_dict(data, max_items=self.strategy.emergency_data_limit)
            numerical_data = {k: v for k, v in flat_data.items() 
                            if isinstance(v, (int, float)) and k != 'timestamp'}
            
            emergency_data['data_summary'] = dict(list(numerical_data.items())[:100])  # Limit to 100 items
            
        except Exception as e:
            logger.error(f"Error preparing emergency data: {e}")
            emergency_data['error'] = str(e)
        
        return emergency_data
    
    def _generate_emergency_fallback(self, data: Dict[str, Any], output_path: str) -> Dict[str, str]:
        """Generate emergency fallback output as JSON."""
        try:
            emergency_data = self._prepare_emergency_data(data)
            
            # Change extension to .json
            emergency_path = str(Path(output_path).with_suffix('.emergency.json'))
            
            # Write JSON data
            with open(emergency_path, 'w', encoding='utf-8') as f:
                json.dump(emergency_data, f, indent=2, ensure_ascii=False)
            
            file_size_mb = Path(emergency_path).stat().st_size / 1024 / 1024
            
            logger.warning(f"Emergency fallback generated: {emergency_path}")
            
            return {
                'path': emergency_path,
                'size_mb': file_size_mb
            }
            
        except Exception as e:
            logger.error(f"Emergency fallback generation failed: {e}")
            raise ReportOutputError(
                f"Emergency fallback failed: {e}",
                output_path=output_path,
                original_error=str(e)
            )
    
    def _adjust_path_for_format(self, original_path: str, format_name: str) -> str:
        """Adjust output path for different format."""
        path = Path(original_path)
        format_extensions = {
            'pdf': '.pdf',
            'excel': '.xlsx',
            'html': '.html',
            'csv': '.csv',
            'json': '.json',
            'txt': '.txt'
        }
        
        extension = format_extensions.get(format_name, path.suffix)
        return str(path.with_suffix(extension))
    
    def _estimate_chart_count(self, data: Dict[str, Any]) -> int:
        """Estimate number of charts that would be generated."""
        chart_count = 0
        
        # Count explicit chart data
        if 'chart_data' in data and isinstance(data['chart_data'], dict):
            chart_count += len(data['chart_data'])
        
        # Estimate charts from data structure
        if 'financial_analysis' in data:
            chart_count += 2  # Usually generates 2-3 charts
        
        if 'projections' in data and isinstance(data['projections'], list):
            chart_count += len(data['projections']) // 3  # Rough estimate
        
        if 'saas_metrics' in data:
            chart_count += 3  # Typical SaaS dashboard
        
        return chart_count
    
    def _flatten_dict(self, data: Dict[str, Any], prefix: str = '', max_items: int = 1000) -> Dict[str, Any]:
        """Flatten nested dictionary structure."""
        flattened = {}
        item_count = 0
        
        for key, value in data.items():
            if item_count >= max_items:
                break
                
            new_key = f"{prefix}.{key}" if prefix else key
            
            if isinstance(value, dict):
                flattened.update(self._flatten_dict(value, new_key, max_items - item_count))
            elif isinstance(value, list) and len(value) <= 20:  # Limit list processing
                for i, item in enumerate(value):
                    if isinstance(item, dict):
                        flattened.update(self._flatten_dict(item, f"{new_key}[{i}]", max_items - item_count))
                    else:
                        flattened[f"{new_key}[{i}]"] = item
                    item_count += 1
                    if item_count >= max_items:
                        break
            else:
                flattened[new_key] = value
                item_count += 1
        
        return flattened


# Global instance for convenience
_global_degradation_manager: Optional[GracefulDegradationManager] = None


def get_degradation_manager(strategy: Optional[DegradationStrategy] = None) -> GracefulDegradationManager:
    """
    Get or create global graceful degradation manager.
    
    Args:
        strategy: Degradation strategy (only used on first call)
        
    Returns:
        GracefulDegradationManager instance
    """
    global _global_degradation_manager
    
    if _global_degradation_manager is None:
        _global_degradation_manager = GracefulDegradationManager(strategy)
    
    return _global_degradation_manager


import time