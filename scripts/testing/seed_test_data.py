#!/usr/bin/env python3
"""
Test data seeding and management script for MCX3D Financial System.

This script provides comprehensive test data generation and database seeding
capabilities for consistent testing across all environments.
"""

import sys
import os
import json
import argparse
import uuid
from pathlib import Path
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
from decimal import Decimal
import random

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mcx3d_finance.db.models import (
    Organization, 
    FinancialAccount, 
    Transaction, 
    ValuationProject,
    ReportGeneration
)
from mcx3d_finance.db.session import get_session
from mcx3d_finance.core.financials.account_mapper import GAAPAccountClassification


class TestDataSeeder:
    """Comprehensive test data generation and database seeding."""
    
    def __init__(self, session=None):
        self.session = session or get_session()
        self.created_records = {
            'organizations': [],
            'accounts': [],
            'transactions': [],
            'valuation_projects': [],
            'report_generations': []
        }
    
    def generate_organization_data(self, 
                                 count: int = 3,
                                 industry_types: List[str] = None) -> List[Dict[str, Any]]:
        """Generate realistic organization test data."""
        if industry_types is None:
            industry_types = ["Technology", "Healthcare", "Finance", "Manufacturing", "Retail"]
        
        organizations = []
        company_prefixes = ["Alpha", "Beta", "Gamma", "Delta", "Omega", "Prime", "Core", "Elite"]
        company_suffixes = ["Systems", "Solutions", "Technologies", "Corp", "Inc", "Group", "Holdings"]
        
        for i in range(count):
            prefix = random.choice(company_prefixes)
            suffix = random.choice(company_suffixes)
            industry = random.choice(industry_types)
            
            org_data = {
                "name": f"{prefix} {suffix}",
                "industry": industry,
                "xero_tenant_id": str(uuid.uuid4()),
                "currency": random.choice(["USD", "EUR", "GBP", "CAD"]),
                "fiscal_year_end": random.choice(["12-31", "06-30", "09-30", "03-31"]),
                "employee_count": random.randint(10, 5000),
                "founded_year": random.randint(1990, 2020),
                "revenue_range": random.choice(["<1M", "1M-5M", "5M-25M", "25M-100M", ">100M"]),
                "business_model": random.choice(["B2B SaaS", "B2C", "Marketplace", "Enterprise", "SMB"]),
                "is_public": random.choice([True, False]),
                "created_at": datetime.now() - timedelta(days=random.randint(30, 365))
            }
            organizations.append(org_data)
        
        return organizations
    
    def generate_account_data(self, 
                            organization_id: int,
                            account_types: List[str] = None) -> List[Dict[str, Any]]:
        """Generate comprehensive chart of accounts for testing."""
        if account_types is None:
            account_types = ["ASSET", "LIABILITY", "EQUITY", "REVENUE", "EXPENSE"]
        
        accounts = []
        
        # Standard chart of accounts structure
        account_templates = {
            "ASSET": [
                {"code": "1000", "name": "Cash and Cash Equivalents", "classification": "CURRENT_ASSETS"},
                {"code": "1100", "name": "Accounts Receivable", "classification": "CURRENT_ASSETS"},
                {"code": "1200", "name": "Inventory", "classification": "CURRENT_ASSETS"},
                {"code": "1500", "name": "Property, Plant & Equipment", "classification": "NON_CURRENT_ASSETS"},
                {"code": "1600", "name": "Intangible Assets", "classification": "NON_CURRENT_ASSETS"},
                {"code": "1700", "name": "Goodwill", "classification": "NON_CURRENT_ASSETS"}
            ],
            "LIABILITY": [
                {"code": "2000", "name": "Accounts Payable", "classification": "CURRENT_LIABILITIES"},
                {"code": "2100", "name": "Accrued Expenses", "classification": "CURRENT_LIABILITIES"},
                {"code": "2200", "name": "Short-term Debt", "classification": "CURRENT_LIABILITIES"},
                {"code": "2500", "name": "Long-term Debt", "classification": "NON_CURRENT_LIABILITIES"},
                {"code": "2600", "name": "Deferred Revenue", "classification": "NON_CURRENT_LIABILITIES"}
            ],
            "EQUITY": [
                {"code": "3000", "name": "Common Stock", "classification": "EQUITY"},
                {"code": "3100", "name": "Retained Earnings", "classification": "EQUITY"},
                {"code": "3200", "name": "Additional Paid-in Capital", "classification": "EQUITY"}
            ],
            "REVENUE": [
                {"code": "4000", "name": "Product Revenue", "classification": "OPERATING_REVENUE"},
                {"code": "4100", "name": "Service Revenue", "classification": "OPERATING_REVENUE"},
                {"code": "4200", "name": "Subscription Revenue", "classification": "OPERATING_REVENUE"},
                {"code": "4900", "name": "Other Revenue", "classification": "NON_OPERATING_REVENUE"}
            ],
            "EXPENSE": [
                {"code": "5000", "name": "Cost of Goods Sold", "classification": "COST_OF_REVENUE"},
                {"code": "6000", "name": "Sales and Marketing", "classification": "OPERATING_EXPENSES"},
                {"code": "6100", "name": "Research and Development", "classification": "OPERATING_EXPENSES"},
                {"code": "6200", "name": "General and Administrative", "classification": "OPERATING_EXPENSES"},
                {"code": "7000", "name": "Interest Expense", "classification": "NON_OPERATING_EXPENSES"},
                {"code": "8000", "name": "Income Tax Expense", "classification": "TAX_EXPENSES"}
            ]
        }
        
        for account_type in account_types:
            if account_type in account_templates:
                for template in account_templates[account_type]:
                    account_data = {
                        "organization_id": organization_id,
                        "xero_account_id": str(uuid.uuid4()),
                        "account_code": template["code"],
                        "account_name": template["name"],
                        "account_type": account_type,
                        "gaap_classification": template["classification"],
                        "is_active": True,
                        "current_balance": Decimal(str(random.uniform(-1000000, 1000000))),
                        "currency": "USD",
                        "created_at": datetime.now() - timedelta(days=random.randint(1, 180))
                    }
                    accounts.append(account_data)
        
        return accounts
    
    def generate_transaction_data(self,
                                organization_id: int,
                                account_ids: List[int],
                                transaction_count: int = 100,
                                date_range_days: int = 365) -> List[Dict[str, Any]]:
        """Generate realistic transaction data for testing."""
        transactions = []
        
        transaction_types = ["INVOICE", "PAYMENT", "EXPENSE", "TRANSFER", "ADJUSTMENT"]
        
        start_date = datetime.now() - timedelta(days=date_range_days)
        
        for i in range(transaction_count):
            transaction_date = start_date + timedelta(days=random.randint(0, date_range_days))
            
            transaction_data = {
                "organization_id": organization_id,
                "xero_transaction_id": str(uuid.uuid4()),
                "transaction_type": random.choice(transaction_types),
                "transaction_date": transaction_date,
                "description": f"Test transaction {i+1}",
                "reference": f"TXN-{i+1:06d}",
                "contact_name": f"Test Contact {random.randint(1, 50)}",
                "account_id": random.choice(account_ids),
                "amount": Decimal(str(random.uniform(-50000, 50000))),
                "currency": "USD",
                "is_reconciled": random.choice([True, False]),
                "created_at": transaction_date,
                "updated_at": transaction_date + timedelta(hours=random.randint(1, 48))
            }
            transactions.append(transaction_data)
        
        return transactions
    
    def generate_valuation_project_data(self,
                                      organization_id: int,
                                      project_count: int = 5) -> List[Dict[str, Any]]:
        """Generate valuation project test data."""
        projects = []
        
        valuation_types = ["DCF", "MULTIPLES", "SAAS", "ASSET_BASED"]
        project_statuses = ["DRAFT", "IN_PROGRESS", "COMPLETED", "ARCHIVED"]
        
        for i in range(project_count):
            project_data = {
                "organization_id": organization_id,
                "project_name": f"Valuation Project {i+1}",
                "valuation_type": random.choice(valuation_types),
                "valuation_date": datetime.now() - timedelta(days=random.randint(1, 90)),
                "status": random.choice(project_statuses),
                "assumptions": json.dumps({
                    "discount_rate": round(random.uniform(0.08, 0.15), 3),
                    "terminal_growth": round(random.uniform(0.02, 0.04), 3),
                    "tax_rate": round(random.uniform(0.15, 0.30), 3)
                }),
                "enterprise_value": Decimal(str(random.uniform(1000000, *********))),
                "equity_value": Decimal(str(random.uniform(800000, 80000000))),
                "created_by": f"test_user_{random.randint(1, 10)}",
                "created_at": datetime.now() - timedelta(days=random.randint(5, 180)),
                "updated_at": datetime.now() - timedelta(days=random.randint(1, 30))
            }
            projects.append(project_data)
        
        return projects
    
    def generate_report_generation_data(self,
                                      organization_id: int,
                                      valuation_project_ids: List[int],
                                      report_count: int = 10) -> List[Dict[str, Any]]:
        """Generate report generation history for testing."""
        reports = []
        
        report_types = ["BALANCE_SHEET", "INCOME_STATEMENT", "CASH_FLOW", "DCF_VALUATION", "SAAS_VALUATION"]
        output_formats = ["PDF", "EXCEL", "HTML"]
        report_statuses = ["COMPLETED", "FAILED", "IN_PROGRESS"]
        
        for i in range(report_count):
            report_data = {
                "organization_id": organization_id,
                "valuation_project_id": random.choice(valuation_project_ids) if valuation_project_ids else None,
                "report_type": random.choice(report_types),
                "output_format": random.choice(output_formats),
                "status": random.choice(report_statuses),
                "file_path": f"/tmp/test_reports/report_{i+1}.pdf",
                "file_size": random.randint(50000, 2000000),
                "generation_time": round(random.uniform(0.5, 30.0), 2),
                "parameters": json.dumps({
                    "period": "2024-Q1",
                    "include_charts": random.choice([True, False]),
                    "currency": "USD"
                }),
                "error_message": None if random.choice(report_statuses) != "FAILED" else "Test error message",
                "created_by": f"test_user_{random.randint(1, 10)}",
                "created_at": datetime.now() - timedelta(days=random.randint(1, 30))
            }
            reports.append(report_data)
        
        return reports
    
    def seed_organizations(self, count: int = 3) -> List[int]:
        """Seed organization data and return IDs."""
        print(f"🏢 Seeding {count} organizations...")
        
        org_data_list = self.generate_organization_data(count)
        org_ids = []
        
        for org_data in org_data_list:
            # Create organization record (this would depend on your actual model structure)
            org_id = len(self.created_records['organizations']) + 1  # Simplified ID generation
            
            # Store the organization data (in real implementation, save to database)
            self.created_records['organizations'].append({
                'id': org_id,
                'data': org_data
            })
            
            org_ids.append(org_id)
            print(f"   Created organization: {org_data['name']} (ID: {org_id})")
        
        return org_ids
    
    def seed_accounts(self, organization_ids: List[int]) -> List[int]:
        """Seed chart of accounts for all organizations."""
        print(f"📊 Seeding chart of accounts for {len(organization_ids)} organizations...")
        
        all_account_ids = []
        
        for org_id in organization_ids:
            account_data_list = self.generate_account_data(org_id)
            
            for account_data in account_data_list:
                account_id = len(self.created_records['accounts']) + 1
                
                self.created_records['accounts'].append({
                    'id': account_id,
                    'organization_id': org_id,
                    'data': account_data
                })
                
                all_account_ids.append(account_id)
            
            print(f"   Created {len(account_data_list)} accounts for organization {org_id}")
        
        return all_account_ids
    
    def seed_transactions(self, organization_ids: List[int], account_ids: List[int]) -> List[int]:
        """Seed transaction data for testing."""
        print(f"💰 Seeding transactions for {len(organization_ids)} organizations...")
        
        all_transaction_ids = []
        
        for org_id in organization_ids:
            # Get account IDs for this organization
            org_account_ids = [
                acc['id'] for acc in self.created_records['accounts'] 
                if acc['organization_id'] == org_id
            ]
            
            if not org_account_ids:
                continue
            
            transaction_data_list = self.generate_transaction_data(
                org_id, org_account_ids, transaction_count=random.randint(50, 150)
            )
            
            for transaction_data in transaction_data_list:
                transaction_id = len(self.created_records['transactions']) + 1
                
                self.created_records['transactions'].append({
                    'id': transaction_id,
                    'organization_id': org_id,
                    'data': transaction_data
                })
                
                all_transaction_ids.append(transaction_id)
            
            print(f"   Created {len(transaction_data_list)} transactions for organization {org_id}")
        
        return all_transaction_ids
    
    def seed_valuation_projects(self, organization_ids: List[int]) -> List[int]:
        """Seed valuation project data for testing."""
        print(f"📈 Seeding valuation projects for {len(organization_ids)} organizations...")
        
        all_project_ids = []
        
        for org_id in organization_ids:
            project_data_list = self.generate_valuation_project_data(
                org_id, project_count=random.randint(2, 7)
            )
            
            for project_data in project_data_list:
                project_id = len(self.created_records['valuation_projects']) + 1
                
                self.created_records['valuation_projects'].append({
                    'id': project_id,
                    'organization_id': org_id,
                    'data': project_data
                })
                
                all_project_ids.append(project_id)
            
            print(f"   Created {len(project_data_list)} valuation projects for organization {org_id}")
        
        return all_project_ids
    
    def seed_report_generations(self, organization_ids: List[int], project_ids: List[int]) -> List[int]:
        """Seed report generation history for testing."""
        print(f"📄 Seeding report generation history...")
        
        all_report_ids = []
        
        for org_id in organization_ids:
            # Get project IDs for this organization
            org_project_ids = [
                proj['id'] for proj in self.created_records['valuation_projects'] 
                if proj['organization_id'] == org_id
            ]
            
            report_data_list = self.generate_report_generation_data(
                org_id, org_project_ids, report_count=random.randint(5, 15)
            )
            
            for report_data in report_data_list:
                report_id = len(self.created_records['report_generations']) + 1
                
                self.created_records['report_generations'].append({
                    'id': report_id,
                    'organization_id': org_id,
                    'data': report_data
                })
                
                all_report_ids.append(report_id)
            
            print(f"   Created {len(report_data_list)} report generation records for organization {org_id}")
        
        return all_report_ids
    
    def save_seed_data(self, output_file: str = "test_seed_data.json"):
        """Save generated seed data to file for reuse."""
        output_path = Path(output_file)
        
        seed_summary = {
            "generation_timestamp": datetime.now().isoformat(),
            "summary": {
                "organizations": len(self.created_records['organizations']),
                "accounts": len(self.created_records['accounts']),
                "transactions": len(self.created_records['transactions']),
                "valuation_projects": len(self.created_records['valuation_projects']),
                "report_generations": len(self.created_records['report_generations'])
            },
            "data": self.created_records
        }
        
        with open(output_path, 'w') as f:
            json.dump(seed_summary, f, indent=2, default=str)
        
        print(f"💾 Seed data saved to: {output_path.absolute()}")
        return str(output_path.absolute())
    
    def create_test_scenarios(self):
        """Create specific test scenarios for comprehensive testing."""
        scenarios = {
            "minimal_company": {
                "description": "Small company with basic financial structure",
                "organization_count": 1,
                "transaction_count": 25,
                "complexity": "simple"
            },
            "growth_saas": {
                "description": "Growing SaaS company with subscription revenue",
                "organization_count": 1,
                "transaction_count": 200,
                "complexity": "medium",
                "industry": "Technology",
                "revenue_pattern": "subscription"
            },
            "enterprise_multi": {
                "description": "Multiple enterprises with complex structures",
                "organization_count": 5,
                "transaction_count": 500,
                "complexity": "complex",
                "industries": ["Technology", "Healthcare", "Finance"]
            },
            "valuation_heavy": {
                "description": "Companies with extensive valuation history",
                "organization_count": 2,
                "valuation_projects_per_org": 10,
                "reports_per_project": 5,
                "complexity": "complex"
            }
        }
        
        return scenarios
    
    def run_full_seed(self, organization_count: int = 3) -> Dict[str, Any]:
        """Run complete data seeding process."""
        print("🚀 Starting comprehensive test data seeding...")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # Seed organizations
        org_ids = self.seed_organizations(organization_count)
        
        # Seed chart of accounts
        account_ids = self.seed_accounts(org_ids)
        
        # Seed transactions
        transaction_ids = self.seed_transactions(org_ids, account_ids)
        
        # Seed valuation projects
        project_ids = self.seed_valuation_projects(org_ids)
        
        # Seed report generations
        report_ids = self.seed_report_generations(org_ids, project_ids)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Generate summary
        summary = {
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "duration_seconds": duration,
            "records_created": {
                "organizations": len(org_ids),
                "accounts": len(account_ids),
                "transactions": len(transaction_ids),
                "valuation_projects": len(project_ids),
                "report_generations": len(report_ids)
            },
            "organization_ids": org_ids,
            "scenarios": self.create_test_scenarios()
        }
        
        print(f"\n✅ Seeding completed in {duration:.2f} seconds")
        print(f"📊 Summary:")
        for record_type, count in summary["records_created"].items():
            print(f"   {record_type}: {count}")
        
        return summary


def main():
    """Main entry point for test data seeding."""
    parser = argparse.ArgumentParser(description="MCX3D Test Data Seeding Script")
    parser.add_argument("--organizations", "-o", type=int, default=3,
                       help="Number of organizations to create (default: 3)")
    parser.add_argument("--output", "-f", type=str, default="test_seed_data.json",
                       help="Output file for seed data (default: test_seed_data.json)")
    parser.add_argument("--scenario", "-s", type=str, 
                       choices=["minimal", "growth", "enterprise", "valuation"],
                       help="Predefined seeding scenario")
    parser.add_argument("--dry-run", action="store_true",
                       help="Generate data without saving to database")
    
    args = parser.parse_args()
    
    try:
        seeder = TestDataSeeder()
        
        if args.scenario:
            scenarios = seeder.create_test_scenarios()
            if args.scenario == "minimal":
                org_count = 1
            elif args.scenario == "growth":
                org_count = 1
            elif args.scenario == "enterprise":
                org_count = 5
            elif args.scenario == "valuation":
                org_count = 2
            else:
                org_count = args.organizations
        else:
            org_count = args.organizations
        
        # Run the seeding process
        summary = seeder.run_full_seed(org_count)
        
        # Save seed data to file
        output_file = seeder.save_seed_data(args.output)
        
        # Print final summary
        print(f"\n🎯 Test Data Seeding Summary:")
        print(f"   Organizations: {summary['records_created']['organizations']}")
        print(f"   Accounts: {summary['records_created']['accounts']}")
        print(f"   Transactions: {summary['records_created']['transactions']}")
        print(f"   Valuation Projects: {summary['records_created']['valuation_projects']}")
        print(f"   Report Generations: {summary['records_created']['report_generations']}")
        print(f"   Duration: {summary['duration_seconds']:.2f} seconds")
        print(f"   Output File: {output_file}")
        
        if args.dry_run:
            print("\n⚠️  DRY RUN: No data was saved to database")
        
    except KeyboardInterrupt:
        print("\n⚠️  Seeding interrupted by user")
    except Exception as e:
        print(f"❌ Error during seeding: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()