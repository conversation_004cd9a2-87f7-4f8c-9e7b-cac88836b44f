"""
Real-time Alerting System for MCX3D Financial Platform

Provides multi-channel alerting with severity-based escalation, business anomaly detection,
and automated incident response for production monitoring.
"""

import os
import json
import asyncio
import aiohttp
import smtplib
from enum import Enum
from typing import Dict, Any, List, Callable, Optional
from datetime import datetime, timedelta, timezone
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import logging

from .structured_logger import main_logger

logger = logging.getLogger(__name__)

class AlertSeverity(Enum):
    """Alert severity levels with escalation rules."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class AlertManager:
    """
    Production-ready alerting system with multi-channel delivery and escalation.
    
    Features:
    - Multi-channel delivery (Slack, email, PagerDuty, webhook)
    - Severity-based escalation rules
    - Alert deduplication and rate limiting
    - Business anomaly detection
    - Automated incident response
    """
    
    def __init__(self):
        # Load configurable alert settings
        try:
            from .config_loader import get_monitoring_config
            self.config_loader = get_monitoring_config()
            self.alert_config = self.config_loader.get_alert_thresholds()
            rate_limit_config = self.config_loader.get_rate_limit_config()
            
            # Configure rate limiting from config
            self.rate_limit_window = timedelta(minutes=rate_limit_config.get('rate_limit_window_minutes', 5))
            self.max_alerts_per_window = rate_limit_config.get('max_alerts_per_window', 10)
            self.deduplication_window = timedelta(minutes=rate_limit_config.get('deduplication_minutes', 1))
            
            logger.info(f"Loaded alert configuration for environment: {self.config_loader.environment}")
        except Exception as e:
            logger.warning(f"Failed to load alert configuration, using defaults: {e}")
            self.alert_config = {
                'email_alerts': ['WARNING', 'ERROR', 'CRITICAL'],
                'slack_alerts': ['ERROR', 'CRITICAL'],
                'pagerduty_alerts': ['CRITICAL']
            }
            self.rate_limit_window = timedelta(minutes=5)
            self.max_alerts_per_window = 10
            self.deduplication_window = timedelta(minutes=1)
        
        # Dynamic alert handlers based on configuration
        self.alert_handlers = {
            AlertSeverity.INFO: [self._log_alert],
            AlertSeverity.WARNING: [self._log_alert],
            AlertSeverity.ERROR: [self._log_alert],
            AlertSeverity.CRITICAL: [self._log_alert]
        }
        
        # Add handlers based on configuration
        for severity in [AlertSeverity.WARNING, AlertSeverity.ERROR, AlertSeverity.CRITICAL]:
            if self._should_send_alert_type('email_alerts', severity.value.upper()):
                self.alert_handlers[severity].append(self._send_email)
            
            if self._should_send_alert_type('slack_alerts', severity.value.upper()):
                self.alert_handlers[severity].append(self._send_slack)
            
            if self._should_send_alert_type('pagerduty_alerts', severity.value.upper()):
                self.alert_handlers[severity].extend([self._send_pagerduty, self._create_incident])
        
        # Alert deduplication tracking
        self.recent_alerts = {}
        self.alert_counts = {}
        
        # Configuration
        self.slack_webhook_url = os.getenv('SLACK_WEBHOOK_URL')
        self.pagerduty_integration_key = os.getenv('PAGERDUTY_INTEGRATION_KEY')
        self.email_config = {
            'smtp_server': os.getenv('SMTP_SERVER', 'localhost'),
            'smtp_port': int(os.getenv('SMTP_PORT', '587')),
            'username': os.getenv('SMTP_USERNAME'),
            'password': os.getenv('SMTP_PASSWORD'),
            'from_email': os.getenv('ALERT_FROM_EMAIL', '<EMAIL>'),
            'to_emails': os.getenv('ALERT_TO_EMAILS', '').split(',')
        }
    
    def _should_send_alert_type(self, alert_type: str, severity: str) -> bool:
        """Check if specific alert type should be sent for given severity."""
        enabled_severities = self.alert_config.get(alert_type, [])
        return severity in enabled_severities
    
    async def trigger_alert(self, severity: AlertSeverity, title: str, message: str, 
                          context: Dict[str, Any], alert_key: Optional[str] = None):
        """
        Trigger alert with appropriate escalation and deduplication.
        
        Args:
            severity: Alert severity level
            title: Alert title/summary
            message: Detailed alert message
            context: Additional context data
            alert_key: Optional key for deduplication
        """
        alert_data = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'severity': severity.value,
            'title': title,
            'message': message,
            'context': context,
            'service': 'mcx3d-finance',
            'alert_key': alert_key or f"{title}_{severity.value}"
        }
        
        # Check rate limiting and deduplication
        if self._should_suppress_alert(alert_data):
            logger.info(f"Alert suppressed due to rate limiting: {title}")
            return
        
        # Record alert in metrics
        from .metrics import ALERT_COUNT
        ALERT_COUNT.labels(
            alert_type=alert_key or 'generic',
            severity=severity.value,
            component=context.get('component', 'unknown')
        ).inc()
        
        # Log the alert
        main_logger.log_business_event(
            'alert_triggered',
            alert_title=title,
            alert_severity=severity.value,
            alert_key=alert_key,
            **context
        )
        
        # Execute alert handlers
        handlers = self.alert_handlers.get(severity, [])
        try:
            await asyncio.gather(*[handler(alert_data) for handler in handlers])
        except Exception as e:
            logger.error(f"Failed to execute alert handlers: {e}")
    
    def _should_suppress_alert(self, alert_data: Dict[str, Any]) -> bool:
        """
        Check if alert should be suppressed due to rate limiting.
        
        Args:
            alert_data: Alert data
            
        Returns:
            True if alert should be suppressed
        """
        alert_key = alert_data['alert_key']
        now = datetime.now(timezone.utc)
        
        # Clean old entries
        self._cleanup_old_alerts(now)
        
        # Check if we've seen this alert recently
        if alert_key in self.recent_alerts:
            last_seen = self.recent_alerts[alert_key]
            if now - last_seen < self.deduplication_window:
                return True
        
        # Check rate limiting
        count = self.alert_counts.get(alert_key, 0)
        if count >= self.max_alerts_per_window:
            return True
        
        # Update tracking
        self.recent_alerts[alert_key] = now
        self.alert_counts[alert_key] = count + 1
        
        return False
    
    def _cleanup_old_alerts(self, now: datetime):
        """Clean up old alert tracking data."""
        cutoff = now - self.rate_limit_window
        
        # Clean recent alerts
        to_remove = [key for key, timestamp in self.recent_alerts.items() 
                    if timestamp < cutoff]
        for key in to_remove:
            del self.recent_alerts[key]
            if key in self.alert_counts:
                del self.alert_counts[key]
    
    async def _log_alert(self, alert_data: Dict[str, Any]):
        """Log alert to structured logging system."""
        logger.warning(
            f"ALERT: {alert_data['title']}",
            extra={
                'alert_data': alert_data,
                'timestamp': alert_data['timestamp'],
                'severity': alert_data['severity']
            }
        )
    
    async def _send_email(self, alert_data: Dict[str, Any]):
        """Send alert via email."""
        if not self.email_config['username'] or not self.email_config['to_emails'][0]:
            logger.warning("Email configuration incomplete, skipping email alert")
            return
        
        try:
            msg = MIMEMultipart()
            msg['From'] = self.email_config['from_email']
            msg['To'] = ', '.join(self.email_config['to_emails'])
            msg['Subject'] = f"MCX3D Alert [{alert_data['severity'].upper()}]: {alert_data['title']}"
            
            # Create email body
            body = self._format_email_body(alert_data)
            msg.attach(MIMEText(body, 'html'))
            
            # Send email
            with smtplib.SMTP(self.email_config['smtp_server'], self.email_config['smtp_port']) as server:
                server.starttls()
                server.login(self.email_config['username'], self.email_config['password'])
                server.send_message(msg)
            
            logger.info(f"Email alert sent for: {alert_data['title']}")
            
        except Exception as e:
            logger.error(f"Failed to send email alert: {e}")
    
    async def _send_slack(self, alert_data: Dict[str, Any]):
        """Send alert to Slack channel."""
        if not self.slack_webhook_url:
            logger.warning("Slack webhook URL not configured, skipping Slack alert")
            return
        
        try:
            color = self._get_slack_color(alert_data['severity'])
            
            payload = {
                'text': f"🚨 MCX3D Financial Alert: {alert_data['title']}",
                'attachments': [{
                    'color': color,
                    'fields': [
                        {'title': 'Severity', 'value': alert_data['severity'].upper(), 'short': True},
                        {'title': 'Service', 'value': alert_data['service'], 'short': True},
                        {'title': 'Time', 'value': alert_data['timestamp'], 'short': True},
                        {'title': 'Component', 'value': alert_data['context'].get('component', 'Unknown'), 'short': True},
                        {'title': 'Message', 'value': alert_data['message'], 'short': False}
                    ],
                    'footer': 'MCX3D Monitoring',
                    'ts': datetime.now(timezone.utc).timestamp()
                }]
            }
            
            # Add context information
            if alert_data['context']:
                context_fields = []
                for key, value in alert_data['context'].items():
                    if key not in ['component']:  # Skip already displayed fields
                        context_fields.append({'title': key.replace('_', ' ').title(), 'value': str(value), 'short': True})
                
                if context_fields:
                    payload['attachments'][0]['fields'].extend(context_fields)
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.slack_webhook_url, json=payload) as response:
                    if response.status == 200:
                        logger.info(f"Slack alert sent for: {alert_data['title']}")
                    else:
                        logger.error(f"Failed to send Slack alert: {response.status}")
                        
        except Exception as e:
            logger.error(f"Failed to send Slack alert: {e}")
    
    async def _send_pagerduty(self, alert_data: Dict[str, Any]):
        """Send alert to PagerDuty."""
        if not self.pagerduty_integration_key:
            logger.warning("PagerDuty integration key not configured, skipping PagerDuty alert")
            return
        
        try:
            payload = {
                'routing_key': self.pagerduty_integration_key,
                'event_action': 'trigger',
                'dedup_key': alert_data['alert_key'],
                'payload': {
                    'summary': alert_data['title'],
                    'severity': alert_data['severity'],
                    'source': 'mcx3d-finance',
                    'component': alert_data['context'].get('component', 'unknown'),
                    'group': 'financial-system',
                    'class': 'application',
                    'custom_details': alert_data['context']
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    'https://events.pagerduty.com/v2/enqueue',
                    json=payload,
                    headers={'Content-Type': 'application/json'}
                ) as response:
                    if response.status == 202:
                        logger.info(f"PagerDuty alert sent for: {alert_data['title']}")
                    else:
                        logger.error(f"Failed to send PagerDuty alert: {response.status}")
                        
        except Exception as e:
            logger.error(f"Failed to send PagerDuty alert: {e}")
    
    async def _create_incident(self, alert_data: Dict[str, Any]):
        """Create incident record for critical alerts."""
        try:
            incident_data = {
                'incident_id': f"INC-{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}",
                'title': alert_data['title'],
                'severity': alert_data['severity'],
                'status': 'open',
                'created_at': alert_data['timestamp'],
                'context': alert_data['context'],
                'alert_key': alert_data['alert_key']
            }
            
            # Log incident creation
            main_logger.log_business_event(
                'incident_created',
                incident_id=incident_data['incident_id'],
                incident_title=alert_data['title'],
                incident_severity=alert_data['severity'],
                **alert_data['context']
            )
            
            logger.critical(f"Critical incident created: {incident_data['incident_id']}")
            
        except Exception as e:
            logger.error(f"Failed to create incident: {e}")
    
    def _get_slack_color(self, severity: str) -> str:
        """Get Slack attachment color based on severity."""
        colors = {
            'info': 'good',
            'warning': 'warning',
            'error': 'danger',
            'critical': '#FF0000'
        }
        return colors.get(severity, 'warning')
    
    def _format_email_body(self, alert_data: Dict[str, Any]) -> str:
        """Format email body for alert."""
        severity_colors = {
            'info': '#36a64f',
            'warning': '#ffcc00',
            'error': '#ff6600',
            'critical': '#ff0000'
        }
        
        color = severity_colors.get(alert_data['severity'], '#ffcc00')
        
        return f"""
        <html>
        <body>
            <div style="font-family: Arial, sans-serif; max-width: 600px;">
                <div style="background-color: {color}; color: white; padding: 20px; border-radius: 5px 5px 0 0;">
                    <h2>MCX3D Financial Alert</h2>
                    <h3>{alert_data['title']}</h3>
                </div>
                <div style="border: 1px solid #ddd; padding: 20px; border-radius: 0 0 5px 5px;">
                    <p><strong>Severity:</strong> {alert_data['severity'].upper()}</p>
                    <p><strong>Time:</strong> {alert_data['timestamp']}</p>
                    <p><strong>Service:</strong> {alert_data['service']}</p>
                    <p><strong>Component:</strong> {alert_data['context'].get('component', 'Unknown')}</p>
                    
                    <h4>Message:</h4>
                    <p>{alert_data['message']}</p>
                    
                    <h4>Context:</h4>
                    <ul>
                        {''.join([f"<li><strong>{key}:</strong> {value}</li>" 
                                for key, value in alert_data['context'].items()])}
                    </ul>
                    
                    <p style="margin-top: 30px; font-size: 12px; color: #666;">
                        This alert was generated by MCX3D Financial Monitoring System
                    </p>
                </div>
            </div>
        </body>
        </html>
        """


# Business Logic Alert Triggers
class BusinessAnomalyDetector:
    """Detect business anomalies and trigger appropriate alerts."""
    
    def __init__(self, alert_manager: AlertManager):
        self.alert_manager = alert_manager
        self.baseline_metrics = {}
    
    async def check_report_generation_anomalies(self, current_rate: float, avg_duration: float):
        """Check for report generation anomalies."""
        if avg_duration > 300:  # 5 minutes
            await self.alert_manager.trigger_alert(
                AlertSeverity.WARNING,
                "Slow Report Generation Detected",
                f"Average report generation time is {avg_duration:.1f} seconds",
                {
                    'component': 'reporting',
                    'average_duration_seconds': avg_duration,
                    'threshold_seconds': 300
                },
                'slow_report_generation'
            )
        
        if current_rate < 0.1:  # Less than 0.1 reports per minute
            await self.alert_manager.trigger_alert(
                AlertSeverity.ERROR,
                "Report Generation Rate Critically Low",
                f"Current report generation rate: {current_rate:.2f} reports/minute",
                {
                    'component': 'reporting',
                    'current_rate': current_rate,
                    'threshold_rate': 0.1
                },
                'low_report_rate'
            )
    
    async def check_valuation_accuracy(self, accuracy_score: float):
        """Check valuation accuracy and alert if below threshold."""
        if accuracy_score < 0.8:  # 80% accuracy threshold
            severity = AlertSeverity.CRITICAL if accuracy_score < 0.6 else AlertSeverity.ERROR
            
            await self.alert_manager.trigger_alert(
                severity,
                "Valuation Accuracy Below Threshold",
                f"Current valuation accuracy: {accuracy_score:.1%}",
                {
                    'component': 'valuation',
                    'accuracy_score': accuracy_score,
                    'threshold': 0.8
                },
                'low_valuation_accuracy'
            )
    
    async def check_system_health(self, component: str, status: str, metrics: Dict[str, Any]):
        """Check system component health and alert on issues."""
        if status == 'error':
            await self.alert_manager.trigger_alert(
                AlertSeverity.CRITICAL,
                f"System Component Failure: {component}",
                f"Component {component} is in error state",
                {
                    'component': component,
                    'status': status,
                    **metrics
                },
                f'component_failure_{component}'
            )
        elif status == 'degraded':
            await self.alert_manager.trigger_alert(
                AlertSeverity.WARNING,
                f"System Component Degraded: {component}",
                f"Component {component} is experiencing degraded performance",
                {
                    'component': component,
                    'status': status,
                    **metrics
                },
                f'component_degraded_{component}'
            )


# Global alert manager instance
alert_manager = AlertManager()
anomaly_detector = BusinessAnomalyDetector(alert_manager)

# Convenience functions for common alerts
async def alert_system_error(component: str, error: Exception, context: Dict[str, Any] = None):
    """Trigger system error alert."""
    await alert_manager.trigger_alert(
        AlertSeverity.ERROR,
        f"System Error in {component}",
        f"Error: {str(error)}",
        {
            'component': component,
            'error_type': type(error).__name__,
            'error_message': str(error),
            **(context or {})
        },
        f'system_error_{component}'
    )

async def alert_business_anomaly(metric_name: str, current_value: float, 
                                expected_value: float, context: Dict[str, Any] = None):
    """Trigger business anomaly alert."""
    deviation = abs(current_value - expected_value) / expected_value * 100
    severity = AlertSeverity.CRITICAL if deviation > 50 else AlertSeverity.WARNING
    
    await alert_manager.trigger_alert(
        severity,
        f"Business Metric Anomaly: {metric_name}",
        f"Current: {current_value}, Expected: {expected_value} ({deviation:.1f}% deviation)",
        {
            'component': 'business_intelligence',
            'metric_name': metric_name,
            'current_value': current_value,
            'expected_value': expected_value,
            'deviation_percent': deviation,
            **(context or {})
        },
        f'business_anomaly_{metric_name}'
    )

async def alert_performance_degradation(operation: str, current_duration: float, 
                                      baseline_duration: float, context: Dict[str, Any] = None):
    """Trigger performance degradation alert."""
    increase_percent = (current_duration - baseline_duration) / baseline_duration * 100
    severity = AlertSeverity.CRITICAL if increase_percent > 200 else AlertSeverity.WARNING
    
    await alert_manager.trigger_alert(
        severity,
        f"Performance Degradation: {operation}",
        f"Duration increased by {increase_percent:.1f}% (was {baseline_duration:.1f}s, now {current_duration:.1f}s)",
        {
            'component': 'performance',
            'operation': operation,
            'current_duration': current_duration,
            'baseline_duration': baseline_duration,
            'increase_percent': increase_percent,
            **(context or {})
        },
        f'performance_degradation_{operation}'
    )