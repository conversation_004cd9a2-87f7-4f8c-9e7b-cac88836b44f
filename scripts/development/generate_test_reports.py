#!/usr/bin/env python
"""Generate test reports using the available data."""

import os
import sys
from datetime import datetime, timedelta
import logging

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from mcx3d_finance.core.financials.balance_sheet import BalanceSheetGenerator
from mcx3d_finance.core.financials.income_statement import IncomeStatementGenerator
from mcx3d_finance.core.financials.cash_flow import CashFlowGenerator
from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.reporting.generator import ReportGenerator

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def generate_all_reports() -> None:
    """Generate all three financial reports."""
    
    # Use organization ID 2 (Modular CX)
    org_id = 2
    
    # Date ranges
    as_of_date = datetime.now()
    from_date = datetime.now() - timedelta(days=365)
    to_date = datetime.now()
    
    # Initialize generators
    balance_sheet_gen = BalanceSheetGenerator(organization_id=org_id)
    income_statement_gen = IncomeStatementGenerator(organization_id=org_id)
    cash_flow_gen = CashFlowGenerator(db_session=SessionLocal())
    
    try:
        # Generate Balance Sheet
        logger.info("Generating Balance Sheet...")
        balance_sheet = balance_sheet_gen.generate_balance_sheet(as_of_date=as_of_date)
        
        # Generate Income Statement
        logger.info("Generating Income Statement...")
        income_statement = income_statement_gen.generate_income_statement(
            from_date=from_date,
            to_date=to_date
        )
        
        # Generate Cash Flow Statement
        logger.info("Generating Cash Flow Statement...")
        cash_flow = cash_flow_gen.generate_cash_flow_statement(
            organization_id=org_id,
            from_date=from_date,
            to_date=to_date,
            method="indirect"
        )
        
        # Create output directory in project root
        output_dir = os.path.join(project_root, "generated_reports")
        os.makedirs(output_dir, exist_ok=True)
        
        # Save reports as JSON for review
        import json
        
        with open(f"{output_dir}/balance_sheet.json", "w") as f:
            json.dump(balance_sheet, f, indent=2, default=str)
        
        with open(f"{output_dir}/income_statement.json", "w") as f:
            json.dump(income_statement, f, indent=2, default=str)
        
        with open(f"{output_dir}/cash_flow.json", "w") as f:
            json.dump(cash_flow, f, indent=2, default=str)
        
        logger.info(f"Reports saved to {output_dir}/")
        
        # Print summary of generated reports
        logger.info("\n📊 Report Generation Summary:")
        logger.info(f"  • Balance Sheet: {os.path.basename(output_dir)}/balance_sheet.json")
        logger.info(f"  • Income Statement: {os.path.basename(output_dir)}/income_statement.json")
        logger.info(f"  • Cash Flow: {os.path.basename(output_dir)}/cash_flow.json")
        
        logger.info("\n✅ All reports generated successfully!")
        logger.info(f"Check the '{os.path.basename(output_dir)}' directory for the generated files.")
        
    except Exception as e:
        logger.error(f"Error generating reports: {e}")
        import traceback
        traceback.print_exc()
    finally:
        cash_flow_gen.db.close()


if __name__ == "__main__":
    generate_all_reports()