"""
Financial Documentation API Endpoints for MCX3D LTD

Provides endpoints for generating comprehensive financial documentation
using real-time Xero data.
"""

import logging
from datetime import date, datetime
from typing import List, Optional, Dict, Any
import os
import asyncio

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from fastapi.responses import FileResponse, JSONResponse
from sqlalchemy.orm import Session

from ..api.auth_middleware import get_current_user
from ..db.models import User
from ..db.session import get_db
from ..integrations.xero_client import XeroClient
from ..reporting.documentation_builder import FinancialDocumentationBuilder
from ..tasks.report_tasks import generate_financial_package_task
from ..core.cache import get_cache
# from ..monitoring.metrics_collector import metrics_collector  # TODO: Implement metrics collector
from ..utils.rate_limiter import RateLimiter
from .schemas import (
    FinancialPackageRequest,
    FinancialPackageResponse,
    DocumentGenerationStatus,
    ReportFormat
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/financial-docs", tags=["financial-documentation"])

# Rate limiter for documentation generation
doc_rate_limiter = RateLimiter()


@router.post("/generate-package", response_model=FinancialPackageResponse)
async def generate_financial_package(
    request: FinancialPackageRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Generate comprehensive financial documentation package for MCX3D LTD.
    
    This endpoint triggers the generation of a complete financial documentation
    package including:
    - Executive Summary
    - Financial Statements (Balance Sheet, Income Statement, Cash Flow)
    - Notes to Financial Statements
    - Financial Analysis and KPIs
    - Valuations (optional)
    
    The generation is performed asynchronously and returns a task ID for tracking.
    """
    
    # Rate limiting
    if not await doc_rate_limiter.check_rate_limit(f"user:{current_user.id}"):
        raise HTTPException(
            status_code=429,
            detail="Rate limit exceeded. Maximum 10 documentation requests per hour."
        )
    
    # Validate user permissions
    if not current_user.has_permission("generate_financial_docs"):
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions to generate financial documentation"
        )
    
    # Create task for async generation
    task_id = f"fin_docs_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{current_user.id}"
    
    # Queue the task
    task = generate_financial_package_task.apply_async(
        args=[
            task_id,
            request.report_date.isoformat() if request.report_date else None,
            request.include_projections,
            request.formats,
            request.email_on_completion
        ],
        task_id=task_id
    )
    
    # Log the request
    logger.info(f"Financial package generation requested by user {current_user.email}, task_id: {task_id}")
    
    # Track metrics
    # metrics_collector.increment_counter(  # TODO: Implement metrics collector
    #     "financial_docs_generation_requested",
    #     tags={"user_id": str(current_user.id)}
    # )
    
    return FinancialPackageResponse(
        task_id=task_id,
        status="queued",
        message="Financial documentation generation has been queued",
        estimated_completion_time=300  # 5 minutes estimate
    )


@router.get("/status/{task_id}", response_model=DocumentGenerationStatus)
async def get_generation_status(
    task_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Check the status of a financial documentation generation task.
    """
    
    # Check cache first
    cache_service = get_cache()
    cached_status = await cache_service.get(f"doc_status:{task_id}")
    if cached_status:
        return DocumentGenerationStatus(**cached_status)
    
    # Get task status from Celery
    from ..tasks.report_tasks import app as celery_app
    
    task = celery_app.AsyncResult(task_id)
    
    if task.state == "PENDING":
        status = "queued"
        progress = 0
        message = "Task is waiting to be processed"
    elif task.state == "STARTED":
        status = "processing"
        progress = task.info.get("progress", 0) if task.info else 0
        message = task.info.get("message", "Generating documentation...") if task.info else "Processing..."
    elif task.state == "SUCCESS":
        status = "completed"
        progress = 100
        message = "Documentation generated successfully"
    elif task.state == "FAILURE":
        status = "failed"
        progress = 0
        message = str(task.info) if task.info else "Generation failed"
    else:
        status = task.state.lower()
        progress = 0
        message = "Unknown status"
    
    response = DocumentGenerationStatus(
        task_id=task_id,
        status=status,
        progress=progress,
        message=message,
        result=task.result if task.state == "SUCCESS" else None
    )
    
    # Cache the status
    await cache_service.set(
        f"doc_status:{task_id}",
        response.dict(),
        ttl=300  # 5 minutes
    )
    
    return response


@router.get("/download/{task_id}")
async def download_financial_package(
    task_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Download the generated financial documentation package.
    """
    
    # Check task status
    from ..tasks.report_tasks import app as celery_app
    
    task = celery_app.AsyncResult(task_id)
    
    if task.state != "SUCCESS":
        raise HTTPException(
            status_code=400,
            detail=f"Documentation not ready. Current status: {task.state}"
        )
    
    # Get the package path from task result
    result = task.result
    if not result or "package_path" not in result:
        raise HTTPException(
            status_code=500,
            detail="Package path not found in task result"
        )
    
    package_path = result["package_path"]
    
    if not os.path.exists(package_path):
        raise HTTPException(
            status_code=404,
            detail="Generated package file not found"
        )
    
    # Log download
    logger.info(f"Financial package downloaded by user {current_user.email}, task_id: {task_id}")
    
    # Track metrics
    # metrics_collector.increment_counter(  # TODO: Implement metrics collector
    #     "financial_docs_downloaded",
    #     tags={"user_id": str(current_user.id)}
    # )
    
    return FileResponse(
        path=package_path,
        media_type="application/zip",
        filename=os.path.basename(package_path)
    )


@router.get("/executive-summary")
async def get_executive_summary(
    report_date: Optional[date] = Query(None, description="Report date (defaults to today)"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get executive summary data in JSON format.
    
    Returns key financial metrics and highlights without generating full documentation.
    """
    
    # Check cache
    cache_key = f"exec_summary:{report_date or 'latest'}:{current_user.organization_id}"
    cache_service = get_cache()
    cached_data = await cache_service.get(cache_key)
    if cached_data:
        return JSONResponse(content=cached_data)
    
    try:
        # Initialize Xero client
        xero_client = XeroClient(organization_id=current_user.organization_id)
        
        # Initialize documentation builder
        doc_builder = FinancialDocumentationBuilder(xero_client)
        
        # Fetch basic financial data
        xero_data = await doc_builder._fetch_xero_data()
        financial_data = await doc_builder._process_financial_data(xero_data)
        metrics_data = await doc_builder._calculate_metrics(financial_data)
        
        # Generate summary
        summary = doc_builder._generate_summary({
            **financial_data,
            **metrics_data,
            'report_date': report_date or date.today()
        })
        
        # Cache the result
        await cache_service.set(cache_key, summary, ttl=3600)  # 1 hour
        
        return JSONResponse(content=summary)
        
    except Exception as e:
        logger.error(f"Error generating executive summary: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to generate executive summary"
        )


@router.get("/financial-statements/{statement_type}")
async def get_financial_statement(
    statement_type: str,
    format: ReportFormat = Query(ReportFormat.json),
    report_date: Optional[date] = Query(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific financial statement in the requested format.
    
    Statement types:
    - balance-sheet
    - income-statement
    - cash-flow
    
    Formats:
    - json
    - pdf
    - excel
    """
    
    valid_statements = ["balance-sheet", "income-statement", "cash-flow"]
    if statement_type not in valid_statements:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid statement type. Must be one of: {', '.join(valid_statements)}"
        )
    
    # For PDF/Excel, generate and return file
    if format in [ReportFormat.pdf, ReportFormat.excel]:
        # Create temporary file path
        output_dir = f"/tmp/financial_docs/{current_user.id}"
        os.makedirs(output_dir, exist_ok=True)
        
        filename = f"MCX3D_{statement_type}_{date.today().strftime('%Y%m%d')}.{format.value}"
        output_path = os.path.join(output_dir, filename)
        
        # Generate the statement
        try:
            xero_client = XeroClient(organization_id=current_user.organization_id)
            doc_builder = FinancialDocumentationBuilder(xero_client)
            
            # Fetch and process data
            xero_data = await doc_builder._fetch_xero_data()
            financial_data = await doc_builder._process_financial_data(xero_data)
            
            # Generate specific statement
            if statement_type == "balance-sheet":
                if format == ReportFormat.pdf:
                    doc_builder.report_generator.generate_balance_sheet_pdf(
                        financial_data['balance_sheet'], output_path
                    )
                else:  # Excel
                    doc_builder.report_generator.generate_balance_sheet_excel(
                        financial_data['balance_sheet'], output_path
                    )
            elif statement_type == "income-statement":
                if format == ReportFormat.pdf:
                    doc_builder.report_generator.generate_income_statement_pdf(
                        financial_data['income_statement'], output_path
                    )
                else:  # Excel
                    doc_builder.report_generator.generate_income_statement_excel(
                        financial_data['income_statement'], output_path
                    )
            elif statement_type == "cash-flow":
                if format == ReportFormat.pdf:
                    doc_builder.report_generator.generate_cash_flow_pdf(
                        financial_data['cash_flow'], output_path
                    )
                else:  # Excel
                    doc_builder.report_generator.generate_cash_flow_excel(
                        financial_data['cash_flow'], output_path
                    )
            
            return FileResponse(
                path=output_path,
                media_type="application/pdf" if format == ReportFormat.pdf else "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                filename=filename
            )
            
        except Exception as e:
            logger.error(f"Error generating {statement_type}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to generate {statement_type}"
            )
    
    # For JSON, return data directly
    try:
        cache_key = f"statement:{statement_type}:{report_date or 'latest'}:{current_user.organization_id}"
        cache_service = get_cache()
        cached_data = await cache_service.get(cache_key)
        if cached_data:
            return JSONResponse(content=cached_data)
        
        xero_client = XeroClient(organization_id=current_user.organization_id)
        doc_builder = FinancialDocumentationBuilder(xero_client)
        
        xero_data = await doc_builder._fetch_xero_data()
        financial_data = await doc_builder._process_financial_data(xero_data)
        
        if statement_type == "balance-sheet":
            data = financial_data['balance_sheet']
        elif statement_type == "income-statement":
            data = financial_data['income_statement']
        else:  # cash-flow
            data = financial_data['cash_flow']
        
        await cache_service.set(cache_key, data, ttl=3600)
        
        return JSONResponse(content=data)
        
    except Exception as e:
        logger.error(f"Error fetching {statement_type} data: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch {statement_type} data"
        )


@router.get("/analysis/kpis")
async def get_financial_kpis(
    include_saas_metrics: bool = Query(True, description="Include SaaS-specific metrics"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get comprehensive financial KPIs and metrics.
    
    Returns:
    - Financial ratios (liquidity, profitability, efficiency)
    - Growth metrics
    - SaaS metrics (if applicable)
    """
    
    cache_key = f"kpis:{include_saas_metrics}:{current_user.organization_id}"
    cache_service = get_cache()
    cached_data = await cache_service.get(cache_key)
    if cached_data:
        return JSONResponse(content=cached_data)
    
    try:
        xero_client = XeroClient(organization_id=current_user.organization_id)
        doc_builder = FinancialDocumentationBuilder(xero_client)
        
        xero_data = await doc_builder._fetch_xero_data()
        financial_data = await doc_builder._process_financial_data(xero_data)
        metrics_data = await doc_builder._calculate_metrics(financial_data)
        
        response = {
            "financial_ratios": metrics_data['financial_ratios'],
            "growth_metrics": metrics_data['growth_metrics']
        }
        
        if include_saas_metrics:
            response["saas_metrics"] = metrics_data['saas_metrics']
        
        await cache_service.set(cache_key, response, ttl=3600)
        
        return JSONResponse(content=response)
        
    except Exception as e:
        logger.error(f"Error calculating KPIs: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to calculate financial KPIs"
        )


@router.post("/schedule-generation")
async def schedule_recurring_generation(
    frequency: str = Query(..., description="Generation frequency: daily, weekly, monthly"),
    day_of_week: Optional[int] = Query(None, description="Day of week (0-6) for weekly"),
    day_of_month: Optional[int] = Query(None, description="Day of month (1-31) for monthly"),
    formats: List[ReportFormat] = Query([ReportFormat.pdf, ReportFormat.excel]),
    email_recipients: List[str] = Query([]),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Schedule recurring generation of financial documentation.
    
    Requires admin permissions.
    """
    
    if not current_user.is_admin:
        raise HTTPException(
            status_code=403,
            detail="Admin permissions required to schedule recurring generation"
        )
    
    # Validate frequency and parameters
    if frequency not in ["daily", "weekly", "monthly"]:
        raise HTTPException(
            status_code=400,
            detail="Frequency must be one of: daily, weekly, monthly"
        )
    
    if frequency == "weekly" and day_of_week is None:
        raise HTTPException(
            status_code=400,
            detail="day_of_week is required for weekly frequency"
        )
    
    if frequency == "monthly" and day_of_month is None:
        raise HTTPException(
            status_code=400,
            detail="day_of_month is required for monthly frequency"
        )
    
    # Create schedule in database
    # This would integrate with a task scheduler like Celery Beat
    # Implementation details omitted for brevity
    
    return JSONResponse(
        content={
            "status": "scheduled",
            "frequency": frequency,
            "formats": [f.value for f in formats],
            "email_recipients": email_recipients,
            "message": "Recurring generation scheduled successfully"
        }
    )