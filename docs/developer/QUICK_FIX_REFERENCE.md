# MCX3D Financials - Quick Fix Reference

## 🚨 Critical Issues & Quick Fixes

### 1. N+1 Query Problem (HIGHEST PRIORITY)

**Issue**: Thousands of SQL queries during Xero sync
**Files**: `mcx3d_finance/integrations/xero_data_storage.py`
**Lines**: 248-254 (invoices), 300-307 (bank transactions)

**Quick Fix**:
```python
# Before processing invoices/transactions, bulk load contacts:
contact_ids = [item.get("contact_id") for item in items if item.get("contact_id")]
contacts = db.query(Contact).filter(
    Contact.organization_id == org_id,
    Contact.xero_contact_id.in_(contact_ids)
).all()
contact_map = {c.xero_contact_id: c.id for c in contacts}

# Then use map instead of individual queries:
invoice_data["contact_id"] = contact_map.get(xero_contact_id)
```

### 2. Cash Flow PDF Generation (NotImplementedError)

**Issue**: Cash flow reports return NotImplementedError
**File**: `mcx3d_finance/api/reports.py`
**Line**: 327

**Quick Fix**:
```python
# Replace NotImplementedError with actual implementation:
if format == "pdf":
    report_generator.generate_cash_flow_pdf(report_data, output_path)
elif format == "excel":
    report_generator.generate_cash_flow_excel(report_data, output_path)

# Implement methods in ReportGenerator class
```

### 3. Empty PDF Files (22 bytes)

**Issue**: PDF generation creates empty files
**Probable Cause**: Data not passed correctly or exception swallowed

**Debug Steps**:
1. Add logging before PDF generation
2. Verify `report_data` contains actual data
3. Check for silent exceptions in PDF generation
4. Ensure file is properly written and closed

### 4. Long-Running CLI Commands Timeout

**Issue**: DCF valuation and analytics timeout on large datasets
**Files**: `mcx3d_finance/cli/valuation.py`, `mcx3d_finance/cli/analytics.py`

**Quick Fix**:
```python
# Add async option to CLI commands:
@click.option("--async", "async_mode", is_flag=True, help="Run asynchronously")

# In command implementation:
if async_mode:
    task = valuation_task.delay(params)
    click.echo(f"Task ID: {task.id}")
else:
    # Existing sync code
```

### 5. API 404 Errors

**Issue**: Report endpoints return 404
**Probable Cause**: Authentication middleware blocking requests

**Debug Steps**:
1. Check if routes are registered: `app.routes`
2. Test without auth: temporarily disable `@require_auth`
3. Verify auth token is being passed correctly
4. Check CORS configuration

## 🛠️ Quick Performance Wins

### Database Indexing
```sql
-- Add indexes for common queries
CREATE INDEX idx_invoices_org_xero ON invoices(organization_id, xero_invoice_id);
CREATE INDEX idx_contacts_org_xero ON contacts(organization_id, xero_contact_id);
CREATE INDEX idx_transactions_org_date ON transactions(organization_id, date);
```

### Query Batching
```python
# Use SQLAlchemy bulk operations
db.bulk_insert_mappings(Invoice, invoice_dicts)
db.bulk_update_mappings(Invoice, update_dicts)
```

### Eager Loading
```python
# Load related data in one query
invoices = db.query(Invoice)\
    .options(joinedload(Invoice.contact))\
    .filter(Invoice.organization_id == org_id)\
    .all()
```

## 📋 Testing Commands

### Test N+1 Query Fix
```bash
# Monitor queries during sync
python -m mcx3d_finance.cli.main sync xero -o 1 --no-async --optimize
```

### Test Report Generation
```bash
# Test each report type
curl -X GET "http://localhost:8000/api/reports/income-statement?organization_id=1&start_date=2024-01-01&end_date=2024-12-31&format=pdf" -H "Authorization: Bearer TOKEN"
```

### Performance Benchmark
```bash
# Run performance tests
pytest -m performance -v --benchmark-only
```

## 🚀 Emergency Rollback

If issues occur after deployment:

1. **Revert Code**:
```bash
git revert HEAD
git push origin main
```

2. **Restore Database**:
```bash
pg_restore -d mcx3d_finance backup_before_deploy.sql
```

3. **Clear Cache**:
```bash
redis-cli FLUSHDB
```

4. **Restart Services**:
```bash
systemctl restart mcx3d-finance-api
systemctl restart mcx3d-finance-celery
```

## 📊 Success Metrics

Monitor these after fixes:

| Metric | Before | Target | How to Measure |
|--------|--------|--------|----------------|
| Sync Queries | 5000+ | <50 | Query monitor |
| Sync Time | 20+ min | <5 min | CLI timer |
| API Response | >2s | <200ms | APM tools |
| PDF Generation | Fails | <2s | Report logs |
| Error Rate | >5% | <0.1% | Error tracking |

## 🔍 Debug Helpers

### Enable Query Logging
```python
# In config.py
SQLALCHEMY_ECHO = True  # See all SQL queries
```

### Profile Slow Code
```python
import cProfile
import pstats

profiler = cProfile.Profile()
profiler.enable()
# ... code to profile ...
profiler.disable()
stats = pstats.Stats(profiler).sort_stats('cumulative')
stats.print_stats(20)  # Top 20 functions
```

### Monitor Celery Tasks
```bash
# Watch task queue
celery -A mcx3d_finance.tasks.celery_app inspect active

# See task results
celery -A mcx3d_finance.tasks.celery_app result <task_id>
```

## 📝 Commit Message Templates

```bash
# Performance fix
fix(sync): Resolve N+1 query issue in Xero data storage

- Implement batch loading for contacts
- Reduce queries from 5000+ to <50
- Add query monitoring for regression detection

Closes #123

# Feature implementation
feat(reports): Implement cash flow PDF generation

- Add CashFlowReportGenerator class
- Support PDF and Excel formats
- Include operating, investing, and financing sections

Closes #456
```

## 🎯 Priority Order

1. **Day 1**: Fix N+1 queries (immediate 90% performance gain)
2. **Day 2**: Implement cash flow reports (unblock users)
3. **Day 3**: Fix PDF generation issues (complete reporting)
4. **Day 4**: Add async processing (prevent timeouts)
5. **Day 5**: Resolve API auth issues (enable integrations)

---

**Remember**: Always test fixes in staging before production deployment!