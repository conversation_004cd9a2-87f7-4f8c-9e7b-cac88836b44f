# User Management Guide - MCX3D Finance

This guide covers the complete user management system for MCX3D Finance, including user creation, organization management, and authentication.

## Overview

The MCX3D Finance user management system provides:

- **User Registration API**: RESTful API endpoint for user registration
- **CLI User Management**: Command-line tools for user creation and management
- **Role-Based Access Control**: Admin, user, and viewer roles with organization-level permissions
- **Security Features**: Password hashing, audit logging, input validation, and rate limiting
- **Multi-Tenant Support**: Users can belong to multiple organizations with different roles

## 🚀 Quick Start

### API User Registration

**Endpoint:** `POST /api/auth/register`

**Request Example:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "full_name": "<PERSON>",
  "organization_name": "Acme Corporation",
  "role": "admin"
}
```

**Response Example:**
```json
{
  "user_id": 123,
  "email": "<EMAIL>",
  "full_name": "<PERSON>",
  "organization_id": 456,
  "organization_name": "Acme Corporation",
  "role": "admin",
  "message": "User registered successfully"
}
```

### CLI User Creation

**Basic User Creation:**
```bash
# Set audit log location
export AUDIT_LOG_FILE="./logs/audit.log"

# Create a regular user
python3 -m mcx3d_finance.cli.main user create \
  --email <EMAIL> \
  --name "John Doe" \
  --org-name "Acme Corporation" \
  --role admin
```

**Create Admin User:**
```bash
# Create superuser with access to all organizations
python3 -m mcx3d_finance.cli.main user create-admin \
  --email <EMAIL> \
  --name "System Administrator"
```

## 📋 CLI Commands Reference

### User Management Commands

#### `user create` - Create New User
Creates a new user account with optional organization association.

**Options:**
- `--email` (required): User email address
- `--password` (required): User password (prompted securely)
- `--name` (required): User full name
- `--org-name`: Organization name (creates new org if doesn't exist)
- `--org-id`: Existing organization ID to associate user with
- `--role`: User role (`user`, `admin`, `viewer`) - default: `user`
- `--superuser`: Create superuser with global admin privileges

**Examples:**
```bash
# Create user with new organization
python3 -m mcx3d_finance.cli.main user create \
  --email <EMAIL> \
  --name "Jane Smith" \
  --org-name "Tech Startup Inc" \
  --role admin

# Create user and associate with existing organization
python3 -m mcx3d_finance.cli.main user create \
  --email <EMAIL> \
  --name "Data Analyst" \
  --org-id 123 \
  --role user

# Create superuser
python3 -m mcx3d_finance.cli.main user create \
  --email <EMAIL> \
  --name "Super Administrator" \
  --superuser
```

#### `user create-admin` - Create Admin User
Creates a superuser account with global administrative privileges.

**Options:**
- `--email` (required): Admin email address
- `--password` (required): Admin password (prompted securely)
- `--name` (required): Admin full name

**Example:**
```bash
python3 -m mcx3d_finance.cli.main user create-admin \
  --email <EMAIL> \
  --name "System Administrator"
```

#### `user list` - List Users
Lists users in the system with filtering options.

**Options:**
- `--org-id`: Filter by organization ID
- `--role`: Filter by role (`user`, `admin`, `viewer`)
- `--active/--all`: Show only active users (default) or all users
- `--format`: Output format (`table`, `json`) - default: `table`

**Examples:**
```bash
# List all active users
python3 -m mcx3d_finance.cli.main user list

# List users in specific organization with admin role
python3 -m mcx3d_finance.cli.main user list --org-id 123 --role admin

# Export user data as JSON
python3 -m mcx3d_finance.cli.main user list --format json > users.json

# List all users including inactive
python3 -m mcx3d_finance.cli.main user list --all
```

#### `user add-to-org` - Add User to Organization
Associates an existing user with an organization and assigns a role.

**Arguments:**
- `user_id` (required): User ID to add to organization

**Options:**
- `--org-id` (required): Organization ID
- `--role`: User role in organization (`user`, `admin`, `viewer`) - default: `user`

**Example:**
```bash
python3 -m mcx3d_finance.cli.main user add-to-org 123 \
  --org-id 456 \
  --role admin
```

## 🔐 Security Features

### Password Security
- **Minimum Length**: 8 characters required
- **Hashing**: bcrypt with salt for secure password storage
- **Validation**: Server-side password strength validation

### Authentication & Authorization
- **JWT Tokens**: Secure token-based authentication
- **Multi-Factor Authentication**: TOTP and backup codes support
- **Account Lockout**: Protection against brute force attacks
- **Role-Based Access**: Organization-level role assignments

### Audit Logging
All user management operations are logged with:
- **Event Type**: Registration, login, role changes
- **User Information**: User ID, email, IP address
- **Operational Details**: Organization associations, role assignments
- **Timestamps**: UTC timestamps for all events

### Input Validation
- **Email Validation**: RFC-compliant email format checking
- **Input Sanitization**: XSS and injection prevention
- **Rate Limiting**: API endpoint protection against abuse

## 🏢 Organization Management

### Organization Creation
Organizations are automatically created when:
1. **API Registration**: User provides `organization_name` in registration request
2. **CLI Creation**: User specifies `--org-name` that doesn't exist

### Organization Association
Users can belong to multiple organizations with different roles:
- **Admin**: Full access to organization data and user management
- **User**: Standard access to financial data and reports
- **Viewer**: Read-only access to reports and dashboards

### Multi-Tenant Architecture
- **Data Isolation**: Each organization's data is completely isolated
- **Role Inheritance**: Superusers have access across all organizations
- **Flexible Permissions**: Fine-grained control over data access

## 📊 Usage Examples

### Complete User Onboarding Workflow

```bash
# 1. Create admin user for new organization
python3 -m mcx3d_finance.cli.main user create \
  --email <EMAIL> \
  --name "CEO Name" \
  --org-name "New Company LLC" \
  --role admin

# 2. List organizations to get org ID
python3 -m mcx3d_finance.cli.main sync list-orgs

# 3. Add additional users to the organization
python3 -m mcx3d_finance.cli.main user create \
  --email <EMAIL> \
  --name "CFO Name" \
  --org-id 123 \
  --role admin

python3 -m mcx3d_finance.cli.main user create \
  --email <EMAIL> \
  --name "Financial Analyst" \
  --org-id 123 \
  --role user

# 4. Verify user creation
python3 -m mcx3d_finance.cli.main user list --org-id 123
```

### API Integration Example

```python
import requests

# Register new user via API
registration_data = {
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "full_name": "Developer Name",
    "organization_name": "Tech Company",
    "role": "user"
}

response = requests.post(
    "http://localhost:8000/api/auth/register",
    json=registration_data
)

if response.status_code == 200:
    user_data = response.json()
    print(f"User created: {user_data['user_id']}")
    print(f"Organization: {user_data['organization_name']}")
else:
    print(f"Registration failed: {response.text}")
```

## 🛠 Troubleshooting

### Common Issues

#### "Permission denied: '/var/log/mcx3d_audit.log'"
**Solution**: Set custom audit log location:
```bash
export AUDIT_LOG_FILE="./logs/audit.log"
mkdir -p logs
```

#### "User with this email already exists"
**Solution**: Check existing users:
```bash
python3 -m mcx3d_finance.cli.main user list --format json | grep "<EMAIL>"
```

#### "Organization with ID X not found"
**Solution**: List available organizations:
```bash
python3 -m mcx3d_finance.cli.main sync list-orgs
```

### Validation Errors
- **Email Format**: Must be valid RFC-compliant email
- **Password Length**: Minimum 8 characters required
- **Name Length**: Minimum 2 characters required
- **Role Values**: Must be one of: `user`, `admin`, `viewer`

## 🔄 Integration with Financial Data

After creating users and organizations, you can:

1. **Connect to Xero**: Users can authorize Xero connections for their organizations
2. **Download Financial Data**: Use sync commands to fetch company financial data
3. **Generate Reports**: Create financial reports and valuations
4. **Export Data**: Export financial data in various formats

See the [Financial Data Management Guide](./financial-data-guide.md) for details on downloading and managing financial data.

## 📞 Support

For additional help:
- Check the [API Documentation](../api/README.md) for detailed endpoint specifications
- Review [Security Guidelines](../operations/security.md) for best practices
- See [Troubleshooting Guide](../operations/troubleshooting.md) for common issues

---

**Next Steps**: Once users are created, proceed to [Financial Data Management](./financial-data-guide.md) to learn how to download and work with company financial data.