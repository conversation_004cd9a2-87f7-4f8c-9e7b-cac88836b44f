"""
UK-specific financial analysis and ratios.
"""

from typing import Dict, Any

def calculate_uk_financial_ratios(
    balance_sheet: Dict[str, Any], pnl: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Calculate UK-specific financial ratios.

    Args:
        balance_sheet: A UK-compliant balance sheet.
        pnl: A UK-compliant profit and loss account.

    Returns:
        A dictionary of financial ratios.
    """
    analysis = {
        "liquidity_ratios": _calculate_liquidity_ratios(balance_sheet),
        "gearing_ratios": _calculate_gearing_ratios(balance_sheet),
        "profitability_ratios": _calculate_profitability_ratios(balance_sheet, pnl),
        "asset_turnover_ratios": _calculate_asset_turnover_ratios(balance_sheet, pnl),
        "working_capital_analysis": _analyze_working_capital(balance_sheet),
    }
    return analysis

def _calculate_liquidity_ratios(balance_sheet: Dict[str, Any]) -> Dict[str, float]:
    """Calculate liquidity ratios."""
    current_assets = balance_sheet["current_assets"]["total"]
    current_liabilities = balance_sheet["creditors_due_within_one_year"]["total"]
    stocks = balance_sheet["current_assets"]["stocks"]

    current_ratio = round(current_assets / current_liabilities, 2) if current_liabilities > 0 else 0
    acid_test_ratio = round((current_assets - stocks) / current_liabilities, 2) if current_liabilities > 0 else 0

    return {
        "current_ratio": current_ratio,
        "acid_test_ratio": acid_test_ratio,
    }

def _calculate_gearing_ratios(balance_sheet: Dict[str, Any]) -> Dict[str, float]:
    """Calculate gearing ratios."""
    total_debt = balance_sheet["creditors_due_within_one_year"]["total"] + balance_sheet["creditors_due_after_one_year"]["total"]
    equity = balance_sheet["capital_and_reserves"]["total"]
    
    gearing_ratio = round((total_debt / equity) * 100, 1) if equity > 0 else 0

    return {
        "gearing_ratio_percent": gearing_ratio,
    }

def _calculate_profitability_ratios(balance_sheet: Dict[str, Any], pnl: Dict[str, Any]) -> Dict[str, float]:
    """Calculate profitability ratios, including ROCE."""
    operating_profit = pnl["operating_profit"]
    capital_employed = balance_sheet["total_assets_less_current_liabilities"]
    
    roce = round((operating_profit / capital_employed) * 100, 1) if capital_employed > 0 else 0

    return {
        "return_on_capital_employed_percent": roce,
    }

def _calculate_asset_turnover_ratios(balance_sheet: Dict[str, Any], pnl: Dict[str, Any]) -> Dict[str, float]:
    """Calculate asset turnover ratios."""
    turnover = pnl["turnover"]
    total_assets = balance_sheet["fixed_assets"]["total"] + balance_sheet["current_assets"]["total"]

    asset_turnover = round(turnover / total_assets, 2) if total_assets > 0 else 0

    return {
        "asset_turnover": asset_turnover,
    }

def _analyze_working_capital(balance_sheet: Dict[str, Any]) -> Dict[str, float]:
    """Analyze working capital."""
    working_capital = balance_sheet["net_current_assets"]

    return {
        "working_capital": working_capital,
    }
