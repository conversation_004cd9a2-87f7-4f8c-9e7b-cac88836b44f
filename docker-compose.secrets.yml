version: '3.8'

# Docker Compose configuration for production with proper secret management
# This file extends the base docker-compose.yml with production secrets

services:
  web:
    secrets:
      - db_password
      - secret_key
      - encryption_key
      - xero_client_secret
      - smtp_password
    environment:
      # Override sensitive environment variables to use secrets
      - DATABASE_URL=postgresql://mcx3d_user:/run/secrets/db_password@db:5432/mcx3d_finance_prod
      - SECRET_KEY_FILE=/run/secrets/secret_key
      - ENCRYPTION_KEY_FILE=/run/secrets/encryption_key
      - XERO_CLIENT_SECRET_FILE=/run/secrets/xero_client_secret
      - SMTP_PASSWORD_FILE=/run/secrets/smtp_password
      # Non-sensitive environment variables
      - ENVIRONMENT=production
      - DEBUG=False
      - LOG_LEVEL=INFO
      - ENABLE_METRICS=true
      - ENABLE_ALERTING=true

  worker:
    secrets:
      - db_password
      - secret_key
      - encryption_key
      - xero_client_secret
    environment:
      - D<PERSON><PERSON><PERSON>E_URL=postgresql://mcx3d_user:/run/secrets/db_password@db:5432/mcx3d_finance_prod
      - SECRET_KEY_FILE=/run/secrets/secret_key
      - ENCRYPTION_KEY_FILE=/run/secrets/encryption_key
      - XERO_CLIENT_SECRET_FILE=/run/secrets/xero_client_secret
      - ENVIRONMENT=production
      - DEBUG=False
      - LOG_LEVEL=INFO

  db:
    secrets:
      - db_password
      - db_root_password
    environment:
      - POSTGRES_PASSWORD_FILE=/run/secrets/db_password
      - POSTGRES_ROOT_PASSWORD_FILE=/run/secrets/db_root_password
      - POSTGRES_USER=mcx3d_user
      - POSTGRES_DB=mcx3d_finance_prod
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./scripts/postgres/backup.sh:/usr/local/bin/backup.sh:ro

  redis:
    command: >
      sh -c "
        redis-server
        --requirepass $$(cat /run/secrets/redis_password)
        --maxmemory 256mb
        --maxmemory-policy allkeys-lru
        --save 900 1
        --save 300 10
        --save 60 10000
      "
    secrets:
      - redis_password
    volumes:
      - redis_data_prod:/data

# Production secrets definition
secrets:
  db_password:
    external: true
    external_name: mcx3d_db_password
  db_root_password:
    external: true
    external_name: mcx3d_db_root_password
  secret_key:
    external: true
    external_name: mcx3d_secret_key
  encryption_key:
    external: true
    external_name: mcx3d_encryption_key
  xero_client_secret:
    external: true
    external_name: mcx3d_xero_client_secret
  smtp_password:
    external: true
    external_name: mcx3d_smtp_password
  redis_password:
    external: true
    external_name: mcx3d_redis_password

# Production volumes
volumes:
  postgres_data_prod:
    driver: local
    driver_opts:
      type: none
      device: /var/lib/mcx3d/postgres
      o: bind
  redis_data_prod:
    driver: local
    driver_opts:
      type: none
      device: /var/lib/mcx3d/redis
      o: bind

# Production networks with encryption
networks:
  default:
    driver: overlay
    driver_opts:
      encrypted: "true"
    attachable: true