"""
Database query optimization helpers for the MCX3D financial system.

Provides optimized query patterns to prevent N+1 queries and improve performance
by using SQLAlchemy's eager loading strategies.
"""

from typing import List, Optional
from sqlalchemy.orm import Session, selectinload, joinedload, contains_eager
from sqlalchemy import and_, or_

from .models import Organization, Account, Contact, Transaction, Invoice, BankTransaction, User


class QueryOptimizer:
    """Optimized database queries to prevent N+1 problems."""
    
    @staticmethod
    def get_organization_with_accounts(db: Session, organization_id: int) -> Optional[Organization]:
        """Get organization with all accounts loaded to prevent N+1 queries."""
        return db.query(Organization).options(
            selectinload(Organization.accounts)
        ).filter(Organization.id == organization_id).first()
    
    @staticmethod
    def get_organization_with_contacts(db: Session, organization_id: int) -> Optional[Organization]:
        """Get organization with all contacts loaded to prevent N+1 queries."""
        return db.query(Organization).options(
            selectinload(Organization.contacts)
        ).filter(Organization.id == organization_id).first()
    
    @staticmethod
    def get_organization_with_all_relationships(db: Session, organization_id: int) -> Optional[Organization]:
        """Get organization with all related data loaded efficiently."""
        return db.query(Organization).options(
            selectinload(Organization.accounts),
            selectinload(Organization.contacts),
            selectinload(Organization.transactions).selectinload(Transaction.account),
            selectinload(Organization.transactions).selectinload(Transaction.contact),
            selectinload(Organization.invoices).selectinload(Invoice.contact),
            selectinload(Organization.bank_transactions).selectinload(BankTransaction.contact),
            selectinload(Organization.users),
        ).filter(Organization.id == organization_id).first()
    
    @staticmethod
    def get_contacts_with_invoices(db: Session, organization_id: int) -> List[Contact]:
        """Get all contacts for an organization with their invoices loaded."""
        return db.query(Contact).options(
            selectinload(Contact.invoices),
            joinedload(Contact.organization)
        ).filter(Contact.organization_id == organization_id).all()
    
    @staticmethod
    def get_accounts_with_transactions(db: Session, organization_id: int) -> List[Account]:
        """Get all accounts for an organization with their transactions loaded."""
        return db.query(Account).options(
            selectinload(Account.transactions).selectinload(Transaction.contact),
            joinedload(Account.organization)
        ).filter(Account.organization_id == organization_id).all()
    
    @staticmethod
    def get_transactions_with_relationships(
        db: Session, 
        organization_id: int,
        limit: Optional[int] = None
    ) -> List[Transaction]:
        """Get transactions with all related data loaded efficiently."""
        query = db.query(Transaction).options(
            joinedload(Transaction.organization),
            joinedload(Transaction.account),
            joinedload(Transaction.contact)
        ).filter(Transaction.organization_id == organization_id)
        
        if limit:
            query = query.limit(limit)
            
        return query.all()
    
    @staticmethod
    def get_invoices_with_relationships(
        db: Session,
        organization_id: int,
        limit: Optional[int] = None
    ) -> List[Invoice]:
        """Get invoices with all related data loaded efficiently."""
        query = db.query(Invoice).options(
            joinedload(Invoice.organization),
            joinedload(Invoice.contact)
        ).filter(Invoice.organization_id == organization_id)
        
        if limit:
            query = query.limit(limit)
            
        return query.all()
    
    @staticmethod
    def get_bank_transactions_with_relationships(
        db: Session,
        organization_id: int,
        limit: Optional[int] = None
    ) -> List[BankTransaction]:
        """Get bank transactions with all related data loaded efficiently."""
        query = db.query(BankTransaction).options(
            joinedload(BankTransaction.organization),
            joinedload(BankTransaction.contact)
        ).filter(BankTransaction.organization_id == organization_id)
        
        if limit:
            query = query.limit(limit)
            
        return query.all()
    
    @staticmethod
    def get_user_with_organizations(db: Session, user_id: int) -> Optional[User]:
        """Get user with all associated organizations loaded."""
        return db.query(User).options(
            selectinload(User.organizations),
            selectinload(User.organization_associations).selectinload(UserOrganization.organization)
        ).filter(User.id == user_id).first()
    
    @staticmethod
    def get_organization_summary(db: Session, organization_id: int) -> Optional[dict]:
        """Get organization summary with counts to avoid loading large collections."""
        from sqlalchemy import func
        
        org = db.query(Organization).filter(Organization.id == organization_id).first()
        if not org:
            return None
        
        # Get counts efficiently without loading all records
        accounts_count = db.query(func.count(Account.id)).filter(
            Account.organization_id == organization_id
        ).scalar()
        
        contacts_count = db.query(func.count(Contact.id)).filter(
            Contact.organization_id == organization_id
        ).scalar()
        
        transactions_count = db.query(func.count(Transaction.id)).filter(
            Transaction.organization_id == organization_id
        ).scalar()
        
        invoices_count = db.query(func.count(Invoice.id)).filter(
            Invoice.organization_id == organization_id
        ).scalar()
        
        bank_transactions_count = db.query(func.count(BankTransaction.id)).filter(
            BankTransaction.organization_id == organization_id
        ).scalar()
        
        return {
            'organization': org,
            'counts': {
                'accounts': accounts_count,
                'contacts': contacts_count,
                'transactions': transactions_count,
                'invoices': invoices_count,
                'bank_transactions': bank_transactions_count
            }
        }
    
    @staticmethod
    def get_accounts_with_organization(db: Session, organization_id: int, limit: int = 100) -> List[Account]:
        """Get accounts with organization data loaded efficiently."""
        return db.query(Account).options(
            selectinload(Account.organization)
        ).filter(
            Account.organization_id == organization_id
        ).limit(limit).all()
    
    @staticmethod
    def get_contacts_with_organization(db: Session, organization_id: int, limit: int = 100) -> List[Contact]:
        """Get contacts with organization data loaded efficiently."""
        return db.query(Contact).options(
            selectinload(Contact.organization)
        ).filter(
            Contact.organization_id == organization_id
        ).limit(limit).all()
    
    @staticmethod
    def get_organizations_with_relationships(db: Session, limit: int = 10) -> List[Organization]:
        """Get multiple organizations with key relationships loaded."""
        return db.query(Organization).options(
            selectinload(Organization.accounts),
            selectinload(Organization.contacts),
            selectinload(Organization.users)
        ).limit(limit).all()


# Import after class definition to avoid circular imports
from .models import UserOrganization