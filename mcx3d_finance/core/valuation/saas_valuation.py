"""
Comprehensive SaaS-specific valuation models.

This module provides sophisticated valuation methods specifically designed for
Software-as-a-Service businesses, incorporating ARR multiples, unit economics,
and SaaS-specific metrics.
"""

from typing import Dict, Any, List, Optional, Tuple
from decimal import Decimal
from datetime import datetime, timedelta
import logging
import numpy as np
from sqlalchemy.orm import Session

from ..metrics.saas_kpis import SaaSKPICalculator

logger = logging.getLogger(__name__)


class SaaSValuation:
    """
    Comprehensive SaaS valuation model using industry-standard metrics
    and benchmarks specifically designed for recurring revenue businesses.
    """
    
    def __init__(self, db: Session):
        self.db = db
        self.kpi_calculator = SaaSKPICalculator(db)
        
    def calculate_comprehensive_saas_valuation(self,
                                             organization_id: int,
                                             period_start: datetime,
                                             period_end: datetime,
                                             base_arr_multiple: float = 8.0,
                                             include_scenarios: bool = True) -> Dict[str, Any]:
        """
        Calculate comprehensive SaaS valuation using multiple approaches.
        
        Args:
            organization_id: Xero organization ID
            period_start: Analysis period start
            period_end: Analysis period end  
            base_arr_multiple: Base ARR multiple (default: 8x)
            include_scenarios: Whether to include scenario analysis
            
        Returns:
            Comprehensive valuation analysis
        """
        try:
            logger.info(f"Running comprehensive SaaS valuation for org {organization_id}")
            
            # Get comprehensive SaaS KPIs
            kpis = self.kpi_calculator.calculate_comprehensive_kpis(
                organization_id, period_start, period_end
            )
            
            if not kpis or not kpis.get('kpis'):
                logger.error("Unable to calculate SaaS KPIs")
                return {'error': 'Unable to calculate SaaS KPIs'}
            
            saas_metrics = kpis['kpis']
            
            # Multiple valuation approaches
            valuation_methods = {}
            
            # 1. ARR Multiple Valuation
            arr_valuation = self._calculate_arr_multiple_valuation(
                saas_metrics, base_arr_multiple
            )
            valuation_methods['arr_multiple'] = arr_valuation
            
            # 2. Revenue Multiple Valuation  
            revenue_valuation = self._calculate_revenue_multiple_valuation(
                saas_metrics
            )
            valuation_methods['revenue_multiple'] = revenue_valuation
            
            # 3. DCF-Based SaaS Valuation
            dcf_valuation = self._calculate_saas_dcf_valuation(
                saas_metrics, organization_id
            )
            valuation_methods['saas_dcf'] = dcf_valuation
            
            # 4. Unit Economics Valuation
            unit_economics_valuation = self._calculate_unit_economics_valuation(
                saas_metrics
            )
            valuation_methods['unit_economics'] = unit_economics_valuation
            
            # Calculate weighted average valuation
            weighted_valuation = self._calculate_weighted_valuation(valuation_methods)
            
            # Scenario analysis
            scenario_analysis = {}
            if include_scenarios:
                scenario_analysis = self._calculate_scenario_valuations(
                    saas_metrics, base_arr_multiple
                )
            
            # Quality assessment
            quality_assessment = self._assess_business_quality(saas_metrics)
            
            # Industry benchmarks
            benchmarks = self._get_industry_benchmarks(saas_metrics)
            
            return {
                'methodology': 'Comprehensive SaaS Valuation',
                'organization_id': organization_id,
                'valuation_date': datetime.now().isoformat(),
                'analysis_period': {
                    'start': period_start.isoformat(),
                    'end': period_end.isoformat()
                },
                'key_metrics': self._extract_key_metrics(saas_metrics),
                'valuation_methods': valuation_methods,
                'weighted_valuation': weighted_valuation,
                'scenario_analysis': scenario_analysis,
                'quality_assessment': quality_assessment,
                'industry_benchmarks': benchmarks,
                'full_kpi_analysis': kpis
            }
            
        except Exception as e:
            logger.error(f"Error in comprehensive SaaS valuation: {e}")
            return {'error': str(e)}
    
    def _calculate_arr_multiple_valuation(self, 
                                        saas_metrics: Dict[str, Any], 
                                        base_multiple: float) -> Dict[str, Any]:
        """Calculate ARR-based valuation with quality adjustments."""
        try:
            revenue_metrics = saas_metrics.get('revenue_metrics', {})
            customer_metrics = saas_metrics.get('customer_metrics', {})
            
            arr = revenue_metrics.get('annual_recurring_revenue', 0)
            
            if arr <= 0:
                return {'error': 'No ARR data available'}
            
            # Base valuation
            base_valuation = arr * base_multiple
            
            # Quality adjustments
            adjustments = {}
            
            # Growth rate adjustment (-20% to +50%)
            growth_rate = revenue_metrics.get('revenue_growth_rate', {}).get('annual', 0)
            if growth_rate > 40:
                adjustments['exceptional_growth'] = 0.50
            elif growth_rate > 25:
                adjustments['high_growth'] = 0.30
            elif growth_rate > 15:
                adjustments['good_growth'] = 0.15
            elif growth_rate < 5:
                adjustments['low_growth'] = -0.20
            elif growth_rate < 0:
                adjustments['declining_growth'] = -0.35
            
            # Unit economics adjustment (-25% to +25%)
            ltv_cac_ratio = customer_metrics.get('ltv_cac_ratio', 0)
            if ltv_cac_ratio > 8:
                adjustments['excellent_unit_economics'] = 0.25
            elif ltv_cac_ratio > 5:
                adjustments['good_unit_economics'] = 0.15
            elif ltv_cac_ratio > 3:
                adjustments['adequate_unit_economics'] = 0.05
            elif ltv_cac_ratio < 2:
                adjustments['poor_unit_economics'] = -0.25
            
            # Churn adjustment (-20% to +15%)
            churn_rate = customer_metrics.get('customer_churn_rate', 0)
            if churn_rate < 2:
                adjustments['excellent_retention'] = 0.15
            elif churn_rate < 5:
                adjustments['good_retention'] = 0.10
            elif churn_rate > 10:
                adjustments['high_churn'] = -0.20
            
            # Net Revenue Retention adjustment (-15% to +20%)
            nrr = revenue_metrics.get('net_revenue_retention', 0)
            if nrr > 120:
                adjustments['strong_expansion'] = 0.20
            elif nrr > 110:
                adjustments['good_expansion'] = 0.10
            elif nrr < 90:
                adjustments['revenue_contraction'] = -0.15
            
            # Scale adjustment (0% to +10%)
            if arr > 100000000:  # $100M+ ARR
                adjustments['enterprise_scale'] = 0.10
            elif arr > 50000000:  # $50M+ ARR
                adjustments['large_scale'] = 0.05
            
            # Apply adjustments
            total_adjustment = sum(adjustments.values())
            adjusted_multiple = base_multiple * (1 + total_adjustment)
            adjusted_valuation = arr * adjusted_multiple
            
            return {
                'base_arr': arr,
                'base_multiple': base_multiple,
                'base_valuation': base_valuation,
                'adjustments': adjustments,
                'total_adjustment': total_adjustment,
                'adjusted_multiple': adjusted_multiple,
                'adjusted_valuation': adjusted_valuation,
                'confidence_score': self._calculate_confidence_score(adjustments)
            }
            
        except Exception as e:
            logger.error(f"Error calculating ARR multiple valuation: {e}")
            return {'error': str(e)}
    
    def _calculate_revenue_multiple_valuation(self, saas_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate revenue multiple valuation."""
        try:
            revenue_metrics = saas_metrics.get('revenue_metrics', {})
            
            # Use total revenue if ARR not available
            annual_revenue = revenue_metrics.get('annual_recurring_revenue', 0)
            if annual_revenue <= 0:
                # Estimate from MRR if available
                mrr = revenue_metrics.get('monthly_recurring_revenue', 0)
                annual_revenue = mrr * 12
            
            if annual_revenue <= 0:
                return {'error': 'No revenue data available'}
            
            # SaaS revenue multiples typically range 4x-15x
            # Based on growth rate and margins
            growth_rate = revenue_metrics.get('revenue_growth_rate', {}).get('annual', 0)
            gross_margin = revenue_metrics.get('gross_margin', 70) / 100  # Convert to decimal
            
            # Base multiple calculation
            if growth_rate > 50:
                base_multiple = 12.0
            elif growth_rate > 30:
                base_multiple = 8.0
            elif growth_rate > 20:
                base_multiple = 6.0
            elif growth_rate > 10:
                base_multiple = 4.5
            else:
                base_multiple = 3.0
            
            # Margin adjustment
            if gross_margin > 0.85:
                base_multiple *= 1.2
            elif gross_margin > 0.75:
                base_multiple *= 1.1
            elif gross_margin < 0.60:
                base_multiple *= 0.8
            
            valuation = annual_revenue * base_multiple
            
            return {
                'annual_revenue': annual_revenue,
                'growth_rate': growth_rate,
                'gross_margin': gross_margin,
                'revenue_multiple': base_multiple,
                'valuation': valuation
            }
            
        except Exception as e:
            logger.error(f"Error calculating revenue multiple valuation: {e}")
            return {'error': str(e)}
    
    def _calculate_saas_dcf_valuation(self, 
                                    saas_metrics: Dict[str, Any],
                                    organization_id: int) -> Dict[str, Any]:
        """Calculate DCF valuation optimized for SaaS businesses."""
        try:
            revenue_metrics = saas_metrics.get('revenue_metrics', {})
            customer_metrics = saas_metrics.get('customer_metrics', {})
            
            arr = revenue_metrics.get('annual_recurring_revenue', 0)
            growth_rate = revenue_metrics.get('revenue_growth_rate', {}).get('annual', 0) / 100
            
            if arr <= 0:
                return {'error': 'No ARR data available for DCF'}
            
            # Project 5 years of cash flows
            projections = []
            current_arr = arr
            
            # SaaS-specific assumptions
            gross_margin = 0.80  # Typical SaaS gross margin
            opex_as_percent_of_revenue = 0.70  # Operating expenses
            tax_rate = 0.25
            discount_rate = 0.12  # SaaS discount rate
            terminal_growth = 0.025  # Long-term growth
            
            for year in range(1, 6):
                # Revenue growth typically decelerates
                year_growth = growth_rate * (0.85 ** year)  # Deceleration factor
                current_arr *= (1 + year_growth)
                
                # Calculate cash flows
                gross_profit = current_arr * gross_margin
                operating_expenses = current_arr * opex_as_percent_of_revenue
                ebitda = gross_profit - operating_expenses
                
                # Minimal CapEx for SaaS
                capex = current_arr * 0.02
                ebit = ebitda - capex  # Simplified (no depreciation)
                
                taxes = ebit * tax_rate if ebit > 0 else 0
                nopat = ebit - taxes
                
                # Free cash flow
                fcf = nopat + capex  # Add back capex (no working capital change)
                
                projections.append({
                    'year': year,
                    'arr': current_arr,
                    'growth_rate': year_growth,
                    'free_cash_flow': fcf
                })
            
            # Calculate present value
            pv_fcf = 0
            for proj in projections:
                pv_fcf += proj['free_cash_flow'] / ((1 + discount_rate) ** proj['year'])
            
            # Terminal value
            terminal_fcf = projections[-1]['free_cash_flow'] * (1 + terminal_growth)
            terminal_value = terminal_fcf / (discount_rate - terminal_growth)
            pv_terminal = terminal_value / ((1 + discount_rate) ** 5)
            
            enterprise_value = pv_fcf + pv_terminal
            
            return {
                'projections': projections,
                'pv_operating_fcf': pv_fcf,
                'terminal_value': terminal_value,
                'pv_terminal_value': pv_terminal,
                'enterprise_value': enterprise_value,
                'discount_rate': discount_rate,
                'terminal_growth': terminal_growth
            }
            
        except Exception as e:
            logger.error(f"Error calculating SaaS DCF valuation: {e}")
            return {'error': str(e)}
    
    def _calculate_unit_economics_valuation(self, saas_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate valuation based on unit economics."""
        try:
            customer_metrics = saas_metrics.get('customer_metrics', {})
            
            ltv = customer_metrics.get('customer_lifetime_value', 0)
            cac = customer_metrics.get('customer_acquisition_cost', 0)
            active_customers = customer_metrics.get('active_customers', 0)
            
            if ltv <= 0 or cac <= 0 or active_customers <= 0:
                return {'error': 'Insufficient unit economics data'}
            
            # Customer base value
            customer_base_value = active_customers * ltv
            
            # Subtract acquisition costs
            total_acquisition_cost = active_customers * cac
            net_customer_value = customer_base_value - total_acquisition_cost
            
            # Add customer growth potential
            monthly_new_customers = customer_metrics.get('new_customers_this_month', 0)
            if monthly_new_customers > 0:
                # Project customer growth
                annual_new_customers = monthly_new_customers * 12
                future_customer_value = annual_new_customers * ltv * 3  # 3-year outlook
                future_acquisition_cost = annual_new_customers * cac * 3
                
                net_future_value = future_customer_value - future_acquisition_cost
                # Discount future value
                discounted_future_value = net_future_value / 1.5  # Simple discount
                
                total_valuation = net_customer_value + discounted_future_value
            else:
                total_valuation = net_customer_value
            
            return {
                'active_customers': active_customers,
                'ltv': ltv,
                'cac': cac,
                'ltv_cac_ratio': ltv / cac if cac > 0 else 0,
                'customer_base_value': customer_base_value,
                'total_acquisition_cost': total_acquisition_cost,
                'net_customer_value': net_customer_value,
                'total_valuation': total_valuation
            }
            
        except Exception as e:
            logger.error(f"Error calculating unit economics valuation: {e}")
            return {'error': str(e)}
    
    def _calculate_weighted_valuation(self, valuation_methods: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate weighted average of different valuation methods."""
        try:
            valid_valuations = []
            weights = {
                'arr_multiple': 0.4,    # Primary method for SaaS
                'revenue_multiple': 0.2,
                'saas_dcf': 0.3,       # Important for mature SaaS
                'unit_economics': 0.1   # Supporting method
            }
            
            total_weight = 0
            weighted_sum = 0
            
            for method, data in valuation_methods.items():
                if 'error' not in data:
                    weight = weights.get(method, 0.25)  # Default weight
                    
                    # Get valuation from different method structures
                    if method == 'arr_multiple':
                        valuation = data.get('adjusted_valuation', 0)
                    elif method == 'revenue_multiple':
                        valuation = data.get('valuation', 0)
                    elif method == 'saas_dcf':
                        valuation = data.get('enterprise_value', 0)
                    elif method == 'unit_economics':
                        valuation = data.get('total_valuation', 0)
                    else:
                        valuation = 0
                    
                    if valuation > 0:
                        valid_valuations.append({
                            'method': method,
                            'valuation': valuation,
                            'weight': weight
                        })
                        
                        weighted_sum += valuation * weight
                        total_weight += weight
            
            if total_weight > 0:
                weighted_average = weighted_sum / total_weight
                
                # Calculate range
                valuations = [v['valuation'] for v in valid_valuations]
                min_val = min(valuations)
                max_val = max(valuations)
                
                return {
                    'weighted_average_valuation': weighted_average,
                    'methods_used': len(valid_valuations),
                    'valuation_range': {
                        'minimum': min_val,
                        'maximum': max_val,
                        'spread_percentage': ((max_val - min_val) / weighted_average * 100) if weighted_average > 0 else 0
                    },
                    'individual_valuations': valid_valuations
                }
            else:
                return {'error': 'No valid valuations calculated'}
                
        except Exception as e:
            logger.error(f"Error calculating weighted valuation: {e}")
            return {'error': str(e)}
    
    def _calculate_scenario_valuations(self, 
                                     saas_metrics: Dict[str, Any], 
                                     base_arr_multiple: float) -> Dict[str, Any]:
        """Calculate bull/base/bear scenario valuations."""
        try:
            scenarios = {
                'bear': {'growth_mult': 0.5, 'multiple_mult': 0.7},
                'base': {'growth_mult': 1.0, 'multiple_mult': 1.0},
                'bull': {'growth_mult': 1.5, 'multiple_mult': 1.3}
            }
            
            scenario_results = {}
            
            for scenario_name, multipliers in scenarios.items():
                # Adjust metrics for scenario
                adjusted_metrics = saas_metrics.copy()
                
                # Adjust growth rates
                if 'revenue_metrics' in adjusted_metrics:
                    revenue_metrics = adjusted_metrics['revenue_metrics'].copy()
                    if 'revenue_growth_rate' in revenue_metrics:
                        growth_rates = revenue_metrics['revenue_growth_rate'].copy()
                        for period in growth_rates:
                            growth_rates[period] *= multipliers['growth_mult']
                        revenue_metrics['revenue_growth_rate'] = growth_rates
                    adjusted_metrics['revenue_metrics'] = revenue_metrics
                
                # Calculate scenario valuation
                scenario_multiple = base_arr_multiple * multipliers['multiple_mult']
                scenario_valuation = self._calculate_arr_multiple_valuation(
                    adjusted_metrics, scenario_multiple
                )
                
                scenario_results[scenario_name] = scenario_valuation
            
            return scenario_results
            
        except Exception as e:
            logger.error(f"Error calculating scenario valuations: {e}")
            return {'error': str(e)}
    
    def _assess_business_quality(self, saas_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall SaaS business quality."""
        try:
            revenue_metrics = saas_metrics.get('revenue_metrics', {})
            customer_metrics = saas_metrics.get('customer_metrics', {})
            
            quality_scores = {}
            
            # Growth quality (0-25 points)
            growth_rate = revenue_metrics.get('revenue_growth_rate', {}).get('annual', 0)
            if growth_rate > 40:
                quality_scores['growth'] = 25
            elif growth_rate > 25:
                quality_scores['growth'] = 20
            elif growth_rate > 15:
                quality_scores['growth'] = 15
            elif growth_rate > 5:
                quality_scores['growth'] = 10
            else:
                quality_scores['growth'] = 0
            
            # Unit economics (0-25 points)
            ltv_cac_ratio = customer_metrics.get('ltv_cac_ratio', 0)
            if ltv_cac_ratio > 8:
                quality_scores['unit_economics'] = 25
            elif ltv_cac_ratio > 5:
                quality_scores['unit_economics'] = 20
            elif ltv_cac_ratio > 3:
                quality_scores['unit_economics'] = 15
            else:
                quality_scores['unit_economics'] = 5
            
            # Retention (0-25 points)
            churn_rate = customer_metrics.get('customer_churn_rate', 0)
            if churn_rate < 2:
                quality_scores['retention'] = 25
            elif churn_rate < 5:
                quality_scores['retention'] = 20
            elif churn_rate < 8:
                quality_scores['retention'] = 15
            else:
                quality_scores['retention'] = 5
            
            # Revenue expansion (0-25 points)
            nrr = revenue_metrics.get('net_revenue_retention', 0)
            if nrr > 120:
                quality_scores['expansion'] = 25
            elif nrr > 110:
                quality_scores['expansion'] = 20
            elif nrr > 100:
                quality_scores['expansion'] = 15
            else:
                quality_scores['expansion'] = 5
            
            total_score = sum(quality_scores.values())
            
            # Quality grade
            if total_score >= 90:
                grade = 'A+'
                quality_tier = 'Exceptional'
            elif total_score >= 80:
                grade = 'A'
                quality_tier = 'High Quality'
            elif total_score >= 70:
                grade = 'B+'
                quality_tier = 'Good Quality'
            elif total_score >= 60:
                grade = 'B'
                quality_tier = 'Average Quality'
            else:
                grade = 'C'
                quality_tier = 'Below Average'
            
            return {
                'total_score': total_score,
                'max_score': 100,
                'grade': grade,
                'quality_tier': quality_tier,
                'component_scores': quality_scores
            }
            
        except Exception as e:
            logger.error(f"Error assessing business quality: {e}")
            return {'error': str(e)}
    
    def _get_industry_benchmarks(self, saas_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Get industry benchmarks for comparison."""
        # Industry benchmarks for SaaS businesses
        benchmarks = {
            'arr_multiples': {
                'high_growth': {'min': 10, 'median': 15, 'max': 25},
                'moderate_growth': {'min': 6, 'median': 10, 'max': 15},
                'low_growth': {'min': 3, 'median': 6, 'max': 10}
            },
            'key_metrics': {
                'growth_rate': {'excellent': 40, 'good': 25, 'average': 15, 'poor': 5},
                'churn_rate': {'excellent': 2, 'good': 5, 'average': 8, 'poor': 12},
                'ltv_cac_ratio': {'excellent': 8, 'good': 5, 'average': 3, 'poor': 2},
                'net_revenue_retention': {'excellent': 120, 'good': 110, 'average': 100, 'poor': 90},
                'gross_margin': {'excellent': 85, 'good': 80, 'average': 75, 'poor': 65}
            }
        }
        
        return benchmarks
    
    def _extract_key_metrics(self, saas_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Extract key metrics for summary."""
        revenue_metrics = saas_metrics.get('revenue_metrics', {})
        customer_metrics = saas_metrics.get('customer_metrics', {})
        
        return {
            'arr': revenue_metrics.get('annual_recurring_revenue', 0),
            'mrr': revenue_metrics.get('monthly_recurring_revenue', 0),
            'growth_rate': revenue_metrics.get('revenue_growth_rate', {}).get('annual', 0),
            'churn_rate': customer_metrics.get('customer_churn_rate', 0),
            'ltv_cac_ratio': customer_metrics.get('ltv_cac_ratio', 0),
            'nrr': revenue_metrics.get('net_revenue_retention', 0),
            'active_customers': customer_metrics.get('active_customers', 0)
        }
    
    def _calculate_confidence_score(self, adjustments: Dict[str, float]) -> float:
        """Calculate confidence score based on data quality and adjustments."""
        try:
            # Base confidence
            confidence = 0.7
            
            # Reduce confidence for extreme adjustments
            total_adjustment = abs(sum(adjustments.values()))
            if total_adjustment > 0.5:
                confidence -= 0.2
            elif total_adjustment > 0.3:
                confidence -= 0.1
            
            # Increase confidence for positive factors
            positive_adjustments = [adj for adj in adjustments.values() if adj > 0]
            if len(positive_adjustments) >= 3:
                confidence += 0.1
            
            return max(0.3, min(0.95, confidence))
            
        except Exception as e:
            logger.error(f"Error calculating confidence score: {e}")
            return 0.5