# tests/fixtures/valuation_data.py

import pytest
from decimal import Decimal


@pytest.fixture
def sample_dcf_data():
    """Basic DCF test data for simple testing scenarios."""
    return {
        "company_name": "Test Corp",
        "valuation_date": "2024-01-01",
        "currency": "USD", 
        "industry": "Technology",
        "projections": {
            "revenue": [Decimal("1000000"), Decimal("1200000"), Decimal("1440000"), Decimal("1728000"), Decimal("2073600")],
            "operating_expenses": [Decimal("700000"), Decimal("780000"), Decimal("864000"), Decimal("950400"), Decimal("1038000")],
            "capex": [Decimal("50000"), Decimal("60000"), Decimal("70000"), Decimal("80000"), Decimal("90000")],
            "depreciation": [Decimal("40000"), Decimal("48000"), Decimal("56000"), Decimal("64000"), Decimal("72000")],
            "working_capital_changes": [Decimal("25000"), Decimal("30000"), Decimal("36000"), Decimal("43200"), Decimal("51840")]
        },
        "assumptions": {
            "discount_rate": Decimal("0.10"),
            "terminal_growth": Decimal("0.03"),
            "tax_rate": Decimal("0.25"),
            "years": 5
        },
        "market_data": {
            "risk_free_rate": Decimal("0.035"),
            "market_risk_premium": Decimal("0.065"),
            "beta": Decimal("1.2"),
            "debt_to_equity": Decimal("0.30")
        }
    }


@pytest.fixture
def sample_saas_data():
    """Basic SaaS test data for simple testing scenarios."""
    return {
        "company_name": "SaaS Startup",
        "valuation_date": "2024-01-01",
        "currency": "USD",
        "metrics": {
            "arr": [Decimal("500000"), Decimal("750000"), Decimal("1125000"), Decimal("1687500"), Decimal("2531250")],
            "monthly_churn_rate": [0.05, 0.04, 0.035, 0.03, 0.025],
            "customer_acquisition_cost": Decimal("150"),
            "average_revenue_per_user": Decimal("500"),
            "gross_margin": Decimal("0.85"),
            "customer_lifetime_value": Decimal("2000"),
            "net_revenue_retention": Decimal("1.15"),
            "magic_number": Decimal("1.25")
        },
        "assumptions": {
            "discount_rate": Decimal("0.15"),
            "terminal_multiple": Decimal("8.0"),
            "growth_efficiency": Decimal("0.75")
        },
        "benchmarks": {
            "revenue_multiple_range": (6, 12),
            "growth_rate_median": Decimal("0.40"),
            "churn_rate_benchmark": Decimal("0.035"),
            "cac_payback_benchmark": 10
        }
    }


@pytest.fixture
def comprehensive_dcf_data():
    """Comprehensive DCF test data with detailed projections and market data."""
    return {
        "company_name": "Enterprise Tech Solutions Inc.",
        "valuation_date": "2024-01-15",
        "currency": "USD",
        "industry": "Enterprise Software",
        "description": "Leading provider of enterprise software solutions",
        "projections": {
            "revenue": [
                Decimal("8500000"),   # Year 1: $8.5M
                Decimal("10625000"),  # Year 2: $10.625M (25% growth)
                Decimal("12750000"),  # Year 3: $12.75M (20% growth)
                Decimal("14875000"),  # Year 4: $14.875M (16.7% growth)
                Decimal("16762500"),  # Year 5: $16.763M (12.7% growth)
                Decimal("18438750"),  # Year 6: $18.439M (10% growth)
                Decimal("20099063"),  # Year 7: $20.099M (9% growth)
                Decimal("21505998")   # Year 8: $21.506M (7% growth)
            ],
            "operating_expenses": [
                Decimal("5950000"),   # 70% of revenue
                Decimal("6375000"),   # 60% of revenue
                Decimal("7012500"),   # 55% of revenue
                Decimal("7437500"),   # 50% of revenue
                Decimal("7543125"),   # 45% of revenue
                Decimal("7929750"),   # 43% of revenue
                Decimal("8440778"),   # 42% of revenue
                Decimal("8962519")    # 41.7% of revenue
            ],
            "capex": [
                Decimal("425000"),    # 5% of revenue
                Decimal("531250"),    # 5% of revenue
                Decimal("637500"),    # 5% of revenue
                Decimal("743750"),    # 5% of revenue
                Decimal("838125"),    # 5% of revenue
                Decimal("921938"),    # 5% of revenue
                Decimal("1004953"),   # 5% of revenue
                Decimal("1075300")    # 5% of revenue
            ],
            "depreciation": [
                Decimal("340000"),
                Decimal("425000"),
                Decimal("531250"),
                Decimal("637500"),
                Decimal("743750"),
                Decimal("838125"),
                Decimal("921938"),
                Decimal("1004953")
            ],
            "working_capital_changes": [
                Decimal("170000"),    # 2% of revenue
                Decimal("212500"),    # 2% of revenue
                Decimal("255000"),    # 2% of revenue
                Decimal("297500"),    # 2% of revenue
                Decimal("335250"),    # 2% of revenue
                Decimal("368775"),    # 2% of revenue
                Decimal("401981"),    # 2% of revenue
                Decimal("430120")     # 2% of revenue
            ]
        },
        "assumptions": {
            "discount_rate": Decimal("0.11"),      # 11% WACC
            "terminal_growth": Decimal("0.025"),   # 2.5% long-term growth
            "tax_rate": Decimal("0.21"),           # 21% corporate tax
            "years": 8
        },
        "market_data": {
            "risk_free_rate": Decimal("0.04"),
            "market_risk_premium": Decimal("0.06"),
            "beta": Decimal("1.18"),
            "debt_to_equity": Decimal("0.25"),
            "cost_of_debt": Decimal("0.045"),
            "market_cap": Decimal("25000000"),
            "total_debt": Decimal("6250000")
        },
        "sensitivity_analysis": {
            "discount_rate_range": [Decimal("0.09"), Decimal("0.13")],
            "terminal_growth_range": [Decimal("0.015"), Decimal("0.035")],
            "revenue_growth_scenarios": {
                "base": 1.0,
                "optimistic": 1.15,
                "pessimistic": 0.85
            }
        }
    }


@pytest.fixture
def high_growth_saas_data():
    """High-growth SaaS company data for advanced testing scenarios."""
    return {
        "company_name": "RocketScale SaaS Corp",
        "valuation_date": "2024-01-20",
        "currency": "USD",
        "industry": "SaaS - Vertical",
        "stage": "Series B",
        "metrics": {
            "arr": [
                Decimal("12000000"),   # $12M ARR
                Decimal("21600000"),   # $21.6M ARR (80% growth)
                Decimal("34560000"),   # $34.56M ARR (60% growth)
                Decimal("48384000"),   # $48.384M ARR (40% growth)
                Decimal("62899200"),   # $62.9M ARR (30% growth)
                Decimal("75479040"),   # $75.48M ARR (20% growth)
                Decimal("86801696")    # $86.8M ARR (15% growth)
            ],
            "monthly_churn_rate": [0.06, 0.05, 0.04, 0.035, 0.03, 0.025, 0.022],
            "customer_acquisition_cost": Decimal("850"),
            "average_revenue_per_user": Decimal("2400"),
            "gross_margin": Decimal("0.89"),
            "customer_lifetime_value": Decimal("12000"),
            "net_revenue_retention": Decimal("1.35"),
            "magic_number": Decimal("1.8"),
            "total_customers": [5000, 9000, 14400, 20160, 26208, 31450, 36083],
            "expansion_revenue_rate": Decimal("0.25"),
            "upsell_rate": Decimal("0.35")
        },
        "unit_economics": {
            "ltv_cac_ratio": Decimal("14.12"),
            "cac_payback_months": 6,
            "gross_revenue_retention": Decimal("0.94"),
            "net_dollar_retention": Decimal("135")
        },
        "assumptions": {
            "discount_rate": Decimal("0.12"),      # 12% for high-growth SaaS
            "terminal_multiple": Decimal("15.0"),  # 15x ARR terminal value
            "growth_efficiency": Decimal("0.85"),
            "mature_growth_rate": Decimal("0.15"),
            "terminal_margin": Decimal("0.25")
        },
        "benchmarks": {
            "revenue_multiple_range": (12, 25),
            "growth_rate_median": Decimal("0.65"),
            "churn_rate_benchmark": Decimal("0.025"),
            "cac_payback_benchmark": 8,
            "nrr_benchmark": Decimal("1.30"),
            "magic_number_benchmark": Decimal("1.5")
        },
        "market_sizing": {
            "tam": Decimal("*********00"),  # $50B TAM
            "sam": Decimal("*********0"),   # $8B SAM
            "som": Decimal("*********")     # $800M SOM
        }
    }


@pytest.fixture
def distressed_company_data():
    """Distressed company scenario for edge case testing."""
    return {
        "company_name": "TurnAround Manufacturing Co.",
        "valuation_date": "2024-01-25",
        "currency": "USD",
        "industry": "Manufacturing",
        "stage": "Distressed",
        "projections": {
            "revenue": [
                Decimal("5000000"),    # Year 1: $5M (declining)
                Decimal("4250000"),    # Year 2: $4.25M (-15% decline)
                Decimal("4037500"),    # Year 3: $4.038M (-5% decline)
                Decimal("4239375"),    # Year 4: $4.239M (+5% recovery)
                Decimal("4663313"),    # Year 5: $4.663M (+10% growth)
                Decimal("5363510")     # Year 6: $5.364M (+15% growth)
            ],
            "operating_expenses": [
                Decimal("4500000"),    # 90% of revenue (high costs)
                Decimal("3612500"),    # 85% of revenue (cost cutting)
                Decimal("3230000"),    # 80% of revenue (efficiency)
                Decimal("3391500"),    # 80% of revenue
                Decimal("3730650"),    # 80% of revenue
                Decimal("4290808")     # 80% of revenue
            ],
            "capex": [
                Decimal("100000"),     # Minimal CapEx
                Decimal("127500"),     # 3% of revenue
                Decimal("161500"),     # 4% of revenue
                Decimal("211969"),     # 5% of revenue
                Decimal("279999"),     # 6% of revenue (recovery)
                Decimal("375246")      # 7% of revenue
            ],
            "depreciation": [
                Decimal("800000"),     # High depreciation
                Decimal("750000"),
                Decimal("700000"),
                Decimal("650000"),
                Decimal("600000"),
                Decimal("550000")
            ],
            "working_capital_changes": [
                Decimal("-250000"),    # Cash release from WC
                Decimal("-106250"),    # Continued release
                Decimal("40375"),      # Rebuilding WC
                Decimal("84788"),      # 2% of revenue
                Decimal("116663"),     # 2.5% of revenue
                Decimal("134088")      # 2.5% of revenue
            ]
        },
        "assumptions": {
            "discount_rate": Decimal("0.18"),      # 18% high risk discount
            "terminal_growth": Decimal("0.02"),    # 2% terminal growth
            "tax_rate": Decimal("0.15"),           # Lower effective tax rate
            "years": 6
        },
        "market_data": {
            "risk_free_rate": Decimal("0.035"),
            "market_risk_premium": Decimal("0.065"),
            "beta": Decimal("2.2"),               # High beta (risky)
            "debt_to_equity": Decimal("1.5"),     # High leverage
            "cost_of_debt": Decimal("0.08")       # High cost of debt
        },
        "restructuring_plan": {
            "asset_sales": Decimal("2000000"),
            "debt_reduction": Decimal("3000000"),
            "employee_reduction_savings": Decimal("500000"),
            "facility_consolidation_savings": Decimal("300000")
        }
    }


@pytest.fixture
def benchmark_dataset():
    """Benchmark dataset for performance testing and regression analysis."""
    return {
        "performance_benchmarks": {
            "small_company_dcf": {
                "revenue_range": [Decimal("1000000"), Decimal("5000000")],
                "expected_calculation_time": 0.1,  # seconds
                "expected_pdf_size_range": [15000, 50000],  # bytes
                "expected_excel_size_range": [8000, 25000]   # bytes
            },
            "medium_company_dcf": {
                "revenue_range": [Decimal("5000000"), Decimal("50000000")],
                "expected_calculation_time": 0.25,
                "expected_pdf_size_range": [25000, 75000],
                "expected_excel_size_range": [15000, 45000]
            },
            "large_company_dcf": {
                "revenue_range": [Decimal("50000000"), Decimal("*********")],
                "expected_calculation_time": 0.5,
                "expected_pdf_size_range": [50000, 150000],
                "expected_excel_size_range": [30000, 90000]
            },
            "high_growth_saas": {
                "arr_range": [Decimal("10000000"), Decimal("*********")],
                "expected_calculation_time": 0.3,
                "expected_pdf_size_range": [20000, 80000],
                "expected_excel_size_range": [12000, 50000]
            }
        },
        "quality_thresholds": {
            "min_pdf_pages": 3,
            "max_pdf_pages": 15,
            "min_excel_sheets": 3,
            "max_excel_sheets": 8,
            "required_dcf_sections": [
                "Executive Summary",
                "Financial Projections",
                "DCF Calculation",
                "Sensitivity Analysis",
                "Assumptions"
            ],
            "required_saas_sections": [
                "Executive Summary",
                "SaaS Metrics",
                "Unit Economics",
                "Benchmarking",
                "Valuation Summary"
            ]
        },
        "error_scenarios": {
            "negative_revenue": {
                "projections": {"revenue": [Decimal("-1000000")]},
                "expected_error": "ValueError"
            },
            "zero_discount_rate": {
                "assumptions": {"discount_rate": Decimal("0")},
                "expected_error": "ValueError"
            },
            "missing_projections": {
                "projections": {},
                "expected_error": "KeyError"
            }
        }
    }


# Utility fixtures for data generation
@pytest.fixture
def random_dcf_generator():
    """Generator function for creating random DCF test data."""
    import random
    
    def generate_dcf_data(seed=None, years=5, base_revenue=1000000):
        if seed:
            random.seed(seed)
            
        growth_rates = [random.uniform(0.05, 0.50) for _ in range(years)]
        
        revenue = [base_revenue]
        for i in range(1, years):
            revenue.append(revenue[-1] * (1 + growth_rates[i-1]))
            
        operating_expenses = [rev * random.uniform(0.60, 0.80) for rev in revenue]
        capex = [rev * random.uniform(0.03, 0.08) for rev in revenue]
        depreciation = [capex[max(0, i-1)] * random.uniform(0.15, 0.25) for i in range(years)]
        wc_changes = [rev * random.uniform(0.01, 0.03) for rev in revenue]
        
        return {
            "company_name": f"Random Test Corp {seed or random.randint(1000, 9999)}",
            "valuation_date": "2024-01-01",
            "currency": "USD",
            "projections": {
                "revenue": [Decimal(str(int(r))) for r in revenue],
                "operating_expenses": [Decimal(str(int(e))) for e in operating_expenses],
                "capex": [Decimal(str(int(c))) for c in capex],
                "depreciation": [Decimal(str(int(d))) for d in depreciation],
                "working_capital_changes": [Decimal(str(int(w))) for w in wc_changes]
            },
            "assumptions": {
                "discount_rate": Decimal(str(round(random.uniform(0.08, 0.18), 3))),
                "terminal_growth": Decimal(str(round(random.uniform(0.02, 0.04), 3))),
                "tax_rate": Decimal(str(round(random.uniform(0.20, 0.30), 2))),
                "years": years
            }
        }
    
    return generate_dcf_data


@pytest.fixture
def test_data_validator():
    """Validator for ensuring test data integrity."""
    def validate_dcf_data(dcf_data):
        """Validate DCF data structure and business logic."""
        required_fields = ["company_name", "projections", "assumptions"]
        for field in required_fields:
            assert field in dcf_data, f"Missing required field: {field}"
            
        required_projections = ["revenue", "operating_expenses", "capex", "depreciation", "working_capital_changes"]
        for projection in required_projections:
            assert projection in dcf_data["projections"], f"Missing projection: {projection}"
            
        # Business logic validations
        revenue = dcf_data["projections"]["revenue"]
        expenses = dcf_data["projections"]["operating_expenses"]
        
        assert len(revenue) == len(expenses), "Revenue and expenses must have same length"
        assert all(r > 0 for r in revenue), "Revenue must be positive"
        assert all(e >= 0 for e in expenses), "Operating expenses must be non-negative"
        
        # Financial ratios validation
        for i, (rev, exp) in enumerate(zip(revenue, expenses)):
            margin = (rev - exp) / rev
            assert -0.5 <= margin <= 0.8, f"Unrealistic operating margin in year {i+1}: {margin:.2%}"
            
        # Assumptions validation
        discount_rate = dcf_data["assumptions"]["discount_rate"]
        assert 0.01 <= discount_rate <= 0.50, f"Unrealistic discount rate: {discount_rate:.2%}"
        
        return True
        
    def validate_saas_data(saas_data):
        """Validate SaaS data structure and metrics."""
        required_fields = ["company_name", "metrics"]
        for field in required_fields:
            assert field in saas_data, f"Missing required field: {field}"
            
        metrics = saas_data["metrics"]
        required_metrics = ["arr", "monthly_churn_rate", "customer_acquisition_cost"]
        for metric in required_metrics:
            assert metric in metrics, f"Missing SaaS metric: {metric}"
            
        # SaaS-specific validations
        arr = metrics["arr"]
        churn_rates = metrics["monthly_churn_rate"]
        
        assert all(a > 0 for a in arr), "ARR must be positive"
        assert all(0 <= c <= 0.20 for c in churn_rates), "Monthly churn rate must be between 0% and 20%"
        
        if "ltv_cac_ratio" in metrics:
            ltv_cac = metrics["ltv_cac_ratio"]
            assert ltv_cac >= 1, "LTV/CAC ratio must be at least 1.0"
            
        return True
    
    return {"validate_dcf": validate_dcf_data, "validate_saas": validate_saas_data}
