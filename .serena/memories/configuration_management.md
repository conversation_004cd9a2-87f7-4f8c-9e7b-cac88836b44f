# MCX3D Finance Configuration Management - Enhanced

## Configuration Architecture

### Multi-Layer Configuration System
The application uses a sophisticated multi-layer configuration system with environment detection:

1. **Primary Configuration**: `config.yml` - Main application settings
2. **Monitoring Configuration**: `mcx3d_finance/config/monitoring.yml` - Environment-specific monitoring thresholds
3. **Environment Variables**: Override any file-based settings
4. **Runtime Configuration**: Dynamic configuration loading and validation

### Configuration Loading Priority
1. **Environment Variables**: Highest priority, overrides all file-based settings
2. **Monitoring Configuration**: Environment-specific monitoring thresholds from `monitoring.yml`
3. **Primary Configuration**: Application settings from `config.yml`
4. **Default Fallbacks**: Built-in safe defaults for production environments

### Environment Detection & Configuration
- **Environment Variables**: `ENVIRONMENT`, `ENV`, or `APP_ENV`
- **Default Behavior**: Production-safe defaults (fail-secure approach)
- **Supported Environments**: development, testing, staging, production

## Enhanced Monitoring Configuration (`mcx3d_finance/config/monitoring.yml`)

### Environment-Specific Health Thresholds

#### Database Health Monitoring
```yaml
database:
  max_response_time_ms: 50-200      # Environment-specific response time limits
  max_active_connections: 20-100    # Connection pool monitoring
  critical_response_time_ms: 500-2000  # Critical threshold alerts
  max_connection_usage_percent: 70-80  # Connection pool utilization
```

#### Redis Performance Monitoring
```yaml
redis:
  max_response_time_ms: 5-20        # Cache operation response times
  max_memory_usage_percent: 70-90   # Memory utilization thresholds
  critical_response_time_ms: 50-200 # Critical performance alerts
  max_hit_ratio_threshold: 0.7-0.9 # Cache effectiveness monitoring
```

#### System Resource Monitoring
```yaml
system:
  max_cpu_percent: 70-90            # CPU utilization alerts
  max_memory_percent: 75-90         # Memory usage monitoring
  max_disk_percent: 80-95           # Disk space monitoring
  critical_*_percent: 90-98         # Critical resource alerts
```

#### Application Performance Monitoring
```yaml
application:
  max_response_time_ms: 1000-5000   # API response time monitoring
  critical_response_time_ms: 3000-10000  # Critical performance alerts
  min_success_rate: 0.90-0.99       # Success rate thresholds
  max_error_rate_percent: 1-10      # Error rate monitoring
```

#### Celery Task Monitoring
```yaml
celery:
  max_queue_length: 50-1000         # Task queue monitoring
  critical_queue_length: 200-5000   # Critical queue backlog alerts
  max_failed_tasks: 2-10            # Failed task monitoring
  critical_failed_tasks: 10-50      # Critical failure alerts
  max_task_processing_time_ms: 15000-60000  # Task execution monitoring
```

#### External Service Monitoring
```yaml
external_services:
  max_response_time_ms: 3000-10000  # External API response monitoring
  critical_response_time_ms: 5000-20000  # Critical external service alerts
  min_success_rate: 0.8-0.95        # External service reliability
  circuit_breaker_threshold: 3-10   # Circuit breaker configuration
```

### Advanced Alert Configuration

#### Multi-Channel Alerting
```yaml
alert_thresholds:
  email_alerts: [WARNING, ERROR, CRITICAL]     # Email notification levels
  slack_alerts: [ERROR, CRITICAL]              # Slack integration alerts
  pagerduty_alerts: [CRITICAL]                 # PagerDuty escalation
```

#### Intelligent Rate Limiting
```yaml
rate_limiting:
  max_alerts_per_window: 5-50       # Alert frequency control
  rate_limit_window_minutes: 1-10   # Alert window duration
  deduplication_minutes: 0-5        # Alert deduplication
```

#### Monitoring Intervals
```yaml
monitoring_intervals:
  health_check_interval_seconds: 10-60     # Health check frequency
  metrics_collection_interval_seconds: 5-30  # Metrics collection rate
  external_service_check_interval_seconds: 30-120  # External service monitoring
  celery_metrics_interval_seconds: 10-60   # Background task monitoring
```

## Primary Application Configuration (`config.yml`)

### Core Application Settings
```yaml
# Environment detection with production-safe defaults
environment: production

# Database configuration (requires DATABASE_URL environment variable)
database:
  pool_size: 10-20                  # Connection pool sizing
  pool_pre_ping: true               # Connection health validation
  pool_recycle: 3600                # Connection lifecycle management
  timeout: 30-60                    # Query timeout configuration
  echo_sql: false                   # SQL logging (disabled in production)
```

### Enhanced Security Configuration
```yaml
security:
  access_token_expire_minutes: 15   # Short-lived JWT tokens
  refresh_token_expire_days: 30     # Refresh token lifecycle
  max_login_attempts: 3             # Brute force protection
  lockout_duration_minutes: 30      # Account lockout policy
  enable_audit_encryption: true     # Encrypted audit trails
  enable_field_encryption: true     # Field-level data encryption
  enable_key_rotation: true         # Automatic cryptographic key rotation
  enforce_mfa_for_admin: true       # Multi-factor authentication requirements
```

### Advanced Rate Limiting
```yaml
rate_limiting:
  api_default: 100                  # General API rate limits (per minute)
  api_authenticated: 500            # Enhanced limits for authenticated users
  auth_login: 3                     # Login attempt limits (per 5 minutes)
  auth_register: 1                  # Registration limits (per hour)
  report_generation: 5              # Report generation limits (per 5 minutes)
  enable_ip_rate_limiting: true     # IP-based rate limiting
  enable_user_rate_limiting: true   # User-based rate limiting
```

### Comprehensive Monitoring Integration
```yaml
monitoring:
  enable_metrics: true              # Prometheus metrics collection
  enable_tracing: true              # Distributed tracing
  enable_alerting: true             # Alert system activation
  enable_business_intelligence: true # Business KPI monitoring
  enable_audit_trails: true        # Comprehensive audit logging
  health_check_interval_seconds: 60 # Health monitoring frequency
  metrics_retention_days: 90        # Metrics data retention policy
```

## Environment-Specific Configuration Patterns

### Development Environment Optimizations
- **Enhanced Debugging**: Verbose logging, SQL query echoing, detailed error messages
- **Relaxed Security**: Extended token lifetimes, reduced rate limiting for development workflow
- **Mock Services**: Optional external service mocking for offline development
- **Hot Reloading**: Automatic application restart on code changes
- **Permissive Thresholds**: Higher resource usage limits for development flexibility

### Production Environment Hardening
- **Maximum Security**: Strict security policies, short token lifetimes, comprehensive audit logging
- **Performance Optimization**: Connection pooling, aggressive caching, optimized monitoring intervals
- **Comprehensive Monitoring**: Real-time health checks, multi-channel alerting, business intelligence
- **Compliance Features**: GDPR compliance, data retention policies, encrypted audit trails
- **SSL/TLS Enforcement**: HTTPS-only, HSTS headers, secure cookie configuration

### Testing Environment Optimization
- **Isolated Resources**: Separate test databases, dedicated Redis instances
- **Mock Integration**: Comprehensive external service mocking for reliable testing
- **Simplified Authentication**: Reduced complexity for automated testing workflows
- **Fast Feedback**: Optimized monitoring intervals and thresholds for rapid test execution
- **Comprehensive Coverage**: Enhanced logging for test debugging and analysis

### Staging Environment Validation
- **Production Similarity**: Near-production configuration for realistic testing
- **Enhanced Monitoring**: Detailed performance monitoring and alerting validation
- **Security Validation**: Full security feature testing in production-like environment
- **Load Testing**: Performance validation under realistic load conditions

## Configuration Management Features

### Runtime Configuration Management
- **Dynamic Loading**: Environment-specific configuration loading at startup
- **Validation System**: Comprehensive configuration validation with detailed error reporting
- **Hot Reloading**: Configuration updates without application restart (where safe)
- **Health Endpoints**: Configuration status monitoring via `/health/config` endpoint

### Security Best Practices
- **Secret Management**: All sensitive values managed via environment variables
- **Environment Variable Validation**: Required variables validated at application startup
- **Production-Safe Defaults**: All defaults assume production environment for security
- **Configuration Encryption**: Sensitive configuration data encrypted at rest

### Configuration Access Patterns
- **Type-Safe Access**: Pydantic-based configuration with automatic type validation
- **Environment Override**: Any configuration setting can be overridden via environment variables
- **Centralized Management**: Single source of truth for all configuration settings
- **Audit Trail**: Configuration changes logged and tracked for compliance

### Monitoring and Observability
- **Configuration Monitoring**: Real-time monitoring of configuration effectiveness
- **Performance Impact**: Configuration impact on application performance tracking
- **Alert Integration**: Configuration-driven alert thresholds and escalation policies
- **Business Intelligence**: Configuration-driven KPI monitoring and reporting