# Secret Management Guide

This guide covers secure handling of sensitive data in MCX3D Financials v2.

## Overview

MCX3D Financials uses Docker secrets for managing sensitive configuration in production environments. This ensures that passwords, API keys, and other sensitive data are never exposed in environment variables or configuration files.

## Architecture

### Development Environment
- Uses `.env.development` file with test credentials
- Secrets are passed as environment variables
- Suitable for local development only

### Production Environment  
- Uses Docker secrets for all sensitive data
- Secrets are mounted as files in containers
- Supports secret rotation without container restart
- Integrates with Docker Swarm or Kubernetes

## Required Secrets

| Secret Name | Description | Generation Method |
|------------|-------------|-------------------|
| `mcx3d_db_password` | PostgreSQL user password | Auto-generated 32 chars |
| `mcx3d_db_root_password` | PostgreSQL root password | Auto-generated 32 chars |
| `mcx3d_secret_key` | JWT signing key | Auto-generated 64 chars |
| `mcx3d_encryption_key` | Fernet encryption key | Fernet.generate_key() |
| `mcx3d_xero_client_secret` | Xero OAuth client secret | From Xero app settings |
| `mcx3d_smtp_password` | SMTP server password | From email provider |
| `mcx3d_redis_password` | Redis authentication | Auto-generated 32 chars |

## Quick Start

### 1. Initialize Docker Swarm (if not already done)
```bash
docker swarm init
```

### 2. Create Production Secrets
```bash
# Initialize all secrets with generated values
./scripts/deployment/manage_secrets.sh init

# Verify all secrets are created
./scripts/deployment/manage_secrets.sh verify
```

### 3. Update External Secrets
```bash
# Update Xero client secret with actual value
./scripts/deployment/manage_secrets.sh update mcx3d_xero_client_secret 'your-actual-secret'

# Update SMTP password
./scripts/deployment/manage_secrets.sh update mcx3d_smtp_password 'your-smtp-password'
```

### 4. Deploy with Secrets
```bash
# Deploy using production compose file with secrets
docker stack deploy -c docker-compose.yml -c docker-compose.secrets.yml mcx3d_prod
```

## Managing Secrets

### Creating a Secret
```bash
# Using the management script
./scripts/deployment/manage_secrets.sh create secret_name secret_value

# Using Docker directly
echo -n "secret_value" | docker secret create secret_name -
```

### Updating a Secret
```bash
# Using the management script (handles removal of old secret)
./scripts/deployment/manage_secrets.sh update secret_name new_secret_value

# Manual update
docker secret rm secret_name
echo -n "new_secret_value" | docker secret create secret_name -
```

### Listing Secrets
```bash
docker secret ls
```

### Inspecting a Secret (metadata only)
```bash
docker secret inspect secret_name
```

## Application Integration

### Reading Secrets in Python
```python
def read_secret_file(secret_path: str) -> str:
    """Read a Docker secret from file."""
    try:
        with open(secret_path, 'r') as f:
            return f.read().strip()
    except FileNotFoundError:
        return None

# In your configuration
secret_key = os.getenv('SECRET_KEY')
if not secret_key:
    # Try to read from Docker secret file
    secret_key_file = os.getenv('SECRET_KEY_FILE')
    if secret_key_file:
        secret_key = read_secret_file(secret_key_file)
```

### Environment Variable Pattern
The application checks for secrets in this order:
1. Direct environment variable (e.g., `SECRET_KEY`)
2. File path environment variable (e.g., `SECRET_KEY_FILE`)
3. Default value or error

## Security Best Practices

### 1. Secret Generation
- Use cryptographically secure random generators
- Minimum lengths: 32 characters for passwords, 64 for keys
- Include mixed case, numbers, and special characters
- Avoid patterns or dictionary words

### 2. Secret Storage
- Never commit secrets to version control
- Use `.gitignore` for all secret files
- Store production secrets in secure vaults
- Maintain separate secrets per environment

### 3. Secret Rotation
- Rotate secrets every 90 days
- Update one service at a time
- Test in staging before production
- Keep previous version for rollback

### 4. Access Control
- Limit secret access to necessary services
- Use principle of least privilege
- Audit secret access regularly
- Monitor for unauthorized access

## Kubernetes Integration

For Kubernetes deployments, convert Docker secrets to Kubernetes secrets:

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: mcx3d-secrets
type: Opaque
data:
  db-password: <base64-encoded-password>
  secret-key: <base64-encoded-key>
  encryption-key: <base64-encoded-key>
```

Create from command line:
```bash
kubectl create secret generic mcx3d-secrets \
  --from-literal=db-password='your-password' \
  --from-literal=secret-key='your-key' \
  --from-literal=encryption-key='your-encryption-key'
```

## HashiCorp Vault Integration (Optional)

For enterprise deployments, integrate with HashiCorp Vault:

```python
# vault_config.py
import hvac

client = hvac.Client(url='https://vault.company.com')
client.token = os.getenv('VAULT_TOKEN')

# Read secrets from Vault
secrets = client.read('secret/data/mcx3d/production')
db_password = secrets['data']['data']['db_password']
```

## Troubleshooting

### Secret Not Found
```bash
# Check if secret exists
docker secret ls | grep mcx3d

# Verify secret is accessible in container
docker exec <container_id> ls -la /run/secrets/
```

### Permission Denied
```bash
# Ensure proper file permissions
docker exec <container_id> stat /run/secrets/secret_name
```

### Secret Rotation Issues
1. Create new secret with temporary name
2. Update service to use new secret
3. Verify service is working
4. Remove old secret

## Backup and Recovery

### Backup Strategy
1. Document all secret names and purposes
2. Store encrypted backups of secret values
3. Test recovery procedures regularly
4. Maintain offline copies in secure location

### Recovery Procedure
1. Initialize Docker Swarm on new system
2. Run secret initialization script
3. Update with actual production values
4. Deploy services with secrets
5. Verify all services are functional

## Monitoring and Compliance

### Audit Requirements
- Log all secret access attempts
- Monitor for unusual access patterns
- Regular security assessments
- Compliance reporting (SOC2, ISO27001)

### Alert Conditions
- Failed authentication attempts
- Expired secrets
- Unauthorized access attempts
- Secret rotation reminders

## Emergency Procedures

### Compromised Secret
1. Immediately rotate affected secret
2. Update all dependent services
3. Review access logs
4. Notify security team
5. Document incident

### Lost Secret
1. Check secure backups
2. Regenerate if no backup exists
3. Update all services
4. Test functionality
5. Update documentation

## References

- [Docker Secrets Documentation](https://docs.docker.com/engine/swarm/secrets/)
- [OWASP Secret Management](https://cheatsheetseries.owasp.org/cheatsheets/Secrets_Management_Cheat_Sheet.html)
- [12-Factor App Config](https://12factor.net/config)