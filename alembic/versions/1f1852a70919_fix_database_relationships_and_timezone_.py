"""fix_database_relationships_and_timezone_handling

Revision ID: 1f1852a70919
Revises: 005_add_user_tables
Create Date: 2025-07-23 20:21:20.137077

This migration fixes:
1. Missing back_populates relationships in UserOrganization model
2. Timezone handling - ensures all datetime fields use timezone-aware defaults
3. Database schema consistency and integrity

"""
from typing import Sequence, Union
from datetime import datetime, timezone

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1f1852a70919'
down_revision: Union[str, Sequence[str], None] = '005_add_user_tables'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def utc_now():
    """Return timezone-aware UTC datetime for default values."""
    return datetime.now(timezone.utc)


def upgrade() -> None:
    """
    Upgrade schema to fix database relationships and timezone handling.
    
    This migration is primarily about model relationship fixes and timezone standardization.
    The core database schema remains the same, but we're ensuring consistency.
    """
    # Note: The main fixes are in the SQLAlchemy model definitions:
    # 1. Added proper back_populates relationships between User, Organization, and UserOrganization
    # 2. Changed all datetime defaults from datetime.utcnow to timezone-aware datetime.now(timezone.utc)
    # 3. Enhanced database connection validation and health checks
    
    # Since the table structures haven't changed, but only the model definitions,
    # this migration serves as a documentation point and ensures any new
    # installations use the corrected model definitions.
    
    # If there were any missing constraints or indexes from the relationship fixes,
    # they would be added here. For this specific case, the relationships were
    # already defined at the table level, we just fixed the ORM mappings.
    
    # Add a comment to track this migration
    op.execute("""
        COMMENT ON TABLE user_organizations IS 
        'Junction table for many-to-many relationship between users and organizations. 
         Updated in migration 1f1852a70919 to fix ORM relationships and timezone handling.'
    """)


def downgrade() -> None:
    """
    Downgrade schema - remove the comment added in upgrade.
    """
    # Remove the comment
    op.execute("""
        COMMENT ON TABLE user_organizations IS NULL
    """)
    
    # Note: We don't need to revert the model changes as they were improvements
    # to the ORM definitions, not schema changes. The timezone handling changes
    # are also improvements that don't require schema rollback.
