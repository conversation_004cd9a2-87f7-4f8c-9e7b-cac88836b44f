#!/bin/bash

# Example Usage for MCX3D Financials CLI

# --- Data Sync ---
# Sync all data from Xero
mcx3d-finance data sync --all

# Sync only accounts and contacts
mcx3d-finance data sync --accounts --contacts

# --- Reporting ---
# Generate an Income Statement for the last 12 months
mcx3d-finance reports generate --report-type income_statement --period 12

# Generate a Balance Sheet for the current date
mcx3d-finance reports generate --report-type balance_sheet

# Generate a Cash Flow statement and save to a specific file
mcx3d-finance reports generate --report-type cash_flow --output-file reports/cash_flow.csv

# --- Valuation ---
# Run a DCF valuation using a custom assumptions file
mcx3d-finance valuation run-dcf --assumptions-file sample_data/dcf_assumptions.json

# Run a multiples-based valuation
mcx3d-finance valuation run-multiples --industry "SaaS"