from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from mcx3d_finance.db.session import get_db
from mcx3d_finance.core.metrics import saas_kpis
from mcx3d_finance.api.schemas import SaasKpisResponse

router = APIRouter()


@router.get("/metrics/saas-kpis", response_model=SaasKpisResponse)
def get_saas_kpis(organization_id: int, period: str, db: Session = Depends(get_db)):
    kpis = saas_kpis.calculate_saas_kpis(db, organization_id, period)
    return {"kpis": kpis}
