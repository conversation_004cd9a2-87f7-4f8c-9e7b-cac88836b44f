"""
Tests for database configuration and relationship fixes (Issue #5).

This test suite verifies that the fixes implemented for GitHub issue #5 work correctly:
1. Missing foreign key relationships in UserOrganization model
2. Timezone inconsistencies in datetime handling
3. Database connection validation
4. Database URL security validation
"""

import pytest
import time
from datetime import datetime, timezone
from unittest.mock import Mock, patch
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from mcx3d_finance.db.models import User, Organization, UserOrganization, utc_now
from mcx3d_finance.db.session import (
    check_database_connection, 
    validate_database_startup, 
    get_database_health
)
from mcx3d_finance.core.config import get_database_url


class TestModelRelationships:
    """Test that ORM relationships work correctly after fixes."""
    
    def test_user_organization_relationships(self, db_session):
        """Test that User-Organization relationships work bidirectionally."""
        # Create test data
        user = User(
            email="<EMAIL>",
            hashed_password="hashed",
            full_name="Test User",
            created_at=utc_now(),
            updated_at=utc_now()
        )
        
        organization = Organization(
            name="Test Org",
            xero_tenant_id="test-tenant-123",
            created_at=utc_now(),
            updated_at=utc_now()
        )
        
        db_session.add(user)
        db_session.add(organization)
        db_session.commit()
        
        # Create association
        user_org = UserOrganization(
            user_id=user.id,
            organization_id=organization.id,
            role="admin",
            created_at=utc_now()
        )
        
        db_session.add(user_org)
        db_session.commit()
        
        # Test relationships
        assert len(user.organization_associations) == 1
        assert user.organization_associations[0].organization == organization
        assert user.organization_associations[0].role == "admin"
        
        assert len(organization.user_associations) == 1
        assert organization.user_associations[0].user == user
        assert organization.user_associations[0].role == "admin"
        
        # Test many-to-many relationship
        assert organization in user.organizations
        assert user in organization.users
    
    def test_cascade_deletion(self, db_session):
        """Test that cascade deletion works properly."""
        # Create test data
        user = User(
            email="<EMAIL>",
            hashed_password="hashed",
            full_name="Cascade User",
            created_at=utc_now(),
            updated_at=utc_now()
        )
        
        organization = Organization(
            name="Cascade Org",
            xero_tenant_id="cascade-tenant-123",
            created_at=utc_now(),
            updated_at=utc_now()
        )
        
        db_session.add(user)
        db_session.add(organization)
        db_session.commit()
        
        # Create association
        user_org = UserOrganization(
            user_id=user.id,
            organization_id=organization.id,
            role="user",
            created_at=utc_now()
        )
        
        db_session.add(user_org)
        db_session.commit()
        
        user_id = user.id
        org_id = organization.id
        
        # Delete user - should cascade delete associations
        db_session.delete(user)
        db_session.commit()
        
        # Verify association was deleted
        remaining_associations = db_session.query(UserOrganization).filter_by(
            user_id=user_id
        ).all()
        assert len(remaining_associations) == 0
        
        # Organization should still exist
        remaining_org = db_session.query(Organization).filter_by(id=org_id).first()
        assert remaining_org is not None


class TestTimezoneHandling:
    """Test timezone-aware datetime handling."""
    
    def test_utc_now_function(self):
        """Test that utc_now returns timezone-aware datetime."""
        now = utc_now()
        
        assert now.tzinfo is not None
        assert now.tzinfo == timezone.utc
        assert isinstance(now, datetime)
    
    def test_model_datetime_defaults(self, db_session):
        """Test that model datetime defaults are timezone-aware."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed",
            full_name="Timezone User"
        )
        
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        
        # Check that default datetime fields are timezone-aware
        assert user.created_at.tzinfo is not None
        assert user.updated_at.tzinfo is not None
        assert user.created_at.tzinfo == timezone.utc
        assert user.updated_at.tzinfo == timezone.utc
    
    def test_timezone_consistency(self, db_session):
        """Test that all timezone-aware datetimes are consistent."""
        org = Organization(
            name="Timezone Org",
            xero_tenant_id="timezone-tenant"
        )
        
        db_session.add(org)
        db_session.commit()
        db_session.refresh(org)
        
        # All datetime fields should have UTC timezone
        if org.created_at:
            assert org.created_at.tzinfo == timezone.utc
        if org.updated_at:
            assert org.updated_at.tzinfo == timezone.utc
        if org.last_sync_at:
            assert org.last_sync_at.tzinfo == timezone.utc


class TestDatabaseConnectionValidation:
    """Test database connection validation functionality."""
    
    @patch('mcx3d_finance.db.session.engine')
    def test_check_database_connection_healthy(self, mock_engine):
        """Test database connection check when healthy."""
        # Mock successful connection
        mock_connection = Mock()
        mock_result = Mock()
        mock_result.fetchone.return_value = (1,)
        mock_connection.execute.return_value = mock_result
        mock_connection.__enter__ = Mock(return_value=mock_connection)
        mock_connection.__exit__ = Mock(return_value=None)
        
        mock_pool = Mock()
        mock_pool.size.return_value = 10
        mock_pool.checkedin.return_value = 8
        mock_pool.checkedout.return_value = 2
        mock_pool.overflow.return_value = 0
        mock_pool.invalid.return_value = 0
        
        mock_engine.connect.return_value = mock_connection
        mock_engine.pool = mock_pool
        mock_engine.url = Mock()
        mock_engine.url.__str__.return_value = "postgresql://user:pass@localhost/db"
        
        result = check_database_connection()
        
        assert result['status'] == 'healthy'
        assert result['message'] == 'Database connection successful'
        assert 'pool_status' in result
        assert result['pool_status']['size'] == 10
    
    @patch('mcx3d_finance.db.session.engine')
    def test_check_database_connection_unhealthy(self, mock_engine):
        """Test database connection check when unhealthy."""
        # Mock connection failure
        mock_engine.connect.side_effect = Exception("Connection failed")
        
        result = check_database_connection()
        
        assert result['status'] == 'unhealthy'
        assert 'Connection failed' in result['message']
        assert 'error' in result
    
    @patch('mcx3d_finance.db.session.check_database_connection')
    def test_validate_database_startup_success(self, mock_check):
        """Test successful database startup validation."""
        mock_check.return_value = {'status': 'healthy', 'message': 'OK'}
        
        result = validate_database_startup()
        
        assert result is True
        mock_check.assert_called_once()
    
    @patch('mcx3d_finance.db.session.check_database_connection')
    def test_validate_database_startup_failure(self, mock_check):
        """Test failed database startup validation."""
        mock_check.return_value = {'status': 'unhealthy', 'message': 'Failed'}
        
        result = validate_database_startup()
        
        assert result is False
        mock_check.assert_called_once()


class TestDatabaseURLValidation:
    """Test database URL security validation."""
    
    def test_insecure_pattern_detection(self):
        """Test that insecure patterns are detected."""
        from mcx3d_finance.core.config import get_database_url
        
        # Test various insecure patterns
        insecure_urls = [
            "postgresql://user:password@localhost/db",
            "**********************************",
            "********************************",
            "********************************",
            "***************************************"
        ]
        
        for url in insecure_urls:
            with patch('mcx3d_finance.core.config.settings') as mock_settings:
                mock_settings.database_url = url
                with patch('mcx3d_finance.core.config.config', {}):
                    with pytest.raises(ValueError) as exc_info:
                        get_database_url()
                    assert "Insecure database URL detected" in str(exc_info.value)
    
    def test_valid_database_url_acceptance(self):
        """Test that secure database URLs are accepted."""
        secure_url = "********************************************************/mydb"
        
        with patch('mcx3d_finance.core.config.settings') as mock_settings:
            mock_settings.database_url = secure_url
            with patch('mcx3d_finance.core.config.config', {}):
                result = get_database_url()
                assert result == secure_url
    
    def test_database_url_format_validation(self):
        """Test database URL format validation."""
        # Test invalid scheme
        with patch('mcx3d_finance.core.config.settings') as mock_settings:
            mock_settings.database_url = "invalidscheme://user:pass@host/db"
            with patch('mcx3d_finance.core.config.config', {}):
                with pytest.raises(ValueError) as exc_info:
                    get_database_url()
                assert "Unsupported database scheme" in str(exc_info.value)
        
        # Test missing credentials for non-SQLite
        with patch('mcx3d_finance.core.config.settings') as mock_settings:
            mock_settings.database_url = "postgresql://host/db"
            with patch('mcx3d_finance.core.config.config', {}):
                with pytest.raises(ValueError) as exc_info:
                    get_database_url()
                assert "must include username and password" in str(exc_info.value)
        
        # Test short password
        with patch('mcx3d_finance.core.config.settings') as mock_settings:
            mock_settings.database_url = "*******************************"
            with patch('mcx3d_finance.core.config.config', {}):
                with pytest.raises(ValueError) as exc_info:
                    get_database_url()
                assert "password must be at least 8 characters" in str(exc_info.value)
    
    def test_sqlite_url_acceptance(self):
        """Test that SQLite URLs don't require credentials."""
        sqlite_url = "sqlite:///./test.db"
        
        with patch('mcx3d_finance.core.config.settings') as mock_settings:
            mock_settings.database_url = sqlite_url
            with patch('mcx3d_finance.core.config.config', {}):
                result = get_database_url()
                assert result == sqlite_url


@pytest.mark.integration
class TestDatabaseHealthChecks:
    """Integration tests for database health checks."""
    
    def test_get_database_health_real_connection(self):
        """Test database health check with real connection."""
        # This test assumes a test database is available
        try:
            health = get_database_health()
            
            # Should have basic health information
            assert 'status' in health
            assert 'response_time_ms' in health
            assert 'connection_health' in health
            
            # If database is available, should be healthy
            if health['status'] == 'healthy':
                assert health['response_time_ms'] >= 0
                assert health['connection_health']['status'] == 'healthy'
                assert 'engine_info' in health
                
        except Exception as e:
            # If no test database available, that's okay for this test
            pytest.skip(f"Database not available for integration test: {e}")