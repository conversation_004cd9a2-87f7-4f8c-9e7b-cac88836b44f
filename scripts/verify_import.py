#!/usr/bin/env python3
"""Verify imported data in the database."""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization, Account, Contact, Transaction, Invoice, BankTransaction

def verify_import():
    db = SessionLocal()
    
    try:
        # Get organization summary
        org = db.query(Organization).filter_by(id=1).first()
        if org:
            print(f'Organization: {org.name} ({org.base_currency})')
            print(f'Accounts: {db.query(Account).filter_by(organization_id=1).count()}')
            print(f'Contacts: {db.query(Contact).filter_by(organization_id=1).count()}')
            print(f'Transactions: {db.query(Transaction).filter_by(organization_id=1).count()}')
            print(f'Invoices: {db.query(Invoice).filter_by(organization_id=1).count()}')
            print(f'Bank Transactions: {db.query(BankTransaction).filter_by(organization_id=1).count()}')
            
            # Show account breakdown
            print('\nAccount Types:')
            accounts = db.query(Account).filter_by(organization_id=1).all()
            account_types = {}
            for acc in accounts:
                acc_type = acc.type if acc.type else 'Unknown'
                account_types[acc_type] = account_types.get(acc_type, 0) + 1
            
            for acc_type, count in sorted(account_types.items()):
                print(f'  {acc_type}: {count}')
            
            # Show sample transactions
            print('\nRecent Transactions:')
            transactions = db.query(Transaction).filter_by(organization_id=1).order_by(Transaction.date.desc()).limit(5).all()
            for t in transactions:
                print(f'  {t.date.strftime("%Y-%m-%d")}: {t.description} - ${t.amount:.2f} ({t.type})')
            
            # Show contact breakdown
            print('\nContact Types:')
            contacts = db.query(Contact).filter_by(organization_id=1).all()
            customers = sum(1 for c in contacts if c.is_customer)
            suppliers = sum(1 for c in contacts if c.is_supplier)
            print(f'  Customers: {customers}')
            print(f'  Suppliers: {suppliers}')
            
        else:
            print('No organization found with ID 1')
            
    finally:
        db.close()

if __name__ == '__main__':
    verify_import()