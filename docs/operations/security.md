# Security Documentation - MCX3D Finance

## Overview

This document outlines the security architecture, best practices, and implementation guidelines for the MCX3D Finance application. Security is implemented in multiple layers to protect sensitive financial data and ensure compliance with industry standards.

## Security Architecture

### Authentication & Authorization

#### JWT-Based Authentication
- **Algorithm**: HS256 (HMAC with SHA-256)
- **Access Token Expiration**: 15 minutes (reduced for security)
- **Refresh Token Expiration**: 30 days with rotation
- **Token Storage**: Client-side only (localStorage/sessionStorage)
- **Refresh Strategy**: Secure refresh token rotation with automatic invalidation

#### Multi-Factor Authentication (MFA)
- **TOTP Support**: Time-based One-Time Passwords (RFC 6238)
- **QR Code Provisioning**: Easy setup with authenticator apps
- **Backup Codes**: 10 single-use recovery codes
- **MFA Challenge Flow**: Secure challenge-response mechanism

#### Session Management
- **Concurrent Sessions**: Maximum 5 sessions per user
- **Session Tracking**: Device information and IP addresses
- **Session Invalidation**: Individual or bulk session termination
- **Idle Timeout**: 60 minutes of inactivity
- **Redis Backend**: Distributed session storage

#### Organization-Level Access Control
- Users can belong to multiple organizations
- Role-based permissions: `admin`, `user`, `viewer`
- Organization access verified on every API request
- Admin role required for sensitive operations (token revocation, etc.)

#### Account Security
- **Account Lockout**: After 5 failed login attempts
- **Lockout Duration**: 30 minutes (configurable)
- **Progressive Delays**: Increasing delays for repeat offenders
- **IP Tracking**: Monitor login attempts by IP address
- **Password Policies**: 
  - Minimum 12 characters
  - Uppercase, lowercase, numbers, and special characters required
  - Password history enforcement
  - Complexity validation with entropy checking

### OAuth2 Integration (Xero)

#### PKCE Support
- State parameter for CSRF protection
- State stored in Redis with 10-minute expiration
- Cryptographically secure random state generation

#### Token Management
- Access tokens encrypted using AES-256 (Fernet)
- Refresh tokens stored encrypted in database
- Automatic token refresh 5 minutes before expiration
- Token revocation tracking

### Data Encryption

#### At Rest
- **Field-Level Encryption**: Multi-level encryption system
  - Standard: Fernet (AES-128) for general sensitive data
  - High: AES-256-GCM for highly sensitive data
  - Maximum: AES-256-GCM with PBKDF2 for critical data
- **Automatic Classification**: Content-based sensitivity detection
- **Key Management**: 
  - Automated key rotation (90-day default)
  - Re-encryption support for key updates
  - Version tracking for decryption
- **OAuth Tokens**: AES-256 encryption using Fernet
- **Database**: PostgreSQL encryption features + field-level encryption
- **Backups**: Encrypted using cloud provider encryption

#### In Transit
- HTTPS required for all API endpoints
- TLS 1.2+ minimum
- Certificate pinning for mobile applications
- Secure WebSocket connections for real-time features

#### Data Protection Features
- **Tokenization**: Replace sensitive data with secure tokens
- **Data Masking**: Configurable masking for display (partial, full, email, phone)
- **Secure Deletion**: Cryptographic erasure with memory overwriting
- **Format-Preserving Encryption**: Maintains data format for legacy systems

### API Security

#### Advanced Rate Limiting
- **Multiple Strategies**: 
  - Sliding Window: Smooth rate distribution
  - Token Bucket: Burst allowance with refill
  - Fixed Window: Simple time-based limits
- **Granular Limits**:
  - API Default: 100 requests/minute (unauthenticated)
  - API Authenticated: 1000 requests/minute
  - Login Attempts: 5 attempts/5 minutes
  - Registration: 3 attempts/hour
  - Password Reset: 3 attempts/hour
  - Report Generation: 10 requests/5 minutes
  - Data Export: 5 requests/hour
- **Advanced Features**:
  - Cost-based operations (expensive operations consume more)
  - Progressive delays for repeat offenders
  - IP whitelist/blacklist with automatic blocking
  - Redis-based for distributed deployments

#### Security Headers
```
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains
Content-Security-Policy: default-src 'self'
```

#### Comprehensive Input Validation
- **SQL Injection Prevention**: 
  - Pattern matching for SQL keywords
  - Parameterized queries enforcement
  - SQLAlchemy ORM protection
- **XSS Protection**:
  - HTML entity encoding
  - Script tag detection and removal
  - Event handler sanitization
- **Path Traversal Prevention**:
  - Path normalization
  - Directory escape detection
  - Absolute path validation
- **Command Injection Protection**:
  - Shell metacharacter blocking
  - Command sequence detection
- **File Upload Security**:
  - MIME type validation
  - File size limits (configurable)
  - Magic number verification
  - Malware scanning hooks
  - Allowed extensions whitelist
- **Type Validation**:
  - Pydantic models with strict validation
  - Custom validators for complex types
  - Pattern matching with regex
  - Length and range validation

### Security Audit & Monitoring

#### Comprehensive Audit Logging
- **30+ Security Event Types**: Track all security-relevant activities
  - Authentication events (login, logout, MFA)
  - Authorization events (access granted/denied)
  - Data access and modifications
  - Configuration changes
  - Security violations
- **Event Details Captured**:
  - Timestamp with microsecond precision
  - User ID and session ID
  - IP address and user agent
  - Action performed and outcome
  - Resource accessed
  - Correlation ID for related events
- **Tamper Detection**: HMAC-based integrity verification
- **Log Retention**: Configurable (365 days default)
- **Real-time Alerting**: Integration with SIEM systems

#### Suspicious Activity Detection
- **Pattern Analysis**:
  - Multiple failed login attempts
  - Unusual access patterns
  - Privilege escalation attempts
  - Data exfiltration indicators
- **Automated Responses**:
  - Account lockout
  - IP blocking
  - Alert generation
  - Session termination

#### Compliance Reporting
- **GDPR Compliance**:
  - Data access logs
  - Consent tracking
  - Right to erasure support
  - Data portability reports
- **PCI-DSS Compliance**:
  - Payment data access logs
  - Security control validation
  - Vulnerability scan reports
- **SOC2 Compliance**:
  - Control effectiveness reports
  - Security incident tracking
  - Change management logs

## Security Best Practices

### Development

1. **Environment Variables**
   - Never commit secrets to version control
   - Use `.env` files for local development
   - Use secret management services in production
   - Rotate secrets regularly

2. **Dependencies**
   - Regular security audits with `pip-audit`
   - Pin dependency versions
   - Monitor for CVEs
   - Update dependencies monthly

3. **Code Review**
   - Security-focused code reviews
   - Automated security scanning (SAST)
   - Dependency vulnerability scanning
   - Regular penetration testing

### Deployment

1. **Infrastructure**
   - Use VPC with private subnets
   - Implement WAF rules
   - Enable cloud provider security features
   - Regular security patches

2. **Monitoring**
   - Security event logging
   - Anomaly detection
   - Failed authentication tracking
   - Audit trail for sensitive operations

3. **Backup & Recovery**
   - Encrypted backups
   - Regular backup testing
   - Disaster recovery plan
   - Data retention policies

## Implementation Guidelines

### Security Utilities

#### Key Generation Utility
```bash
# Generate both JWT and Fernet keys
python -m mcx3d_finance.utils.generate_keys

# Generate only JWT secret (64 characters)
python -m mcx3d_finance.utils.generate_keys --type jwt

# Generate only Fernet encryption key
python -m mcx3d_finance.utils.generate_keys --type fernet
```

#### Security Audit Tool
```bash
# Run comprehensive security audit
python -m mcx3d_finance.utils.security_audit

# Validates:
# - Environment variables
# - Key strength
# - Configuration security
# - Dependency vulnerabilities
```

### Password Requirements
- Minimum 12 characters
- Must include uppercase, lowercase, numbers, and symbols
- No common patterns or dictionary words
- Password history enforcement (no reuse of last 5)
- Account lockout after 5 failed attempts

### Session Management
- Session timeout after 30 minutes of inactivity
- Secure session cookies (HttpOnly, Secure, SameSite)
- Session invalidation on logout
- Concurrent session limits

### Authentication Endpoints

#### Core Authentication
- `POST /api/auth/login` - User login with MFA support
- `POST /api/auth/logout` - Session termination
- `POST /api/auth/refresh` - Token refresh with rotation
- `POST /api/auth/password/change` - Password change with policy

#### Multi-Factor Authentication
- `POST /api/auth/mfa/setup` - Generate MFA secret and QR code
- `POST /api/auth/mfa/verify` - Verify MFA token
- `POST /api/auth/mfa/backup-codes` - Generate backup codes

#### Session Management
- `GET /api/auth/sessions` - List active sessions
- `DELETE /api/auth/sessions/{session_id}` - Terminate specific session
- `DELETE /api/auth/sessions` - Terminate all sessions

### API Security Checklist

- [ ] All endpoints require authentication (except login/public)
- [ ] Organization access verified for data endpoints
- [ ] Rate limiting applied with appropriate limits
- [ ] Input validation on all parameters
- [ ] Error messages don't leak sensitive information
- [ ] Audit logging for sensitive operations
- [ ] CORS properly configured
- [ ] Security headers present
- [ ] MFA enabled for administrative accounts
- [ ] Session management implemented
- [ ] Account lockout protection active

## Incident Response

### Security Incident Procedure

1. **Detection**
   - Monitor security alerts
   - User reports
   - Automated detection

2. **Containment**
   - Isolate affected systems
   - Revoke compromised credentials
   - Block malicious IPs

3. **Investigation**
   - Analyze logs
   - Determine scope
   - Identify root cause

4. **Recovery**
   - Patch vulnerabilities
   - Restore from backups if needed
   - Reset affected credentials

5. **Post-Incident**
   - Document lessons learned
   - Update security procedures
   - Notify affected users if required

### Contact Information

- Security Team: <EMAIL>
- Emergency: +1-XXX-XXX-XXXX
- Bug Bounty: <EMAIL>

## Compliance

### Standards
- OWASP Top 10 compliance
- GDPR data protection
- SOC 2 Type II (planned)
- ISO 27001 (planned)

### Data Privacy
- Minimal data collection
- User consent for data processing
- Right to deletion (GDPR Article 17)
- Data portability
- Privacy by design

## Security Tools

### Recommended Tools
- **SAST**: Bandit for Python
- **Dependency Scanning**: pip-audit, safety
- **Secrets Scanning**: truffleHog, git-secrets
- **WAF**: CloudFlare, AWS WAF
- **Monitoring**: Datadog, Sentry

### Security Testing

```bash
# Run security tests
pytest tests/security/ -v

# Check for vulnerabilities
pip-audit

# Scan for secrets
truffleHog filesystem ./

# Python security linting
bandit -r mcx3d_finance/
```

## Regular Security Tasks

### Daily
- Monitor security alerts
- Review authentication logs
- Check for failed login attempts

### Weekly
- Review access logs
- Update security patches
- Verify backup integrity

### Monthly
- Rotate API keys
- Security training
- Dependency updates
- Access review

### Quarterly
- Penetration testing
- Security audit
- Disaster recovery drill
- Policy review

## Security Configuration

### Production Settings

```python
# Never use these in production - generate your own!
SECRET_KEY = os.environ.get('SECRET_KEY')  # Min 32 chars
ENCRYPTION_KEY = os.environ.get('ENCRYPTION_KEY')  # Fernet key

# Security settings
SECURE_SSL_REDIRECT = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
```

## Reporting Security Issues

If you discover a security vulnerability, please:

1. **Do NOT** create a public GitHub issue
2. Email <EMAIL> with details
3. Include steps to reproduce
4. Allow 90 days for patching before disclosure

We appreciate responsible disclosure and may offer rewards for significant findings.

## Version History

- v2.0.0 - Initial security implementation
- v2.1.0 - Added OAuth2 with PKCE support
- v2.2.0 - Enhanced encryption and rate limiting
- v2.3.0 - Comprehensive security overhaul:
  - Fixed all critical vulnerabilities
  - Added MFA and session management
  - Implemented advanced rate limiting
  - Added comprehensive input validation
  - Implemented security audit logging
  - Added field-level encryption
  - Enhanced API security

Last Updated: 2025-01-23