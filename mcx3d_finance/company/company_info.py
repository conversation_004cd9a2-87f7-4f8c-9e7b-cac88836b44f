"""
MCX3D LTD Company Information

Official company details as registered with Companies House UK.
"""

from datetime import date
from typing import Dict, List, Any

COMPANY_INFO: Dict[str, Any] = {
    # Official Company Details (Companies House)
    "legal_name": "MCX3D LTD",
    "trading_name": "ModularCX",
    "company_number": "13325322",
    "company_type": "Private limited Company",
    "company_status": "Active",
    
    # Registration Information
    "incorporation_date": date(2021, 4, 9),
    "jurisdiction": "England and Wales",
    "registered_office": {
        "address_line_1": "1 Vincent Square",
        "city": "London",
        "country": "United Kingdom",
        "postal_code": "SW1P 2PN"
    },
    
    # Business Information
    "nature_of_business": "Business and domestic software development",
    "sic_codes": ["62012"],
    "sic_descriptions": ["Business and domestic software development"],
    
    # Company Description
    "description": (
        "MCX3D LTD, trading as ModularCX, is a leading 3D technology company "
        "specializing in interactive 3D shopping experiences, product configurators, "
        "and augmented reality solutions for e-commerce. Founded in 2018, the company "
        "develops innovative solutions to transform digital experiences through its "
        "proprietary Morpho 3D Platform."
    ),
    
    # Key Personnel
    "key_personnel": [
        {
            "name": "Bilal Itani",
            "position": "Founder and CEO",
            "role": "Executive"
        },
        {
            "name": "Mohammad al Akkaoui",
            "position": "Partner and CFO",
            "role": "Executive"
        },
        {
            "name": "Youssef Grab",
            "position": "Partner and CTO",
            "role": "Executive"
        }
    ],
    
    # Financial Year
    "financial_year_end": {
        "day": 30,
        "month": 4  # April
    },
    "accounting_reference_date": date(2024, 4, 30),
    
    # Compliance Dates
    "next_accounts_due": date(2025, 1, 31),
    "last_accounts_made_up_to": date(2023, 4, 30),
    "next_confirmation_statement_due": date(2025, 4, 23),
    "last_confirmation_statement_date": date(2024, 4, 9),
    
    # Business Metrics
    "founded_year": 2018,
    "team_size": 20,
    "industry": "3D Technology / E-commerce Solutions",
    
    # Contact Information (for reports)
    "website": "https://www.modularcx.co.uk",
    "report_contact_email": "<EMAIL>",  # Placeholder - update with actual
    
    # Report Settings
    "currency": "GBP",
    "currency_symbol": "£",
    "report_language": "en-GB",
    "date_format": "%d %B %Y",  # e.g., "31 December 2024"
    
    # Reporting periods
    "REPORTING_PERIODS": {
        "current_year": {
            "start": date(2023, 5, 1),
            "end": date(2024, 4, 30),
            "label": "FY 2023-24"
        },
        "prior_year": {
            "start": date(2022, 5, 1),
            "end": date(2023, 4, 30),
            "label": "FY 2022-23"
        },
        "comparison_years": 2,  # Number of years to show in comparatives
    }
}

# Reporting periods configuration
REPORTING_PERIODS = {
    "current_year": {
        "start": date(2023, 5, 1),
        "end": date(2024, 4, 30),
        "label": "FY 2023-24"
    },
    "prior_year": {
        "start": date(2022, 5, 1),
        "end": date(2023, 4, 30),
        "label": "FY 2022-23"
    },
    "comparison_years": 2,  # Number of years to show in comparatives
}

# VAT and Tax Information
TAX_INFO = {
    "vat_registered": True,  # Assumption - update with actual
    "vat_number": "GB XXXXXXXXX",  # Placeholder - update with actual
    "corporation_tax_rate": 0.19,  # UK corporation tax rate for small companies
}