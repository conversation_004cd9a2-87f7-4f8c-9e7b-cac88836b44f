# Data Seeding and Import Guide

This guide explains how to seed test data and import financial data into MCX3D Financials v2.

## Overview

MCX3D Financials provides powerful CLI commands for:
- Generating realistic test data for development and testing
- Importing financial data from JSON files
- Managing test databases with consistent sample data

## Seed Sample Data

The `seed sample-data` command generates comprehensive test data for a specified organization.

### Basic Usage

```bash
# Seed data for organization ID 1
mcx3d-finance seed sample-data --org-id 1

# Seed 12 months of historical data
mcx3d-finance seed sample-data --org-id 1 --months 12

# Clear existing data before seeding
mcx3d-finance seed sample-data --org-id 1 --clear
```

### What Gets Created

The command creates:
- **Chart of Accounts**: 18 standard accounts (Assets, Liabilities, Equity, Revenue, Expenses)
- **Contacts**: 8 contacts configured as customers, suppliers, or both
- **Transactions**: Revenue and expense transactions for the specified period
- **Invoices**: Customer invoices with realistic amounts and tax calculations
- **Bank Transactions**: Bank account activity matching the transaction patterns

### Example Output

```
🌱 Seeding sample data for: Demo Company Inc

📊 Creating chart of accounts...
✅ Created 18 accounts

👥 Creating contacts...
✅ Created 8 contacts

💰 Creating transactions...
✅ Created 253 transactions

📄 Creating invoices...
✅ Created 55 invoices

🏦 Creating bank transactions...
✅ Created 183 bank transactions

✅ Sample data seeded successfully!
```

## Import from JSON

The `seed from-json` command allows importing financial data from JSON files.

### Supported Data Types

- `accounts` - Chart of accounts
- `contacts` - Customer and supplier contacts
- `transactions` - Financial transactions
- `invoices` - Sales and purchase invoices

### Basic Usage

```bash
# Import accounts from JSON
mcx3d-finance seed from-json --org-id 1 --file accounts.json --data-type accounts

# Import contacts
mcx3d-finance seed from-json --org-id 1 --file contacts.json --data-type contacts

# Import transactions
mcx3d-finance seed from-json --org-id 1 --file transactions.json --data-type transactions

# Import invoices
mcx3d-finance seed from-json --org-id 1 --file invoices.json --data-type invoices
```

### JSON File Formats

#### Accounts JSON Format
```json
[
  {
    "account_id": "acc-001",
    "code": "1000",
    "account_name": "Cash and Cash Equivalents",
    "account_type": "Bank",
    "description": "Main operating bank account"
  }
]
```

#### Contacts JSON Format
```json
[
  {
    "contact_id": "cont-001",
    "name": "Acme Corporation",
    "email": "<EMAIL>",
    "is_customer": true,
    "is_supplier": false
  }
]
```

#### Transactions JSON Format
```json
[
  {
    "transaction_id": "trans-001",
    "date": "2025-07-01T00:00:00Z",
    "amount": 1500.00,
    "description": "Sales revenue",
    "reference": "INV-001",
    "type": "RECEIVE",
    "currency": "USD",
    "is_reconciled": true
  }
]
```

#### Invoices JSON Format
```json
[
  {
    "invoice_id": "inv-001",
    "invoice_number": "INV-001",
    "date": "2025-07-01T00:00:00Z",
    "due_date": "2025-07-31T00:00:00Z",
    "status": "AUTHORISED",
    "sub_total": 1000.00,
    "tax_amount": 100.00,
    "total": 1100.00,
    "type": "ACCREC"
  }
]
```

## Database Seeding Script

For more advanced seeding scenarios, use the Python script directly:

```bash
# Basic seeding
python scripts/seed_database.py

# Reset and reseed database
python scripts/seed_database.py --reset
```

This script:
- Creates database tables if they don't exist
- Seeds organizations with different currencies
- Creates user accounts with default passwords
- Generates comprehensive test data

### Default Test Users

After seeding, these test accounts are available:
- **Admin**: <EMAIL> / password123
- **Analyst**: <EMAIL> / password123
- **Viewer**: <EMAIL> / password123

## Best Practices

1. **Development Environment**: Always use seeding in development/test environments only
2. **Clear Data**: Use the `--clear` flag to ensure consistent test data
3. **Backup First**: Always backup production data before any import operations
4. **Validate JSON**: Ensure JSON files are properly formatted before import
5. **Test Small**: Test with small datasets before importing large files

## Troubleshooting

### Common Issues

1. **"Organization not found"**
   - Ensure the organization exists in the database
   - Check the organization ID is correct

2. **"Invalid JSON format"**
   - Validate JSON syntax using a JSON validator
   - Check field names match the expected format

3. **"Duplicate key error"**
   - Use unique IDs in your import files
   - Consider using the `--clear` flag for sample data

4. **"Field validation error"**
   - Check that required fields are present
   - Ensure data types match (e.g., dates, numbers)

### Debug Mode

Run commands with debug logging for detailed information:

```bash
mcx3d-finance --debug seed sample-data --org-id 1
```

## Integration with Testing

The seeding commands are perfect for:
- Setting up consistent test environments
- Creating data for automated tests
- Demonstrating features to stakeholders
- Performance testing with realistic data volumes

Example test setup:
```bash
#!/bin/bash
# Setup test environment
mcx3d-finance seed sample-data --org-id 1 --clear
# Run tests
pytest tests/
# Cleanup if needed
```

## Summary

The data seeding and import features provide flexible options for managing test data in MCX3D Financials. Whether you need quick sample data for development or need to import existing financial data, these tools streamline the process while ensuring data integrity.