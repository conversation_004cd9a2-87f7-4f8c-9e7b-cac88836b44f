[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts =
    --cov=mcx3d_finance
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=85
    --strict-markers
    --tb=short
markers =
    e2e: End-to-end tests requiring full system
    integration: Integration tests with database and external services
    performance: Performance and benchmark tests
    slow: Tests that take significant time to execute
    benchmark: Baseline performance metrics for regression testing
    unit: Unit tests for isolated component testing
    docker: Tests requiring Docker environment
    pdf: Tests that generate and validate PDF outputs
    excel: Tests that generate and validate Excel outputs
    cli: Command-line interface tests
    report_generation: Tests specifically for report generation functionality
    smoke: Critical smoke tests for production validation
    production: Production environment validation tests
    validation: System validation and health checks