version: '3.8'

services:
  nginx:
    build: ./nginx
    container_name: mcx3d_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - static_volume:/app/static
      - media_volume:/app/media
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - web
    networks:
      - mcx3d_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  web:
    build: .
    container_name: mcx3d_web
    expose:
      - "8000"
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - SECRET_KEY_FILE=/run/secrets/secret_key
      - DATABASE_URL=postgresql://mcx3d_user:${DB_PASSWORD}@db:5432/mcx3d_finance_prod
      - REDIS_URL=redis://redis:6379
      - RA<PERSON>_LIMIT_STORAGE_URL=redis://redis:6379
      - CORS_ORIGINS=https://mcx3d.com,https://app.mcx3d.com
      - SESSION_COOKIE_SECURE=true
      - CSRF_COOKIE_SECURE=true
      - OAUTH2_REDIRECT_HTTPS=true
    secrets:
      - secret_key
      - encryption_key
      - xero_client_id
      - xero_client_secret
    volumes:
      - ./mcx3d_finance:/app/mcx3d_finance
      - static_volume:/app/static
      - media_volume:/app/media
      - upload_volume:/app/uploads
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - mcx3d_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G

  db:
    image: postgres:15-alpine
    container_name: mcx3d_db
    environment:
      - POSTGRES_USER=mcx3d_user
      - POSTGRES_PASSWORD_FILE=/run/secrets/db_password
      - POSTGRES_DB=mcx3d_finance_prod
      - PGDATA=/var/lib/postgresql/data/pgdata
    secrets:
      - db_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    networks:
      - mcx3d_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mcx3d_user -d mcx3d_finance_prod"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G

  redis:
    image: redis:7-alpine
    container_name: mcx3d_redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - mcx3d_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

  celery:
    build: .
    container_name: mcx3d_celery
    command: celery -A mcx3d_finance.tasks worker --loglevel=info --concurrency=4
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql://mcx3d_user:${DB_PASSWORD}@db:5432/mcx3d_finance_prod
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - SECRET_KEY_FILE=/run/secrets/secret_key
    secrets:
      - secret_key
      - encryption_key
    volumes:
      - ./mcx3d_finance:/app/mcx3d_finance
    depends_on:
      - db
      - redis
    networks:
      - mcx3d_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G

  celery-beat:
    build: .
    container_name: mcx3d_celery_beat
    command: celery -A mcx3d_finance.tasks beat --loglevel=info
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql://mcx3d_user:${DB_PASSWORD}@db:5432/mcx3d_finance_prod
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - SECRET_KEY_FILE=/run/secrets/secret_key
    secrets:
      - secret_key
    volumes:
      - ./mcx3d_finance:/app/mcx3d_finance
    depends_on:
      - db
      - redis
    networks:
      - mcx3d_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

networks:
  mcx3d_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
  redis_data:
  static_volume:
  media_volume:
  upload_volume:

secrets:
  db_password:
    external: true
  secret_key:
    external: true
  encryption_key:
    external: true
  xero_client_id:
    external: true
  xero_client_secret:
    external: true