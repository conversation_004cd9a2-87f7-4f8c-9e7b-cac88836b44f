"""
Tests for API endpoint consolidation (Issue #6 verification).

Verifies that duplicate authentication endpoints have been eliminated
and all auth endpoints are properly consolidated into a single router.
"""

import pytest
import os
import sys
from pathlib import Path
from fastapi.testclient import TestClient
from fastapi import FastAP<PERSON>
from typing import List, Dict, Set

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from mcx3d_finance.main import app
from mcx3d_finance.api import auth


class TestEndpointConsolidation:
    """Test suite for verifying endpoint consolidation fixes from Issue #6."""
    
    def test_no_duplicate_auth_files_exist(self):
        """
        Verify that no duplicate auth route files exist.
        Issue #6 reported both auth.py and auth_routes.py existed.
        """
        api_dir = project_root / "mcx3d_finance" / "api"
        
        # Check that auth.py exists (consolidated endpoints)
        auth_py_path = api_dir / "auth.py"
        assert auth_py_path.exists(), "Consolidated auth.py file must exist"
        
        # Check that auth_routes.py does NOT exist (was duplicate)
        auth_routes_py_path = api_dir / "auth_routes.py"
        assert not auth_routes_py_path.exists(), \
            "Duplicate auth_routes.py file should not exist (Issue #6)"
        
        # Check for any other potential auth-related duplicates
        auth_files = list(api_dir.glob("*auth*.py"))
        auth_file_names = [f.name for f in auth_files]
        
        # Should only have auth.py and auth_middleware.py
        expected_auth_files = {"auth.py", "auth_middleware.py"}
        actual_auth_files = set(auth_file_names)
        
        unexpected_files = actual_auth_files - expected_auth_files
        assert not unexpected_files, \
            f"Unexpected auth files found: {unexpected_files}. Only auth.py and auth_middleware.py should exist."
    
    def test_consolidated_auth_endpoints_exist(self):
        """
        Verify that all expected auth endpoints exist in the consolidated router.
        """
        # Get all routes from the auth router
        auth_routes = []
        for route in auth.router.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                for method in route.methods:
                    auth_routes.append(f"{method} {route.path}")
        
        # Expected consolidated endpoints (from Issue #6 description)
        expected_endpoints = {
            "POST /login",
            "POST /refresh", 
            "POST /mfa/setup",
            "POST /mfa/verify",
            "POST /password/change",
            "GET /xero/authorize",
            "GET /xero/callback",
            "POST /xero/refresh/{organization_id}",
            "DELETE /xero/revoke/{organization_id}",
            "GET /xero/status/{organization_id}"
        }
        
        # Check that all expected endpoints exist
        for expected_endpoint in expected_endpoints:
            assert any(expected_endpoint in route for route in auth_routes), \
                f"Expected endpoint '{expected_endpoint}' not found in consolidated auth router"
    
    def test_no_conflicting_route_patterns(self):
        """
        Verify there are no conflicting or duplicate route patterns in the app.
        """
        client = TestClient(app)
        
        # Get all routes from the FastAPI app
        all_routes = {}
        duplicate_routes = []
        
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                for method in route.methods:
                    if method != 'HEAD':  # Ignore HEAD methods as they're auto-generated
                        route_key = f"{method} {route.path}"
                        if route_key in all_routes:
                            duplicate_routes.append(route_key)
                        else:
                            all_routes[route_key] = route
        
        assert not duplicate_routes, \
            f"Duplicate route patterns found: {duplicate_routes}. Each endpoint should be unique."
    
    def test_auth_router_properly_included(self):
        """
        Verify that the auth router is properly included in the main app.
        """
        # Check that auth routes are accessible through the main app
        client = TestClient(app)
        
        # Test that auth endpoints are reachable (should return proper responses, not 404)
        auth_endpoints_to_test = [
            ("/api/auth/xero/authorize", "GET"),
            ("/api/auth/login", "POST"),
        ]
        
        for endpoint, method in auth_endpoints_to_test:
            if method == "GET":
                response = client.get(endpoint)
            else:
                response = client.post(endpoint, json={})
            
            # Should not return 404 (not found) - endpoint should exist
            assert response.status_code != 404, \
                f"Auth endpoint {method} {endpoint} not found - router may not be properly included"
    
    def test_no_legacy_xero_login_endpoint(self):
        """
        Verify that the legacy /xero/login endpoint mentioned in Issue #6 no longer exists.
        Issue #6 reported auth_routes.py had @router.get("/xero/login") which conflicted 
        with auth.py's @router.get("/xero/authorize").
        """
        client = TestClient(app)
        
        # The legacy endpoint should not exist
        response = client.get("/api/auth/xero/login")
        assert response.status_code == 404, \
            "Legacy /xero/login endpoint should not exist (Issue #6 fix)"
        
        # The consolidated endpoint should exist
        response = client.get("/api/auth/xero/authorize")
        # Should not be 404 - endpoint should exist (may return other status codes for auth/validation)
        assert response.status_code != 404, \
            "Consolidated /xero/authorize endpoint should exist"
    
    def test_auth_endpoint_consistency(self):
        """
        Verify that auth endpoints follow consistent patterns and naming.
        """
        auth_routes = []
        for route in auth.router.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                auth_routes.append(route.path)
        
        # All auth endpoints should follow consistent patterns
        xero_endpoints = [path for path in auth_routes if 'xero' in path]
        
        # Xero endpoints should follow consistent naming
        expected_xero_patterns = [
            "/xero/authorize",  # Not "/xero/login" 
            "/xero/callback",
            "/xero/refresh/{organization_id}",
            "/xero/revoke/{organization_id}",
            "/xero/status/{organization_id}"
        ]
        
        for expected_pattern in expected_xero_patterns:
            assert expected_pattern in auth_routes, \
                f"Expected Xero endpoint pattern '{expected_pattern}' not found"
        
        # Ensure no legacy patterns exist
        legacy_patterns = ["/xero/login"]  # From Issue #6
        for legacy_pattern in legacy_patterns:
            assert legacy_pattern not in auth_routes, \
                f"Legacy endpoint pattern '{legacy_pattern}' should not exist (Issue #6 fix)"


class TestEndpointDocumentation:
    """Test that consolidated endpoints are properly documented."""
    
    def test_openapi_schema_consistency(self):
        """
        Verify that the OpenAPI schema reflects the consolidated endpoints correctly.
        """
        # Get OpenAPI schema
        openapi_schema = app.openapi()
        paths = openapi_schema.get("paths", {})
        
        # Check that auth endpoints are documented
        auth_paths = [path for path in paths.keys() if "auth" in path or "xero" in path]
        
        assert len(auth_paths) > 0, "Auth endpoints should be documented in OpenAPI schema"
        
        # Verify specific endpoints are documented
        expected_documented_endpoints = [
            "/api/auth/login",
            "/api/auth/xero/authorize", 
            "/api/auth/xero/callback"
        ]
        
        for endpoint in expected_documented_endpoints:
            # Check if endpoint exists in some form in the schema
            endpoint_found = any(endpoint in path for path in paths.keys())
            assert endpoint_found, f"Endpoint {endpoint} should be documented in OpenAPI schema"
    
    def test_endpoint_tags_consistency(self):
        """
        Verify that consolidated auth endpoints have consistent tags.
        """
        # All auth endpoints should be tagged consistently
        openapi_schema = app.openapi()
        paths = openapi_schema.get("paths", {})
        
        auth_endpoints = {}
        for path, methods in paths.items():
            if "auth" in path or "xero" in path:
                for method, details in methods.items():
                    if method.lower() in ['get', 'post', 'put', 'delete']:
                        tags = details.get('tags', [])
                        auth_endpoints[f"{method.upper()} {path}"] = tags
        
        # All auth endpoints should have the "Authentication" tag
        for endpoint, tags in auth_endpoints.items():
            assert "Authentication" in tags, \
                f"Endpoint {endpoint} should have 'Authentication' tag for consistency"


if __name__ == "__main__":
    # Run specific tests for endpoint consolidation
    pytest.main([__file__, "-v", "--tb=short"])