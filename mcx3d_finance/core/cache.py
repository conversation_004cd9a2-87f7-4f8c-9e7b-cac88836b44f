"""
Redis caching configuration and utilities for MCX3D Financials.
"""
import os
import json
import pickle
import logging
from typing import Optional, Any, Union, Callable
from datetime import datetime, timedelta
from functools import wraps
from redis import Redis, ConnectionPool
from redis.exceptions import RedisError

logger = logging.getLogger(__name__)


class CacheConfig:
    """Redis cache configuration."""
    
    # Redis connection settings
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    REDIS_PASSWORD: Optional[str] = os.getenv("REDIS_PASSWORD")
    REDIS_MAX_CONNECTIONS: int = int(os.getenv("REDIS_MAX_CONNECTIONS", "50"))
    REDIS_SOCKET_TIMEOUT: int = int(os.getenv("REDIS_SOCKET_TIMEOUT", "5"))
    REDIS_SOCKET_CONNECT_TIMEOUT: int = int(os.getenv("REDIS_SOCKET_CONNECT_TIMEOUT", "5"))
    REDIS_RETRY_ON_TIMEOUT: bool = True
    REDIS_HEALTH_CHECK_INTERVAL: int = int(os.getenv("REDIS_HEALTH_CHECK_INTERVAL", "30"))
    
    # Cache TTL settings (in seconds)
    CACHE_TTL_DEFAULT: int = int(os.getenv("CACHE_TTL_DEFAULT", "3600"))  # 1 hour
    CACHE_TTL_SHORT: int = int(os.getenv("CACHE_TTL_SHORT", "300"))  # 5 minutes
    CACHE_TTL_MEDIUM: int = int(os.getenv("CACHE_TTL_MEDIUM", "1800"))  # 30 minutes
    CACHE_TTL_LONG: int = int(os.getenv("CACHE_TTL_LONG", "86400"))  # 24 hours
    
    # Cache key prefixes
    KEY_PREFIX: str = "mcx3d:"
    KEY_SEPARATOR: str = ":"
    
    # Cache key patterns
    REPORT_KEY_PATTERN: str = f"{KEY_PREFIX}report{KEY_SEPARATOR}{{org_id}}{KEY_SEPARATOR}{{report_type}}{KEY_SEPARATOR}{{params_hash}}"
    XERO_DATA_KEY_PATTERN: str = f"{KEY_PREFIX}xero{KEY_SEPARATOR}{{org_id}}{KEY_SEPARATOR}{{data_type}}{KEY_SEPARATOR}{{xero_id}}"
    USER_SESSION_KEY_PATTERN: str = f"{KEY_PREFIX}session{KEY_SEPARATOR}{{user_id}}"
    API_RATE_LIMIT_KEY_PATTERN: str = f"{KEY_PREFIX}rate{KEY_SEPARATOR}{{client_ip}}{KEY_SEPARATOR}{{endpoint}}"
    
    # Serialization settings
    USE_PICKLE: bool = os.getenv("CACHE_USE_PICKLE", "true").lower() == "true"
    COMPRESSION_ENABLED: bool = os.getenv("CACHE_COMPRESSION", "true").lower() == "true"


class RedisCache:
    """Redis cache client with connection pooling and error handling."""
    
    def __init__(self):
        self._pool: Optional[ConnectionPool] = None
        self._client: Optional[Redis] = None
        self._connected: bool = False
        self._initialize_connection()
    
    def _initialize_connection(self) -> None:
        """Initialize Redis connection pool."""
        try:
            # Parse Redis URL
            import redis
            pool_kwargs = {
                "max_connections": CacheConfig.REDIS_MAX_CONNECTIONS,
                "socket_timeout": CacheConfig.REDIS_SOCKET_TIMEOUT,
                "socket_connect_timeout": CacheConfig.REDIS_SOCKET_CONNECT_TIMEOUT,
                "retry_on_timeout": CacheConfig.REDIS_RETRY_ON_TIMEOUT,
                "health_check_interval": CacheConfig.REDIS_HEALTH_CHECK_INTERVAL,
            }
            
            if CacheConfig.REDIS_PASSWORD:
                pool_kwargs["password"] = CacheConfig.REDIS_PASSWORD
            
            self._pool = redis.ConnectionPool.from_url(
                CacheConfig.REDIS_URL,
                **pool_kwargs
            )
            self._client = redis.Redis(connection_pool=self._pool, decode_responses=False)
            
            # Test connection
            self._client.ping()
            self._connected = True
            logger.info("Redis cache connected successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self._connected = False
    
    def is_connected(self) -> bool:
        """Check if Redis is connected."""
        return self._connected
    
    def _serialize(self, value: Any) -> bytes:
        """Serialize value for storage."""
        if CacheConfig.USE_PICKLE:
            data = pickle.dumps(value)
        else:
            data = json.dumps(value).encode('utf-8')
        
        if CacheConfig.COMPRESSION_ENABLED:
            import zlib
            data = zlib.compress(data)
        
        return data
    
    def _deserialize(self, data: bytes) -> Any:
        """Deserialize value from storage."""
        if data is None:
            return None
        
        if CacheConfig.COMPRESSION_ENABLED:
            import zlib
            data = zlib.decompress(data)
        
        if CacheConfig.USE_PICKLE:
            return pickle.loads(data)
        else:
            return json.loads(data.decode('utf-8'))
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        if not self._connected:
            return None
        
        try:
            data = self._client.get(key)
            return self._deserialize(data)
        except Exception as e:
            logger.warning(f"Cache get error for key {key}: {e}")
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache with optional TTL."""
        if not self._connected:
            return False
        
        try:
            ttl = ttl or CacheConfig.CACHE_TTL_DEFAULT
            data = self._serialize(value)
            return bool(self._client.setex(key, ttl, data))
        except Exception as e:
            logger.warning(f"Cache set error for key {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete key from cache."""
        if not self._connected:
            return False
        
        try:
            return bool(self._client.delete(key))
        except Exception as e:
            logger.warning(f"Cache delete error for key {key}: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        if not self._connected:
            return False
        
        try:
            return bool(self._client.exists(key))
        except Exception as e:
            logger.warning(f"Cache exists error for key {key}: {e}")
            return False
    
    def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching a pattern."""
        if not self._connected:
            return 0
        
        try:
            keys = self._client.keys(pattern)
            if keys:
                return self._client.delete(*keys)
            return 0
        except Exception as e:
            logger.warning(f"Cache clear pattern error for pattern {pattern}: {e}")
            return 0
    
    def get_ttl(self, key: str) -> Optional[int]:
        """Get TTL for a key in seconds."""
        if not self._connected:
            return None
        
        try:
            ttl = self._client.ttl(key)
            return ttl if ttl > 0 else None
        except Exception as e:
            logger.warning(f"Cache get TTL error for key {key}: {e}")
            return None
    
    def close(self) -> None:
        """Close Redis connection."""
        if self._client:
            self._client.close()
        if self._pool:
            self._pool.disconnect()
        self._connected = False


# Global cache instance
_cache_instance: Optional[RedisCache] = None


def get_cache() -> RedisCache:
    """Get global cache instance."""
    global _cache_instance
    if _cache_instance is None:
        _cache_instance = RedisCache()
    return _cache_instance


def cache_key(*args, **kwargs) -> str:
    """Generate cache key from arguments."""
    parts = [CacheConfig.KEY_PREFIX]
    
    # Add positional arguments
    for arg in args:
        if isinstance(arg, (str, int, float)):
            parts.append(str(arg))
        else:
            # Hash complex objects
            import hashlib
            parts.append(hashlib.md5(str(arg).encode()).hexdigest()[:8])
    
    # Add keyword arguments
    if kwargs:
        sorted_items = sorted(kwargs.items())
        for key, value in sorted_items:
            parts.append(f"{key}={value}")
    
    return CacheConfig.KEY_SEPARATOR.join(parts)


def cached(ttl: Optional[int] = None, key_prefix: Optional[str] = None):
    """Decorator for caching function results."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            if key_prefix:
                cache_key_parts = [key_prefix, func.__name__]
            else:
                cache_key_parts = [func.__module__, func.__name__]
            
            # Add function arguments to key
            cache_key_parts.extend([str(arg) for arg in args])
            if kwargs:
                cache_key_parts.extend([f"{k}={v}" for k, v in sorted(kwargs.items())])
            
            key = cache_key(*cache_key_parts)
            
            # Try to get from cache
            cache = get_cache()
            result = cache.get(key)
            
            if result is not None:
                logger.debug(f"Cache hit for key: {key}")
                return result
            
            # Call function and cache result
            logger.debug(f"Cache miss for key: {key}")
            result = func(*args, **kwargs)
            
            if result is not None:
                cache.set(key, result, ttl)
            
            return result
        
        return wrapper
    return decorator


def invalidate_cache(pattern: str) -> int:
    """Invalidate cache entries matching a pattern."""
    cache = get_cache()
    count = cache.clear_pattern(pattern)
    logger.info(f"Invalidated {count} cache entries matching pattern: {pattern}")
    return count


def invalidate_org_cache(org_id: int) -> None:
    """Invalidate all cache entries for an organization."""
    patterns = [
        f"{CacheConfig.KEY_PREFIX}*{CacheConfig.KEY_SEPARATOR}{org_id}{CacheConfig.KEY_SEPARATOR}*",
        f"{CacheConfig.KEY_PREFIX}report{CacheConfig.KEY_SEPARATOR}{org_id}{CacheConfig.KEY_SEPARATOR}*",
        f"{CacheConfig.KEY_PREFIX}xero{CacheConfig.KEY_SEPARATOR}{org_id}{CacheConfig.KEY_SEPARATOR}*",
    ]
    
    total = 0
    for pattern in patterns:
        total += invalidate_cache(pattern)
    
    logger.info(f"Invalidated {total} cache entries for organization {org_id}")


def get_cache_stats() -> dict:
    """Get cache statistics."""
    cache = get_cache()
    
    if not cache.is_connected():
        return {
            "connected": False,
            "error": "Redis not connected"
        }
    
    try:
        info = cache._client.info()
        return {
            "connected": True,
            "used_memory": info.get("used_memory_human", "N/A"),
            "connected_clients": info.get("connected_clients", 0),
            "total_commands": info.get("total_commands_processed", 0),
            "keyspace_hits": info.get("keyspace_hits", 0),
            "keyspace_misses": info.get("keyspace_misses", 0),
            "hit_rate": round(
                info.get("keyspace_hits", 0) / 
                max(info.get("keyspace_hits", 0) + info.get("keyspace_misses", 0), 1) * 100, 
                2
            ),
            "evicted_keys": info.get("evicted_keys", 0),
            "expired_keys": info.get("expired_keys", 0),
        }
    except Exception as e:
        logger.error(f"Failed to get cache stats: {e}")
        return {
            "connected": True,
            "error": str(e)
        }