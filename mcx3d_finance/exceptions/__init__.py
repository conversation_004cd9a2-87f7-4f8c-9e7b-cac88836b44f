"""
MCX3D Finance Exception Hierarchy

This module provides comprehensive exception handling for the MCX3D financial
valuation system. All exceptions inherit from MCX3DException to enable 
consistent error handling patterns across the application.

Usage:
    from mcx3d_finance.exceptions import (
        ReportGenerationError,
        FinancialCalculationError,
        ValidationError
    )
"""

from .base import (
    MCX3DException,
    MCX3DConfigurationError,
    MCX3DSystemError,
    MCX3DResourceError,
    MCX3DTimeoutError
)

from .reporting import (
    MCX3DReportingException,
    ReportGenerationError,
    ReportDataValidationError,
    ReportOutputError,
    ChartGenerationError,
    ReportTemplateError,
    ReportFormatError,
    ReportMemoryError
)

from .financial import (
    MCX3DFinancialException,
    FinancialCalculationError,
    FinancialDataError,
    ValuationError,
    ProjectionError,
    DCFCalculationError,
    MultiplesValuationError,
    SaaSValuationError,
    FinancialRangeError,
    NumericalStabilityError
)

from .validation import (
    MCX3DValidationException,
    ValidationError,
    DataIntegrityError,
    SchemaValidationError,
    BusinessRuleValidationError,
    ConfigurationValidationError,
    RequiredFieldError,
    DataTypeValidationError
)

from .integration import (
    MCX3DIntegrationException,
    XeroIntegrationError,
    APIConnectionError,
    DataSyncError,
    AuthenticationError,
    RateLimitError
)

__all__ = [
    # Base exceptions
    'MCX3DException',
    'MCX3DConfigurationError',
    'MCX3DSystemError',
    'MCX3DResourceError',
    'MCX3DTimeoutError',
    
    # Reporting exceptions
    'MCX3DReportingException',
    'ReportGenerationError',
    'ReportDataValidationError',
    'ReportOutputError',
    'ChartGenerationError',
    'ReportTemplateError',
    'ReportFormatError',
    'ReportMemoryError',
    
    # Financial exceptions
    'MCX3DFinancialException',
    'FinancialCalculationError',
    'FinancialDataError',
    'ValuationError',
    'ProjectionError',
    'DCFCalculationError',
    'MultiplesValuationError',
    'SaaSValuationError',
    'FinancialRangeError',
    'NumericalStabilityError',
    
    # Validation exceptions
    'MCX3DValidationException',
    'ValidationError',
    'DataIntegrityError',
    'SchemaValidationError',
    'BusinessRuleValidationError',
    'ConfigurationValidationError',
    'RequiredFieldError',
    'DataTypeValidationError',
    
    # Integration exceptions
    'MCX3DIntegrationException',
    'XeroIntegrationError',
    'APIConnectionError',
    'DataSyncError',
    'AuthenticationError',
    'RateLimitError',
]