#!/usr/bin/env python3
"""
MCX3D Financial System - Phase 1 Usage Examples
Comprehensive examples showing how to use all Phase 1 components.
"""

import sys
import os
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import Phase 1 components
from mcx3d_finance.core.financials.cash_flow import CashFlowGenerator
from mcx3d_finance.core.valuation.dcf import DCFValuation
from mcx3d_finance.core.valuation.multiples import MultiplesValuation
from mcx3d_finance.core.metrics.saas_kpis import SaaSKPICalculator


def example_cash_flow_analysis():
    """Example: Generate comprehensive cash flow analysis."""
    print("=" * 60)
    print("📊 CASH FLOW ANALYSIS EXAMPLE")
    print("=" * 60)
    
    # Mock database session for example
    from unittest.mock import Mock
    mock_session = Mock()
    mock_org = Mock()
    mock_org.name = "TechCorp SaaS Inc."
    mock_org.id = 1
    mock_session.query().filter().first.return_value = mock_org
    mock_session.query().join().filter().scalar.return_value = 250000
    
    # Create cash flow generator
    generator = CashFlowGenerator(mock_session)
    
    # Define analysis period
    from_date = datetime(2024, 1, 1)
    to_date = datetime(2024, 12, 31)
    
    print(f"Analyzing cash flows for {mock_org.name}")
    print(f"Period: {from_date.strftime('%Y-%m-%d')} to {to_date.strftime('%Y-%m-%d')}")
    
    # Generate indirect cash flow statement
    print("\n🔄 Generating Indirect Cash Flow Statement...")
    indirect_cf = generator.generate_cash_flow_statement(
        organization_id=1,
        from_date=from_date,
        to_date=to_date,
        method="indirect",
        include_comparative=True
    )
    
    # Display key results
    operating = indirect_cf["operating_activities"]
    investing = indirect_cf["investing_activities"]
    financing = indirect_cf["financing_activities"]
    summary = indirect_cf["cash_summary"]
    
    print(f"Net Income: ${operating.get('net_income', 0):,.2f}")
    print(f"Operating Cash Flow: ${operating['net_cash_from_operating']:,.2f}")
    print(f"Investing Cash Flow: ${investing['net_cash_from_investing']:,.2f}")
    print(f"Financing Cash Flow: ${financing['net_cash_from_financing']:,.2f}")
    print(f"Net Change in Cash: ${summary['net_change_in_cash']:,.2f}")
    print(f"Ending Cash Balance: ${summary['ending_cash']:,.2f}")
    
    # Generate direct cash flow statement
    print("\n➡️  Generating Direct Cash Flow Statement...")
    direct_cf = generator.generate_cash_flow_statement(
        organization_id=1,
        from_date=from_date,
        to_date=to_date,
        method="direct"
    )
    
    direct_operating = direct_cf["operating_activities"]
    print(f"Cash Receipts from Customers: ${direct_operating['cash_receipts_from_customers']:,.2f}")
    print(f"Cash Payments to Suppliers: ${direct_operating['cash_payments_to_suppliers']:,.2f}")
    print(f"Cash Payments to Employees: ${direct_operating['cash_payments_to_employees']:,.2f}")
    
    # Financial analysis
    analysis = indirect_cf["financial_analysis"]
    print("\n📈 Financial Analysis:")
    print(f"Free Cash Flow: ${analysis['cash_flow_ratios']['free_cash_flow']:,.2f}")
    print(f"Cash Burn Rate: ${analysis['liquidity_analysis']['cash_burn_rate']:,.2f}/month")
    print(f"Cash Runway: {analysis['liquidity_analysis']['cash_runway']:.1f} months")
    
    return indirect_cf, direct_cf


def example_dcf_valuation():
    """Example: Perform comprehensive DCF valuation."""
    print("\n" + "=" * 60)
    print("💰 DCF VALUATION EXAMPLE")
    print("=" * 60)
    
    # Create DCF valuation instance
    dcf = DCFValuation()
    
    # Sample financial projections (5-year forecast)
    projections = [
        {
            "year": 1,
            "revenue": 5000000,
            "operating_expenses": 3500000,
            "ebitda": 1500000,
            "free_cash_flow": 1200000,
            "capex": 200000,
        },
        {
            "year": 2,
            "revenue": 6500000,
            "operating_expenses": 4200000,
            "ebitda": 2300000,
            "free_cash_flow": 1800000,
            "capex": 300000,
        },
        {
            "year": 3,
            "revenue": 8450000,
            "operating_expenses": 5200000,
            "ebitda": 3250000,
            "free_cash_flow": 2600000,
            "capex": 400000,
        },
        {
            "year": 4,
            "revenue": 10985000,
            "operating_expenses": 6400000,
            "ebitda": 4585000,
            "free_cash_flow": 3700000,
            "capex": 500000,
        },
        {
            "year": 5,
            "revenue": 14281000,
            "operating_expenses": 7800000,
            "ebitda": 6481000,
            "free_cash_flow": 5200000,
            "capex": 600000,
        },
    ]
    
    print("Financial Projections:")
    for proj in projections:
        print(f"Year {proj['year']}: Revenue ${proj['revenue']:,.0f}, FCF ${proj['free_cash_flow']:,.0f}")
    
    # Calculate WACC
    print("\n📊 Calculating WACC...")
    wacc = dcf.calculate_wacc(
        market_value_equity=50000000,
        market_value_debt=10000000,
        cost_of_equity=0.14,
        cost_of_debt=0.06,
        tax_rate=0.25
    )
    print(f"WACC: {wacc:.2%}")
    
    # Perform DCF valuation with scenarios
    print("\n🎯 Performing DCF Valuation...")
    dcf_results = dcf.calculate_dcf_valuation(
        financial_projections=projections,
        discount_rate=wacc,
        terminal_growth_rate=0.03,
        scenarios=["base", "upside", "downside", "conservative"]
    )
    
    # Display results
    print("\nValuation Results:")
    for scenario, results in dcf_results["valuation_results"].items():
        enterprise_value = results["enterprise_value"]
        terminal_value = results["terminal_value"]
        print(f"{scenario.title()}: ${enterprise_value:,.0f} (Terminal: ${terminal_value:,.0f})")
    
    # Sensitivity analysis
    sensitivity = dcf_results["sensitivity_analysis"]
    print("\nSensitivity Analysis:")
    print(f"Base Valuation: ${sensitivity['base_valuation']:,.0f}")
    print(f"Valuation Range: ${sensitivity['min_valuation']:,.0f} - ${sensitivity['max_valuation']:,.0f}")
    print(f"Volatility: {sensitivity['volatility']:.1%}")
    
    # Monte Carlo simulation
    print("\n🎲 Running Monte Carlo Simulation...")
    monte_carlo = dcf.perform_monte_carlo_simulation(
        projections=projections,
        discount_rate=wacc,
        terminal_growth_rate=0.03,
        num_simulations=5000
    )
    
    print(f"Monte Carlo Results ({monte_carlo['num_simulations']} simulations):")
    print(f"Mean Valuation: ${monte_carlo['mean_valuation']:,.0f}")
    print(f"Median Valuation: ${monte_carlo['median_valuation']:,.0f}")
    print(f"Standard Deviation: ${monte_carlo['standard_deviation']:,.0f}")
    print(f"90% Confidence Interval: ${monte_carlo['percentiles']['p5']:,.0f} - ${monte_carlo['percentiles']['p95']:,.0f}")
    
    # Enterprise to equity conversion
    print("\n🏢 Converting to Equity Value...")
    equity_conversion = dcf.convert_enterprise_to_equity_value(
        enterprise_value=dcf_results["valuation_results"]["base"]["enterprise_value"],
        cash_and_equivalents=2000000,
        total_debt=5000000,
        shares_outstanding=1000000
    )
    
    print(f"Enterprise Value: ${equity_conversion['enterprise_value']:,.0f}")
    print(f"Less: Total Debt: ${equity_conversion['total_debt']:,.0f}")
    print(f"Plus: Cash: ${equity_conversion['cash_and_equivalents']:,.0f}")
    print(f"Equity Value: ${equity_conversion['equity_value']:,.0f}")
    print(f"Value per Share: ${equity_conversion['value_per_share']:.2f}")
    
    return dcf_results


def example_multiples_valuation():
    """Example: Perform multiples-based valuation."""
    print("\n" + "=" * 60)
    print("📈 MULTIPLES VALUATION EXAMPLE")
    print("=" * 60)
    
    # Create multiples valuation instance
    multiples = MultiplesValuation()
    
    # Target company metrics
    target_metrics = {
        "revenue": 12000000,
        "ebitda": 3600000,
        "net_income": 2000000,
        "book_value": 8000000,
        "free_cash_flow": 2800000,
        "sales": 12000000,
    }
    
    print("Target Company Metrics:")
    for metric, value in target_metrics.items():
        print(f"{metric.replace('_', ' ').title()}: ${value:,.0f}")
    
    # Comparable companies data
    comparable_companies = [
        {
            "company": "SaaS Competitor A",
            "ev_revenue": 4.2,
            "ev_ebitda": 14.5,
            "pe_ratio": 28.0,
            "price_to_book": 3.2,
            "ev_fcf": 18.0,
        },
        {
            "company": "SaaS Competitor B",
            "ev_revenue": 5.8,
            "ev_ebitda": 19.2,
            "pe_ratio": 35.0,
            "price_to_book": 4.1,
            "ev_fcf": 22.5,
        },
        {
            "company": "SaaS Competitor C",
            "ev_revenue": 3.9,
            "ev_ebitda": 12.8,
            "pe_ratio": 24.5,
            "price_to_book": 2.8,
            "ev_fcf": 16.2,
        },
        {
            "company": "SaaS Competitor D",
            "ev_revenue": 6.2,
            "ev_ebitda": 21.0,
            "pe_ratio": 32.0,
            "price_to_book": 3.8,
            "ev_fcf": 25.0,
        },
        {
            "company": "SaaS Competitor E",
            "ev_revenue": 4.7,
            "ev_ebitda": 16.5,
            "pe_ratio": 29.5,
            "price_to_book": 3.5,
            "ev_fcf": 20.0,
        },
    ]
    
    print(f"\nComparable Companies ({len(comparable_companies)} companies):")
    for comp in comparable_companies:
        print(f"{comp['company']}: EV/Rev {comp['ev_revenue']:.1f}x, EV/EBITDA {comp['ev_ebitda']:.1f}x, P/E {comp['pe_ratio']:.1f}x")
    
    # Perform comprehensive multiples valuation
    print("\n🎯 Performing Multiples Valuation...")
    multiples_results = multiples.calculate_comprehensive_multiples_valuation(
        target_metrics=target_metrics,
        comparable_companies=comparable_companies,
        valuation_multiples=["ev_revenue", "ev_ebitda", "pe_ratio", "ev_fcf"],
        weights={"ev_revenue": 0.3, "ev_ebitda": 0.4, "pe_ratio": 0.2, "ev_fcf": 0.1}
    )
    
    # Display industry statistics
    print("\nIndustry Statistics:")
    industry_stats = multiples_results["industry_statistics"]
    for multiple, stats in industry_stats.items():
        print(f"{multiple.upper()}: Mean {stats['mean']:.1f}x, Median {stats['median']:.1f}x, Range {stats['min']:.1f}x-{stats['max']:.1f}x")
    
    # Display individual valuations
    print("\nIndividual Multiple Valuations:")
    individual_vals = multiples_results["individual_valuations"]
    for multiple, valuation in individual_vals.items():
        if "error" not in valuation:
            recommended = valuation["recommended_valuation"]
            confidence = valuation["confidence_score"]
            print(f"{multiple.upper()}: ${recommended:,.0f} (Confidence: {confidence:.1%})")
    
    # Display weighted valuation
    weighted_val = multiples_results["weighted_valuation"]
    print(f"\nWeighted Valuation: ${weighted_val['weighted_valuation']:,.0f}")
    
    # Display quality-adjusted valuation
    quality_adj = multiples_results["quality_adjusted_valuation"]
    print(f"Quality-Adjusted Valuation: ${quality_adj['quality_adjusted_valuation']:,.0f}")
    
    # Display valuation summary
    summary = multiples_results["valuation_summary"]
    print("\nValuation Summary:")
    if "valuation_range" in summary:
        val_range = summary["valuation_range"]
        print(f"Range: ${val_range['minimum']:,.0f} - ${val_range['maximum']:,.0f}")
        print(f"Average: ${val_range['average']:,.0f}")
    if "recommended_valuation" in summary:
        print(f"Recommended: ${summary['recommended_valuation']:,.0f}")
    if "valuation_confidence" in summary:
        print(f"Confidence: {summary['valuation_confidence']}")

    # Show any error messages
    if "error" in summary:
        print(f"Note: {summary['error']}")
    
    return multiples_results


def example_saas_kpis():
    """Example: Calculate comprehensive SaaS KPIs."""
    print("\n" + "=" * 60)
    print("🚀 SAAS KPIS EXAMPLE")
    print("=" * 60)
    
    # Create SaaS KPI calculator
    calculator = SaaSKPICalculator()
    
    # Calculate comprehensive KPIs
    print("Calculating SaaS KPIs for Q4 2024...")
    kpis_result = calculator.calculate_comprehensive_kpis(
        organization_id=1,
        period_start=datetime(2024, 10, 1),
        period_end=datetime(2024, 12, 31)
    )
    
    kpis = kpis_result["kpis"]
    
    # Revenue Metrics
    print("\n💰 Revenue Metrics:")
    revenue_metrics = kpis["revenue_metrics"]
    print(f"MRR: ${revenue_metrics['monthly_recurring_revenue']:,.2f}")
    print(f"ARR: ${revenue_metrics['annual_recurring_revenue']:,.2f}")
    print(f"Revenue Growth Rate: {revenue_metrics['revenue_growth_rate']['monthly']:.1f}% MoM")
    print(f"Revenue Churn Rate: {revenue_metrics['revenue_churn_rate']:.1f}%")
    print(f"Net Revenue Retention: {revenue_metrics['net_revenue_retention']:.1f}%")
    print(f"ARPU: ${revenue_metrics['average_revenue_per_user']:,.2f}")
    
    # Customer Metrics
    print("\n👥 Customer Metrics:")
    customer_metrics = kpis["customer_metrics"]
    print(f"Total Customers: {customer_metrics['total_customers']:,}")
    print(f"Active Customers: {customer_metrics['active_customers']:,}")
    print(f"Customer Churn Rate: {customer_metrics['customer_churn_rate']:.1f}%")
    print(f"CAC: ${customer_metrics['customer_acquisition_cost']:,.2f}")
    print(f"LTV: ${customer_metrics['customer_lifetime_value']:,.2f}")
    print(f"LTV/CAC Ratio: {customer_metrics['ltv_cac_ratio']:.1f}x")
    print(f"Payback Period: {customer_metrics['payback_period_months']:.1f} months")
    
    # Unit Economics
    print("\n📊 Unit Economics:")
    unit_economics = kpis["unit_economics"]
    print(f"Gross Margin: {unit_economics['gross_margin_percent']:.1f}%")
    print(f"Monthly Gross Profit per Customer: ${unit_economics['monthly_gross_profit_per_customer']:,.2f}")
    print(f"Unit Economics Health: {unit_economics['unit_economics_health']}")
    
    # Growth Metrics
    print("\n📈 Growth Metrics:")
    growth_metrics = kpis["growth_metrics"]
    print(f"Customer Growth Rate: {growth_metrics['customer_growth_rate']:.1f}%")
    print(f"Net New MRR: ${growth_metrics['net_new_mrr']:,.2f}")
    print(f"Growth Efficiency: {growth_metrics['growth_efficiency']:.2f}")
    
    # Efficiency Metrics
    print("\n⚡ Efficiency Metrics:")
    efficiency_metrics = kpis["efficiency_metrics"]
    print(f"Magic Number: {efficiency_metrics['magic_number']:.2f}")
    print(f"Rule of 40: {efficiency_metrics['rule_of_40']:.1f}")
    print(f"Sales Efficiency: {efficiency_metrics['sales_efficiency']}")
    
    # Health Score
    print("\n🏥 Health Score:")
    health_score = kpis["health_score"]
    print(f"Overall Score: {health_score['overall_score']:.1f}/100")
    print(f"Health Grade: {health_score['health_grade']}")
    print(f"Health Status: {health_score['health_status']}")
    
    # Component Scores
    component_scores = health_score["component_scores"]
    print(f"Revenue Score: {component_scores['revenue_score']:.1f}/25")
    print(f"Customer Score: {component_scores['customer_score']:.1f}/25")
    print(f"Unit Economics Score: {component_scores['unit_economics_score']:.1f}/25")
    print(f"Efficiency Score: {component_scores['efficiency_score']:.1f}/25")
    
    # Recommendations
    print("\n💡 Recommendations:")
    recommendations = health_score["recommendations"]
    for i, rec in enumerate(recommendations, 1):
        print(f"{i}. {rec}")
    
    # Summary
    print("\n📋 Executive Summary:")
    summary = kpis_result["summary"]
    print(f"Health Status: {summary['health_status']}")
    
    highlights = summary["key_highlights"]
    print("Key Highlights:")
    for highlight in highlights:
        print(f"  • {highlight}")
    
    concerns = summary["key_concerns"]
    print("Key Concerns:")
    for concern in concerns:
        print(f"  • {concern}")
    
    return kpis_result


def main():
    """Run all Phase 1 examples."""
    print("🚀 MCX3D Financial System - Phase 1 Usage Examples")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Run all examples
        example_cash_flow_analysis()
        example_dcf_valuation()
        example_multiples_valuation()
        example_saas_kpis()
        
        print("\n" + "=" * 60)
        print("✅ ALL EXAMPLES COMPLETED SUCCESSFULLY")
        print("=" * 60)
        print("Phase 1 components are ready for production use!")
        
    except Exception as e:
        print(f"\n❌ Example execution failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
