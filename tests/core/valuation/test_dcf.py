import pytest
from mcx3d_finance.core.valuation.dcf import calculate_dcf_valuation


@pytest.fixture
def sample_financial_projections():
    """
    Provides a sample list of financial projections for testing.
    """
    return [100, 120, 150, 180, 200]


def test_calculate_dcf_valuation_with_sample_data(sample_financial_projections):
    """
    Tests the DCF valuation calculation with a typical set of financial projections.
    """
    valuation = calculate_dcf_valuation(sample_financial_projections, 0.1, 0.02)

    # Expected values are calculated manually for verification
    expected_present_values = [
        100 / (1.1) ** 1,
        120 / (1.1) ** 2,
        150 / (1.1) ** 3,
        180 / (1.1) ** 4,
        200 / (1.1) ** 5,
    ]
    expected_terminal_value = (200 * 1.02) / (0.1 - 0.02)
    expected_present_terminal_value = expected_terminal_value / (1.1) ** 5
    expected_valuation = sum(expected_present_values) + expected_present_terminal_value

    assert valuation == pytest.approx(expected_valuation)


def test_calculate_dcf_valuation_with_no_projections():
    """
    Tests the DCF valuation calculation with an empty list of financial projections.
    """
    with pytest.raises(IndexError):
        calculate_dcf_valuation([], 0.1, 0.02)


def test_calculate_dcf_valuation_with_zero_discount_rate():
    """
    Tests the DCF valuation calculation with a zero discount rate.
    """
    with pytest.raises(ZeroDivisionError):
        calculate_dcf_valuation([100], 0.02, 0.02)


def test_calculate_dcf_valuation_with_equal_rates():
    """
    Tests the DCF valuation calculation when the discount rate is equal to
    the terminal growth rate.
    """
    with pytest.raises(ZeroDivisionError):
        calculate_dcf_valuation([100], 0.02, 0.02)
