# Start from a Python 3.9 base image
FROM python:3.9

# Set the working directory
WORKDIR /app

# Copy the requirements.txt file and install the dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application code
COPY . .

# Install the application in editable mode
RUN pip install -e .

# Expose the port for the FastAPI application
EXPOSE 8000

# Define the command to run the application using uvicorn
CMD ["uvicorn", "mcx3d_finance.main:app", "--host", "0.0.0.0", "--port", "8000"]
