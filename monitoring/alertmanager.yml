global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: ''
  smtp_auth_password: ''
  slack_api_url: '${SLACK_WEBHOOK_URL}'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default-receiver'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 5s
      repeat_interval: 30m
    - match:
        severity: warning
      receiver: 'warning-alerts'
      repeat_interval: 2h
    - match:
        severity: error
      receiver: 'error-alerts'
      repeat_interval: 1h

receivers:
  - name: 'default-receiver'
    slack_configs:
      - channel: '#mcx3d-monitoring'
        title: 'MCX3D Financial Alert'
        text: '{{ range .Alerts }}{{ .Annotations.description }}{{ end }}'
        send_resolved: true

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: 'CRITICAL: MCX3D Financial System Alert'
        body: |
          Alert: {{ .GroupLabels.alertname }}
          Severity: {{ .CommonLabels.severity }}
          Component: {{ .CommonLabels.component }}
          
          {{ range .Alerts }}
          Description: {{ .Annotations.description }}
          Started: {{ .StartsAt }}
          {{ end }}
    slack_configs:
      - channel: '#mcx3d-critical'
        title: '🚨 CRITICAL ALERT: {{ .GroupLabels.alertname }}'
        text: |
          *Severity:* {{ .CommonLabels.severity }}
          *Component:* {{ .CommonLabels.component }}
          {{ range .Alerts }}
          *Description:* {{ .Annotations.description }}
          *Started:* {{ .StartsAt }}
          {{ end }}
        send_resolved: true
        color: 'danger'
    pagerduty_configs:
      - routing_key: '${PAGERDUTY_INTEGRATION_KEY}'
        description: 'MCX3D Financial Critical Alert: {{ .GroupLabels.alertname }}'

  - name: 'error-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: 'ERROR: MCX3D Financial System Alert'
        body: |
          Alert: {{ .GroupLabels.alertname }}
          Severity: {{ .CommonLabels.severity }}
          Component: {{ .CommonLabels.component }}
          
          {{ range .Alerts }}
          Description: {{ .Annotations.description }}
          Started: {{ .StartsAt }}
          {{ end }}
    slack_configs:
      - channel: '#mcx3d-monitoring'
        title: '❌ ERROR: {{ .GroupLabels.alertname }}'
        text: |
          *Severity:* {{ .CommonLabels.severity }}
          *Component:* {{ .CommonLabels.component }}
          {{ range .Alerts }}
          *Description:* {{ .Annotations.description }}
          {{ end }}
        send_resolved: true
        color: '#ff6600'

  - name: 'warning-alerts'
    slack_configs:
      - channel: '#mcx3d-monitoring'
        title: '⚠️ WARNING: {{ .GroupLabels.alertname }}'
        text: |
          *Severity:* {{ .CommonLabels.severity }}
          *Component:* {{ .CommonLabels.component }}
          {{ range .Alerts }}
          *Description:* {{ .Annotations.description }}
          {{ end }}
        send_resolved: true
        color: 'warning'

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'cluster', 'service']