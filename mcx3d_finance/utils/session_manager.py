"""
Session management with refresh token support and security features.
Implements secure session handling with Redis backend.
"""
import logging
import secrets
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta, timezone
from jose import jwt, JWTError
import redis
import json

from ..core.config import get_security_config

logger = logging.getLogger(__name__)


class SessionManager:
    """
    Manages user sessions with refresh tokens and security features.
    
    Features:
    - Refresh token rotation
    - Session invalidation
    - Device tracking
    - Concurrent session limits
    - Session activity tracking
    """
    
    def __init__(self, redis_client: Optional[redis.Redis] = None):
        """
        Initialize session manager.
        
        Args:
            redis_client: Redis client instance. If None, creates new one.
        """
        self.security_config = get_security_config()
        self.secret_key = self.security_config["secret_key"]
        self.algorithm = self.security_config.get("algorithm", "HS256")
        
        # Token expiration settings
        self.access_token_expire = timedelta(
            minutes=self.security_config.get("access_token_expire_minutes", 15)
        )
        self.refresh_token_expire = timedelta(
            days=self.security_config.get("refresh_token_expire_days", 30)
        )
        
        # Session settings
        self.max_sessions_per_user = self.security_config.get("max_sessions_per_user", 5)
        self.session_idle_timeout = timedelta(
            minutes=self.security_config.get("session_idle_timeout_minutes", 60)
        )
        
        # Redis connection
        if redis_client:
            self.redis = redis_client
        else:
            redis_url = self.security_config.get("redis_url", "redis://localhost:6379/0")
            self.redis = redis.from_url(redis_url, decode_responses=True)
    
    def create_session(
        self,
        user_id: str,
        user_data: Dict[str, Any],
        device_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, str]:
        """
        Create a new session with access and refresh tokens.
        
        Args:
            user_id: User identifier
            user_data: User data to include in tokens
            device_info: Device/client information
            
        Returns:
            Dictionary with access_token, refresh_token, and session_id
        """
        # Generate session ID
        session_id = secrets.token_urlsafe(32)
        
        # Check concurrent session limit
        self._enforce_session_limit(user_id)
        
        # Create tokens
        access_token = self._create_access_token(user_id, user_data, session_id)
        refresh_token = self._create_refresh_token(user_id, session_id)
        
        # Store session in Redis
        session_data = {
            "user_id": user_id,
            "session_id": session_id,
            "user_data": user_data,
            "device_info": device_info or {},
            "created_at": datetime.now(timezone.utc).isoformat(),
            "last_activity": datetime.now(timezone.utc).isoformat(),
            "refresh_token": refresh_token,
            "is_active": True
        }
        
        # Store session with expiration
        session_key = f"session:{user_id}:{session_id}"
        self.redis.setex(
            session_key,
            self.refresh_token_expire,
            json.dumps(session_data)
        )
        
        # Add to user's session list
        user_sessions_key = f"user_sessions:{user_id}"
        self.redis.sadd(user_sessions_key, session_id)
        self.redis.expire(user_sessions_key, self.refresh_token_expire)
        
        logger.info(f"Created session {session_id} for user {user_id}")
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "session_id": session_id,
            "expires_in": int(self.access_token_expire.total_seconds())
        }
    
    def refresh_session(self, refresh_token: str) -> Dict[str, str]:
        """
        Refresh access token using refresh token with rotation.
        
        Args:
            refresh_token: Current refresh token
            
        Returns:
            Dictionary with new access_token and refresh_token
            
        Raises:
            ValueError: If refresh token is invalid or expired
        """
        try:
            # Decode refresh token
            payload = jwt.decode(
                refresh_token,
                self.secret_key,
                algorithms=[self.algorithm]
            )
            
            user_id = payload["sub"]
            session_id = payload["session_id"]
            
            # Get session from Redis
            session_key = f"session:{user_id}:{session_id}"
            session_data = self.redis.get(session_key)
            
            if not session_data:
                raise ValueError("Session not found")
            
            session = json.loads(session_data)
            
            # Verify refresh token matches
            if session["refresh_token"] != refresh_token:
                logger.warning(f"Refresh token mismatch for session {session_id}")
                raise ValueError("Invalid refresh token")
            
            # Check if session is active
            if not session.get("is_active", True):
                raise ValueError("Session is inactive")
            
            # Rotate refresh token (one-time use)
            new_access_token = self._create_access_token(
                user_id,
                session["user_data"],
                session_id
            )
            new_refresh_token = self._create_refresh_token(user_id, session_id)
            
            # Update session
            session["refresh_token"] = new_refresh_token
            session["last_activity"] = datetime.now(timezone.utc).isoformat()
            
            self.redis.setex(
                session_key,
                self.refresh_token_expire,
                json.dumps(session)
            )
            
            logger.info(f"Refreshed session {session_id} for user {user_id}")
            
            return {
                "access_token": new_access_token,
                "refresh_token": new_refresh_token,
                "expires_in": int(self.access_token_expire.total_seconds())
            }
            
        except JWTError as e:
            logger.error(f"JWT error during refresh: {e}")
            raise ValueError("Invalid refresh token")
        except Exception as e:
            logger.error(f"Error refreshing session: {e}")
            raise ValueError("Failed to refresh session")
    
    def invalidate_session(self, user_id: str, session_id: str):
        """
        Invalidate a specific session.
        
        Args:
            user_id: User identifier
            session_id: Session identifier
        """
        session_key = f"session:{user_id}:{session_id}"
        session_data = self.redis.get(session_key)
        
        if session_data:
            session = json.loads(session_data)
            session["is_active"] = False
            session["invalidated_at"] = datetime.now(timezone.utc).isoformat()
            
            # Keep for audit trail but mark as inactive
            self.redis.setex(
                session_key,
                timedelta(days=7),  # Keep for 7 days for audit
                json.dumps(session)
            )
            
            # Remove from active sessions
            user_sessions_key = f"user_sessions:{user_id}"
            self.redis.srem(user_sessions_key, session_id)
            
            logger.info(f"Invalidated session {session_id} for user {user_id}")
    
    def invalidate_all_sessions(self, user_id: str):
        """
        Invalidate all sessions for a user.
        
        Args:
            user_id: User identifier
        """
        user_sessions_key = f"user_sessions:{user_id}"
        session_ids = self.redis.smembers(user_sessions_key)
        
        for session_id in session_ids:
            self.invalidate_session(user_id, session_id)
        
        logger.info(f"Invalidated all sessions for user {user_id}")
    
    def get_active_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all active sessions for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            List of active session details
        """
        user_sessions_key = f"user_sessions:{user_id}"
        session_ids = self.redis.smembers(user_sessions_key)
        
        active_sessions = []
        for session_id in session_ids:
            session_key = f"session:{user_id}:{session_id}"
            session_data = self.redis.get(session_key)
            
            if session_data:
                session = json.loads(session_data)
                if session.get("is_active", True):
                    # Remove sensitive data
                    safe_session = {
                        "session_id": session_id,
                        "device_info": session.get("device_info", {}),
                        "created_at": session["created_at"],
                        "last_activity": session["last_activity"]
                    }
                    active_sessions.append(safe_session)
        
        return active_sessions
    
    def update_session_activity(self, user_id: str, session_id: str):
        """
        Update last activity timestamp for a session.
        
        Args:
            user_id: User identifier
            session_id: Session identifier
        """
        session_key = f"session:{user_id}:{session_id}"
        session_data = self.redis.get(session_key)
        
        if session_data:
            session = json.loads(session_data)
            session["last_activity"] = datetime.now(timezone.utc).isoformat()
            
            self.redis.setex(
                session_key,
                self.refresh_token_expire,
                json.dumps(session)
            )
    
    def _create_access_token(
        self,
        user_id: str,
        user_data: Dict[str, Any],
        session_id: str
    ) -> str:
        """Create JWT access token."""
        payload = {
            "sub": user_id,
            "session_id": session_id,
            "type": "access",
            "exp": datetime.now(timezone.utc) + self.access_token_expire,
            **user_data
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def _create_refresh_token(self, user_id: str, session_id: str) -> str:
        """Create JWT refresh token."""
        payload = {
            "sub": user_id,
            "session_id": session_id,
            "type": "refresh",
            "exp": datetime.now(timezone.utc) + self.refresh_token_expire,
            "jti": secrets.token_urlsafe(16)  # Unique token ID
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def _enforce_session_limit(self, user_id: str):
        """
        Enforce maximum concurrent sessions per user.
        
        Args:
            user_id: User identifier
        """
        user_sessions_key = f"user_sessions:{user_id}"
        session_ids = list(self.redis.smembers(user_sessions_key))
        
        if len(session_ids) >= self.max_sessions_per_user:
            # Get session details to find oldest
            sessions = []
            for session_id in session_ids:
                session_key = f"session:{user_id}:{session_id}"
                session_data = self.redis.get(session_key)
                if session_data:
                    session = json.loads(session_data)
                    sessions.append((session_id, session["created_at"]))
            
            # Sort by creation time and remove oldest
            sessions.sort(key=lambda x: x[1])
            oldest_session_id = sessions[0][0]
            
            self.invalidate_session(user_id, oldest_session_id)
            logger.info(
                f"Removed oldest session {oldest_session_id} for user {user_id} "
                f"due to session limit"
            )


# Export for convenience
session_manager = SessionManager()