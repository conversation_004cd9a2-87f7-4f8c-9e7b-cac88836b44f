from fastapi import FastAPI
from mcx3d_finance.api import auth, webhook_routes, reports, metrics, xero_import_routes, health, financial_docs
from mcx3d_finance.api.error_middleware import ErrorHandlingMiddleware
from mcx3d_finance.core.config import validate_startup_config, get_config_health_status, detect_environment
from mcx3d_finance.security.middleware import setup_security_middleware
from mcx3d_finance.security.config import SecurityConfig
import logging
import os

logger = logging.getLogger(__name__)

# Validate configuration before starting the application
try:
    validate_startup_config()
    logger.info(f"✅ Starting MCX3D Financials API in {detect_environment()} environment")
except Exception as e:
    logger.error(f"❌ Configuration validation failed: {e}")
    logger.error("Application startup aborted due to configuration errors.")
    raise SystemExit(1)

app = FastAPI(
    title="MCX3D Financials API",
    description="API for financial reporting and KPI calculation.",
    version="2.0.0",
)

# Add centralized error handling middleware (must be first)
app.add_middleware(ErrorHandlingMiddleware)

# Setup comprehensive security middleware
# This includes CORS, security headers, CSRF, rate limiting, and request logging
setup_security_middleware(app)

# Include routers
app.include_router(auth.router, tags=["Authentication"])  # Consolidated auth endpoints
app.include_router(webhook_routes.router, prefix="/webhooks", tags=["Webhooks"])
app.include_router(reports.router, tags=["Reports"])  # Note: reports router already has /api prefix
app.include_router(metrics.router, prefix="/api", tags=["Metrics"])
app.include_router(xero_import_routes.router, tags=["Xero Import"])
app.include_router(health.router, tags=["Health"])
app.include_router(financial_docs.router, tags=["Financial Documentation"])


@app.get("/")
def read_root():
    return {"message": "Welcome to the MCX3D Financials API"}
