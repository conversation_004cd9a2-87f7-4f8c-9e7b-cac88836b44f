"""
Tests for encryption utilities.
"""
import pytest
import json
import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from unittest.mock import patch, Mock

from mcx3d_finance.utils.encryption import (
    TokenEncryption,
    SensitiveDataEncryption,
    EncryptionError,
    get_token_encryption,
    get_data_encryption,
    encrypt_token,
    decrypt_token
)


class TestTokenEncryption:
    """Test suite for TokenEncryption class."""

    @pytest.fixture
    def token_encryption(self):
        """Create TokenEncryption instance with test key."""
        with patch('mcx3d_finance.utils.encryption.get_security_config') as mock_config:
            # Generate a valid Fernet key for testing
            test_key = Fernet.generate_key().decode()
            mock_config.return_value = {
                'encryption_key': test_key
            }
            yield TokenEncryption()

    @pytest.fixture
    def sample_token(self):
        """Sample OAuth token for testing."""
        return {
            "access_token": "test_access_token_12345",
            "refresh_token": "test_refresh_token_67890",
            "expires_in": 1800,
            "token_type": "Bearer",
            "scope": "accounting.transactions"
        }

    def test_initialization_with_key(self):
        """Test initialization with provided encryption key."""
        with patch('mcx3d_finance.utils.encryption.get_security_config') as mock_config:
            test_key = Fernet.generate_key().decode()
            mock_config.return_value = {'encryption_key': test_key}
            
            encryptor = TokenEncryption()
            assert encryptor._fernet is not None

    def test_initialization_without_key(self):
        """Test initialization without encryption key (generates new)."""
        with patch('mcx3d_finance.utils.encryption.get_security_config') as mock_config:
            mock_config.return_value = {'encryption_key': None}
            
            with patch('mcx3d_finance.utils.encryption.logger') as mock_logger:
                encryptor = TokenEncryption()
                
                # Should log warning about generating new key
                mock_logger.warning.assert_called()
                assert encryptor._fernet is not None

    def test_initialization_with_invalid_key(self):
        """Test initialization with invalid encryption key."""
        with patch('mcx3d_finance.utils.encryption.get_security_config') as mock_config:
            mock_config.return_value = {'encryption_key': 'invalid_key'}
            
            with pytest.raises(EncryptionError) as exc_info:
                TokenEncryption()
            
            assert "Encryption initialization failed" in str(exc_info.value)

    def test_encrypt_token_success(self, token_encryption, sample_token):
        """Test successful token encryption."""
        encrypted = token_encryption.encrypt_token(sample_token)
        
        assert encrypted is not None
        assert isinstance(encrypted, str)
        assert len(encrypted) > 0
        # Should be base64 encoded
        base64.urlsafe_b64decode(encrypted.encode())

    def test_decrypt_token_success(self, token_encryption, sample_token):
        """Test successful token decryption."""
        encrypted = token_encryption.encrypt_token(sample_token)
        decrypted = token_encryption.decrypt_token(encrypted)
        
        assert decrypted == sample_token
        assert decrypted["access_token"] == sample_token["access_token"]
        assert decrypted["refresh_token"] == sample_token["refresh_token"]

    def test_encrypt_decrypt_roundtrip(self, token_encryption):
        """Test encryption/decryption roundtrip with various data."""
        test_cases = [
            {"simple": "data"},
            {"complex": {"nested": ["array", 123, True]}},
            {"unicode": "测试数据 🔐"},
            {"empty": {}},
            {"null_values": {"key": None}},
        ]
        
        for token in test_cases:
            encrypted = token_encryption.encrypt_token(token)
            decrypted = token_encryption.decrypt_token(encrypted)
            assert decrypted == token

    def test_encrypt_token_error(self, token_encryption):
        """Test encryption error handling."""
        # Non-serializable object
        invalid_token = {"function": lambda x: x}
        
        with pytest.raises(EncryptionError) as exc_info:
            token_encryption.encrypt_token(invalid_token)
        
        assert "Failed to encrypt token" in str(exc_info.value)

    def test_decrypt_token_invalid_data(self, token_encryption):
        """Test decryption with invalid encrypted data."""
        with pytest.raises(EncryptionError) as exc_info:
            token_encryption.decrypt_token("invalid_encrypted_data")
        
        assert "Failed to decrypt token" in str(exc_info.value)

    def test_decrypt_token_corrupted_data(self, token_encryption, sample_token):
        """Test decryption with corrupted encrypted data."""
        encrypted = token_encryption.encrypt_token(sample_token)
        # Corrupt the data by modifying a character
        corrupted = encrypted[:-1] + 'X'
        
        with pytest.raises(EncryptionError) as exc_info:
            token_encryption.decrypt_token(corrupted)
        
        assert "Failed to decrypt token" in str(exc_info.value)

    def test_rotate_encryption_key(self, sample_token):
        """Test encryption key rotation."""
        old_key = Fernet.generate_key().decode()
        new_key = Fernet.generate_key().decode()
        
        # Encrypt with old key
        with patch('mcx3d_finance.utils.encryption.get_security_config') as mock_config:
            mock_config.return_value = {'encryption_key': old_key}
            old_encryptor = TokenEncryption()
            encrypted_old = old_encryptor.encrypt_token(sample_token)
        
        # Rotate to new key
        with patch('mcx3d_finance.utils.encryption.get_security_config') as mock_config:
            mock_config.return_value = {'encryption_key': new_key}
            new_encryptor = TokenEncryption()
            encrypted_new = new_encryptor.rotate_encryption_key(
                old_key, new_key, encrypted_old
            )
        
        # Verify new encryption works
        decrypted = new_encryptor.decrypt_token(encrypted_new)
        assert decrypted == sample_token

    def test_rotate_encryption_key_error(self, token_encryption):
        """Test key rotation error handling."""
        old_key = "invalid_old_key"
        new_key = Fernet.generate_key().decode()
        
        with pytest.raises(EncryptionError) as exc_info:
            token_encryption.rotate_encryption_key(
                old_key, new_key, "some_encrypted_data"
            )
        
        assert "Failed to rotate encryption key" in str(exc_info.value)


class TestSensitiveDataEncryption:
    """Test suite for SensitiveDataEncryption class."""

    @pytest.fixture
    def data_encryption(self):
        """Create SensitiveDataEncryption instance."""
        with patch('mcx3d_finance.utils.encryption.get_security_config') as mock_config:
            test_key = Fernet.generate_key().decode()
            mock_config.return_value = {'encryption_key': test_key}
            yield SensitiveDataEncryption()

    def test_encrypt_field_success(self, data_encryption):
        """Test successful field encryption."""
        value = "sensitive_data_123"
        encrypted = data_encryption.encrypt_field(value)
        
        assert encrypted.startswith("ENC:")
        assert len(encrypted) > len("ENC:")
        assert value not in encrypted

    def test_encrypt_field_empty(self, data_encryption):
        """Test encrypting empty field."""
        assert data_encryption.encrypt_field("") == ""
        assert data_encryption.encrypt_field(None) is None

    def test_decrypt_field_success(self, data_encryption):
        """Test successful field decryption."""
        value = "sensitive_data_123"
        encrypted = data_encryption.encrypt_field(value)
        decrypted = data_encryption.decrypt_field(encrypted)
        
        assert decrypted == value

    def test_decrypt_field_not_encrypted(self, data_encryption):
        """Test decrypting non-encrypted field."""
        plain_value = "plain_text"
        assert data_encryption.decrypt_field(plain_value) == plain_value

    def test_decrypt_field_empty(self, data_encryption):
        """Test decrypting empty field."""
        assert data_encryption.decrypt_field("") == ""
        assert data_encryption.decrypt_field(None) is None

    def test_encrypt_pii_success(self, data_encryption):
        """Test encrypting PII fields in dictionary."""
        data = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "phone": "************",
            "public_id": "12345",
            "notes": "Some notes"
        }
        fields_to_encrypt = ["email", "phone"]
        
        encrypted_data = data_encryption.encrypt_pii(data, fields_to_encrypt)
        
        # Verify specified fields are encrypted
        assert encrypted_data["email"].startswith("ENC:")
        assert encrypted_data["phone"].startswith("ENC:")
        # Verify other fields are not encrypted
        assert encrypted_data["name"] == "John Doe"
        assert encrypted_data["public_id"] == "12345"
        assert encrypted_data["notes"] == "Some notes"

    def test_encrypt_pii_missing_fields(self, data_encryption):
        """Test encrypting PII with missing fields."""
        data = {"name": "John Doe"}
        fields_to_encrypt = ["email", "phone"]
        
        encrypted_data = data_encryption.encrypt_pii(data, fields_to_encrypt)
        
        # Should not raise error for missing fields
        assert encrypted_data == data

    def test_decrypt_pii_success(self, data_encryption):
        """Test decrypting PII fields in dictionary."""
        data = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "phone": "************"
        }
        fields_to_encrypt = ["email", "phone"]
        
        encrypted_data = data_encryption.encrypt_pii(data, fields_to_encrypt)
        decrypted_data = data_encryption.decrypt_pii(encrypted_data, fields_to_encrypt)
        
        assert decrypted_data == data

    def test_encrypt_decrypt_pii_roundtrip(self, data_encryption):
        """Test PII encryption/decryption roundtrip."""
        original_data = {
            "user_id": 123,
            "username": "johndoe",
            "email": "<EMAIL>",
            "ssn": "***********",
            "credit_card": "4111-1111-1111-1111",
            "public_data": "This is public"
        }
        sensitive_fields = ["email", "ssn", "credit_card"]
        
        encrypted = data_encryption.encrypt_pii(original_data, sensitive_fields)
        decrypted = data_encryption.decrypt_pii(encrypted, sensitive_fields)
        
        assert decrypted == original_data


class TestSingletonInstances:
    """Test singleton instance functions."""

    def test_get_token_encryption_singleton(self):
        """Test token encryption singleton."""
        with patch('mcx3d_finance.utils.encryption.get_security_config') as mock_config:
            test_key = Fernet.generate_key().decode()
            mock_config.return_value = {'encryption_key': test_key}
            
            instance1 = get_token_encryption()
            instance2 = get_token_encryption()
            
            assert instance1 is instance2

    def test_get_data_encryption_singleton(self):
        """Test data encryption singleton."""
        with patch('mcx3d_finance.utils.encryption.get_security_config') as mock_config:
            test_key = Fernet.generate_key().decode()
            mock_config.return_value = {'encryption_key': test_key}
            
            instance1 = get_data_encryption()
            instance2 = get_data_encryption()
            
            assert instance1 is instance2

    def test_utility_functions(self):
        """Test utility encryption/decryption functions."""
        with patch('mcx3d_finance.utils.encryption.get_security_config') as mock_config:
            test_key = Fernet.generate_key().decode()
            mock_config.return_value = {'encryption_key': test_key}
            
            token = {"test": "data"}
            
            encrypted = encrypt_token(token)
            decrypted = decrypt_token(encrypted)
            
            assert decrypted == token