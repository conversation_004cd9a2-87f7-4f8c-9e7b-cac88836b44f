"""
Enhanced data validation module with comprehensive financial integrity checks.
Provides FRS 102 compliance validation, business rule enforcement, and data quality monitoring.
"""

from typing import Dict, Any, List, Optional, Union
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime
from enum import Enum
import logging
from dataclasses import dataclass
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class ValidationSeverity(Enum):
    """Validation result severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ValidationCategory(Enum):
    """Categories of validation checks."""
    FINANCIAL_INTEGRITY = "financial_integrity"
    BUSINESS_RULES = "business_rules"
    CROSS_REFERENCE = "cross_reference"
    REGULATORY_COMPLIANCE = "regulatory_compliance"
    DATA_FRESHNESS = "data_freshness"
    DATA_QUALITY = "data_quality"


@dataclass
class ValidationResult:
    """Result of a validation check."""
    check_name: str
    category: ValidationCategory
    severity: ValidationSeverity
    passed: bool
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


@dataclass
class ValidationReport:
    """Comprehensive validation report."""
    organization_id: int
    validation_timestamp: datetime
    total_checks: int
    passed_checks: int
    failed_checks: int
    results: List[ValidationResult]
    summary: Dict[str, Any]
    
    @property
    def success_rate(self) -> float:
        """Calculate validation success rate."""
        return (self.passed_checks / self.total_checks * 100) if self.total_checks > 0 else 0.0
    
    @property
    def has_critical_errors(self) -> bool:
        """Check if report contains critical errors."""
        return any(r.severity == ValidationSeverity.CRITICAL and not r.passed for r in self.results)


class BaseValidator(ABC):
    """Abstract base class for all validators."""
    
    def __init__(self):
        self.precision = Decimal("0.01")
    
    def _round_currency(self, value: Union[float, Decimal]) -> Decimal:
        """Round currency values to 2 decimal places for FRS 102 compliance."""
        if isinstance(value, Decimal):
            return value.quantize(self.precision, rounding=ROUND_HALF_UP)
        return Decimal(str(value)).quantize(self.precision, rounding=ROUND_HALF_UP)
    
    @abstractmethod
    def validate(self, data: Dict[str, Any]) -> List[ValidationResult]:
        """Perform validation checks on the provided data."""
        pass
    
    def _create_result(
        self,
        check_name: str,
        category: ValidationCategory,
        severity: ValidationSeverity,
        passed: bool,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        """Create a validation result."""
        return ValidationResult(
            check_name=check_name,
            category=category,
            severity=severity,
            passed=passed,
            message=message,
            details=details
        )


class FinancialIntegrityValidator(BaseValidator):
    """Validator for financial data integrity checks."""
    
    def validate(self, data: Dict[str, Any]) -> List[ValidationResult]:
        """Validate financial data integrity."""
        results = []
        
        try:
            # Balance sheet equation validation
            if "balance_sheet" in data:
                results.extend(self._validate_balance_sheet_equation(data["balance_sheet"]))
            
            # Cash flow consistency validation
            if "cash_flow" in data:
                results.extend(self._validate_cash_flow_consistency(data["cash_flow"]))
            
            # Income statement validation
            if "income_statement" in data:
                results.extend(self._validate_income_statement(data["income_statement"]))
            
            # Cross-statement validation
            if all(key in data for key in ["balance_sheet", "income_statement", "cash_flow"]):
                results.extend(self._validate_cross_statement_consistency(data))
                
        except Exception as e:
            logger.error(f"Error in financial integrity validation: {e}")
            results.append(self._create_result(
                "financial_integrity_error",
                ValidationCategory.FINANCIAL_INTEGRITY,
                ValidationSeverity.CRITICAL,
                False,
                f"Financial integrity validation failed: {str(e)}"
            ))
        
        return results
    
    def _validate_balance_sheet_equation(self, balance_sheet: Dict[str, Any]) -> List[ValidationResult]:
        """Validate that Assets = Liabilities + Equity."""
        results = []
        
        try:
            assets = self._round_currency(balance_sheet.get("assets", {}).get("total_assets", 0))
            liabilities = self._round_currency(balance_sheet.get("liabilities", {}).get("total_liabilities", 0))
            equity = self._round_currency(balance_sheet.get("equity", {}).get("total_equity", 0))
            
            difference = abs(assets - (liabilities + equity))
            
            # Allow for small rounding differences (less than $1)
            if difference > Decimal("1.00"):
                results.append(self._create_result(
                    "balance_sheet_equation",
                    ValidationCategory.FINANCIAL_INTEGRITY,
                    ValidationSeverity.ERROR,
                    False,
                    f"Balance sheet equation failed. Assets: ${assets}, Liabilities + Equity: ${liabilities + equity}, Difference: ${difference}",
                    {"assets": float(assets), "liabilities": float(liabilities), "equity": float(equity), "difference": float(difference)}
                ))
            else:
                results.append(self._create_result(
                    "balance_sheet_equation",
                    ValidationCategory.FINANCIAL_INTEGRITY,
                    ValidationSeverity.INFO,
                    True,
                    "Balance sheet equation validated successfully",
                    {"difference": float(difference)}
                ))
                
        except Exception as e:
            results.append(self._create_result(
                "balance_sheet_equation",
                ValidationCategory.FINANCIAL_INTEGRITY,
                ValidationSeverity.ERROR,
                False,
                f"Error validating balance sheet equation: {str(e)}"
            ))
        
        return results
    
    def _validate_cash_flow_consistency(self, cash_flow: Dict[str, Any]) -> List[ValidationResult]:
        """Validate cash flow statement consistency."""
        results = []
        
        try:
            operating = self._round_currency(cash_flow.get("operating_activities", {}).get("net_cash_from_operating", 0))
            investing = self._round_currency(cash_flow.get("investing_activities", {}).get("net_cash_from_investing", 0))
            financing = self._round_currency(cash_flow.get("financing_activities", {}).get("net_cash_from_financing", 0))
            
            net_change = operating + investing + financing
            reported_change = self._round_currency(cash_flow.get("cash_summary", {}).get("net_change_in_cash", 0))
            
            difference = abs(net_change - reported_change)
            
            if difference > Decimal("1.00"):
                results.append(self._create_result(
                    "cash_flow_consistency",
                    ValidationCategory.FINANCIAL_INTEGRITY,
                    ValidationSeverity.ERROR,
                    False,
                    f"Cash flow consistency check failed. Calculated: ${net_change}, Reported: ${reported_change}, Difference: ${difference}",
                    {"calculated_change": float(net_change), "reported_change": float(reported_change), "difference": float(difference)}
                ))
            else:
                results.append(self._create_result(
                    "cash_flow_consistency",
                    ValidationCategory.FINANCIAL_INTEGRITY,
                    ValidationSeverity.INFO,
                    True,
                    "Cash flow consistency validated successfully"
                ))
                
        except Exception as e:
            results.append(self._create_result(
                "cash_flow_consistency",
                ValidationCategory.FINANCIAL_INTEGRITY,
                ValidationSeverity.ERROR,
                False,
                f"Error validating cash flow consistency: {str(e)}"
            ))
        
        return results
    
    def _validate_income_statement(self, income_statement: Dict[str, Any]) -> List[ValidationResult]:
        """Validate income statement structure and calculations."""
        results = []
        
        try:
            revenue = self._round_currency(income_statement.get("revenue", {}).get("total_revenue", 0))
            expenses = self._round_currency(income_statement.get("expenses", {}).get("total_expenses", 0))
            net_income = self._round_currency(income_statement.get("net_income", 0))
            
            calculated_net_income = revenue - expenses
            difference = abs(calculated_net_income - net_income)
            
            if difference > Decimal("1.00"):
                results.append(self._create_result(
                    "income_statement_calculation",
                    ValidationCategory.FINANCIAL_INTEGRITY,
                    ValidationSeverity.ERROR,
                    False,
                    f"Income statement calculation error. Revenue - Expenses = ${calculated_net_income}, Reported Net Income: ${net_income}",
                    {"revenue": float(revenue), "expenses": float(expenses), "calculated_net_income": float(calculated_net_income), "reported_net_income": float(net_income)}
                ))
            else:
                results.append(self._create_result(
                    "income_statement_calculation",
                    ValidationCategory.FINANCIAL_INTEGRITY,
                    ValidationSeverity.INFO,
                    True,
                    "Income statement calculations validated successfully"
                ))
                
        except Exception as e:
            results.append(self._create_result(
                "income_statement_calculation",
                ValidationCategory.FINANCIAL_INTEGRITY,
                ValidationSeverity.ERROR,
                False,
                f"Error validating income statement: {str(e)}"
            ))
        
        return results
    
    def _validate_cross_statement_consistency(self, data: Dict[str, Any]) -> List[ValidationResult]:
        """Validate consistency across financial statements."""
        results = []
        
        try:
            # Validate that net income from income statement matches cash flow starting point
            income_net_income = self._round_currency(data["income_statement"].get("net_income", 0))
            cash_flow_net_income = self._round_currency(
                data["cash_flow"].get("operating_activities", {}).get("net_income", 0)
            )
            
            if abs(income_net_income - cash_flow_net_income) > Decimal("1.00"):
                results.append(self._create_result(
                    "cross_statement_net_income",
                    ValidationCategory.FINANCIAL_INTEGRITY,
                    ValidationSeverity.WARNING,
                    False,
                    f"Net income mismatch between statements. Income Statement: ${income_net_income}, Cash Flow: ${cash_flow_net_income}",
                    {"income_statement_net_income": float(income_net_income), "cash_flow_net_income": float(cash_flow_net_income)}
                ))
            else:
                results.append(self._create_result(
                    "cross_statement_net_income",
                    ValidationCategory.FINANCIAL_INTEGRITY,
                    ValidationSeverity.INFO,
                    True,
                    "Cross-statement net income consistency validated"
                ))
                
        except Exception as e:
            results.append(self._create_result(
                "cross_statement_consistency",
                ValidationCategory.FINANCIAL_INTEGRITY,
                ValidationSeverity.ERROR,
                False,
                f"Error validating cross-statement consistency: {str(e)}"
            ))
        
        return results


class BusinessRuleValidator(BaseValidator):
    """Validator for business rule compliance."""

    def validate(self, data: Dict[str, Any]) -> List[ValidationResult]:
        """Validate business rules."""
        results = []

        try:
            # Revenue validation
            if "income_statement" in data:
                results.extend(self._validate_revenue_rules(data["income_statement"]))

            # Account balance validation
            if "balance_sheet" in data:
                results.extend(self._validate_account_balances(data["balance_sheet"]))

            # Date consistency validation
            results.extend(self._validate_date_consistency(data))

            # Transaction validation
            if "transactions" in data:
                results.extend(self._validate_transaction_rules(data["transactions"]))

        except Exception as e:
            logger.error(f"Error in business rule validation: {e}")
            results.append(self._create_result(
                "business_rule_error",
                ValidationCategory.BUSINESS_RULES,
                ValidationSeverity.CRITICAL,
                False,
                f"Business rule validation failed: {str(e)}"
            ))

        return results

    def _validate_revenue_rules(self, income_statement: Dict[str, Any]) -> List[ValidationResult]:
        """Validate revenue-related business rules."""
        results = []

        try:
            total_revenue = self._round_currency(income_statement.get("revenue", {}).get("total_revenue", 0))

            # Check for negative revenue
            if total_revenue < 0:
                results.append(self._create_result(
                    "negative_revenue",
                    ValidationCategory.BUSINESS_RULES,
                    ValidationSeverity.ERROR,
                    False,
                    f"Negative revenue detected: ${total_revenue}",
                    {"revenue": float(total_revenue)}
                ))
            else:
                results.append(self._create_result(
                    "revenue_sign_check",
                    ValidationCategory.BUSINESS_RULES,
                    ValidationSeverity.INFO,
                    True,
                    "Revenue sign validation passed"
                ))

            # Check for unusually high revenue growth (potential data error)
            if "comparative_period" in income_statement:
                prev_revenue = self._round_currency(
                    income_statement["comparative_period"].get("revenue", {}).get("total_revenue", 0)
                )
                if prev_revenue > 0:
                    growth_rate = ((total_revenue - prev_revenue) / prev_revenue) * 100
                    if growth_rate > 1000:  # More than 1000% growth
                        results.append(self._create_result(
                            "unusual_revenue_growth",
                            ValidationCategory.BUSINESS_RULES,
                            ValidationSeverity.WARNING,
                            False,
                            f"Unusual revenue growth detected: {growth_rate:.1f}%",
                            {"growth_rate": float(growth_rate), "current_revenue": float(total_revenue), "previous_revenue": float(prev_revenue)}
                        ))
                    else:
                        results.append(self._create_result(
                            "revenue_growth_check",
                            ValidationCategory.BUSINESS_RULES,
                            ValidationSeverity.INFO,
                            True,
                            f"Revenue growth within normal range: {growth_rate:.1f}%"
                        ))

        except Exception as e:
            results.append(self._create_result(
                "revenue_validation_error",
                ValidationCategory.BUSINESS_RULES,
                ValidationSeverity.ERROR,
                False,
                f"Error validating revenue rules: {str(e)}"
            ))

        return results

    def _validate_account_balances(self, balance_sheet: Dict[str, Any]) -> List[ValidationResult]:
        """Validate account balance business rules."""
        results = []

        try:
            # Check for negative cash (potential issue)
            cash_accounts = balance_sheet.get("assets", {}).get("current_assets", {})
            cash_balance = self._round_currency(cash_accounts.get("cash_and_cash_equivalents", 0))

            if cash_balance < 0:
                results.append(self._create_result(
                    "negative_cash",
                    ValidationCategory.BUSINESS_RULES,
                    ValidationSeverity.WARNING,
                    False,
                    f"Negative cash balance detected: ${cash_balance}",
                    {"cash_balance": float(cash_balance)}
                ))
            else:
                results.append(self._create_result(
                    "cash_balance_check",
                    ValidationCategory.BUSINESS_RULES,
                    ValidationSeverity.INFO,
                    True,
                    "Cash balance validation passed"
                ))

            # Check for negative equity (potential solvency issue)
            total_equity = self._round_currency(balance_sheet.get("equity", {}).get("total_equity", 0))
            if total_equity < 0:
                results.append(self._create_result(
                    "negative_equity",
                    ValidationCategory.BUSINESS_RULES,
                    ValidationSeverity.WARNING,
                    False,
                    f"Negative equity detected: ${total_equity} (potential solvency concern)",
                    {"total_equity": float(total_equity)}
                ))
            else:
                results.append(self._create_result(
                    "equity_balance_check",
                    ValidationCategory.BUSINESS_RULES,
                    ValidationSeverity.INFO,
                    True,
                    "Equity balance validation passed"
                ))

        except Exception as e:
            results.append(self._create_result(
                "account_balance_error",
                ValidationCategory.BUSINESS_RULES,
                ValidationSeverity.ERROR,
                False,
                f"Error validating account balances: {str(e)}"
            ))

        return results

    def _validate_date_consistency(self, data: Dict[str, Any]) -> List[ValidationResult]:
        """Validate date consistency across data."""
        results = []

        try:
            dates_to_check = []

            # Collect dates from different statements
            for statement_type in ["balance_sheet", "income_statement", "cash_flow"]:
                if statement_type in data:
                    report_date = data[statement_type].get("report_date")
                    if report_date:
                        if isinstance(report_date, str):
                            report_date = datetime.fromisoformat(report_date.replace('Z', '+00:00'))
                        dates_to_check.append((statement_type, report_date))

            # Check if all dates are the same
            if len(dates_to_check) > 1:
                base_date = dates_to_check[0][1]
                inconsistent_dates = [(name, date) for name, date in dates_to_check if date != base_date]

                if inconsistent_dates:
                    results.append(self._create_result(
                        "date_consistency",
                        ValidationCategory.BUSINESS_RULES,
                        ValidationSeverity.WARNING,
                        False,
                        f"Inconsistent report dates across statements: {inconsistent_dates}",
                        {"inconsistent_dates": [(name, date.isoformat()) for name, date in inconsistent_dates]}
                    ))
                else:
                    results.append(self._create_result(
                        "date_consistency",
                        ValidationCategory.BUSINESS_RULES,
                        ValidationSeverity.INFO,
                        True,
                        "Date consistency validation passed"
                    ))

        except Exception as e:
            results.append(self._create_result(
                "date_consistency_error",
                ValidationCategory.BUSINESS_RULES,
                ValidationSeverity.ERROR,
                False,
                f"Error validating date consistency: {str(e)}"
            ))

        return results

    def _validate_transaction_rules(self, transactions: List[Dict[str, Any]]) -> List[ValidationResult]:
        """Validate transaction-level business rules."""
        results = []

        try:
            if not transactions:
                results.append(self._create_result(
                    "transaction_data_availability",
                    ValidationCategory.BUSINESS_RULES,
                    ValidationSeverity.WARNING,
                    False,
                    "No transaction data available for validation"
                ))
                return results

            # Check for duplicate transactions
            transaction_keys = []
            duplicates = []

            for txn in transactions:
                key = (
                    txn.get("date"),
                    txn.get("amount"),
                    txn.get("account_id"),
                    txn.get("description", "")[:50]  # First 50 chars of description
                )
                if key in transaction_keys:
                    duplicates.append(txn)
                else:
                    transaction_keys.append(key)

            if duplicates:
                results.append(self._create_result(
                    "duplicate_transactions",
                    ValidationCategory.BUSINESS_RULES,
                    ValidationSeverity.WARNING,
                    False,
                    f"Found {len(duplicates)} potential duplicate transactions",
                    {"duplicate_count": len(duplicates)}
                ))
            else:
                results.append(self._create_result(
                    "duplicate_transaction_check",
                    ValidationCategory.BUSINESS_RULES,
                    ValidationSeverity.INFO,
                    True,
                    "No duplicate transactions detected"
                ))

            # Check for transactions with zero amounts
            zero_amount_txns = [txn for txn in transactions if txn.get("amount", 0) == 0]
            if zero_amount_txns:
                results.append(self._create_result(
                    "zero_amount_transactions",
                    ValidationCategory.BUSINESS_RULES,
                    ValidationSeverity.WARNING,
                    False,
                    f"Found {len(zero_amount_txns)} transactions with zero amounts",
                    {"zero_amount_count": len(zero_amount_txns)}
                ))
            else:
                results.append(self._create_result(
                    "zero_amount_check",
                    ValidationCategory.BUSINESS_RULES,
                    ValidationSeverity.INFO,
                    True,
                    "No zero-amount transactions detected"
                ))

        except Exception as e:
            results.append(self._create_result(
                "transaction_validation_error",
                ValidationCategory.BUSINESS_RULES,
                ValidationSeverity.ERROR,
                False,
                f"Error validating transaction rules: {str(e)}"
            ))

        return results


class CrossReferenceValidator(BaseValidator):
    """Validator for cross-reference consistency between related records."""

    def validate(self, data: Dict[str, Any]) -> List[ValidationResult]:
        """Validate cross-reference consistency."""
        results = []

        try:
            # Invoice-payment validation
            if "invoices" in data and "payments" in data:
                results.extend(self._validate_invoice_payment_consistency(data["invoices"], data["payments"]))

            # Account-transaction validation
            if "accounts" in data and "transactions" in data:
                results.extend(self._validate_account_transaction_consistency(data["accounts"], data["transactions"]))

            # Contact-invoice validation
            if "contacts" in data and "invoices" in data:
                results.extend(self._validate_contact_invoice_consistency(data["contacts"], data["invoices"]))

            # Transaction-account balance validation
            if "transactions" in data and "balance_sheet" in data:
                results.extend(self._validate_transaction_balance_consistency(data["transactions"], data["balance_sheet"]))

        except Exception as e:
            logger.error(f"Error in cross-reference validation: {e}")
            results.append(self._create_result(
                "cross_reference_error",
                ValidationCategory.CROSS_REFERENCE,
                ValidationSeverity.CRITICAL,
                False,
                f"Cross-reference validation failed: {str(e)}"
            ))

        return results

    def _validate_invoice_payment_consistency(self, invoices: List[Dict[str, Any]], payments: List[Dict[str, Any]]) -> List[ValidationResult]:
        """Validate consistency between invoices and payments."""
        results = []

        try:
            # Create payment lookup by invoice ID
            payments_by_invoice: Dict[str, List[Dict[str, Any]]] = {}
            for payment in payments:
                invoice_id = payment.get("invoice_id")
                if invoice_id:
                    if invoice_id not in payments_by_invoice:
                        payments_by_invoice[invoice_id] = []
                    payments_by_invoice[invoice_id].append(payment)

            orphaned_payments = 0
            overpaid_invoices = 0

            for invoice in invoices:
                invoice_id = invoice.get("id")
                if not invoice_id:
                    continue

                invoice_total = self._round_currency(invoice.get("total", 0))

                # Check if invoice has corresponding payments
                invoice_payments = payments_by_invoice.get(str(invoice_id), [])
                total_payments = sum(self._round_currency(p.get("amount", 0)) for p in invoice_payments)

                # Check for overpayment
                if total_payments > invoice_total:
                    overpaid_invoices += 1
                    results.append(self._create_result(
                        f"overpaid_invoice_{invoice_id}",
                        ValidationCategory.CROSS_REFERENCE,
                        ValidationSeverity.WARNING,
                        False,
                        f"Invoice {invoice_id} overpaid: Invoice total ${invoice_total}, Payments ${total_payments}",
                        {"invoice_id": invoice_id, "invoice_total": float(invoice_total), "total_payments": float(total_payments)}
                    ))

            # Check for orphaned payments (payments without corresponding invoices)
            invoice_ids = {inv.get("id") for inv in invoices}
            for payment in payments:
                invoice_id = payment.get("invoice_id")
                if invoice_id and invoice_id not in invoice_ids:
                    orphaned_payments += 1

            if orphaned_payments > 0:
                results.append(self._create_result(
                    "orphaned_payments",
                    ValidationCategory.CROSS_REFERENCE,
                    ValidationSeverity.WARNING,
                    False,
                    f"Found {orphaned_payments} payments without corresponding invoices",
                    {"orphaned_count": orphaned_payments}
                ))

            # Summary result
            if overpaid_invoices == 0 and orphaned_payments == 0:
                results.append(self._create_result(
                    "invoice_payment_consistency",
                    ValidationCategory.CROSS_REFERENCE,
                    ValidationSeverity.INFO,
                    True,
                    "Invoice-payment consistency validation passed"
                ))

        except Exception as e:
            results.append(self._create_result(
                "invoice_payment_error",
                ValidationCategory.CROSS_REFERENCE,
                ValidationSeverity.ERROR,
                False,
                f"Error validating invoice-payment consistency: {str(e)}"
            ))

        return results

    def _validate_account_transaction_consistency(self, accounts: List[Dict[str, Any]], transactions: List[Dict[str, Any]]) -> List[ValidationResult]:
        """Validate consistency between accounts and transactions."""
        results = []

        try:
            # Create account lookup
            account_ids = {acc.get("id") for acc in accounts}

            # Check for transactions referencing non-existent accounts
            orphaned_transactions = 0
            for transaction in transactions:
                account_id = transaction.get("account_id")
                if account_id and account_id not in account_ids:
                    orphaned_transactions += 1

            if orphaned_transactions > 0:
                results.append(self._create_result(
                    "orphaned_transactions",
                    ValidationCategory.CROSS_REFERENCE,
                    ValidationSeverity.ERROR,
                    False,
                    f"Found {orphaned_transactions} transactions referencing non-existent accounts",
                    {"orphaned_count": orphaned_transactions}
                ))
            else:
                results.append(self._create_result(
                    "account_transaction_consistency",
                    ValidationCategory.CROSS_REFERENCE,
                    ValidationSeverity.INFO,
                    True,
                    "Account-transaction consistency validation passed"
                ))

        except Exception as e:
            results.append(self._create_result(
                "account_transaction_error",
                ValidationCategory.CROSS_REFERENCE,
                ValidationSeverity.ERROR,
                False,
                f"Error validating account-transaction consistency: {str(e)}"
            ))

        return results

    def _validate_contact_invoice_consistency(self, contacts: List[Dict[str, Any]], invoices: List[Dict[str, Any]]) -> List[ValidationResult]:
        """Validate consistency between contacts and invoices."""
        results = []

        try:
            # Create contact lookup
            contact_ids = {contact.get("id") for contact in contacts}

            # Check for invoices referencing non-existent contacts
            orphaned_invoices = 0
            for invoice in invoices:
                contact_id = invoice.get("contact_id")
                if contact_id and contact_id not in contact_ids:
                    orphaned_invoices += 1

            if orphaned_invoices > 0:
                results.append(self._create_result(
                    "orphaned_invoices",
                    ValidationCategory.CROSS_REFERENCE,
                    ValidationSeverity.WARNING,
                    False,
                    f"Found {orphaned_invoices} invoices referencing non-existent contacts",
                    {"orphaned_count": orphaned_invoices}
                ))
            else:
                results.append(self._create_result(
                    "contact_invoice_consistency",
                    ValidationCategory.CROSS_REFERENCE,
                    ValidationSeverity.INFO,
                    True,
                    "Contact-invoice consistency validation passed"
                ))

        except Exception as e:
            results.append(self._create_result(
                "contact_invoice_error",
                ValidationCategory.CROSS_REFERENCE,
                ValidationSeverity.ERROR,
                False,
                f"Error validating contact-invoice consistency: {str(e)}"
            ))

        return results

    def _validate_transaction_balance_consistency(self, transactions: List[Dict[str, Any]], balance_sheet: Dict[str, Any]) -> List[ValidationResult]:
        """Validate that transaction totals align with balance sheet accounts."""
        results = []

        try:
            # Group transactions by account
            transaction_totals = {}
            for transaction in transactions:
                account_id = transaction.get("account_id")
                amount = self._round_currency(transaction.get("amount", 0))

                if account_id:
                    if account_id not in transaction_totals:
                        transaction_totals[account_id] = Decimal("0")
                    transaction_totals[account_id] += amount

            # This is a simplified check - in practice, you'd need to map
            # account IDs to balance sheet line items and consider debits/credits
            results.append(self._create_result(
                "transaction_balance_summary",
                ValidationCategory.CROSS_REFERENCE,
                ValidationSeverity.INFO,
                True,
                f"Processed {len(transaction_totals)} accounts with transaction activity",
                {"accounts_with_activity": len(transaction_totals)}
            ))

        except Exception as e:
            results.append(self._create_result(
                "transaction_balance_error",
                ValidationCategory.CROSS_REFERENCE,
                ValidationSeverity.ERROR,
                False,
                f"Error validating transaction-balance consistency: {str(e)}"
            ))

        return results


class RegulatoryComplianceValidator(BaseValidator):
    """Validator for regulatory compliance (FRS 102/UK GAAP) checks."""

    def validate(self, data: Dict[str, Any]) -> List[ValidationResult]:
        """Validate regulatory compliance."""
        results = []

        try:
            # FRS 102 compliance checks
            results.extend(self._validate_frs102_compliance(data))

            # Audit trail requirements
            results.extend(self._validate_audit_trail(data))

            # Financial statement disclosure requirements
            results.extend(self._validate_disclosure_requirements(data))

            # Revenue recognition compliance
            if "income_statement" in data:
                results.extend(self._validate_revenue_recognition(data["income_statement"]))

        except Exception as e:
            logger.error(f"Error in regulatory compliance validation: {e}")
            results.append(self._create_result(
                "regulatory_compliance_error",
                ValidationCategory.REGULATORY_COMPLIANCE,
                ValidationSeverity.CRITICAL,
                False,
                f"Regulatory compliance validation failed: {str(e)}"
            ))

        return results

    def _validate_frs102_compliance(self, data: Dict[str, Any]) -> List[ValidationResult]:
        """Validate FRS 102 compliance requirements."""
        results = []

        try:
            # Check for required financial statement components
            required_statements = ["balance_sheet", "income_statement", "cash_flow"]
            missing_statements = [stmt for stmt in required_statements if stmt not in data]

            if missing_statements:
                results.append(self._create_result(
                    "missing_financial_statements",
                    ValidationCategory.REGULATORY_COMPLIANCE,
                    ValidationSeverity.ERROR,
                    False,
                    f"Missing required financial statements: {missing_statements}",
                    {"missing_statements": missing_statements}
                ))
            else:
                results.append(self._create_result(
                    "financial_statements_complete",
                    ValidationCategory.REGULATORY_COMPLIANCE,
                    ValidationSeverity.INFO,
                    True,
                    "All required financial statements present"
                ))

            # Validate balance sheet classification
            if "balance_sheet" in data:
                results.extend(self._validate_balance_sheet_classification(data["balance_sheet"]))

            # Validate income statement structure
            if "income_statement" in data:
                results.extend(self._validate_income_statement_structure(data["income_statement"]))

        except Exception as e:
            results.append(self._create_result(
                "frs102_compliance_error",
                ValidationCategory.REGULATORY_COMPLIANCE,
                ValidationSeverity.ERROR,
                False,
                f"Error validating FRS 102 compliance: {str(e)}"
            ))

        return results

    def _validate_balance_sheet_classification(self, balance_sheet: Dict[str, Any]) -> List[ValidationResult]:
        """Validate balance sheet account classification per FRS 102."""
        results = []

        try:
            # Check for required balance sheet sections
            required_sections = ["assets", "liabilities", "equity"]
            missing_sections = [section for section in required_sections if section not in balance_sheet]

            if missing_sections:
                results.append(self._create_result(
                    "balance_sheet_structure",
                    ValidationCategory.REGULATORY_COMPLIANCE,
                    ValidationSeverity.ERROR,
                    False,
                    f"Missing balance sheet sections: {missing_sections}",
                    {"missing_sections": missing_sections}
                ))
            else:
                results.append(self._create_result(
                    "balance_sheet_structure",
                    ValidationCategory.REGULATORY_COMPLIANCE,
                    ValidationSeverity.INFO,
                    True,
                    "Balance sheet structure complies with FRS 102 requirements"
                ))

            # Validate current vs non-current classification
            if "assets" in balance_sheet:
                assets = balance_sheet["assets"]
                if "current_assets" not in assets or "non_current_assets" not in assets:
                    results.append(self._create_result(
                        "asset_classification",
                        ValidationCategory.REGULATORY_COMPLIANCE,
                        ValidationSeverity.WARNING,
                        False,
                        "Assets should be classified as current and fixed per FRS 102"
                    ))
                else:
                    results.append(self._create_result(
                        "asset_classification",
                        ValidationCategory.REGULATORY_COMPLIANCE,
                        ValidationSeverity.INFO,
                        True,
                        "Asset classification complies with FRS 102"
                    ))

        except Exception as e:
            results.append(self._create_result(
                "balance_sheet_classification_error",
                ValidationCategory.REGULATORY_COMPLIANCE,
                ValidationSeverity.ERROR,
                False,
                f"Error validating balance sheet classification: {str(e)}"
            ))

        return results

    def _validate_income_statement_structure(self, income_statement: Dict[str, Any]) -> List[ValidationResult]:
        """Validate income statement structure per FRS 102."""
        results = []

        try:
            # Check for required income statement components
            required_components = ["revenue", "expenses"]
            missing_components = [comp for comp in required_components if comp not in income_statement]

            if missing_components:
                results.append(self._create_result(
                    "income_statement_structure",
                    ValidationCategory.REGULATORY_COMPLIANCE,
                    ValidationSeverity.ERROR,
                    False,
                    f"Missing income statement components: {missing_components}",
                    {"missing_components": missing_components}
                ))
            else:
                results.append(self._create_result(
                    "income_statement_structure",
                    ValidationCategory.REGULATORY_COMPLIANCE,
                    ValidationSeverity.INFO,
                    True,
                    "Income statement structure complies with FRS 102"
                ))

        except Exception as e:
            results.append(self._create_result(
                "income_statement_structure_error",
                ValidationCategory.REGULATORY_COMPLIANCE,
                ValidationSeverity.ERROR,
                False,
                f"Error validating income statement structure: {str(e)}"
            ))

        return results

    def _validate_audit_trail(self, data: Dict[str, Any]) -> List[ValidationResult]:
        """Validate audit trail requirements."""
        results = []

        try:
            # Check for transaction audit trail
            if "transactions" in data:
                transactions_with_audit_trail = 0
                total_transactions = len(data["transactions"])

                for transaction in data["transactions"]:
                    # Check for required audit fields
                    audit_fields = ["created_date", "created_by", "last_modified_date"]
                    if all(field in transaction for field in audit_fields):
                        transactions_with_audit_trail += 1

                audit_coverage = (transactions_with_audit_trail / total_transactions * 100) if total_transactions > 0 else 0

                if audit_coverage < 95:  # Require 95% audit trail coverage
                    results.append(self._create_result(
                        "audit_trail_coverage",
                        ValidationCategory.REGULATORY_COMPLIANCE,
                        ValidationSeverity.WARNING,
                        False,
                        f"Insufficient audit trail coverage: {audit_coverage:.1f}% (minimum 95% required)",
                        {"coverage_percentage": audit_coverage, "transactions_with_trail": transactions_with_audit_trail, "total_transactions": total_transactions}
                    ))
                else:
                    results.append(self._create_result(
                        "audit_trail_coverage",
                        ValidationCategory.REGULATORY_COMPLIANCE,
                        ValidationSeverity.INFO,
                        True,
                        f"Audit trail coverage meets requirements: {audit_coverage:.1f}%"
                    ))
            else:
                results.append(self._create_result(
                    "audit_trail_data",
                    ValidationCategory.REGULATORY_COMPLIANCE,
                    ValidationSeverity.WARNING,
                    False,
                    "No transaction data available for audit trail validation"
                ))

        except Exception as e:
            results.append(self._create_result(
                "audit_trail_error",
                ValidationCategory.REGULATORY_COMPLIANCE,
                ValidationSeverity.ERROR,
                False,
                f"Error validating audit trail: {str(e)}"
            ))

        return results

    def _validate_disclosure_requirements(self, data: Dict[str, Any]) -> List[ValidationResult]:
        """Validate financial statement disclosure requirements."""
        results = []

        try:
            # Check for required disclosures in financial statements
            disclosure_checks = []

            # Check for accounting policies disclosure
            if "accounting_policies" not in data:
                disclosure_checks.append("accounting_policies")

            # Check for significant estimates disclosure
            if "significant_estimates" not in data:
                disclosure_checks.append("significant_estimates")

            if disclosure_checks:
                results.append(self._create_result(
                    "disclosure_requirements",
                    ValidationCategory.REGULATORY_COMPLIANCE,
                    ValidationSeverity.WARNING,
                    False,
                    f"Missing recommended disclosures: {disclosure_checks}",
                    {"missing_disclosures": disclosure_checks}
                ))
            else:
                results.append(self._create_result(
                    "disclosure_requirements",
                    ValidationCategory.REGULATORY_COMPLIANCE,
                    ValidationSeverity.INFO,
                    True,
                    "Disclosure requirements validation passed"
                ))

        except Exception as e:
            results.append(self._create_result(
                "disclosure_requirements_error",
                ValidationCategory.REGULATORY_COMPLIANCE,
                ValidationSeverity.ERROR,
                False,
                f"Error validating disclosure requirements: {str(e)}"
            ))

        return results

    def _validate_revenue_recognition(self, income_statement: Dict[str, Any]) -> List[ValidationResult]:
        """Validate revenue recognition compliance."""
        results = []

        try:
            # Basic revenue recognition validation
            revenue_data = income_statement.get("revenue", {})

            # Check for revenue breakdown (ASC 606 compliance)
            if "revenue_breakdown" in revenue_data:
                results.append(self._create_result(
                    "revenue_recognition_detail",
                    ValidationCategory.REGULATORY_COMPLIANCE,
                    ValidationSeverity.INFO,
                    True,
                    "Revenue recognition detail provided"
                ))
            else:
                results.append(self._create_result(
                    "revenue_recognition_detail",
                    ValidationCategory.REGULATORY_COMPLIANCE,
                    ValidationSeverity.WARNING,
                    False,
                    "Consider providing revenue recognition detail for ASC 606 compliance"
                ))

        except Exception as e:
            results.append(self._create_result(
                "revenue_recognition_error",
                ValidationCategory.REGULATORY_COMPLIANCE,
                ValidationSeverity.ERROR,
                False,
                f"Error validating revenue recognition: {str(e)}"
            ))

        return results


class DataFreshnessValidator(BaseValidator):
    """Validator for data freshness and staleness detection."""

    def __init__(self, max_age_hours: int = 24):
        super().__init__()
        self.max_age_hours = max_age_hours

    def validate(self, data: Dict[str, Any]) -> List[ValidationResult]:
        """Validate data freshness."""
        results = []

        try:
            # Timestamp validation
            results.extend(self._validate_timestamps(data))

            # Data staleness detection
            results.extend(self._validate_data_staleness(data))

            # Sync status monitoring
            results.extend(self._validate_sync_status(data))

            # Last update validation
            results.extend(self._validate_last_updates(data))

        except Exception as e:
            logger.error(f"Error in data freshness validation: {e}")
            results.append(self._create_result(
                "data_freshness_error",
                ValidationCategory.DATA_FRESHNESS,
                ValidationSeverity.CRITICAL,
                False,
                f"Data freshness validation failed: {str(e)}"
            ))

        return results

    def _validate_timestamps(self, data: Dict[str, Any]) -> List[ValidationResult]:
        """Validate timestamp consistency and format."""
        results = []

        try:
            current_time = datetime.utcnow()
            invalid_timestamps = []
            future_timestamps = []

            # Check timestamps in various data sections
            timestamp_fields = [
                ("balance_sheet", "report_date"),
                ("income_statement", "report_date"),
                ("cash_flow", "report_date"),
                ("metadata", "last_sync_date"),
                ("metadata", "data_extraction_date")
            ]

            for section, field in timestamp_fields:
                if section in data and field in data[section]:
                    timestamp_str = data[section][field]
                    try:
                        if isinstance(timestamp_str, str):
                            timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                        elif isinstance(timestamp_str, datetime):
                            timestamp = timestamp_str
                        else:
                            invalid_timestamps.append(f"{section}.{field}")
                            continue

                        # Check for future timestamps
                        if timestamp > current_time:
                            future_timestamps.append(f"{section}.{field}: {timestamp.isoformat()}")

                    except (ValueError, TypeError):
                        invalid_timestamps.append(f"{section}.{field}")

            # Report invalid timestamps
            if invalid_timestamps:
                results.append(self._create_result(
                    "invalid_timestamps",
                    ValidationCategory.DATA_FRESHNESS,
                    ValidationSeverity.ERROR,
                    False,
                    f"Invalid timestamp formats found: {invalid_timestamps}",
                    {"invalid_timestamps": invalid_timestamps}
                ))

            # Report future timestamps
            if future_timestamps:
                results.append(self._create_result(
                    "future_timestamps",
                    ValidationCategory.DATA_FRESHNESS,
                    ValidationSeverity.WARNING,
                    False,
                    f"Future timestamps detected: {future_timestamps}",
                    {"future_timestamps": future_timestamps}
                ))

            # Success case
            if not invalid_timestamps and not future_timestamps:
                results.append(self._create_result(
                    "timestamp_validation",
                    ValidationCategory.DATA_FRESHNESS,
                    ValidationSeverity.INFO,
                    True,
                    "All timestamps are valid and consistent"
                ))

        except Exception as e:
            results.append(self._create_result(
                "timestamp_validation_error",
                ValidationCategory.DATA_FRESHNESS,
                ValidationSeverity.ERROR,
                False,
                f"Error validating timestamps: {str(e)}"
            ))

        return results

    def _validate_data_staleness(self, data: Dict[str, Any]) -> List[ValidationResult]:
        """Detect stale data based on age."""
        results = []

        try:
            current_time = datetime.utcnow()
            stale_data_sections = []

            # Check last sync date
            if "metadata" in data and "last_sync_date" in data["metadata"]:
                last_sync_str = data["metadata"]["last_sync_date"]
                try:
                    if isinstance(last_sync_str, str):
                        last_sync = datetime.fromisoformat(last_sync_str.replace('Z', '+00:00'))
                    else:
                        last_sync = last_sync_str

                    age_hours = (current_time - last_sync).total_seconds() / 3600

                    if age_hours > self.max_age_hours:
                        stale_data_sections.append(f"last_sync ({age_hours:.1f} hours old)")

                except (ValueError, TypeError):
                    stale_data_sections.append("last_sync (invalid date)")

            # Check financial statement dates
            for statement_type in ["balance_sheet", "income_statement", "cash_flow"]:
                if statement_type in data and "report_date" in data[statement_type]:
                    report_date_str = data[statement_type]["report_date"]
                    try:
                        if isinstance(report_date_str, str):
                            report_date = datetime.fromisoformat(report_date_str.replace('Z', '+00:00'))
                        else:
                            report_date = report_date_str

                        age_days = (current_time - report_date).days

                        # Financial statements older than 90 days might be stale
                        if age_days > 90:
                            stale_data_sections.append(f"{statement_type} ({age_days} days old)")

                    except (ValueError, TypeError):
                        stale_data_sections.append(f"{statement_type} (invalid date)")

            # Report stale data
            if stale_data_sections:
                results.append(self._create_result(
                    "stale_data_detection",
                    ValidationCategory.DATA_FRESHNESS,
                    ValidationSeverity.WARNING,
                    False,
                    f"Stale data detected: {stale_data_sections}",
                    {"stale_sections": stale_data_sections}
                ))
            else:
                results.append(self._create_result(
                    "data_freshness_check",
                    ValidationCategory.DATA_FRESHNESS,
                    ValidationSeverity.INFO,
                    True,
                    "All data is within acceptable freshness thresholds"
                ))

        except Exception as e:
            results.append(self._create_result(
                "data_staleness_error",
                ValidationCategory.DATA_FRESHNESS,
                ValidationSeverity.ERROR,
                False,
                f"Error validating data staleness: {str(e)}"
            ))

        return results

    def _validate_sync_status(self, data: Dict[str, Any]) -> List[ValidationResult]:
        """Validate synchronization status."""
        results = []

        try:
            if "metadata" not in data:
                results.append(self._create_result(
                    "sync_metadata_missing",
                    ValidationCategory.DATA_FRESHNESS,
                    ValidationSeverity.WARNING,
                    False,
                    "Sync metadata not available for validation"
                ))
                return results

            metadata = data["metadata"]

            # Check sync status
            sync_status = metadata.get("sync_status", "unknown")
            if sync_status == "failed":
                results.append(self._create_result(
                    "sync_status_failed",
                    ValidationCategory.DATA_FRESHNESS,
                    ValidationSeverity.ERROR,
                    False,
                    "Last synchronization failed",
                    {"sync_status": sync_status, "sync_error": metadata.get("sync_error", "Unknown error")}
                ))
            elif sync_status == "in_progress":
                results.append(self._create_result(
                    "sync_status_in_progress",
                    ValidationCategory.DATA_FRESHNESS,
                    ValidationSeverity.INFO,
                    True,
                    "Synchronization currently in progress"
                ))
            elif sync_status == "completed":
                results.append(self._create_result(
                    "sync_status_completed",
                    ValidationCategory.DATA_FRESHNESS,
                    ValidationSeverity.INFO,
                    True,
                    "Last synchronization completed successfully"
                ))
            else:
                results.append(self._create_result(
                    "sync_status_unknown",
                    ValidationCategory.DATA_FRESHNESS,
                    ValidationSeverity.WARNING,
                    False,
                    f"Unknown sync status: {sync_status}"
                ))

        except Exception as e:
            results.append(self._create_result(
                "sync_status_error",
                ValidationCategory.DATA_FRESHNESS,
                ValidationSeverity.ERROR,
                False,
                f"Error validating sync status: {str(e)}"
            ))

        return results

    def _validate_last_updates(self, data: Dict[str, Any]) -> List[ValidationResult]:
        """Validate last update timestamps for different data types."""
        results = []

        try:
            current_time = datetime.utcnow()
            update_checks = []

            # Check various data type updates
            data_types = ["transactions", "invoices", "accounts", "contacts"]

            for data_type in data_types:
                if data_type in data and isinstance(data[data_type], list) and data[data_type]:
                    # Find the most recent update in this data type
                    most_recent = None
                    for item in data[data_type]:
                        if "last_modified_date" in item:
                            try:
                                modified_date_str = item["last_modified_date"]
                                if isinstance(modified_date_str, str):
                                    modified_date = datetime.fromisoformat(modified_date_str.replace('Z', '+00:00'))
                                else:
                                    modified_date = modified_date_str

                                if most_recent is None or modified_date > most_recent:
                                    most_recent = modified_date
                            except (ValueError, TypeError):
                                continue

                    if most_recent:
                        age_hours = (current_time - most_recent).total_seconds() / 3600
                        update_checks.append({
                            "data_type": data_type,
                            "last_update": most_recent.isoformat(),
                            "age_hours": age_hours
                        })

            # Report update status
            if update_checks:
                recent_updates = [check for check in update_checks if isinstance(check["age_hours"], (int, float)) and int(check["age_hours"]) <= 24]
                old_updates = [check for check in update_checks if isinstance(check["age_hours"], (int, float)) and int(check["age_hours"]) > 24]

                if old_updates:
                    old_updates_str = ', '.join([f"{u.get('data_type', 'unknown')} ({u.get('age_hours', 0):.1f}h)" for u in old_updates])
                    results.append(self._create_result(
                        "old_data_updates",
                        ValidationCategory.DATA_FRESHNESS,
                        ValidationSeverity.WARNING,
                        False,
                        f"Some data types have old updates: {old_updates_str}",
                        {"old_updates": old_updates}
                    ))

                if recent_updates:
                    results.append(self._create_result(
                        "recent_data_updates",
                        ValidationCategory.DATA_FRESHNESS,
                        ValidationSeverity.INFO,
                        True,
                        f"Recent updates found for: {[u['data_type'] for u in recent_updates]}"
                    ))
            else:
                results.append(self._create_result(
                    "no_update_timestamps",
                    ValidationCategory.DATA_FRESHNESS,
                    ValidationSeverity.WARNING,
                    False,
                    "No last update timestamps found in data"
                ))

        except Exception as e:
            results.append(self._create_result(
                "last_updates_error",
                ValidationCategory.DATA_FRESHNESS,
                ValidationSeverity.ERROR,
                False,
                f"Error validating last updates: {str(e)}"
            ))

        return results


class DataValidationEngine:
    """Main data validation engine that orchestrates all validation checks."""

    def __init__(self):
        self.validators = {
            ValidationCategory.FINANCIAL_INTEGRITY: FinancialIntegrityValidator(),
            ValidationCategory.BUSINESS_RULES: BusinessRuleValidator(),
            ValidationCategory.CROSS_REFERENCE: CrossReferenceValidator(),
            ValidationCategory.REGULATORY_COMPLIANCE: RegulatoryComplianceValidator(),
            ValidationCategory.DATA_FRESHNESS: DataFreshnessValidator(),
        }
    
    def add_validator(self, category: ValidationCategory, validator: BaseValidator):
        """Add a custom validator."""
        self.validators[category] = validator
    
    def validate_data(self, organization_id: int, data: Dict[str, Any]) -> ValidationReport:
        """Perform comprehensive data validation."""
        all_results = []
        validation_timestamp = datetime.utcnow()
        
        try:
            logger.info(f"Starting data validation for organization {organization_id}")
            
            # Run all validators
            for category, validator in self.validators.items():
                try:
                    results = validator.validate(data)
                    all_results.extend(results)
                    logger.info(f"Completed {category.value} validation: {len(results)} checks")
                except Exception as e:
                    logger.error(f"Error in {category.value} validation: {e}")
                    all_results.append(ValidationResult(
                        check_name=f"{category.value}_error",
                        category=category,
                        severity=ValidationSeverity.CRITICAL,
                        passed=False,
                        message=f"Validator error: {str(e)}",
                        timestamp=validation_timestamp
                    ))
            
            # Generate summary
            total_checks = len(all_results)
            passed_checks = sum(1 for r in all_results if r.passed)
            failed_checks = total_checks - passed_checks
            
            summary = self._generate_summary(all_results)
            
            report = ValidationReport(
                organization_id=organization_id,
                validation_timestamp=validation_timestamp,
                total_checks=total_checks,
                passed_checks=passed_checks,
                failed_checks=failed_checks,
                results=all_results,
                summary=summary
            )
            
            logger.info(f"Validation completed: {passed_checks}/{total_checks} checks passed ({report.success_rate:.1f}%)")
            
            return report
            
        except Exception as e:
            logger.error(f"Critical error in data validation: {e}")
            raise
    
    def _generate_summary(self, results: List[ValidationResult]) -> Dict[str, Any]:
        """Generate validation summary statistics."""
        summary: Dict[str, Any] = {
            "by_category": {},
            "by_severity": {},
            "critical_issues": [],
            "recommendations": []
        }
        
        # Group by category
        for category in ValidationCategory:
            category_results = [r for r in results if r.category == category]
            if category_results:
                summary["by_category"][category.value] = {
                    "total": len(category_results),
                    "passed": sum(1 for r in category_results if r.passed),
                    "failed": sum(1 for r in category_results if not r.passed)
                }
        
        # Group by severity
        for severity in ValidationSeverity:
            severity_results = [r for r in results if r.severity == severity and not r.passed]
            if severity_results:
                summary["by_severity"][severity.value] = len(severity_results)
        
        # Collect critical issues
        critical_issues = [r for r in results if r.severity == ValidationSeverity.CRITICAL and not r.passed]
        summary["critical_issues"] = [{"check": r.check_name, "message": r.message} for r in critical_issues]
        
        # Generate recommendations
        if critical_issues:
            summary["recommendations"].append("Address critical validation failures immediately")
        
        error_count = len([r for r in results if r.severity == ValidationSeverity.ERROR and not r.passed])
        if error_count > 0:
            summary["recommendations"].append(f"Review and fix {error_count} validation errors")
        
        return summary

    def generate_validation_report_html(self, report: ValidationReport) -> str:
        """Generate HTML validation report."""
        try:
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Data Validation Report - Organization {report.organization_id}</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                    .summary {{ margin: 20px 0; }}
                    .success {{ color: green; }}
                    .warning {{ color: orange; }}
                    .error {{ color: red; }}
                    .critical {{ color: darkred; font-weight: bold; }}
                    .result-item {{ margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }}
                    .result-item.passed {{ border-left-color: green; }}
                    .result-item.failed {{ border-left-color: red; }}
                    table {{ border-collapse: collapse; width: 100%; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                    th {{ background-color: #f2f2f2; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Data Validation Report</h1>
                    <p><strong>Organization ID:</strong> {report.organization_id}</p>
                    <p><strong>Validation Date:</strong> {report.validation_timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}</p>
                    <p><strong>Success Rate:</strong> {report.success_rate:.1f}% ({report.passed_checks}/{report.total_checks} checks passed)</p>
                </div>

                <div class="summary">
                    <h2>Summary</h2>
                    <table>
                        <tr><th>Category</th><th>Total</th><th>Passed</th><th>Failed</th></tr>
            """

            for category, stats in report.summary.get("by_category", {}).items():
                html_content += f"""
                        <tr>
                            <td>{category.replace('_', ' ').title()}</td>
                            <td>{stats['total']}</td>
                            <td class="success">{stats['passed']}</td>
                            <td class="error">{stats['failed']}</td>
                        </tr>
                """

            html_content += """
                    </table>
                </div>

                <div class="results">
                    <h2>Validation Results</h2>
            """

            for result in report.results:
                status_class = "passed" if result.passed else "failed"
                severity_class = result.severity.value
                html_content += f"""
                    <div class="result-item {status_class}">
                        <h4 class="{severity_class}">{result.check_name} - {result.severity.value.upper()}</h4>
                        <p>{result.message}</p>
                        <small>Category: {result.category.value} | Time: {result.timestamp.strftime('%H:%M:%S') if result.timestamp else 'N/A'}</small>
                    </div>
                """

            html_content += """
                </div>
            </body>
            </html>
            """

            return html_content

        except Exception as e:
            logger.error(f"Error generating HTML report: {e}")
            return f"<html><body><h1>Error generating report: {str(e)}</h1></body></html>"

    def generate_validation_alert(self, report: ValidationReport) -> Dict[str, Any]:
        """Generate alert data for critical validation failures."""
        try:
            alert_data = {
                "organization_id": report.organization_id,
                "timestamp": report.validation_timestamp.isoformat(),
                "alert_level": "info",
                "message": "",
                "details": {},
                "requires_action": False
            }

            # Determine alert level based on validation results
            if report.has_critical_errors:
                alert_data["alert_level"] = "critical"
                alert_data["requires_action"] = True
                critical_count = len([r for r in report.results if r.severity == ValidationSeverity.CRITICAL and not r.passed])
                alert_data["message"] = f"CRITICAL: {critical_count} critical validation failures detected"
            elif report.failed_checks > 0:
                error_count = len([r for r in report.results if r.severity == ValidationSeverity.ERROR and not r.passed])
                warning_count = len([r for r in report.results if r.severity == ValidationSeverity.WARNING and not r.passed])

                if error_count > 0:
                    alert_data["alert_level"] = "error"
                    alert_data["requires_action"] = True
                    alert_data["message"] = f"ERROR: {error_count} validation errors detected"
                elif warning_count > 0:
                    alert_data["alert_level"] = "warning"
                    alert_data["message"] = f"WARNING: {warning_count} validation warnings detected"
            else:
                alert_data["message"] = "All validation checks passed successfully"

            # Add summary details
            alert_data["details"] = {
                "total_checks": report.total_checks,
                "passed_checks": report.passed_checks,
                "failed_checks": report.failed_checks,
                "success_rate": report.success_rate,
                "critical_issues": report.summary.get("critical_issues", []),
                "recommendations": report.summary.get("recommendations", [])
            }

            return alert_data

        except Exception as e:
            logger.error(f"Error generating validation alert: {e}")
            return {
                "organization_id": report.organization_id,
                "timestamp": datetime.utcnow().isoformat(),
                "alert_level": "error",
                "message": f"Error generating validation alert: {str(e)}",
                "requires_action": True
            }

    def export_validation_results(self, report: ValidationReport, format_type: str = "json") -> str:
        """Export validation results in specified format."""
        try:
            if format_type.lower() == "json":
                import json

                export_data = {
                    "organization_id": report.organization_id,
                    "validation_timestamp": report.validation_timestamp.isoformat(),
                    "total_checks": report.total_checks,
                    "passed_checks": report.passed_checks,
                    "failed_checks": report.failed_checks,
                    "success_rate": report.success_rate,
                    "has_critical_errors": report.has_critical_errors,
                    "summary": report.summary,
                    "results": [
                        {
                            "check_name": r.check_name,
                            "category": r.category.value,
                            "severity": r.severity.value,
                            "passed": r.passed,
                            "message": r.message,
                            "details": r.details,
                            "timestamp": r.timestamp.isoformat() if r.timestamp else None
                        }
                        for r in report.results
                    ]
                }

                return json.dumps(export_data, indent=2)

            elif format_type.lower() == "csv":
                import csv
                import io

                output = io.StringIO()
                writer = csv.writer(output)

                # Write header
                writer.writerow([
                    "Check Name", "Category", "Severity", "Passed", "Message", "Timestamp"
                ])

                # Write results
                for result in report.results:
                    writer.writerow([
                        result.check_name,
                        result.category.value,
                        result.severity.value,
                        result.passed,
                        result.message,
                        result.timestamp.isoformat() if result.timestamp else ""
                    ])

                return output.getvalue()

            else:
                raise ValueError(f"Unsupported export format: {format_type}")

        except Exception as e:
            logger.error(f"Error exporting validation results: {e}")
            return f"Error exporting results: {str(e)}"
