[settings]
# Profile for import grouping and sorting
profile = black
multi_line_output = 3
line_length = 88
known_first_party = mcx3d_finance
known_third_party = fastapi,sqlalchemy,pydantic,xero_python,requests,jose,redis,celery,pytest,alembic
sections = FUTURE,STDLIB,THIRDPARTY,FIRSTPARTY,LOCALFOLDER
import_heading_stdlib = Standard library imports
import_heading_thirdparty = Third party imports
import_heading_firstparty = Local application imports
import_heading_localfolder = Relative imports
combine_as_imports = true
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true