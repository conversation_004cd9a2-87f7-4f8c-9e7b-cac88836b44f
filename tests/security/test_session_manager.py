"""
Tests for the SessionManager utility.
"""
import pytest
from unittest.mock import patch
from datetime import datetime, timedelta, timezone
from jose import jwt

from mcx3d_finance.utils.session_manager import SessionManager

class TestSessionManager:
    """
    Test suite for the SessionManager.
    """

    def test_create_session_generates_tokens(self, session_manager: SessionManager):
        """
        Test that a new session creates valid access and refresh tokens.
        """
        session = session_manager.create_session(
            user_id="1", user_data={"email": "<EMAIL>"}
        )

        assert "access_token" in session
        assert "refresh_token" in session
        assert "session_id" in session

        # Verify access token
        access_payload = jwt.decode(
            session["access_token"],
            session_manager.secret_key,
            algorithms=[session_manager.algorithm],
        )
        assert access_payload["sub"] == "1"
        assert access_payload["email"] == "<EMAIL>"
        assert access_payload["type"] == "access"

        # Verify refresh token
        refresh_payload = jwt.decode(
            session["refresh_token"],
            session_manager.secret_key,
            algorithms=[session_manager.algorithm],
        )
        assert refresh_payload["sub"] == "1"
        assert refresh_payload["type"] == "refresh"

    def test_refresh_token_rotation(self, session_manager: SessionManager):
        """
        Test that using a refresh token invalidates the old one and issues a new one.
        """
        # Create a session
        session = session_manager.create_session(
            user_id="2", user_data={"email": "<EMAIL>"}
        )
        old_refresh_token = session["refresh_token"]

        # Refresh the session
        new_session = session_manager.refresh_session(old_refresh_token)
        new_refresh_token = new_session["refresh_token"]

        assert old_refresh_token != new_refresh_token

        # Try to use the old refresh token again
        with pytest.raises(ValueError, match="Invalid refresh token"):
            session_manager.refresh_session(old_refresh_token)

    def test_concurrent_session_limit(self, session_manager: SessionManager):
        """
        Test that creating more than the maximum number of sessions (5)
        invalidates the oldest session.
        """
        user_id = "3"
        sessions = []

        # Create 5 sessions
        for _ in range(5):
            session = session_manager.create_session(
                user_id=user_id, user_data={"email": "<EMAIL>"}
            )
            sessions.append(session)

        # The first session should still be active
        first_session_id = sessions[0]["session_id"]
        active_sessions = session_manager.get_active_sessions(user_id)
        assert any(s["session_id"] == first_session_id for s in active_sessions)

        # Create a 6th session
        session_manager.create_session(
            user_id=user_id, user_data={"email": "<EMAIL>"}
        )

        # The first session should now be invalidated
        active_sessions = session_manager.get_active_sessions(user_id)
        assert not any(s["session_id"] == first_session_id for s in active_sessions)
        assert len(active_sessions) == 5

    def test_invalidate_session(self, session_manager: SessionManager):
        """
        Test both single session invalidation and invalidating all sessions for a user.
        """
        user_id = "4"
        session1 = session_manager.create_session(
            user_id=user_id, user_data={"email": "<EMAIL>"}
        )
        session2 = session_manager.create_session(
            user_id=user_id, user_data={"email": "<EMAIL>"}
        )

        # Invalidate the first session
        session_manager.invalidate_session(user_id, session1["session_id"])
        active_sessions = session_manager.get_active_sessions(user_id)
        assert len(active_sessions) == 1
        assert active_sessions[0]["session_id"] == session2["session_id"]

        # Invalidate all sessions
        session_manager.invalidate_all_sessions(user_id)
        active_sessions = session_manager.get_active_sessions(user_id)
        assert len(active_sessions) == 0

    def test_session_ttl_expiration(self, session_manager: SessionManager):
        """
        Test that sessions expire correctly in Redis.
        """
        user_id = "5"
        session = session_manager.create_session(
            user_id=user_id, user_data={"email": "<EMAIL>"}
        )
        session_key = f"session:{user_id}:{session['session_id']}"

        # Check that the session has a TTL
        ttl = session_manager.redis.ttl(session_key)
        assert ttl > 0

    @patch("redis.Redis.get", side_effect=ConnectionError("Redis is down"))
    def test_redis_connection_error(self, redis_mock, session_manager: SessionManager):
        """
        Test behavior when Redis is unavailable.
        """
        with pytest.raises(ValueError, match="Failed to refresh session"):
            session_manager.refresh_session("some_token")
