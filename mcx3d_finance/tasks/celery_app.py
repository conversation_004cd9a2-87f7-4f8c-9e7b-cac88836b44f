import os
from celery import Celery

# Default Redis URL
REDIS_URL = os.environ.get("REDIS_URL", "redis://localhost:6379/0")

# Create Celery application instance
celery_app = Celery(
    "mcx3d_finance",
    broker=REDIS_URL,
    backend=REDIS_URL,
    include=["mcx3d_finance.tasks.example", "mcx3d_finance.tasks.calculation"],
)

# Optional configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
)
