"""
MCP Xero Client Wrapper

This client provides a unified interface to access Xero data through MCP tools
with intelligent fallback to direct API calls when needed.
"""

import os
import json
import logging
import subprocess
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

from .mcp_bridge import MCPXeroBridge
from .xero_client import XeroClient

logger = logging.getLogger(__name__)


class MCPXeroClient:
    """Unified Xero client using MCP tools with direct API fallback."""

    def __init__(self, organization_id: int = 2):
        self.organization_id = organization_id
        self.mcp_bridge = MCPXeroBridge(organization_id)
        self._direct_client = None
        self.use_mcp = True  # Flag to control MCP vs direct API usage

    @property
    def direct_client(self) -> XeroClient:
        """Lazy-load direct API client as fallback."""
        if not self._direct_client:
            self._direct_client = XeroClient(self.organization_id)
        return self._direct_client

    def _setup_mcp_environment(self) -> bool:
        """Set up environment variables for MCP tools."""
        try:
            env_vars = self.mcp_bridge.create_mcp_environment()
            if env_vars:
                # Update current process environment
                os.environ.update(env_vars)
                logger.info("MCP environment variables configured")
                return True
            else:
                logger.warning("Failed to create MCP environment variables")
                return False
        except Exception as e:
            logger.error(f"Error setting up MCP environment: {e}")
            return False

    def _execute_mcp_command(self, command: str, **kwargs) -> Optional[Dict]:
        """Execute MCP command with error handling."""
        if not self.use_mcp:
            return None

        try:
            # Set up MCP environment
            if not self._setup_mcp_environment():
                logger.warning("MCP environment setup failed, using direct API")
                return None

            # This would be the actual MCP command execution
            # For now, we'll simulate the command structure
            logger.info(f"Executing MCP command: {command} with args: {kwargs}")
            
            # In a real implementation, this would call the actual MCP tools
            # For now, we return None to trigger fallback
            return None
            
        except Exception as e:
            logger.error(f"MCP command failed: {command}, error: {e}")
            return None

    def get_organization_details(self) -> Dict[str, Any]:
        """Get organization details via MCP or direct API."""
        logger.info("Fetching organization details...")
        
        # Try MCP first
        mcp_result = self._execute_mcp_command("list-organisation-details")
        
        if mcp_result:
            logger.info("Successfully retrieved organization details via MCP")
            return mcp_result
        
        # Fallback to direct API
        logger.info("Falling back to direct API for organization details")
        try:
            # Using direct API call since we know this works
            import requests
            
            tokens = self.mcp_bridge._get_organization_tokens()
            if not tokens:
                raise Exception("No valid tokens available")
            
            headers = {
                'Authorization': f'Bearer {tokens["access_token"]}',
                'Xero-tenant-id': tokens['tenant_id'],
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            response = requests.get('https://api.xero.com/api.xro/2.0/Organisation', headers=headers)
            if response.status_code == 200:
                data = response.json()
                return data.get('Organisations', [{}])[0] if data.get('Organisations') else {}
            else:
                raise Exception(f"API call failed: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Direct API fallback failed: {e}")
            raise

    def get_accounts(self) -> List[Dict[str, Any]]:
        """Get chart of accounts via MCP or direct API."""
        logger.info("Fetching chart of accounts...")
        
        # Try MCP first
        mcp_result = self._execute_mcp_command("list-accounts")
        
        if mcp_result:
            logger.info("Successfully retrieved accounts via MCP")
            return mcp_result
        
        # Fallback to direct API using requests
        logger.info("Falling back to direct API for accounts")
        try:
            import requests
            
            tokens = self.mcp_bridge._get_organization_tokens()
            if not tokens:
                raise Exception("No valid tokens available")
            
            headers = {
                'Authorization': f'Bearer {tokens["access_token"]}',
                'Xero-tenant-id': tokens['tenant_id'],
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            response = requests.get('https://api.xero.com/api.xro/2.0/Accounts', headers=headers)
            if response.status_code == 200:
                data = response.json()
                return data.get('Accounts', [])
            else:
                raise Exception(f"API call failed: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Failed to retrieve accounts via direct API: {e}")
            raise

    def get_trial_balance(self, date: Optional[datetime] = None) -> Dict[str, Any]:
        """Get trial balance via MCP or direct API."""
        if not date:
            date = datetime.now()
        
        logger.info(f"Fetching trial balance for {date.strftime('%Y-%m-%d')}...")
        
        # Try MCP first
        mcp_result = self._execute_mcp_command("list-trial-balance", date=date.strftime('%Y-%m-%d'))
        
        if mcp_result:
            logger.info("Successfully retrieved trial balance via MCP")
            return mcp_result
        
        # Fallback to direct API
        logger.info("Falling back to direct API for trial balance")
        try:
            return self.direct_client.get_trial_balance(date)
        except Exception as e:
            logger.error(f"Failed to retrieve trial balance via direct API: {e}")
            raise

    def get_profit_and_loss(self, from_date: datetime, to_date: datetime) -> Dict[str, Any]:
        """Get profit & loss report via MCP or direct API."""
        logger.info(f"Fetching P&L from {from_date.strftime('%Y-%m-%d')} to {to_date.strftime('%Y-%m-%d')}...")
        
        # Try MCP first
        mcp_result = self._execute_mcp_command(
            "list-profit-and-loss", 
            from_date=from_date.strftime('%Y-%m-%d'),
            to_date=to_date.strftime('%Y-%m-%d')
        )
        
        if mcp_result:
            logger.info("Successfully retrieved P&L via MCP")
            return mcp_result
        
        # Fallback to direct API
        logger.info("Falling back to direct API for P&L")
        try:
            return self.direct_client.get_profit_and_loss(from_date, to_date)
        except Exception as e:
            logger.error(f"Failed to retrieve P&L via direct API: {e}")
            raise

    def get_invoices(self, page: int = 1, contact_ids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Get invoices via MCP or direct API."""
        logger.info(f"Fetching invoices (page {page})...")
        
        # Try MCP first
        mcp_result = self._execute_mcp_command("list-invoices", page=page, contact_ids=contact_ids)
        
        if mcp_result:
            logger.info("Successfully retrieved invoices via MCP")
            return mcp_result
        
        # Fallback to direct API
        logger.info("Falling back to direct API for invoices")
        try:
            return self.direct_client.get_invoices()
        except Exception as e:
            logger.error(f"Failed to retrieve invoices via direct API: {e}")
            raise

    def get_contacts(self) -> List[Dict[str, Any]]:
        """Get contacts via MCP or direct API."""
        logger.info("Fetching contacts...")
        
        # Try MCP first
        mcp_result = self._execute_mcp_command("list-contacts")
        
        if mcp_result:
            logger.info("Successfully retrieved contacts via MCP")
            return mcp_result
        
        # Fallback to direct API
        logger.info("Falling back to direct API for contacts")
        try:
            return self.direct_client.get_contacts()
        except Exception as e:
            logger.error(f"Failed to retrieve contacts via direct API: {e}")
            raise

    def get_bank_transactions(self, from_date: Optional[datetime] = None, to_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Get bank transactions via MCP or direct API."""
        logger.info("Fetching bank transactions...")
        
        # Try MCP first
        mcp_result = self._execute_mcp_command(
            "list-bank-transactions", 
            from_date=from_date.strftime('%Y-%m-%d') if from_date else None,
            to_date=to_date.strftime('%Y-%m-%d') if to_date else None
        )
        
        if mcp_result:
            logger.info("Successfully retrieved bank transactions via MCP")
            return mcp_result
        
        # Fallback to direct API
        logger.info("Falling back to direct API for bank transactions")
        try:
            return self.direct_client.get_bank_transactions(from_date, to_date)
        except Exception as e:
            logger.error(f"Failed to retrieve bank transactions via direct API: {e}")
            raise

    def create_invoice(self, contact_id: str, line_items: List[Dict], invoice_type: str = "ACCREC") -> Dict[str, Any]:
        """Create invoice via MCP or direct API."""
        logger.info(f"Creating {invoice_type} invoice for contact {contact_id}...")
        
        # Try MCP first
        mcp_result = self._execute_mcp_command(
            "create-invoice",
            contact_id=contact_id,
            line_items=line_items,
            type=invoice_type
        )
        
        if mcp_result:
            logger.info("Successfully created invoice via MCP")
            return mcp_result
        
        # For create operations, we'd need to implement this in the direct client
        logger.warning("Invoice creation not implemented in direct API client")
        raise NotImplementedError("Invoice creation requires MCP tools or direct API implementation")

    def get_connection_status(self) -> Dict[str, Any]:
        """Get connection status and diagnostics."""
        bridge_info = self.mcp_bridge.get_connection_info()
        
        status = {
            "bridge_status": bridge_info,
            "mcp_enabled": self.use_mcp,
            "direct_api_available": True,  # We know this works
            "recommended_method": "MCP with direct API fallback"
        }
        
        return status

    def enable_mcp(self, enabled: bool = True):
        """Enable or disable MCP usage."""
        self.use_mcp = enabled
        logger.info(f"MCP usage {'enabled' if enabled else 'disabled'}")

    def test_connection(self) -> Dict[str, Any]:
        """Test both MCP and direct API connections."""
        results = {
            "mcp_bridge": self.mcp_bridge.test_connection(),
            "direct_api": False,
            "organization_details": None
        }
        
        # Test direct API
        try:
            org_details = self.get_organization_details()
            results["direct_api"] = True
            results["organization_details"] = org_details
        except Exception as e:
            logger.error(f"Direct API test failed: {e}")
        
        return results


def create_mcp_client(organization_id: int = 2) -> MCPXeroClient:
    """Factory function to create MCP Xero client."""
    return MCPXeroClient(organization_id)


def test_mcp_client():
    """Test function for MCP client functionality."""
    print("Testing MCP Xero Client...")
    
    client = create_mcp_client(organization_id=2)  # Modular CX
    
    # Test connection
    print("\n1. Testing Connection:")
    connection_test = client.test_connection()
    print(f"MCP Bridge: {'✅' if connection_test['mcp_bridge'] else '❌'}")
    print(f"Direct API: {'✅' if connection_test['direct_api'] else '❌'}")
    
    if connection_test['organization_details']:
        org = connection_test['organization_details']
        print(f"Organization: {org.get('Name', 'N/A')}")
        print(f"Country: {org.get('CountryCode', 'N/A')}")
        print(f"Currency: {org.get('BaseCurrency', 'N/A')}")
    
    # Test accounts retrieval
    print("\n2. Testing Accounts Retrieval:")
    try:
        accounts = client.get_accounts()
        print(f"✅ Retrieved {len(accounts)} accounts")
    except Exception as e:
        print(f"❌ Accounts retrieval failed: {e}")
    
    # Test connection status
    print("\n3. Connection Status:")
    status = client.get_connection_status()
    bridge_status = status['bridge_status']
    print(f"Organization: {bridge_status.get('organization_name', 'N/A')}")
    print(f"Status: {bridge_status.get('status', 'N/A')}")
    print(f"MCP Enabled: {status['mcp_enabled']}")


if __name__ == "__main__":
    test_mcp_client()