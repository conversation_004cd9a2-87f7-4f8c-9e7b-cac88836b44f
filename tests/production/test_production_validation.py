# tests/production/test_production_validation.py

"""
Production environment validation tests for MCX3D Financial System.

These tests validate system behavior in production-like environments
including configuration, security, performance, and data integrity.
"""

import pytest
import os
import json
import tempfile
import logging
from pathlib import Path
from datetime import datetime, timedelta
from decimal import Decimal
import time
import concurrent.futures
from typing import Dict, List, Any

from mcx3d_finance.reporting.generator import ReportGenerator


@pytest.mark.production
@pytest.mark.validation
class TestProductionConfiguration:
    """Test production configuration and environment setup."""
    
    def test_environment_configuration(self):
        """Validate that required environment configuration is available."""
        # Critical environment variables for production
        critical_vars = [
            'DATABASE_URL',
            'REDIS_URL', 
            'SECRET_KEY'
        ]
        
        # Optional but recommended variables
        optional_vars = [
            'XERO_CLIENT_ID',
            'XERO_CLIENT_SECRET',
            'XERO_WEBHOOK_KEY'
        ]
        
        missing_critical = []
        missing_optional = []
        
        for var in critical_vars:
            if not os.getenv(var):
                missing_critical.append(var)
        
        for var in optional_vars:
            if not os.getenv(var):
                missing_optional.append(var)
        
        # Critical variables must be present in production
        if os.getenv('ENVIRONMENT') == 'production':
            assert not missing_critical, \
                f"Missing critical environment variables: {missing_critical}"
        
        # Log warnings for missing optional variables
        if missing_optional:
            logging.warning(f"Missing optional environment variables: {missing_optional}")
    
    def test_file_permissions_production(self):
        """Test file system permissions in production-like environment."""
        import stat
        
        # Test creating temporary directories and files
        test_dirs = [
            "reports",
            "exports", 
            "temp",
            "logs"
        ]
        
        base_temp_dir = Path(tempfile.gettempdir()) / "mcx3d_production_test"
        
        try:
            base_temp_dir.mkdir(exist_ok=True)
            
            for test_dir in test_dirs:
                dir_path = base_temp_dir / test_dir
                dir_path.mkdir(exist_ok=True)
                
                # Test file creation
                test_file = dir_path / "permission_test.txt"
                test_file.write_text("test content")
                
                # Verify permissions
                file_stat = test_file.stat()
                file_permissions = stat.filemode(file_stat.st_mode)
                
                # File should be readable and writable by owner
                assert file_stat.st_mode & stat.S_IRUSR, f"File not readable in {test_dir}"
                assert file_stat.st_mode & stat.S_IWUSR, f"File not writable in {test_dir}"
                
        finally:
            # Cleanup
            import shutil
            if base_temp_dir.exists():
                shutil.rmtree(base_temp_dir, ignore_errors=True)
    
    def test_logging_configuration(self):
        """Test that logging is properly configured for production."""
        # Test that logging works and doesn't crash
        logger = logging.getLogger('mcx3d_finance.production_test')
        
        try:
            logger.info("Production validation test log entry")
            logger.warning("Test warning message")
            logger.error("Test error message")
            
            # Logging should not raise exceptions
            assert True
            
        except Exception as e:
            pytest.fail(f"Logging configuration error: {e}")
    
    def test_security_headers_and_settings(self):
        """Test security-related configuration."""
        # Verify SECRET_KEY is set and not default/weak
        secret_key = os.getenv('SECRET_KEY', '')
        
        if secret_key:
            # Should be long enough
            assert len(secret_key) >= 32, "SECRET_KEY too short for production"
            
            # Should not be obvious defaults
            weak_keys = ['secret', 'password', 'key', 'test', 'development']
            assert not any(weak in secret_key.lower() for weak in weak_keys), \
                "SECRET_KEY appears to be a weak/default value"
        
        # Test that the system doesn't expose sensitive information in errors
        try:
            # Cause a controlled error
            report_generator = ReportGenerator()
            report_generator.generate_dcf_valuation_pdf(
                dcf_data=None,  # Invalid data
                output_path="/invalid/path/test.pdf"  # Invalid path
            )
        except Exception as e:
            error_message = str(e)
            
            # Error should not contain sensitive paths or configuration
            sensitive_patterns = [
                '/Users/',
                '/home/',
                'password',
                'secret',
                'key='
            ]
            
            for pattern in sensitive_patterns:
                assert pattern.lower() not in error_message.lower(), \
                    f"Error message contains sensitive information: {pattern}"


@pytest.mark.production
@pytest.mark.validation
class TestProductionPerformance:
    """Test performance characteristics in production environment."""
    
    @pytest.fixture
    def performance_test_data(self):
        """Generate test data optimized for performance testing."""
        return {
            "organization_id": 1,
            "company_name": "Performance Test Corp",
            "valuation_date": "2024-01-01",
            "currency": "USD",
            "enterprise_value": 25000000,
            "financial_projections": [
                {
                    "year": year,
                    "revenue": 5000000 * (1.2 ** (year - 1)),
                    "operating_expenses": 3000000 * (1.15 ** (year - 1)),
                    "free_cash_flow": 1000000 * (1.25 ** (year - 1)),
                    "ebitda": 1500000 * (1.22 ** (year - 1)),
                    "revenue_growth_rate": max(0.05, 0.20 - (year * 0.02))
                }
                for year in range(1, 6)  # 5 years of projections
            ],
            "assumptions": {
                "discount_rate": 0.12,
                "terminal_growth": 0.025,
                "tax_rate": 0.21,
                "years": 5
            },
            "sensitivity_analysis": {
                "discount_rate": {
                    "range": [-0.02, -0.01, 0, 0.01, 0.02],
                    "impact": [-15, -8, 0, 8, 15]
                },
                "terminal_growth_rate": {
                    "range": [-0.01, -0.005, 0, 0.005, 0.01],
                    "impact": [-10, -5, 0, 5, 10]
                }
            }
        }
    
    def test_single_report_performance(self, performance_test_data):
        """Test single report generation performance."""
        report_generator = ReportGenerator()
        
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_file:
            temp_path = temp_file.name
        
        try:
            start_time = time.time()
            
            report_generator.generate_dcf_valuation_pdf(
                dcf_data=performance_test_data,
                output_path=temp_path
            )
            
            end_time = time.time()
            generation_time = end_time - start_time
            
            # Production performance requirements
            assert generation_time < 5.0, \
                f"Single report generation too slow: {generation_time:.2f}s"
            
            # Validate output quality wasn't compromised for speed
            file_size = Path(temp_path).stat().st_size
            assert file_size > 10000, \
                f"Report file too small, may indicate incomplete generation: {file_size} bytes"
            
        finally:
            if Path(temp_path).exists():
                Path(temp_path).unlink()
    
    def test_concurrent_report_performance(self, performance_test_data):
        """Test concurrent report generation performance."""
        def generate_report(index):
            report_generator = ReportGenerator()
            
            with tempfile.NamedTemporaryFile(suffix=f"_concurrent_{index}.pdf", delete=False) as temp_file:
                temp_path = temp_file.name
            
            try:
                start_time = time.time()
                
                # Modify data slightly for each report
                test_data = performance_test_data.copy()
                test_data["company_name"] = f"Concurrent Test Corp {index}"
                test_data["organization_id"] = index
                
                report_generator.generate_dcf_valuation_pdf(
                    dcf_data=test_data,
                    output_path=temp_path
                )
                
                end_time = time.time()
                
                return {
                    'index': index,
                    'success': Path(temp_path).exists(),
                    'size': Path(temp_path).stat().st_size if Path(temp_path).exists() else 0,
                    'time': end_time - start_time,
                    'path': temp_path
                }
                
            except Exception as e:
                return {
                    'index': index,
                    'success': False,
                    'error': str(e),
                    'time': 0,
                    'path': temp_path
                }
        
        # Test with realistic concurrent load (5 reports)
        concurrent_count = 5
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_count) as executor:
            start_time = time.time()
            
            futures = [executor.submit(generate_report, i) for i in range(concurrent_count)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            total_time = time.time() - start_time
        
        try:
            # Validate all reports were generated successfully
            successful_results = [r for r in results if r['success']]
            assert len(successful_results) == concurrent_count, \
                f"Only {len(successful_results)}/{concurrent_count} reports generated successfully"
            
            # Performance requirements for concurrent generation
            assert total_time < 15.0, \
                f"Concurrent generation too slow: {total_time:.2f}s for {concurrent_count} reports"
            
            # Individual report times should still be reasonable
            max_individual_time = max(r['time'] for r in successful_results)
            assert max_individual_time < 8.0, \
                f"Individual report in concurrent test too slow: {max_individual_time:.2f}s"
            
            # All reports should be properly sized
            for result in successful_results:
                assert result['size'] > 10000, \
                    f"Report {result['index']} too small: {result['size']} bytes"
        
        finally:
            # Cleanup generated files
            for result in results:
                if 'path' in result and Path(result['path']).exists():
                    Path(result['path']).unlink()
    
    def test_memory_usage_stability(self, performance_test_data):
        """Test memory usage remains stable over multiple operations."""
        try:
            import psutil
            process = psutil.Process()
            
            # Record initial memory usage
            initial_memory = process.memory_info().rss
            memory_measurements = [initial_memory]
            
            report_generator = ReportGenerator()
            
            # Generate 10 reports and monitor memory
            for i in range(10):
                with tempfile.NamedTemporaryFile(suffix=f"_memory_test_{i}.pdf") as temp_file:
                    test_data = performance_test_data.copy()
                    test_data["company_name"] = f"Memory Test Corp {i}"
                    
                    report_generator.generate_dcf_valuation_pdf(
                        dcf_data=test_data,
                        output_path=temp_file.name
                    )
                    
                    # Record memory after each report
                    current_memory = process.memory_info().rss
                    memory_measurements.append(current_memory)
            
            # Analyze memory usage patterns
            max_memory = max(memory_measurements)
            min_memory = min(memory_measurements)
            final_memory = memory_measurements[-1]
            
            # Memory should not grow excessively
            memory_growth = max_memory - initial_memory
            max_acceptable_growth = 200 * 1024 * 1024  # 200MB
            
            assert memory_growth < max_acceptable_growth, \
                f"Memory usage grew too much: {memory_growth / 1024 / 1024:.1f}MB"
            
            # Final memory should not be too different from initial
            # (allowing for some reasonable growth)
            final_growth = final_memory - initial_memory
            max_final_growth = 50 * 1024 * 1024  # 50MB
            
            assert final_growth < max_final_growth, \
                f"Final memory usage too high: {final_growth / 1024 / 1024:.1f}MB growth"
            
        except ImportError:
            pytest.skip("psutil not available for memory testing")


@pytest.mark.production
@pytest.mark.validation
class TestProductionDataIntegrity:
    """Test data integrity and consistency in production scenarios."""
    
    def test_report_reproducibility(self):
        """Test that identical input produces identical output."""
        consistent_data = {
            "organization_id": 1,
            "company_name": "Reproducibility Test Corp",
            "valuation_date": "2024-01-15",
            "enterprise_value": 10000000,
            "financial_projections": [
                {
                    "year": 1,
                    "revenue": 2000000,
                    "free_cash_flow": 400000,
                    "ebitda": 600000,
                    "revenue_growth_rate": 0.15
                },
                {
                    "year": 2,
                    "revenue": 2300000,
                    "free_cash_flow": 480000,
                    "ebitda": 720000,
                    "revenue_growth_rate": 0.15
                }
            ],
            "assumptions": {
                "discount_rate": 0.12,
                "terminal_growth": 0.025,
                "tax_rate": 0.21
            }
        }
        
        report_generator = ReportGenerator()
        file_hashes = []
        
        # Generate the same report 3 times
        for i in range(3):
            with tempfile.NamedTemporaryFile(suffix=f"_reproducibility_{i}.pdf", delete=False) as temp_file:
                temp_path = temp_file.name
            
            try:
                report_generator.generate_dcf_valuation_pdf(
                    dcf_data=consistent_data,
                    output_path=temp_path
                )
                
                # Calculate file hash
                import hashlib
                with open(temp_path, 'rb') as f:
                    file_hash = hashlib.md5(f.read()).hexdigest()
                    file_hashes.append(file_hash)
                
            finally:
                if Path(temp_path).exists():
                    Path(temp_path).unlink()
        
        # All reports should be identical (same hash)
        # Note: This might not be true if reports include timestamps
        # In that case, we'd need to test structural consistency instead
        unique_hashes = set(file_hashes)
        
        if len(unique_hashes) > 1:
            # If hashes differ, it might be due to timestamps
            # Test that at least file sizes are consistent
            with tempfile.NamedTemporaryFile(suffix="_size_test.pdf", delete=False) as temp_file:
                temp_path = temp_file.name
            
            try:
                report_generator.generate_dcf_valuation_pdf(
                    dcf_data=consistent_data,
                    output_path=temp_path
                )
                
                reference_size = Path(temp_path).stat().st_size
                
                # Generate a few more reports and check size consistency
                for i in range(3):
                    with tempfile.NamedTemporaryFile(suffix=f"_size_check_{i}.pdf") as check_file:
                        report_generator.generate_dcf_valuation_pdf(
                            dcf_data=consistent_data,
                            output_path=check_file.name
                        )
                        
                        check_size = Path(check_file.name).stat().st_size
                        size_difference = abs(check_size - reference_size)
                        
                        # Allow for small variations (< 1KB) due to metadata
                        assert size_difference < 1024, \
                            f"Report size inconsistency: {size_difference} bytes difference"
            
            finally:
                if Path(temp_path).exists():
                    Path(temp_path).unlink()
    
    def test_large_dataset_handling(self):
        """Test handling of large datasets typical in production."""
        # Create large dataset with many projections and complex structure
        large_data = {
            "organization_id": 1,
            "company_name": "Large Dataset Test Corp",
            "valuation_date": "2024-01-01",
            "enterprise_value": *********,
            "financial_projections": [
                {
                    "year": year,
                    "revenue": 10000000 * (1.15 ** (year - 1)),
                    "operating_expenses": 6000000 * (1.12 ** (year - 1)),
                    "free_cash_flow": 2000000 * (1.18 ** (year - 1)),
                    "ebitda": 3000000 * (1.16 ** (year - 1)),
                    "revenue_growth_rate": max(0.03, 0.15 - (year * 0.01))
                }
                for year in range(1, 11)  # 10 years of projections
            ],
            "assumptions": {
                "discount_rate": 0.11,
                "terminal_growth": 0.025,
                "tax_rate": 0.21,
                "years": 10
            },
            "sensitivity_analysis": {
                "discount_rate": {
                    "range": [-0.03, -0.02, -0.01, 0, 0.01, 0.02, 0.03],
                    "impact": [-25, -15, -8, 0, 8, 15, 25]
                },
                "terminal_growth_rate": {
                    "range": [-0.015, -0.01, -0.005, 0, 0.005, 0.01, 0.015],
                    "impact": [-18, -12, -6, 0, 6, 12, 18]
                },
                "revenue_growth": {
                    "range": [-0.05, -0.025, 0, 0.025, 0.05],
                    "impact": [-20, -10, 0, 10, 20]
                }
            }
        }
        
        report_generator = ReportGenerator()
        
        with tempfile.NamedTemporaryFile(suffix="_large_dataset.pdf", delete=False) as temp_file:
            temp_path = temp_file.name
        
        try:
            start_time = time.time()
            
            report_generator.generate_dcf_valuation_pdf(
                dcf_data=large_data,
                output_path=temp_path
            )
            
            end_time = time.time()
            generation_time = end_time - start_time
            
            # Should handle large datasets within reasonable time
            assert generation_time < 30.0, \
                f"Large dataset processing too slow: {generation_time:.2f}s"
            
            # Should produce substantial output
            file_size = Path(temp_path).stat().st_size
            assert file_size > 50000, \
                f"Large dataset report too small: {file_size} bytes"
            
            # Validate PDF structure
            with open(temp_path, 'rb') as f:
                content = f.read()
                assert b'%PDF' in content[:10], "Invalid PDF header"
                assert b'%%EOF' in content[-20:], "Invalid PDF footer"
            
        finally:
            if Path(temp_path).exists():
                Path(temp_path).unlink()
    
    def test_edge_case_data_handling(self):
        """Test handling of edge cases and boundary values."""
        edge_cases = [
            {
                "name": "zero_values",
                "data": {
                    "organization_id": 1,
                    "company_name": "Zero Values Test",
                    "enterprise_value": 0,
                    "financial_projections": [{
                        "year": 1,
                        "revenue": 0,
                        "free_cash_flow": 0,
                        "ebitda": 0
                    }]
                }
            },
            {
                "name": "very_large_values",
                "data": {
                    "organization_id": 1,
                    "company_name": "Large Values Test",
                    "enterprise_value": 999999999999,  # ~1 trillion
                    "financial_projections": [{
                        "year": 1,
                        "revenue": *********000,  # 100 billion
                        "free_cash_flow": 20000000000,  # 20 billion
                        "ebitda": 30000000000  # 30 billion
                    }]
                }
            },
            {
                "name": "very_small_values",
                "data": {
                    "organization_id": 1,
                    "company_name": "Small Values Test",
                    "enterprise_value": 1000,  # $1,000
                    "financial_projections": [{
                        "year": 1,
                        "revenue": 500,
                        "free_cash_flow": 50,
                        "ebitda": 100
                    }]
                }
            }
        ]
        
        report_generator = ReportGenerator()
        
        for case in edge_cases:
            with tempfile.NamedTemporaryFile(suffix=f"_{case['name']}.pdf") as temp_file:
                try:
                    report_generator.generate_dcf_valuation_pdf(
                        dcf_data=case["data"],
                        output_path=temp_file.name
                    )
                    
                    # Should handle edge cases without crashing
                    file_size = Path(temp_file.name).stat().st_size
                    assert file_size > 1000, \
                        f"Edge case {case['name']} produced invalid output"
                
                except Exception as e:
                    # If it fails, should fail gracefully with meaningful error
                    assert len(str(e)) > 0, \
                        f"Edge case {case['name']} failed with empty error"
                    
                    # Should not be system errors
                    assert not isinstance(e, (ImportError, NameError, AttributeError)), \
                        f"Edge case {case['name']} caused system error: {e}"


if __name__ == "__main__":
    # Allow running production validation tests directly
    pytest.main([__file__, "-v", "-m", "production"])