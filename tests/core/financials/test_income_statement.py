import pytest
from mcx3d_finance.core.financials.income_statement import calculate_income_statement


@pytest.fixture
def sample_transactions():
    """
    Provides a sample list of transactions for testing.
    """
    return [
        # Revenue
        {"account_type": "revenue", "amount": 1000},
        {"account_type": "revenue", "amount": 2000},
        # Cost of Goods Sold
        {"account_type": "cost_of_goods_sold", "amount": 500},
        {"account_type": "cost_of_goods_sold", "amount": 300},
        # Expenses
        {"account_type": "expense", "amount": 200},
        {"account_type": "expense", "amount": 150},
    ]


def test_calculate_income_statement_with_sample_data(sample_transactions):
    """
    Tests the income statement calculation with a typical set of transactions.
    """
    income_statement = calculate_income_statement(sample_transactions)

    assert income_statement["revenue"] == 3000
    assert income_statement["cost_of_goods_sold"] == 800
    assert income_statement["gross_profit"] == 2200
    assert income_statement["operating_expenses"] == 350
    assert income_statement["operating_income"] == 1850
    assert income_statement["net_income"] == 1850


def test_calculate_income_statement_with_no_transactions():
    """
    Tests the income statement calculation with an empty list of transactions.
    """
    income_statement = calculate_income_statement([])

    assert income_statement["revenue"] == 0
    assert income_statement["cost_of_goods_sold"] == 0
    assert income_statement["gross_profit"] == 0
    assert income_statement["operating_expenses"] == 0
    assert income_statement["operating_income"] == 0
    assert income_statement["net_income"] == 0


def test_calculate_income_statement_with_zero_amounts():
    """
    Tests the income statement calculation with transactions having zero amounts.
    """
    transactions = [
        {"account_type": "revenue", "amount": 0},
        {"account_type": "cost_of_goods_sold", "amount": 0},
        {"account_type": "expense", "amount": 0},
    ]
    income_statement = calculate_income_statement(transactions)

    assert income_statement["revenue"] == 0
    assert income_statement["cost_of_goods_sold"] == 0
    assert income_statement["gross_profit"] == 0
    assert income_statement["operating_expenses"] == 0
    assert income_statement["operating_income"] == 0
    assert income_statement["net_income"] == 0


def test_calculate_income_statement_with_missing_account_types():
    """
    Tests the income statement calculation with transactions that are missing
    the account_type key.
    """
    transactions = [
        {"amount": 100},
        {"amount": 200},
    ]
    income_statement = calculate_income_statement(transactions)

    assert income_statement["revenue"] == 0
    assert income_statement["cost_of_goods_sold"] == 0
    assert income_statement["gross_profit"] == 0
    assert income_statement["operating_expenses"] == 0
    assert income_statement["operating_income"] == 0
    assert income_statement["net_income"] == 0
