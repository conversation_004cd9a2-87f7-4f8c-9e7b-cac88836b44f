# MCX3D Financial Platform - Monitoring Configuration
# Environment-specific health check thresholds and monitoring settings

# Default configuration (fallback)
default:
  health_thresholds:
    database:
      max_response_time_ms: 100
      max_active_connections: 80
      critical_response_time_ms: 1000
      max_connection_usage_percent: 75
    
    redis:
      max_response_time_ms: 10
      max_memory_usage_percent: 80
      critical_response_time_ms: 100
      max_hit_ratio_threshold: 0.8
    
    system:
      max_cpu_percent: 80
      max_memory_percent: 85
      max_disk_percent: 90
      critical_cpu_percent: 95
      critical_memory_percent: 95
      critical_disk_percent: 98
    
    application:
      max_response_time_ms: 2000
      critical_response_time_ms: 5000
      min_success_rate: 0.95
      max_error_rate_percent: 5
    
    celery:
      max_queue_length: 1000
      critical_queue_length: 5000
      max_failed_tasks: 10
      critical_failed_tasks: 50
      min_worker_count: 1
      max_task_processing_time_ms: 30000
    
    external_services:
      max_response_time_ms: 5000
      critical_response_time_ms: 10000
      min_success_rate: 0.9
      timeout_seconds: 10
      retry_attempts: 3
      circuit_breaker_threshold: 5

  alert_thresholds:
    email_alerts:
      - WARNING
      - ERROR
      - CRITICAL
    slack_alerts:
      - ERROR
      - CRITICAL
    pagerduty_alerts:
      - CRITICAL
    
    rate_limiting:
      max_alerts_per_window: 10
      rate_limit_window_minutes: 5
      deduplication_minutes: 1

  monitoring_intervals:
    health_check_interval_seconds: 30
    metrics_collection_interval_seconds: 15
    external_service_check_interval_seconds: 60
    celery_metrics_interval_seconds: 20

# Development environment configuration
development:
  health_thresholds:
    database:
      max_response_time_ms: 200
      max_active_connections: 50
      critical_response_time_ms: 2000
      max_connection_usage_percent: 80
    
    redis:
      max_response_time_ms: 20
      max_memory_usage_percent: 90
      critical_response_time_ms: 200
      max_hit_ratio_threshold: 0.7
    
    system:
      max_cpu_percent: 90
      max_memory_percent: 90
      max_disk_percent: 95
      critical_cpu_percent: 98
      critical_memory_percent: 98
      critical_disk_percent: 99
    
    application:
      max_response_time_ms: 5000
      critical_response_time_ms: 10000
      min_success_rate: 0.90
      max_error_rate_percent: 10
    
    celery:
      max_queue_length: 100
      critical_queue_length: 500
      max_failed_tasks: 5
      critical_failed_tasks: 20
      min_worker_count: 1
      max_task_processing_time_ms: 60000
    
    external_services:
      max_response_time_ms: 10000
      critical_response_time_ms: 20000
      min_success_rate: 0.8
      timeout_seconds: 15
      retry_attempts: 2
      circuit_breaker_threshold: 10

  alert_thresholds:
    email_alerts:
      - CRITICAL
    slack_alerts:
      - ERROR
      - CRITICAL
    pagerduty_alerts: []
    
    rate_limiting:
      max_alerts_per_window: 20
      rate_limit_window_minutes: 10
      deduplication_minutes: 5

  monitoring_intervals:
    health_check_interval_seconds: 60
    metrics_collection_interval_seconds: 30
    external_service_check_interval_seconds: 120
    celery_metrics_interval_seconds: 60

# Testing environment configuration
testing:
  health_thresholds:
    database:
      max_response_time_ms: 50
      max_active_connections: 20
      critical_response_time_ms: 500
      max_connection_usage_percent: 70
    
    redis:
      max_response_time_ms: 5
      max_memory_usage_percent: 70
      critical_response_time_ms: 50
      max_hit_ratio_threshold: 0.9
    
    system:
      max_cpu_percent: 70
      max_memory_percent: 75
      max_disk_percent: 80
      critical_cpu_percent: 90
      critical_memory_percent: 90
      critical_disk_percent: 95
    
    application:
      max_response_time_ms: 1000
      critical_response_time_ms: 3000
      min_success_rate: 0.98
      max_error_rate_percent: 2
    
    celery:
      max_queue_length: 50
      critical_queue_length: 200
      max_failed_tasks: 2
      critical_failed_tasks: 10
      min_worker_count: 1
      max_task_processing_time_ms: 15000
    
    external_services:
      max_response_time_ms: 3000
      critical_response_time_ms: 5000
      min_success_rate: 0.95
      timeout_seconds: 5
      retry_attempts: 1
      circuit_breaker_threshold: 3

  alert_thresholds:
    email_alerts: []
    slack_alerts: []
    pagerduty_alerts: []
    
    rate_limiting:
      max_alerts_per_window: 50
      rate_limit_window_minutes: 1
      deduplication_minutes: 0

  monitoring_intervals:
    health_check_interval_seconds: 10
    metrics_collection_interval_seconds: 5
    external_service_check_interval_seconds: 30
    celery_metrics_interval_seconds: 10

# Production environment configuration
production:
  health_thresholds:
    database:
      max_response_time_ms: 50
      max_active_connections: 100
      critical_response_time_ms: 500
      max_connection_usage_percent: 70
    
    redis:
      max_response_time_ms: 5
      max_memory_usage_percent: 75
      critical_response_time_ms: 50
      max_hit_ratio_threshold: 0.9
    
    system:
      max_cpu_percent: 70
      max_memory_percent: 80
      max_disk_percent: 85
      critical_cpu_percent: 90
      critical_memory_percent: 90
      critical_disk_percent: 95
    
    application:
      max_response_time_ms: 1000
      critical_response_time_ms: 3000
      min_success_rate: 0.99
      max_error_rate_percent: 1
    
    celery:
      max_queue_length: 500
      critical_queue_length: 2000
      max_failed_tasks: 5
      critical_failed_tasks: 25
      min_worker_count: 2
      max_task_processing_time_ms: 20000
    
    external_services:
      max_response_time_ms: 3000
      critical_response_time_ms: 7000
      min_success_rate: 0.95
      timeout_seconds: 8
      retry_attempts: 3
      circuit_breaker_threshold: 3

  alert_thresholds:
    email_alerts:
      - WARNING
      - ERROR
      - CRITICAL
    slack_alerts:
      - ERROR
      - CRITICAL
    pagerduty_alerts:
      - CRITICAL
    
    rate_limiting:
      max_alerts_per_window: 5
      rate_limit_window_minutes: 5
      deduplication_minutes: 2

  monitoring_intervals:
    health_check_interval_seconds: 15
    metrics_collection_interval_seconds: 10
    external_service_check_interval_seconds: 30
    celery_metrics_interval_seconds: 15

# Staging environment configuration
staging:
  health_thresholds:
    database:
      max_response_time_ms: 75
      max_active_connections: 60
      critical_response_time_ms: 750
      max_connection_usage_percent: 75
    
    redis:
      max_response_time_ms: 8
      max_memory_usage_percent: 80
      critical_response_time_ms: 80
      max_hit_ratio_threshold: 0.85
    
    system:
      max_cpu_percent: 75
      max_memory_percent: 85
      max_disk_percent: 90
      critical_cpu_percent: 92
      critical_memory_percent: 92
      critical_disk_percent: 97
    
    application:
      max_response_time_ms: 1500
      critical_response_time_ms: 4000
      min_success_rate: 0.97
      max_error_rate_percent: 3
    
    celery:
      max_queue_length: 200
      critical_queue_length: 1000
      max_failed_tasks: 8
      critical_failed_tasks: 30
      min_worker_count: 1
      max_task_processing_time_ms: 25000
    
    external_services:
      max_response_time_ms: 4000
      critical_response_time_ms: 8000
      min_success_rate: 0.92
      timeout_seconds: 10
      retry_attempts: 3
      circuit_breaker_threshold: 4

  alert_thresholds:
    email_alerts:
      - ERROR
      - CRITICAL
    slack_alerts:
      - ERROR
      - CRITICAL
    pagerduty_alerts:
      - CRITICAL
    
    rate_limiting:
      max_alerts_per_window: 8
      rate_limit_window_minutes: 5
      deduplication_minutes: 1

  monitoring_intervals:
    health_check_interval_seconds: 20
    metrics_collection_interval_seconds: 15
    external_service_check_interval_seconds: 45
    celery_metrics_interval_seconds: 20