# MCX3D Finance Code Style & Conventions

## Code Formatting & Style

### Formatting Standards
- **Line Length**: 88 characters (Black default)
- **Indentation**: 4 spaces (no tabs)
- **Quotes**: Double quotes for strings, single quotes for keys when appropriate
- **Trailing Commas**: Required in multi-line data structures

### Naming Conventions
- **Variables/Functions**: `snake_case`
- **Classes**: `PascalCase`
- **Constants**: `UPPER_SNAKE_CASE`
- **Private Members**: `_private_method` (single underscore prefix)
- **File Names**: `snake_case.py`

### Documentation Standards
- **Docstrings**: Google-style format for all public functions/classes
- **Type Hints**: Required for all function parameters and return types
- **Comments**: Clear, concise comments for complex logic
- **Examples**: Include usage examples in docstrings

### Example Function Documentation
```python
def calculate_tax_amount(gross_amount: float, tax_rate: float) -> float:
    """Calculate tax amount based on gross amount and tax rate.
    
    Args:
        gross_amount: The gross amount before tax
        tax_rate: Tax rate as a decimal (e.g., 0.20 for 20%)
        
    Returns:
        The calculated tax amount
        
    Raises:
        ValueError: If gross_amount or tax_rate is negative
        
    Example:
        >>> calculate_tax_amount(100.0, 0.20)
        20.0
    """
```

## Import Organization (isort)

### Import Order
1. **Standard Library**: Built-in Python modules
2. **Third-Party**: External packages
3. **Local Application**: Relative imports from project

### Import Style
- Use absolute imports when possible
- Group related imports together
- Use `from ... import ...` for frequently used items
- Avoid wildcard imports (`from module import *`)

### Example Import Structure
```python
# Standard library imports
import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional

# Third party imports
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

# Local application imports
from ..db.models import User, Organization
from ..exceptions.auth import AuthenticationError
from ..utils.security import hash_password
```

## Exception Handling

### Custom Exception Hierarchy
- **AuthenticationError**: Authentication and authorization failures
- **ValidationError**: Input validation and data validation errors
- **IntegrationError**: External service integration failures
- **BusinessLogicError**: Business rule violations

### Exception Best Practices
- Use specific exception types instead of generic `Exception`
- Include context information in exception messages
- Log exceptions appropriately based on severity
- Handle exceptions at appropriate levels

## Type Hints

### Required Type Hints
- All function parameters and return types
- Class attributes when not obvious
- Variable annotations for complex types

### Type Hint Guidelines
- Use `Optional[T]` for values that can be `None`
- Use `Union[T1, T2]` for multiple types
- Use `List[T]`, `Dict[K, V]` for collections
- Import from `typing` module for compatibility

## Code Quality Tools

### Automated Tools (CI/CD Enforced)
1. **Black**: Code formatting (88 characters)
2. **isort**: Import sorting with black profile
3. **flake8**: Style and syntax checking
4. **mypy**: Static type checking
5. **pylint**: Advanced code analysis
6. **bandit**: Security vulnerability scanning

### Tool Configuration Files
- `.isort.cfg`: Import sorting configuration
- `.pylintrc`: Pylint rules and exclusions
- `mypy.ini`: Type checking configuration
- `.flake8`: Linting rules and exclusions

### Pre-commit Integration
- All quality checks run automatically before commits
- Manual execution: `pre-commit run --all-files`
- Hook installation: `pre-commit install`

## Security Best Practices

### General Security
- Never hardcode secrets or API keys
- Use environment variables for configuration
- Validate all inputs and sanitize outputs
- Follow secure coding practices for auth

### Code-Level Security
- Use parameterized queries to prevent SQL injection
- Sanitize user inputs with `bleach`
- Encrypt sensitive data at rest
- Implement proper session management

## Performance Considerations

### Development Guidelines
- Profile before optimizing
- Use appropriate data structures and algorithms
- Cache expensive operations when appropriate
- Consider database query optimization

### Code Organization
- Keep functions focused on single responsibility
- Use composition over inheritance
- Minimize coupling between components
- Implement proper error boundaries