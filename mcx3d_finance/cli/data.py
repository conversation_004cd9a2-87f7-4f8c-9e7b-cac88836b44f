import click
from typing import Optional, List
import logging
import json
import csv
import os
import asyncio
from datetime import datetime, timezone
from pathlib import Path
from mcx3d_finance.tasks.sync_tasks import sync_xero_data
from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization, Account, Contact, Transaction, Invoice, BankTransaction
from mcx3d_finance.integrations.xero_sync import XeroSyncEngine
from .error_handler import handle_cli_errors, display_success_message
from contextlib import contextmanager

logger = logging.getLogger(__name__)


@contextmanager
def get_db_session():
    """Context manager for database sessions in CLI."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@click.group()
def sync():
    """Synchronizes data from external sources."""
    pass


def run_direct_sync(org_id: int, incremental: bool, storage_service_class, progress_bar=None, progress_callback=None, use_parallel: bool = False):
    """Run Xero sync directly without Ce<PERSON>y."""
    from mcx3d_finance.db.models import Organization
    from mcx3d_finance.auth.xero_oauth import XeroAuthManager
    from mcx3d_finance.integrations.xero_data_import import XeroDataImportService
    # Use the local get_db_session function defined above
    
    with get_db_session() as db:
        try:
            # Get organization
            organization = db.query(Organization).get(org_id)
            if not organization:
                return {"success": False, "error": f"Organization {org_id} not found"}
            
            # Initialize components
            auth_manager = XeroAuthManager()
            
            # Get access token for organization
            if progress_callback:
                progress_callback(None, {"status": "Authenticating with Xero...", "progress": 5})
            
            token = auth_manager.get_valid_token(org_id)
            if not token:
                return {"success": False, "error": f"No valid token found for organization {org_id}"}
            
            # Initialize import and storage services with optimization
            import_service = XeroDataImportService(org_id)
            
            # Check if we're using the parallel storage service
            if hasattr(storage_service_class, 'store_all_data_parallel'):
                # Parallel storage service with max_workers parameter
                storage_service = storage_service_class(db, optimize=True, max_workers=4)
            else:
                # Standard storage service
                storage_service = storage_service_class(db, optimize=True)
            
            # Progress callbacks
            def import_progress(progress, message):
                if progress_callback:
                    scaled_progress = 10 + int(progress * 0.6)
                    progress_callback(None, {"status": f"Import: {message}", "progress": scaled_progress})
            
            def storage_progress(progress, message):
                if progress_callback:
                    scaled_progress = 70 + int(progress * 0.25)
                    progress_callback(None, {"status": f"Storage: {message}", "progress": scaled_progress})
            
            # Import data from Xero
            if incremental and organization.last_sync_at:
                import_result = import_service.import_incremental_data(
                    since_date=organization.last_sync_at
                )
            else:
                import_result = import_service.import_all_data(
                    progress_callback=import_progress
                )
            
            if not import_result["success"]:
                return {"success": False, "error": import_result.get('error', 'Import failed')}
            
            # Store data in database with optimization
            storage_result = storage_service.store_all_data(
                organization_id=org_id,
                imported_data=import_result["processed_data"],
                progress_callback=storage_progress
            )
            
            if not storage_result["success"]:
                return {"success": False, "error": storage_result.get('error', 'Storage failed')}
            
            # Update organization sync status
            organization.last_sync_at = datetime.now(timezone.utc)
            organization.sync_status = "completed"
            db.commit()
            
            # Return results
            return {
                "success": True,
                "org_id": org_id,
                "sync_type": "incremental" if incremental else "full",
                "results": {
                    "import_stats": import_result["stats"],
                    "storage_stats": storage_result["stats"],
                    "processing_results": import_result["processed_data"].get("processing_results", {}),
                    "errors": []
                }
            }
            
        except Exception as e:
            logger.error(f"Error in direct sync: {e}")
            return {"success": False, "error": str(e)}

@sync.command("xero")
@click.option("--org-id", required=True, type=int, help="The organization ID to sync.")
@click.option(
    "--incremental",
    is_flag=True,
    help="Run an incremental sync instead of a full sync.",
)
@click.option(
    "--async-mode",
    is_flag=True,
    help="Run sync asynchronously using Celery.",
)
@click.option(
    "--show-progress",
    is_flag=True,
    default=True,
    help="Show progress during sync (for synchronous mode).",
)
@click.option(
    "--optimize/--no-optimize",
    default=True,
    help="Use optimized storage service with batch loading (default: True)",
)
@click.option(
    "--monitor-queries",
    is_flag=True,
    help="Enable query monitoring to detect performance issues",
)
@click.option(
    "--parallel/--no-parallel",
    default=False,
    help="Use parallel processing for storing data (experimental, default: False)",
)
def sync_xero(org_id: int, incremental: bool, async_mode: bool, show_progress: bool, optimize: bool, monitor_queries: bool, parallel: bool):
    """Sync data from Xero accounting system.
    
    This command imports all data from Xero including:
    - Chart of Accounts with GAAP classification
    - Contacts with business enrichment
    - Invoices (sales and purchase)
    - Bank transactions
    - Financial reports (Trial Balance, P&L)
    
    The data is processed through validation and enrichment pipelines
    before being stored in the database.
    """
    try:
        # Validate Xero configuration
        from mcx3d_finance.core.config import get_xero_config
        xero_config = get_xero_config()
        if not xero_config.get("client_id") or not xero_config.get("client_secret"):
            click.echo("Error: Xero API credentials not configured", err=True)
            click.echo("Please set XERO_CLIENT_ID and XERO_CLIENT_SECRET environment variables")
            return

        click.echo(f"Starting Xero sync for organization: {org_id}")
        click.echo(f"Mode: {'Incremental' if incremental else 'Full'} sync")
        click.echo(f"Processing: {'Asynchronous' if async_mode else 'Synchronous'}")

        if async_mode:
            # Dispatch Celery task
            task = sync_xero_data.delay(org_id, incremental)
            click.echo(f"\n✅ Sync task queued with ID: {task.id}")
            click.echo(f"Use 'mcx3d-finance sync status {task.id}' to check progress")
            click.echo("\nThe sync will run in the background. You can continue using the CLI.")
        else:
            # Run synchronously with progress display
            click.echo("\nStarting synchronous sync...")
            
            # Import necessary modules for optimized sync
            # Use the local get_db_session function defined above
            from mcx3d_finance.integrations.xero_sync import XeroSyncEngine
            from mcx3d_finance.integrations.xero_data_import import XeroDataImportService
            from mcx3d_finance.monitoring.query_monitor import query_monitor
            
            if parallel:
                from mcx3d_finance.integrations.xero_data_storage_parallel import ParallelXeroDataStorageService as StorageService
                click.echo("🚀 Using parallel storage service with concurrent processing")
                click.echo("⚡ Processing accounts, contacts, invoices, and transactions in parallel")
            elif optimize:
                from mcx3d_finance.integrations.xero_data_storage_optimized import OptimizedXeroDataStorageService as StorageService
                click.echo("✅ Using optimized storage service with batch loading")
            else:
                from mcx3d_finance.integrations.xero_data_storage import XeroDataStorageService as StorageService
                click.echo("ℹ️  Using standard storage service")
            
            if monitor_queries:
                click.echo("📊 Query monitoring enabled")
            
            if show_progress:
                # Import progress bar utilities
                with click.progressbar(length=100, label='Syncing data') as bar:
                    last_progress = 0
                    
                    # Custom progress callback for direct execution
                    def progress_callback(state, meta):
                        nonlocal last_progress
                        progress = meta.get('progress', 0)
                        if progress > last_progress:
                            bar.update(progress - last_progress)
                            last_progress = progress
                        if meta.get('status'):
                            bar.label = meta['status']
                    
                    # For direct execution, we need to run the task function directly
                    # This simulates the Celery task execution
                    from mcx3d_finance.tasks.sync_tasks import sync_xero_data
                    
                    # Run sync directly with optimized storage
                    if monitor_queries:
                        with query_monitor.monitor():
                            result = run_direct_sync(org_id, incremental, StorageService, bar, progress_callback, use_parallel=parallel)
                    else:
                        result = run_direct_sync(org_id, incremental, StorageService, bar, progress_callback, use_parallel=parallel)
            else:
                # Run without progress bar
                if monitor_queries:
                    with query_monitor.monitor():
                        result = run_direct_sync(org_id, incremental, StorageService, None, None, use_parallel=parallel)
                else:
                    result = run_direct_sync(org_id, incremental, StorageService, None, None, use_parallel=parallel)
            
            # Display results
            if result.get("success"):
                click.echo(f"\n✅ Sync completed successfully!")
                
                # Display statistics
                results = result.get('results', {})
                import_stats = results.get('import_stats', {})
                storage_stats = results.get('storage_stats', {})
                
                click.echo("\nImport Statistics:")
                for entity, stats in import_stats.items():
                    if isinstance(stats, dict) and 'imported' in stats:
                        click.echo(f"  {entity}: {stats['imported']}/{stats['total']} imported, {stats['errors']} errors")
                
                click.echo("\nStorage Statistics:")
                for entity, stats in storage_stats.items():
                    if isinstance(stats, dict):
                        created = stats.get('created', 0)
                        updated = stats.get('updated', 0)
                        skipped = stats.get('skipped', 0)
                        click.echo(f"  {entity}: {created} created, {updated} updated, {skipped} skipped")
                
                # Display any errors
                errors = results.get('errors', [])
                if errors:
                    click.echo(f"\n⚠️  Sync completed with {len(errors)} errors:")
                    for error in errors[:5]:  # Show first 5 errors
                        click.echo(f"  - {error}")
                    if len(errors) > 5:
                        click.echo(f"  ... and {len(errors) - 5} more errors")
            else:
                click.echo(f"\n❌ Sync failed: {result.get('error')}", err=True)

    except Exception as e:
        logger.error(f"Error in Xero sync command: {e}")
        click.echo(f"\n❌ Error: {e}", err=True)
        click.echo("Please check the logs for more details.")


@sync.command("status")
@click.argument("task_id")
def check_sync_status(task_id: str):
    """Check the status of a sync task."""
    try:
        from mcx3d_finance.tasks.celery_app import celery_app

        task = celery_app.AsyncResult(task_id)

        click.echo(f"Task ID: {task_id}")
        click.echo(f"Status: {task.status}")

        if task.status == "SUCCESS":
            result = task.result
            click.echo(f"Result: {result}")
        elif task.status == "FAILURE":
            click.echo(f"Error: {task.info}")
        elif task.status == "PENDING":
            click.echo("Task is still processing...")

    except Exception as e:
        logger.error(f"Error checking task status: {e}")
        click.echo(f"Error: {e}", err=True)


@click.command()
@click.option("--file", required=True, help="Excel file to import")
@click.option("--mapping", help="JSON file with account mapping configuration")
@click.option("--org-id", help="Target organization ID")
def import_excel(file: str, mapping: Optional[str], org_id: Optional[str]):
    """Import financial data from Excel file."""
    try:
        click.echo(f"Importing data from: {file}")

        # Implementation would go here

        # Process Excel file and import to database

        click.echo("Import completed successfully")

    except Exception as e:
        logger.error(f"Error importing Excel file: {e}")
        click.echo(f"Error: {e}", err=True)


sync.add_command(import_excel)


@sync.command("download")
@click.option("--org-id", required=True, type=int, help="Organization ID to download data from")
@click.option("--types", help="Comma-separated list of data types (accounts,contacts,transactions,invoices,bank_transactions)")
@click.option("--start-date", help="Start date for data filtering (YYYY-MM-DD)")
@click.option("--end-date", help="End date for data filtering (YYYY-MM-DD)")
@click.option("--incremental", is_flag=True, help="Download only new/updated data since last sync")
@click.option("--output-dir", help="Output directory for downloaded data", default="./data_downloads")
@handle_cli_errors()
def download_data(org_id: int, types: Optional[str], start_date: Optional[str], 
                  end_date: Optional[str], incremental: bool, output_dir: str):
    """Download financial data from connected sources.
    
    Downloads data from Xero and other connected sources and saves to local files.
    
    Examples:
        mcx3d sync download --org-id 123
        mcx3d sync download --org-id 123 --types accounts,contacts --incremental
        mcx3d sync download --org-id 123 --start-date 2024-01-01 --end-date 2024-12-31
    """
    try:
        # Parse data types
        data_types = ["accounts", "contacts", "transactions", "invoices", "bank_transactions"]
        if types:
            data_types = [t.strip() for t in types.split(",")]
        
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Create timestamped subdirectory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        download_path = output_path / f"download_{org_id}_{timestamp}"
        download_path.mkdir(exist_ok=True)

        with get_db_session() as db:
            # Validate organization
            organization = db.query(Organization).filter(Organization.id == org_id).first()
            if not organization:
                click.echo(click.style(f"❌ Organization with ID {org_id} not found", fg="red"))
                return

            click.echo(click.style(f"📥 Downloading data for organization: {organization.name}", fg="blue", bold=True))
            click.echo(f"Data types: {', '.join(data_types)}")
            click.echo(f"Output directory: {download_path}")
            
            if start_date:
                click.echo(f"Start date: {start_date}")
            if end_date:
                click.echo(f"End date: {end_date}")
            
            click.echo(f"Mode: {'Incremental' if incremental else 'Full'} download")
            click.echo()

            # Initialize Xero sync engine
            sync_engine = XeroSyncEngine(db)
            
            # Download data
            download_results = {}
            
            with click.progressbar(data_types, label="Downloading data types") as types_bar:
                for data_type in types_bar:
                    try:
                        # Download from Xero API
                        sync_result = asyncio.run(sync_engine.sync_organization_data(
                            org_id=str(org_id),
                            incremental=incremental,
                            sync_types=[data_type]
                        ))
                        
                        download_results[data_type] = sync_result
                        
                        # Save raw data to JSON file
                        json_file = download_path / f"{data_type}_{timestamp}.json"
                        with open(json_file, 'w') as f:
                            json.dump(sync_result, f, indent=2, default=str)
                        
                    except Exception as e:
                        logger.error(f"Failed to download {data_type}: {e}")
                        download_results[data_type] = {"error": str(e)}

            # Generate download summary
            summary = {
                "organization_id": org_id,
                "organization_name": organization.name,
                "download_timestamp": datetime.now(timezone.utc).isoformat(),
                "data_types": data_types,
                "incremental": incremental,
                "start_date": start_date,
                "end_date": end_date,
                "results": download_results,
                "output_directory": str(download_path)
            }
            
            # Save summary
            summary_file = download_path / "download_summary.json"
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2, default=str)

            # Display results
            click.echo()
            success_msg = f"Data download completed successfully:\n"
            success_msg += f"  • Organization: {organization.name} (ID: {org_id})\n"
            success_msg += f"  • Data types: {len(data_types)}\n"
            success_msg += f"  • Output directory: {download_path}\n"
            success_msg += f"  • Summary file: {summary_file}\n"
            
            errors = [dt for dt, result in download_results.items() if "error" in result]
            if errors:
                success_msg += f"  • Errors: {len(errors)} data types had errors\n"
            
            display_success_message(success_msg)

    except Exception as e:
        logger.error(f"Download failed: {e}")
        click.echo(click.style(f"❌ Download failed: {e}", fg="red"))


@sync.command("export")
@click.option("--org-id", required=True, type=int, help="Organization ID to export data from")
@click.option("--format", "export_format", type=click.Choice(['csv', 'json', 'excel']), default='csv', help="Export format")
@click.option("--tables", help="Comma-separated list of tables to export (accounts,contacts,transactions,invoices)")
@click.option("--start-date", help="Start date for data filtering (YYYY-MM-DD)")
@click.option("--end-date", help="End date for data filtering (YYYY-MM-DD)")
@click.option("--output", help="Output file path")
@handle_cli_errors()
def export_data(org_id: int, export_format: str, tables: Optional[str], 
                start_date: Optional[str], end_date: Optional[str], output: Optional[str]):
    """Export financial data to various formats.
    
    Exports data from the local database to CSV, JSON, or Excel formats.
    
    Examples:
        mcx3d sync export --org-id 123 --format csv
        mcx3d sync export --org-id 123 --format excel --tables accounts,contacts
        mcx3d sync export --org-id 123 --format json --output financial_data.json
    """
    try:
        # Define available tables and their models
        table_models = {
            'accounts': Account,
            'contacts': Contact,
            'transactions': Transaction,
            'invoices': Invoice,
            'bank_transactions': BankTransaction
        }
        
        # Parse tables to export
        export_tables = list(table_models.keys())
        if tables:
            export_tables = [t.strip() for t in tables.split(",")]
            # Validate table names
            invalid_tables = [t for t in export_tables if t not in table_models]
            if invalid_tables:
                click.echo(click.style(f"❌ Invalid table names: {', '.join(invalid_tables)}", fg="red"))
                click.echo(f"Available tables: {', '.join(table_models.keys())}")
                return

        # Generate output filename if not provided
        if not output:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            extension = export_format if export_format != 'excel' else 'xlsx'
            output = f"export_org_{org_id}_{timestamp}.{extension}"

        with get_db_session() as db:
            # Validate organization
            organization = db.query(Organization).filter(Organization.id == org_id).first()
            if not organization:
                click.echo(click.style(f"❌ Organization with ID {org_id} not found", fg="red"))
                return

            click.echo(click.style(f"📤 Exporting data for organization: {organization.name}", fg="blue", bold=True))
            click.echo(f"Tables: {', '.join(export_tables)}")
            click.echo(f"Format: {export_format.upper()}")
            click.echo(f"Output: {output}")
            if start_date:
                click.echo(f"Start date: {start_date}")
            if end_date:
                click.echo(f"End date: {end_date}")
            click.echo()

            # Export data by format
            if export_format == 'json':
                export_data = {}
                
                with click.progressbar(export_tables, label="Exporting tables") as tables_bar:
                    for table_name in tables_bar:
                        model = table_models[table_name]
                        query = db.query(model).filter(model.organization_id == org_id)
                        
                        # Apply date filtering if provided
                        if start_date and hasattr(model, 'date'):
                            query = query.filter(model.date >= start_date)
                        if end_date and hasattr(model, 'date'):
                            query = query.filter(model.date <= end_date)
                        
                        records = query.all()
                        
                        # Convert to dictionaries
                        export_data[table_name] = []
                        for record in records:
                            record_dict = {}
                            for column in record.__table__.columns:
                                value = getattr(record, column.name)
                                if isinstance(value, datetime):
                                    value = value.isoformat()
                                record_dict[column.name] = value
                            export_data[table_name].append(record_dict)
                
                # Save JSON file
                with open(output, 'w') as f:
                    json.dump(export_data, f, indent=2, default=str)
                    
            elif export_format == 'csv':
                # Create directory for CSV files
                output_dir = Path(output).with_suffix('')
                output_dir.mkdir(exist_ok=True)
                
                csv_files = []
                with click.progressbar(export_tables, label="Exporting tables") as tables_bar:
                    for table_name in tables_bar:
                        model = table_models[table_name]
                        query = db.query(model).filter(model.organization_id == org_id)
                        
                        # Apply date filtering if provided
                        if start_date and hasattr(model, 'date'):
                            query = query.filter(model.date >= start_date)
                        if end_date and hasattr(model, 'date'):
                            query = query.filter(model.date <= end_date)
                        
                        records = query.all()
                        
                        if records:
                            csv_file = output_dir / f"{table_name}.csv"
                            csv_files.append(csv_file)
                            
                            with open(csv_file, 'w', newline='') as f:
                                writer = None
                                for record in records:
                                    record_dict = {}
                                    for column in record.__table__.columns:
                                        value = getattr(record, column.name)
                                        if isinstance(value, datetime):
                                            value = value.isoformat()
                                        elif value is None:
                                            value = ''
                                        record_dict[column.name] = str(value)
                                    
                                    if writer is None:
                                        writer = csv.DictWriter(f, fieldnames=record_dict.keys())
                                        writer.writeheader()
                                    
                                    writer.writerow(record_dict)
                
                output = output_dir
                
            elif export_format == 'excel':
                try:
                    import pandas as pd
                    
                    with pd.ExcelWriter(output, engine='openpyxl') as writer:
                        with click.progressbar(export_tables, label="Exporting tables") as tables_bar:
                            for table_name in tables_bar:
                                model = table_models[table_name]
                                query = db.query(model).filter(model.organization_id == org_id)
                                
                                # Apply date filtering if provided
                                if start_date and hasattr(model, 'date'):
                                    query = query.filter(model.date >= start_date)
                                if end_date and hasattr(model, 'date'):
                                    query = query.filter(model.date <= end_date)
                                
                                records = query.all()
                                
                                if records:
                                    # Convert to DataFrame
                                    data = []
                                    for record in records:
                                        record_dict = {}
                                        for column in record.__table__.columns:
                                            value = getattr(record, column.name)
                                            record_dict[column.name] = value
                                        data.append(record_dict)
                                    
                                    df = pd.DataFrame(data)
                                    df.to_excel(writer, sheet_name=table_name, index=False)
                
                except ImportError:
                    click.echo(click.style("❌ pandas and openpyxl are required for Excel export", fg="red"))
                    click.echo("Install with: pip install pandas openpyxl")
                    return

            # Display success message
            success_msg = f"Data export completed successfully:\n"
            success_msg += f"  • Organization: {organization.name} (ID: {org_id})\n"
            success_msg += f"  • Tables: {', '.join(export_tables)}\n"
            success_msg += f"  • Format: {export_format.upper()}\n"
            success_msg += f"  • Output: {output}\n"
            
            display_success_message(success_msg)

    except Exception as e:
        logger.error(f"Export failed: {e}")
        click.echo(click.style(f"❌ Export failed: {e}", fg="red"))


@sync.command("list-orgs")
@handle_cli_errors()
def list_organizations():
    """List all organizations with their sync status.
    
    Shows organizations available for data operations along with their
    connection status and last sync information.
    """
    try:
        with get_db_session() as db:
            organizations = db.query(Organization).filter(Organization.is_active == True).all()
            
            if not organizations:
                click.echo(click.style("No active organizations found", fg="yellow"))
                return

            click.echo(click.style("\n🏢 Organizations", fg="blue", bold=True))
            click.echo("=" * 80)
            
            for org in organizations:
                status = "🟢 Connected" if org.xero_tenant_id else "🔴 Not connected"
                
                click.echo(f"\n🏢 {org.name}")
                click.echo(f"   ID: {org.id}")
                click.echo(f"   Status: {status}")
                click.echo(f"   Currency: {org.base_currency}")
                
                if org.xero_tenant_id:
                    click.echo(f"   Xero Tenant: {org.xero_tenant_id}")
                    click.echo(f"   Tenant Type: {org.xero_tenant_type}")
                
                if org.last_sync_at:
                    click.echo(f"   Last Sync: {org.last_sync_at.strftime('%Y-%m-%d %H:%M:%S')}")
                    click.echo(f"   Sync Status: {org.sync_status}")
                else:
                    click.echo(f"   Last Sync: Never")
                
                click.echo(f"   Created: {org.created_at.strftime('%Y-%m-%d %H:%M:%S') if org.created_at else 'Unknown'}")

            # Summary
            connected_orgs = sum(1 for org in organizations if org.xero_tenant_id)
            click.echo("\n" + "=" * 80)
            click.echo(f"📊 Summary: {len(organizations)} organizations total, {connected_orgs} connected to Xero")

    except Exception as e:
        logger.error(f"Failed to list organizations: {e}")
        click.echo(click.style(f"❌ Failed to list organizations: {e}", fg="red"))
