"""
Pydantic schemas for API request/response validation.
"""
from pydantic import BaseModel, Field, field_validator
from typing import List, Dict, Any, Optional
from datetime import datetime, date
from enum import Enum


class ReportFormat(str, Enum):
    """Supported report output formats."""
    json = "json"
    pdf = "pdf"
    excel = "excel"
    html = "html"


class DateRange(BaseModel):
    """Date range for report generation."""
    start_date: str = Field(..., pattern="^\\d{4}-\\d{2}-\\d{2}$")
    end_date: str = Field(..., pattern="^\\d{4}-\\d{2}-\\d{2}$")
    
    @field_validator("end_date")
    @classmethod
    def end_date_after_start(cls, v, info):
        values = info.data if hasattr(info, 'data') else {}
        if "start_date" in values and v < values["start_date"]:
            raise ValueError("end_date must be after or equal to start_date")
        return v


class IncomeStatementRequest(BaseModel):
    """Request schema for income statement generation."""
    organization_id: int = Field(..., gt=0)
    start_date: str = Field(..., pattern="^\\d{4}-\\d{2}-\\d{2}$")
    end_date: str = Field(..., pattern="^\\d{4}-\\d{2}-\\d{2}$")
    format: ReportFormat = ReportFormat.json
    
    @field_validator("end_date")
    @classmethod
    def validate_date_range(cls, v, info):
        values = info.data if hasattr(info, 'data') else {}
        if "start_date" in values:
            start = datetime.strptime(values["start_date"], "%Y-%m-%d")
            end = datetime.strptime(v, "%Y-%m-%d")
            if end < start:
                raise ValueError("end_date must be after or equal to start_date")
        return v


class BalanceSheetRequest(BaseModel):
    """Request schema for balance sheet generation."""
    organization_id: int = Field(..., gt=0)
    date: str = Field(..., pattern="^\\d{4}-\\d{2}-\\d{2}$")
    format: ReportFormat = ReportFormat.json


class CashFlowRequest(BaseModel):
    """Request schema for cash flow statement generation."""
    organization_id: int = Field(..., gt=0)
    start_date: str = Field(..., pattern="^\\d{4}-\\d{2}-\\d{2}$")
    end_date: str = Field(..., pattern="^\\d{4}-\\d{2}-\\d{2}$")
    format: ReportFormat = ReportFormat.json
    
    @field_validator("end_date")
    @classmethod
    def validate_date_range(cls, v, info):
        values = info.data if hasattr(info, 'data') else {}
        if "start_date" in values:
            start = datetime.strptime(values["start_date"], "%Y-%m-%d")
            end = datetime.strptime(v, "%Y-%m-%d")
            if end < start:
                raise ValueError("end_date must be after or equal to start_date")
        return v


class FinancialProjection(BaseModel):
    """Single year financial projection for DCF."""
    year: int = Field(..., gt=2000, lt=2100)
    revenue: float = Field(..., ge=0)
    costs: float = Field(..., ge=0)
    capex: float = Field(..., ge=0)
    working_capital_change: float
    
    @property
    def free_cash_flow(self) -> float:
        """Calculate free cash flow."""
        return self.revenue - self.costs - self.capex - self.working_capital_change


class DCFValuationRequest(BaseModel):
    """Request schema for DCF valuation."""
    financial_projections: List[FinancialProjection] = Field(..., min_items=1)
    discount_rate: float = Field(..., gt=0, lt=1)
    terminal_growth_rate: float = Field(..., ge=0, lt=1)
    
    @field_validator("terminal_growth_rate")
    @classmethod
    def validate_terminal_growth(cls, v, info):
        values = info.data if hasattr(info, 'data') else {}
        if "discount_rate" in values and v >= values["discount_rate"]:
            raise ValueError("terminal_growth_rate must be less than discount_rate")
        return v


class MultiplesValuationRequest(BaseModel):
    """Request schema for multiples-based valuation."""
    financial_metrics: Dict[str, float] = Field(..., min_items=1)
    comparable_multiples: Dict[str, float] = Field(..., min_items=1)


class SaasKpisRequest(BaseModel):
    """Request schema for SaaS KPIs calculation."""
    organization_id: int = Field(..., gt=0)
    period_months: int = Field(12, ge=1, le=60)
    as_of_date: Optional[str] = Field(None, pattern="^\\d{4}-\\d{2}-\\d{2}$")


class SaasKpisResponse(BaseModel):
    """Response schema for SaaS KPIs."""
    organization_id: int
    period: Dict[str, str]
    metrics: Dict[str, Any]
    health_score: Optional[str]
    recommendations: List[str]
    generated_at: datetime


class FinancialReportResponse(BaseModel):
    """Generic financial report response."""
    report_name: str
    organization_id: int
    period: Optional[Dict[str, str]]
    as_of_date: Optional[str]
    data: Dict[str, Any]
    generated_at: datetime


class ValuationResponse(BaseModel):
    """Generic valuation response."""
    valuation_type: str
    inputs: Dict[str, Any]
    valuation: Dict[str, Any]
    generated_at: datetime


class ErrorResponse(BaseModel):
    """Standard error response."""
    error: str
    detail: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: Optional[str]


class HealthCheckResponse(BaseModel):
    """Health check response."""
    status: str
    version: str
    services: Dict[str, str]
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class TokenResponse(BaseModel):
    """JWT token response."""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    refresh_token: Optional[str]


class OrganizationResponse(BaseModel):
    """Organization information response."""
    id: int
    name: str
    xero_tenant_id: str
    created_at: datetime
    updated_at: Optional[datetime]


class SyncStatusResponse(BaseModel):
    """Data sync status response."""
    organization_id: int
    sync_status: str
    last_sync_at: Optional[datetime]
    next_sync_at: Optional[datetime]
    records_synced: Dict[str, int]
    errors: List[str] = []


class XeroImportRequest(BaseModel):
    """Request schema for Xero data import."""
    organization_id: int = Field(..., gt=0)
    incremental: bool = Field(False, description="If true, only import changed data since last sync")
    force_refresh: bool = Field(False, description="If true, force refresh of all data")


class XeroImportResponse(BaseModel):
    """Response schema for Xero data import."""
    success: bool
    organization_id: int
    import_stats: Dict[str, Dict[str, int]]
    processed_data_summary: Dict[str, Any]
    timestamp: datetime
    error: Optional[str] = None


class XeroSyncStatusRequest(BaseModel):
    """Request schema for Xero sync status."""
    organization_id: int = Field(..., gt=0)


class XeroAuthStatusResponse(BaseModel):
    """Response schema for Xero authentication status."""
    organization_id: int
    is_authenticated: bool
    expires_at: Optional[datetime]
    scopes: List[str] = []
    tenant_name: Optional[str] = None


# Financial Documentation Schemas

class FinancialPackageRequest(BaseModel):
    """Request model for generating financial documentation package."""
    report_date: Optional[date] = Field(None, description="Report date (defaults to today)")
    include_projections: bool = Field(True, description="Include valuation and projections")
    formats: List[ReportFormat] = Field(
        default=[ReportFormat.pdf, ReportFormat.excel],
        description="Output formats for documents"
    )
    email_on_completion: bool = Field(False, description="Send email when generation completes")


class FinancialPackageResponse(BaseModel):
    """Response model for financial package generation request."""
    task_id: str = Field(..., description="Task ID for tracking generation progress")
    status: str = Field(..., description="Current status of the task")
    message: str = Field(..., description="Status message")
    estimated_completion_time: int = Field(..., description="Estimated seconds to completion")


class DocumentGenerationStatus(BaseModel):
    """Status model for document generation task."""
    task_id: str
    status: str = Field(..., description="Task status: queued, processing, completed, failed")
    progress: int = Field(..., ge=0, le=100, description="Progress percentage")
    message: str = Field(..., description="Current status message")
    result: Optional[Dict[str, Any]] = Field(None, description="Task result when completed")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            date: lambda v: v.isoformat()
        }


class ExecutiveSummaryResponse(BaseModel):
    """Response model for executive summary data."""
    revenue: float = Field(..., description="Total revenue")
    net_income: float = Field(..., description="Net income")
    total_assets: float = Field(..., description="Total assets")
    cash_balance: float = Field(..., description="Cash and cash equivalents")
    revenue_growth: float = Field(..., description="Year-over-year revenue growth %")
    key_metrics: Dict[str, float] = Field(..., description="Key financial metrics")
    report_date: date = Field(..., description="Date of the report")
    
    class Config:
        json_encoders = {
            date: lambda v: v.isoformat()
        }


class FinancialKPIsResponse(BaseModel):
    """Response model for financial KPIs."""
    financial_ratios: Dict[str, float] = Field(
        ..., 
        description="Financial ratios (current_ratio, quick_ratio, etc.)"
    )
    growth_metrics: Dict[str, float] = Field(
        ...,
        description="Growth metrics (revenue_growth, yoy_growth, etc.)"
    )
    saas_metrics: Optional[Dict[str, float]] = Field(
        None,
        description="SaaS-specific metrics (mrr, arr, churn_rate, etc.)"
    )
