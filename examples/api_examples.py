import os
import requests
import json

# --- Configuration ---
API_BASE_URL = os.environ.get("API_BASE_URL", "http://localhost:8000")
API_KEY = os.environ.get("MCX3D_API_KEY", "your_api_key_here")

HEADERS = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json",
}

def generate_report(report_type: str, period: int):
    """Generate a financial report."""
    url = f"{API_BASE_URL}/reports/generate"
    payload = {"report_type": report_type, "period_months": period}
    response = requests.post(url, headers=HEADERS, json=payload)
    response.raise_for_status()
    return response.json()

def run_dcf_valuation(assumptions: dict):
    """Run a DCF valuation."""
    url = f"{API_BASE_URL}/valuation/run-dcf"
    response = requests.post(url, headers=HEADERS, json=assumptions)
    response.raise_for_status()
    return response.json()

if __name__ == "__main__":
    # --- Example 1: Generate an Income Statement ---
    print("Generating Income Statement...")
    try:
        income_statement = generate_report("income_statement", 12)
        print("Income Statement:", json.dumps(income_statement, indent=2))
    except requests.exceptions.RequestException as e:
        print(f"Error generating income statement: {e}")

    # --- Example 2: Run a DCF Valuation ---
    print("\nRunning DCF Valuation...")
    dcf_assumptions = {
        "wacc": 0.12,
        "tax_rate": 0.25,
        "perpetual_growth_rate": 0.025,
    }
    try:
        dcf_result = run_dcf_valuation(dcf_assumptions)
        print("DCF Valuation Result:", json.dumps(dcf_result, indent=2))
    except requests.exceptions.RequestException as e:
        print(f"Error running DCF valuation: {e}")