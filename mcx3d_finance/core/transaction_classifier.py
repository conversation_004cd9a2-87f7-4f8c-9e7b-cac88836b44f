"""
Intelligent transaction categorization using rules-based classification for UK FRS 102.
"""

from typing import Dict, Optional, Any, List, Tuple
from decimal import Decimal
import logging
from enum import Enum
from dataclasses import dataclass
import re
from functools import lru_cache
from datetime import datetime

logger = logging.getLogger(__name__)


class UKTransactionCategory(Enum):
    """UK-compliant transaction categories for classification."""
    # Revenue Categories
    TURNOVER = "turnover"
    INTEREST_RECEIVABLE = "interest_receivable"
    OTHER_INCOME = "other_income"
    
    # Expense Categories
    COST_OF_SALES = "cost_of_sales"
    DISTRIBUTION_COSTS = "distribution_costs"
    ADMINISTRATIVE_EXPENSES = "administrative_expenses"
    INTEREST_PAYABLE = "interest_payable"
    TAXATION = "taxation"
    
    # Asset/Liability Categories
    DEBTORS = "debtors"
    CREDITORS = "creditors"
    STOCKS = "stocks"
    TANGIBLE_ASSETS_PURCHASE = "tangible_assets_purchase"
    LOAN_PAYMENT = "loan_payment"
    
    # Cash Flow Categories
    CUSTOMER_PAYMENT = "customer_payment"
    SUPPLIER_PAYMENT = "supplier_payment"
    BANK_TRANSFER = "bank_transfer"
    
    UNCATEGORIZED = "uncategorized"


class ConfidenceLevel(Enum):
    """Confidence levels for classification."""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class ClassificationRule:
    """Transaction classification rule."""
    category: UKTransactionCategory
    patterns: List[str]
    confidence_score: float = 1.0
    description: str = ""
    
    def matches(self, transaction: Dict[str, Any]) -> Tuple[bool, float]:
        """Check if this rule matches the transaction."""
        description = transaction.get("description", "").lower()
        if any(re.search(pattern, description, re.IGNORECASE) for pattern in self.patterns):
            return True, self.confidence_score
        return False, 0.0


@dataclass
class ClassificationResult:
    """Result of transaction classification."""
    transaction_id: str
    category: UKTransactionCategory
    confidence_score: float
    confidence_level: ConfidenceLevel
    rule_description: str


class TransactionClassifier:
    """Intelligent transaction classifier for UK financial reporting."""
    
    def __init__(self):
        self.classification_rules = self._load_classification_rules()
        self.custom_rules: List[ClassificationRule] = []
    
    def _load_classification_rules(self) -> List[ClassificationRule]:
        """Load comprehensive classification rules for UK standards."""
        rules = []
        rules.extend(self._get_revenue_rules())
        rules.extend(self._get_expense_rules())
        rules.extend(self._get_asset_liability_rules())
        return rules
    
    def _get_revenue_rules(self) -> List[ClassificationRule]:
        """Get revenue classification rules."""
        return [
            ClassificationRule(
                category=UKTransactionCategory.TURNOVER,
                patterns=[r"sale|customer payment|invoice"],
                description="Turnover from sales"
            ),
            ClassificationRule(
                category=UKTransactionCategory.INTEREST_RECEIVABLE,
                patterns=[r"interest received|bank interest"],
                description="Interest receivable"
            ),
        ]
    
    def _get_expense_rules(self) -> List[ClassificationRule]:
        """Get expense classification rules."""
        return [
            ClassificationRule(
                category=UKTransactionCategory.COST_OF_SALES,
                patterns=[r"cost of goods|direct cost"],
                description="Cost of sales"
            ),
            ClassificationRule(
                category=UKTransactionCategory.DISTRIBUTION_COSTS,
                patterns=[r"delivery|freight|postage"],
                description="Distribution costs"
            ),
            ClassificationRule(
                category=UKTransactionCategory.ADMINISTRATIVE_EXPENSES,
                patterns=[r"salary|rent|utilities|office supplies"],
                description="Administrative expenses"
            ),
            ClassificationRule(
                category=UKTransactionCategory.INTEREST_PAYABLE,
                patterns=[r"interest paid|loan interest"],
                description="Interest payable"
            ),
            ClassificationRule(
                category=UKTransactionCategory.TAXATION,
                patterns=[r"hmrc|vat|corporation tax"],
                description="Taxation"
            ),
        ]

    def _get_asset_liability_rules(self) -> List[ClassificationRule]:
        """Get asset/liability classification rules."""
        return [
            ClassificationRule(
                category=UKTransactionCategory.TANGIBLE_ASSETS_PURCHASE,
                patterns=[r"equipment|machinery|vehicle"],
                description="Purchase of tangible assets"
            ),
            ClassificationRule(
                category=UKTransactionCategory.LOAN_PAYMENT,
                patterns=[r"loan payment|mortgage payment"],
                description="Loan payments"
            ),
        ]

    @lru_cache(maxsize=1000)
    def classify_transaction(self, transaction_data: str) -> ClassificationResult:
        """Classify a single transaction."""
        import json
        transaction = json.loads(transaction_data)
        
        best_match = None
        best_score = 0.0

        for rule in self.custom_rules + self.classification_rules:
            matches, score = rule.matches(transaction)
            if matches and score > best_score:
                best_match = rule
                best_score = score

        if best_match:
            return ClassificationResult(
                transaction_id=transaction.get("id", "unknown"),
                category=best_match.category,
                confidence_score=best_score,
                confidence_level=self._get_confidence_level(best_score),
                rule_description=best_match.description
            )

        return ClassificationResult(
            transaction_id=transaction.get("id", "unknown"),
            category=UKTransactionCategory.UNCATEGORIZED,
            confidence_score=0.1,
            confidence_level=ConfidenceLevel.LOW,
            rule_description="No matching rule found"
        )

    def _get_confidence_level(self, score: float) -> ConfidenceLevel:
        """Convert numeric score to confidence level."""
        if score >= 0.8:
            return ConfidenceLevel.HIGH
        elif score >= 0.5:
            return ConfidenceLevel.MEDIUM
        else:
            return ConfidenceLevel.LOW

    def add_custom_rule(self, rule: ClassificationRule):
        """Add a custom classification rule."""
        self.custom_rules.append(rule)
        self.classify_transaction.cache_clear()
