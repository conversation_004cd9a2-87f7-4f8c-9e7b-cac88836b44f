#!/usr/bin/env python3
"""
Test Xero data import after OAuth authorization
"""

from dotenv import load_dotenv
import sys
import json
from mcx3d_finance.integrations.xero_client import XeroClient
from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization

def test_xero_connection():
    """Test if we can connect to Xero and fetch data"""
    
    load_dotenv()
    
    print("🚀 MCX3D Financials - Xero Data Import Test")
    print("=" * 50)
    
    # Check if we have any authorized organizations
    db = SessionLocal()
    try:
        organizations = db.query(Organization).filter(
            Organization.xero_token.isnot(None)
        ).all()
        
        if not organizations:
            print("❌ No authorized Xero organizations found.")
            print("📖 Please complete OAuth authorization first:")
            print("   python xero_auth_helper.py")
            return False
            
        print(f"✅ Found {len(organizations)} authorized organization(s)")
        
        for org in organizations:
            print(f"\n🏢 Testing Organization: {org.name} (ID: {org.id})")
            
            try:
                # Create Xero client
                xero_client = XeroClient(org.id)
                print("✅ Xero client initialized successfully")
                
                # Test 1: Get Chart of Accounts
                print("\n📊 Test 1: Fetching Chart of Accounts...")
                accounts = xero_client.get_chart_of_accounts()
                print(f"✅ Retrieved {len(accounts)} accounts")
                
                if accounts:
                    print("   Sample accounts:")
                    for account in accounts[:3]:
                        print(f"   - {account.get('account_name', 'N/A')} ({account.get('account_code', 'N/A')})")
                
                # Test 2: Get Contacts
                print("\n👥 Test 2: Fetching Contacts...")
                contacts = xero_client.get_contacts()
                print(f"✅ Retrieved {len(contacts)} contacts")
                
                if contacts:
                    print("   Sample contacts:")
                    for contact in contacts[:3]:
                        print(f"   - {contact.get('name', 'N/A')}")
                
                # Test 3: Get Recent Transactions
                print("\n💳 Test 3: Fetching Recent Bank Transactions...")
                from datetime import datetime, timedelta
                
                to_date = datetime.now()
                from_date = to_date - timedelta(days=30)  # Last 30 days
                
                transactions = xero_client.get_bank_transactions(from_date, to_date)
                print(f"✅ Retrieved {len(transactions)} transactions")
                
                if transactions:
                    print("   Sample transactions:")
                    for txn in transactions[:3]:
                        amount = txn.get('amount', 0)
                        ref = txn.get('reference', 'N/A')
                        print(f"   - {ref}: ${amount:,.2f}")
                
                print(f"\n🎉 Success! Organization '{org.name}' is ready for data import!")
                
                # Test 4: Test Financial Reports
                print("\n📈 Test 4: Fetching Trial Balance...")
                trial_balance = xero_client.get_trial_balance(datetime.now().date())
                accounts_in_tb = len(trial_balance.get('accounts', []))
                print(f"✅ Retrieved trial balance with {accounts_in_tb} account entries")
                
                return True
                
            except Exception as e:
                print(f"❌ Error testing organization {org.name}: {e}")
                continue
                
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False
    finally:
        db.close()

def create_test_organization():
    """Create a test organization for development"""
    
    load_dotenv()
    
    db = SessionLocal()
    try:
        # Check if test org already exists
        existing_org = db.query(Organization).filter(
            Organization.name == "Test Organization"
        ).first()
        
        if existing_org:
            print(f"✅ Test organization already exists (ID: {existing_org.id})")
            return existing_org.id
            
        # Create new test organization
        test_org = Organization(
            name="Test Organization",
            xero_tenant_id="test-tenant-id-placeholder",
            # Note: xero_token will be set during OAuth flow
        )
        
        db.add(test_org)
        db.commit()
        db.refresh(test_org)
        
        print(f"✅ Created test organization (ID: {test_org.id})")
        return test_org.id
        
    except Exception as e:
        print(f"❌ Error creating test organization: {e}")
        return None
    finally:
        db.close()

def main():
    """Main test function"""
    
    print("🔍 Checking Xero integration status...")
    
    # First create a test organization if needed
    org_id = create_test_organization()
    if not org_id:
        print("❌ Failed to create test organization")
        sys.exit(1)
    
    # Test the connection
    success = test_xero_connection()
    
    if success:
        print("\n🎉 ALL TESTS PASSED!")
        print("🚀 Your Xero integration is working perfectly!")
        print("\n📖 Next steps:")
        print("1. Run a full data sync: docker-compose exec web python -m mcx3d_finance.cli.main sync xero --org-id", org_id)
        print("2. Generate financial reports")
        print("3. Set up automated sync schedules")
    else:
        print("\n❌ TESTS FAILED")
        print("📖 Make sure you've completed OAuth authorization:")
        print("   python xero_auth_helper.py")

if __name__ == "__main__":
    main()