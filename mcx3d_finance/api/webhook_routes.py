import hashlib
import hmac
import base64
from fastapi import APIR<PERSON><PERSON>, Request, Header, HTTPException, Depends
from sqlalchemy.orm import Session
from mcx3d_finance.core.config import get_xero_webhook_key
from mcx3d_finance.db.session import get_db
from mcx3d_finance.db.models import Organization
from mcx3d_finance.tasks.sync_tasks import sync_xero_data

router = APIRouter()


def verify_signature(payload: bytes, signature: str, secret: str) -> bool:
    """
    Verify the Xero webhook signature.
    """
    computed_signature = base64.b64encode(
        hmac.new(secret.encode(), payload, hashlib.sha256).digest()
    ).decode()
    return hmac.compare_digest(computed_signature, signature)


@router.post("/webhooks/xero")
async def xero_webhook(
    request: Request,
    xero_signature: str = Header(None),
    db: Session = Depends(get_db),
):
    """
    Accepts POST requests from Xero and triggers the appropriate Celery task.
    """
    payload = await request.body()
    webhook_key = get_xero_webhook_key()

    if not webhook_key:
        raise HTTPException(status_code=500, detail="Xero webhook key not configured")

    if not verify_signature(payload, xero_signature, webhook_key):
        raise HTTPException(status_code=401, detail="Invalid signature")

    data = await request.json()
    for event in data.get("events", []):
        tenant_id = event.get("tenantId")
        if tenant_id:
            organization = (
                db.query(Organization)
                .filter(Organization.xero_tenant_id == tenant_id)
                .first()
            )
            if organization:
                sync_xero_data.delay(organization.id)

    return {"status": "ok"}
