#!/bin/bash

# Script to manage Docker secrets for MCX3D Financials production deployment
# This script helps create, update, and verify Docker secrets

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_info() { echo -e "ℹ️  $1"; }

# Function to generate secure random string
generate_secret() {
    local length=${1:-64}
    openssl rand -base64 $length | tr -d '\n=' | cut -c1-$length
}

# Function to generate secure password with special characters
generate_password() {
    local length=${1:-32}
    # Generate password with letters, numbers, and safe special characters
    python3 -c "
import secrets
import string
chars = string.ascii_letters + string.digits + '!@#%^&*-_=+'
print(''.join(secrets.choice(chars) for _ in range($length)))
"
}

# Function to create a Docker secret
create_secret() {
    local secret_name=$1
    local secret_value=$2
    
    # Check if secret already exists
    if docker secret ls --format "{{.Name}}" | grep -q "^${secret_name}$"; then
        print_warning "Secret '${secret_name}' already exists. Use 'update' command to modify it."
        return 1
    fi
    
    # Create the secret
    echo -n "$secret_value" | docker secret create "$secret_name" -
    print_success "Created secret: ${secret_name}"
}

# Function to update a Docker secret
update_secret() {
    local secret_name=$1
    local secret_value=$2
    
    # Remove old secret if exists
    if docker secret ls --format "{{.Name}}" | grep -q "^${secret_name}$"; then
        docker secret rm "$secret_name" >/dev/null
        print_info "Removed old secret: ${secret_name}"
    fi
    
    # Create new secret
    echo -n "$secret_value" | docker secret create "$secret_name" -
    print_success "Updated secret: ${secret_name}"
}

# Function to verify all required secrets exist
verify_secrets() {
    local required_secrets=(
        "mcx3d_db_password"
        "mcx3d_db_root_password"
        "mcx3d_secret_key"
        "mcx3d_encryption_key"
        "mcx3d_xero_client_secret"
        "mcx3d_smtp_password"
        "mcx3d_redis_password"
    )
    
    local missing_secrets=()
    
    print_info "Verifying Docker secrets..."
    
    for secret in "${required_secrets[@]}"; do
        if docker secret ls --format "{{.Name}}" | grep -q "^${secret}$"; then
            print_success "Found: ${secret}"
        else
            print_error "Missing: ${secret}"
            missing_secrets+=("$secret")
        fi
    done
    
    if [ ${#missing_secrets[@]} -eq 0 ]; then
        print_success "All required secrets are present!"
        return 0
    else
        print_error "${#missing_secrets[@]} secrets are missing"
        return 1
    fi
}

# Function to initialize all secrets
init_secrets() {
    print_info "Initializing production secrets for MCX3D Financials..."
    
    # Check if running in Swarm mode
    if ! docker info 2>/dev/null | grep -q "Swarm: active"; then
        print_error "Docker is not in Swarm mode. Run 'docker swarm init' first."
        exit 1
    fi
    
    # Generate secrets
    local db_password=$(generate_password 32)
    local db_root_password=$(generate_password 32)
    local secret_key=$(generate_password 64)
    local encryption_key=$(python3 -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())")
    local redis_password=$(generate_password 32)
    
    # Create secrets
    create_secret "mcx3d_db_password" "$db_password"
    create_secret "mcx3d_db_root_password" "$db_root_password"
    create_secret "mcx3d_secret_key" "$secret_key"
    create_secret "mcx3d_encryption_key" "$encryption_key"
    create_secret "mcx3d_redis_password" "$redis_password"
    
    # Placeholder for secrets that need to be manually set
    print_warning "The following secrets need to be set manually with actual values:"
    print_info "  - mcx3d_xero_client_secret (from Xero app settings)"
    print_info "  - mcx3d_smtp_password (from email provider)"
    
    # Create placeholder secrets
    create_secret "mcx3d_xero_client_secret" "PLACEHOLDER_REPLACE_WITH_ACTUAL_SECRET"
    create_secret "mcx3d_smtp_password" "PLACEHOLDER_REPLACE_WITH_ACTUAL_SECRET"
    
    print_success "Secret initialization complete!"
    print_info "Save the following information securely:"
    echo ""
    echo "Database Password: $db_password"
    echo "Database Root Password: $db_root_password"
    echo "Secret Key: $secret_key"
    echo "Encryption Key: $encryption_key"
    echo "Redis Password: $redis_password"
    echo ""
    print_warning "Replace placeholder secrets with actual values before deploying!"
}

# Function to backup secrets
backup_secrets() {
    local backup_dir="./secrets_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    print_info "Backing up secrets to $backup_dir ..."
    
    # Note: Docker secrets cannot be read back, so this creates a template
    cat > "$backup_dir/README.md" << EOF
# MCX3D Financials Secrets Backup

Created: $(date)

## Important Notes

Docker secrets cannot be exported once created. This backup contains:
1. A list of secret names that exist in your Docker Swarm
2. Instructions for recreating them if needed

## Existing Secrets

$(docker secret ls --format "table {{.Name}}\t{{.CreatedAt}}")

## To Restore Secrets

Use the manage_secrets.sh script with the 'init' command to create new secrets,
then update with your actual values using:

\`\`\`bash
./manage_secrets.sh update <secret_name> <secret_value>
\`\`\`

## Required Secrets

- mcx3d_db_password
- mcx3d_db_root_password  
- mcx3d_secret_key
- mcx3d_encryption_key
- mcx3d_xero_client_secret
- mcx3d_smtp_password
- mcx3d_redis_password
EOF

    print_success "Backup information saved to $backup_dir/README.md"
}

# Function to show usage
usage() {
    echo "MCX3D Financials - Docker Secrets Management"
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  init                    Initialize all required secrets with generated values"
    echo "  verify                  Verify all required secrets exist"
    echo "  create <name> <value>   Create a new secret"
    echo "  update <name> <value>   Update an existing secret"
    echo "  backup                  Create backup documentation for secrets"
    echo "  help                    Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 init"
    echo "  $0 verify"
    echo "  $0 update mcx3d_xero_client_secret 'your-actual-secret'"
    echo ""
}

# Main script logic
case "${1:-help}" in
    init)
        init_secrets
        ;;
    verify)
        verify_secrets
        ;;
    create)
        if [ $# -lt 3 ]; then
            print_error "Usage: $0 create <name> <value>"
            exit 1
        fi
        create_secret "$2" "$3"
        ;;
    update)
        if [ $# -lt 3 ]; then
            print_error "Usage: $0 update <name> <value>"
            exit 1
        fi
        update_secret "$2" "$3"
        ;;
    backup)
        backup_secrets
        ;;
    help|--help|-h)
        usage
        ;;
    *)
        print_error "Unknown command: $1"
        usage
        exit 1
        ;;
esac