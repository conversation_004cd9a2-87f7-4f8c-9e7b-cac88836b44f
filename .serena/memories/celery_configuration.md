# MCX3D Finance Celery Configuration

## Current Celery Setup

### Celery Application (`mcx3d_finance/tasks/celery_app.py`)
```python
celery_app = Celery(
    "mcx3d_finance",
    broker=REDIS_URL,
    backend=REDIS_URL,
    include=["mcx3d_finance.tasks.example", "mcx3d_finance.tasks.calculation"],
)
```

### Configuration Settings
- **Broker**: Redis (`redis://localhost:6379/0`)
- **Backend**: Redis (same as broker)
- **Serialization**: JSON for tasks, results, and content
- **Timezone**: UTC with UTC enabled
- **Task Modules**: `example`, `calculation`

### Current Task Modules
1. **example.py**: Example/template tasks
2. **calculation.py**: Financial calculation tasks  
3. **sync_tasks.py**: Data synchronization tasks

### Integration with Monitoring
- **Health Checks**: Worker process monitoring via `psutil`
- **Metrics**: Task execution metrics via Prometheus
- **Performance**: Task duration and success rate tracking
- **Queue Monitoring**: Redis-based queue length monitoring

### Docker Integration
- Celery workers run as separate containers
- Redis container serves as message broker
- Shared codebase and configuration with web application
- Environment variable configuration for Redis URL

### Configuration Patterns
- Environment-specific Redis URLs
- Configurable worker concurrency
- Task routing and queue management
- Error handling and retry policies

### Monitoring Integration
- Worker health checks in `HealthChecker`
- Process detection via command line inspection
- Queue length monitoring through Redis
- Failed task tracking and alerting

### Production Considerations
- **Scaling**: Multiple worker containers
- **Monitoring**: Process supervision and health checks  
- **Error Handling**: Dead letter queues and retry policies
- **Performance**: Worker pool optimization
- **Security**: Redis authentication and SSL connections

### Task Organization
- **Calculation Tasks**: Financial computations and analysis
- **Sync Tasks**: Data synchronization with external services
- **Report Tasks**: Background report generation
- **Maintenance Tasks**: Cleanup and housekeeping operations