"""
Tests for CLI period parsing functionality.
"""
import pytest
from datetime import datetime
from mcx3d_finance.cli.reports import parse_period, parse_date


class TestParsePeriod:
    """Test the parse_period function with various period formats."""

    def test_quarterly_periods(self):
        """Test quarterly period parsing."""
        # Q1: Jan-Mar
        start, end = parse_period("2024-Q1")
        assert start == "2024-01-01"
        assert end == "2024-03-31"
        
        # Q2: Apr-Jun
        start, end = parse_period("2024-Q2")
        assert start == "2024-04-01"
        assert end == "2024-06-30"
        
        # Q3: Jul-Sep
        start, end = parse_period("2024-Q3")
        assert start == "2024-07-01"
        assert end == "2024-09-30"
        
        # Q4: Oct-Dec
        start, end = parse_period("2024-Q4")
        assert start == "2024-10-01"
        assert end == "2024-12-31"

    def test_monthly_periods(self):
        """Test monthly period parsing."""
        # January
        start, end = parse_period("2024-01")
        assert start == "2024-01-01"
        assert end == "2024-01-31"
        
        # February (leap year)
        start, end = parse_period("2024-02")
        assert start == "2024-02-01"
        assert end == "2024-02-29"
        
        # February (non-leap year)
        start, end = parse_period("2023-02")
        assert start == "2023-02-01"
        assert end == "2023-02-28"
        
        # April (30 days)
        start, end = parse_period("2024-04")
        assert start == "2024-04-01"
        assert end == "2024-04-30"
        
        # December
        start, end = parse_period("2024-12")
        assert start == "2024-12-01"
        assert end == "2024-12-31"

    def test_yearly_periods(self):
        """Test yearly period parsing."""
        start, end = parse_period("2024")
        assert start == "2024-01-01"
        assert end == "2024-12-31"
        
        start, end = parse_period("2023")
        assert start == "2023-01-01"
        assert end == "2023-12-31"

    def test_invalid_periods(self):
        """Test error handling for invalid period formats."""
        # Invalid quarter
        start, end = parse_period("2024-Q5")
        assert start is None
        assert end is None
        
        # Invalid month
        start, end = parse_period("2024-13")
        assert start is None
        assert end is None
        
        start, end = parse_period("2024-00")
        assert start is None
        assert end is None
        
        # Invalid format
        start, end = parse_period("invalid")
        assert start is None
        assert end is None
        
        # Invalid year format
        start, end = parse_period("24")
        assert start is None
        assert end is None

    def test_edge_cases(self):
        """Test edge cases in period parsing."""
        # Valid ranges
        assert parse_period("2000-Q1")[0] == "2000-01-01"
        assert parse_period("2099-Q4")[1] == "2099-12-31"
        
        # Different year formats should fail
        start, end = parse_period("24-Q1")
        assert start is None
        assert end is None


class TestParseDate:
    """Test the parse_date function for balance sheet dates."""

    def test_valid_dates(self):
        """Test valid date parsing."""
        start, end = parse_date("2024-03-31")
        assert start == "2024-03-31 00:00:00"
        assert end == "2024-03-31 23:59:59"
        
        start, end = parse_date("2024-12-31")
        assert start == "2024-12-31 00:00:00"
        assert end == "2024-12-31 23:59:59"

    def test_invalid_dates(self):
        """Test error handling for invalid dates."""
        # Invalid format
        start, end = parse_date("2024-3-31")
        assert start is None
        assert end is None
        
        # Invalid date
        start, end = parse_date("2024-02-30")
        assert start is None
        assert end is None
        
        # Invalid month
        start, end = parse_date("2024-13-01")
        assert start is None
        assert end is None
        
        # Wrong format
        start, end = parse_date("03/31/2024")
        assert start is None
        assert end is None


class TestIntegration:
    """Integration tests for period parsing."""

    def test_period_consistency(self):
        """Test that periods are parsed consistently."""
        # Ensure quarterly and monthly parsing don't overlap incorrectly
        q1_start, q1_end = parse_period("2024-Q1")
        jan_start, jan_end = parse_period("2024-01")
        mar_start, mar_end = parse_period("2024-03")
        
        assert q1_start == jan_start  # Q1 starts same as January
        assert q1_end == mar_end      # Q1 ends same as March
        
        # Ensure yearly covers all months
        year_start, year_end = parse_period("2024")
        jan_start, _ = parse_period("2024-01")
        _, dec_end = parse_period("2024-12")
        
        assert year_start == jan_start
        assert year_end == dec_end

    def test_leap_year_handling(self):
        """Test that leap years are handled correctly."""
        # Leap year
        _, feb_end_2024 = parse_period("2024-02")
        assert feb_end_2024 == "2024-02-29"
        
        # Non-leap year
        _, feb_end_2023 = parse_period("2023-02")
        assert feb_end_2023 == "2023-02-28"
        
        # Century year (non-leap)
        _, feb_end_1900 = parse_period("1900-02")
        assert feb_end_1900 == "1900-02-28"
        
        # Century year (leap)  
        _, feb_end_2000 = parse_period("2000-02")
        assert feb_end_2000 == "2000-02-29"