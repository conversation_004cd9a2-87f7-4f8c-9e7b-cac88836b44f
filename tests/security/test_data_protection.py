"""
Tests for the DataProtection utility.
"""
import pytest
from mcx3d_finance.utils.data_protection import (
    DataProtection,
    EncryptionLevel,
    DataClassification,
)

class TestDataProtection:
    """
    Test suite for the DataProtection service.
    """

    def test_encryption_decryption(self, data_protection: DataProtection):
        """
        Test that data is correctly encrypted and decrypted at all levels.
        """
        test_data = "This is a secret message."

        # Test standard encryption
        encrypted_standard = data_protection.encrypt_field(
            test_data, level=EncryptionLevel.STANDARD
        )
        decrypted_standard = data_protection.decrypt_field(encrypted_standard)
        assert decrypted_standard == test_data

        # Test high encryption
        encrypted_high = data_protection.encrypt_field(
            test_data, level=EncryptionLevel.HIGH
        )
        decrypted_high = data_protection.decrypt_field(encrypted_high)
        assert decrypted_high == test_data

        # Test maximum encryption
        encrypted_maximum = data_protection.encrypt_field(
            test_data, level=EncryptionLevel.MAXIMUM
        )
        decrypted_maximum = data_protection.decrypt_field(encrypted_maximum)
        assert decrypted_maximum == test_data

    def test_key_rotation(self, data_protection: DataProtection):
        """
        Test that data encrypted with an old key can still be decrypted
        after a key rotation.
        """
        test_data = "This is some very secret data."
        encrypted_data = data_protection.encrypt_field(
            test_data, level=EncryptionLevel.HIGH
        )

        # Rotate the key
        data_protection.rotate_encryption_key("a_new_secret_key")

        # The old data should still be decryptable
        decrypted_data = data_protection.decrypt_field(encrypted_data)
        assert decrypted_data == test_data

        # New data should be encrypted with the new key
        new_encrypted_data = data_protection.encrypt_field(
            test_data, level=EncryptionLevel.HIGH
        )
        assert new_encrypted_data.key_version == 1

    def test_data_masking(self, data_protection: DataProtection):
        """
        Test all data masking types.
        """
        assert data_protection.mask_data("1234567890", mask_type="partial") == "1234**7890"
        assert data_protection.mask_data("1234567890", mask_type="full") == "**********"
        assert (
            data_protection.mask_data("<EMAIL>", mask_type="email")
            == "t**<EMAIL>"
        )
        assert (
            data_protection.mask_data("**************", mask_type="phone")
            == "1-8******1234"
        )

    def test_tokenization(self, data_protection: DataProtection):
        """
        Test that tokenization produces a token.
        """
        token = data_protection.tokenize("sensitive_value")
        assert token.startswith("tok_")

    def test_secure_delete(self, data_protection: DataProtection):
        """
        Test that the secure deletion function works.
        """
        sensitive_data = {"secret": "data"}
        assert data_protection.secure_delete(sensitive_data)
        assert not sensitive_data
