"""
Comprehensive tests for Xero OAuth2 authentication manager.
"""
import pytest
import json
import base64
from datetime import datetime, timedelta, timezone
from unittest.mock import Mock, patch, MagicMock
from urllib.parse import urlparse, parse_qs

from mcx3d_finance.auth.xero_oauth import XeroAuthManager
from mcx3d_finance.db.models import Organization


class TestXeroAuthManager:
    """Test suite for XeroAuthManager."""

    @pytest.fixture
    def auth_manager(self):
        """Create XeroAuthManager instance."""
        with patch('mcx3d_finance.auth.xero_oauth.get_xero_config') as mock_config:
            mock_config.return_value = {
                'client_id': 'test_client_id',
                'client_secret': 'test_client_secret',
                'redirect_uri': 'http://localhost:8000/callback',
                'scopes': 'accounting.transactions'
            }
            
            with patch('mcx3d_finance.auth.xero_oauth.get_redis_client') as mock_redis:
                mock_redis.return_value = Mock()
                yield XeroAuthManager()

    @pytest.fixture
    def mock_organization(self, db_session):
        """Create a test organization."""
        org = Organization(
            id=1,
            name="Test Organization",
            xero_tenant_id="test-tenant-id",
            xero_token=None,
            created_at=datetime.now(timezone.utc)
        )
        db_session.add(org)
        db_session.commit()
        return org

    def test_generate_auth_url_success(self, auth_manager):
        """Test successful authorization URL generation."""
        # Mock OAuth2 initialization
        auth_manager.api_client.oauth2 = Mock()
        auth_manager.api_client.oauth2.well_known_open_id_configuration = Mock()
        auth_manager.api_client.oauth2.authorization_url = Mock(
            return_value="https://login.xero.com/identity/connect/authorize?client_id=test"
        )
        
        # Generate URL
        auth_url = auth_manager.generate_auth_url()
        
        # Assertions
        assert auth_url is not None
        assert "authorize" in auth_url
        assert auth_manager.redis_client.set.called
        
        # Verify state was stored in Redis
        call_args = auth_manager.redis_client.set.call_args
        assert call_args[0][0].startswith("xero_oauth_state:")
        assert call_args[1]["ex"] == 600  # 10 minute expiration

    def test_generate_auth_url_with_custom_state(self, auth_manager):
        """Test authorization URL generation with custom state."""
        custom_state = "custom_state_123"
        
        auth_manager.api_client.oauth2 = Mock()
        auth_manager.api_client.oauth2.well_known_open_id_configuration = Mock()
        auth_manager.api_client.oauth2.authorization_url = Mock(
            return_value=f"https://login.xero.com/authorize?state={custom_state}"
        )
        
        auth_url = auth_manager.generate_auth_url(state=custom_state)
        
        assert auth_url is not None
        assert custom_state in auth_url
        auth_manager.redis_client.set.assert_called_once()

    def test_generate_auth_url_error(self, auth_manager):
        """Test authorization URL generation error handling."""
        auth_manager.api_client.oauth2 = Mock()
        auth_manager.api_client.oauth2.well_known_open_id_configuration = Mock(
            side_effect=Exception("OAuth config error")
        )
        
        auth_url = auth_manager.generate_auth_url()
        
        assert auth_url is None

    def test_handle_callback_success(self, auth_manager, mock_organization, db_session):
        """Test successful OAuth callback handling."""
        # Setup
        state = "test_state"
        auth_manager.redis_client.get.return_value = b"valid"
        
        # Mock OAuth token exchange
        token = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "expires_in": 1800,
            "token_type": "Bearer"
        }
        auth_manager.api_client.oauth2 = Mock()
        auth_manager.api_client.oauth2.fetch_token = Mock(return_value=token)
        
        # Mock tenant info
        connection = Mock()
        connection.tenant_id = "test-tenant-id"
        connection.tenant_name = "Test Organization"
        connection.tenant_type = "ORGANISATION"
        connection.creation_date_utc = datetime.now(timezone.utc)
        connection.auth_event_id = "test-auth-event"
        
        with patch('mcx3d_finance.auth.xero_oauth.IdentityApi') as mock_identity:
            mock_api = Mock()
            mock_api.get_connections.return_value = [connection]
            mock_identity.return_value = mock_api
            
            # Execute callback
            callback_url = f"http://localhost:8000/callback?code=test_code&state={state}"
            result = auth_manager.handle_callback(callback_url)
        
        # Assertions
        assert result["success"] is True
        assert result["organization_id"] == mock_organization.id
        assert result["organization_name"] == "Test Organization"
        assert result["tenant_id"] == "test-tenant-id"
        
        # Verify Redis cleanup
        auth_manager.redis_client.delete.assert_called_with(f"xero_oauth_state:{state}")
        
        # Verify token was stored
        db_session.refresh(mock_organization)
        assert mock_organization.xero_token is not None

    def test_handle_callback_invalid_state(self, auth_manager):
        """Test callback handling with invalid CSRF state."""
        state = "invalid_state"
        auth_manager.redis_client.get.return_value = None
        
        callback_url = f"http://localhost:8000/callback?code=test_code&state={state}"
        result = auth_manager.handle_callback(callback_url)
        
        assert result["success"] is False
        assert "Invalid state parameter" in result["error"]

    def test_handle_callback_authorization_error(self, auth_manager):
        """Test callback handling with authorization error."""
        state = "test_state"
        auth_manager.redis_client.get.return_value = b"valid"
        
        callback_url = f"http://localhost:8000/callback?error=access_denied&error_description=User+denied+access&state={state}"
        result = auth_manager.handle_callback(callback_url)
        
        assert result["success"] is False
        assert "User denied access" in result["error"]

    def test_handle_callback_token_exchange_error(self, auth_manager):
        """Test callback handling with token exchange error."""
        state = "test_state"
        auth_manager.redis_client.get.return_value = b"valid"
        
        auth_manager.api_client.oauth2 = Mock()
        auth_manager.api_client.oauth2.fetch_token = Mock(
            side_effect=Exception("Token exchange failed")
        )
        
        callback_url = f"http://localhost:8000/callback?code=test_code&state={state}"
        result = auth_manager.handle_callback(callback_url)
        
        assert result["success"] is False
        assert "Token exchange failed" in result["error"]

    def test_get_valid_token_no_refresh_needed(self, auth_manager, mock_organization, db_session):
        """Test getting valid token when no refresh is needed."""
        # Setup token that doesn't need refresh
        token = {
            "access_token": "valid_token",
            "refresh_token": "refresh_token",
            "expires_in": 3600
        }
        
        with patch.object(auth_manager, '_decrypt_token', return_value=token):
            mock_organization.xero_token = "encrypted_token"
            mock_organization.token_expires_at = datetime.now(timezone.utc) + timedelta(hours=1)
            db_session.commit()
            
            result = auth_manager.get_valid_token(mock_organization.id)
        
        assert result == token

    def test_get_valid_token_with_refresh(self, auth_manager, mock_organization, db_session):
        """Test getting valid token with automatic refresh."""
        # Setup expired token
        old_token = {
            "access_token": "old_token",
            "refresh_token": "refresh_token",
            "expires_in": 1800
        }
        new_token = {
            "access_token": "new_token",
            "refresh_token": "new_refresh_token",
            "expires_in": 1800
        }
        
        with patch.object(auth_manager, '_decrypt_token', return_value=old_token):
            with patch.object(auth_manager, '_refresh_token', return_value=new_token):
                mock_organization.xero_token = "encrypted_token"
                mock_organization.token_expires_at = datetime.now(timezone.utc) - timedelta(minutes=1)
                db_session.commit()
                
                result = auth_manager.get_valid_token(mock_organization.id)
        
        assert result == new_token

    def test_get_valid_token_no_organization(self, auth_manager):
        """Test getting token for non-existent organization."""
        result = auth_manager.get_valid_token(999)
        assert result is None

    def test_token_encryption_decryption(self, auth_manager):
        """Test token encryption and decryption."""
        token = {
            "access_token": "test_token",
            "refresh_token": "refresh_token",
            "expires_in": 1800
        }
        
        # Test with proper encryption
        with patch('mcx3d_finance.auth.xero_oauth.get_token_encryption') as mock_encryption:
            mock_encryptor = Mock()
            mock_encryptor.encrypt_token.return_value = "encrypted_data"
            mock_encryptor.decrypt_token.return_value = token
            mock_encryption.return_value = mock_encryptor
            
            encrypted = auth_manager._encrypt_token(token)
            assert encrypted == "encrypted_data"
            
            decrypted = auth_manager._decrypt_token(encrypted)
            assert decrypted == token

    def test_token_encryption_fallback(self, auth_manager):
        """Test token encryption fallback to base64."""
        token = {
            "access_token": "test_token",
            "refresh_token": "refresh_token",
            "expires_in": 1800
        }
        
        # Test fallback when encryption fails
        with patch('mcx3d_finance.auth.xero_oauth.get_token_encryption') as mock_encryption:
            mock_encryption.side_effect = Exception("Encryption not available")
            
            encrypted = auth_manager._encrypt_token(token)
            
            # Should fallback to base64
            expected = base64.b64encode(json.dumps(token).encode()).decode()
            assert encrypted == expected

    def test_refresh_token_success(self, auth_manager, mock_organization, db_session):
        """Test successful token refresh."""
        old_token = {
            "access_token": "old_token",
            "refresh_token": "refresh_token"
        }
        new_token = {
            "access_token": "new_token",
            "refresh_token": "new_refresh_token",
            "expires_in": 1800
        }
        
        auth_manager.api_client.refresh_oauth2_token = Mock(return_value=new_token)
        
        with patch.object(auth_manager, '_encrypt_token', return_value="encrypted_new_token"):
            result = auth_manager._refresh_token(old_token, mock_organization, db_session)
        
        assert result == new_token
        assert mock_organization.xero_token == "encrypted_new_token"
        assert mock_organization.token_expires_at > datetime.now(timezone.utc)

    def test_refresh_token_error(self, auth_manager, mock_organization, db_session):
        """Test token refresh error handling."""
        old_token = {
            "access_token": "old_token",
            "refresh_token": "refresh_token"
        }
        
        auth_manager.api_client.refresh_oauth2_token = Mock(
            side_effect=Exception("Refresh failed")
        )
        
        with pytest.raises(Exception) as exc_info:
            auth_manager._refresh_token(old_token, mock_organization, db_session)
        
        assert "Refresh failed" in str(exc_info.value)

    def test_revoke_token_success(self, auth_manager, mock_organization, db_session):
        """Test successful token revocation."""
        token = {"access_token": "token_to_revoke"}
        
        with patch.object(auth_manager, '_decrypt_token', return_value=token):
            mock_organization.xero_token = "encrypted_token"
            db_session.commit()
            
            result = auth_manager.revoke_token(mock_organization.id)
        
        assert result is True
        db_session.refresh(mock_organization)
        assert mock_organization.xero_token is None
        assert mock_organization.token_expires_at is None

    def test_revoke_token_no_token(self, auth_manager, mock_organization, db_session):
        """Test revoking token when no token exists."""
        mock_organization.xero_token = None
        db_session.commit()
        
        result = auth_manager.revoke_token(mock_organization.id)
        assert result is False

    def test_get_tenant_info_success(self, auth_manager):
        """Test successful tenant info retrieval."""
        token = {"access_token": "test_token"}
        
        connection = Mock()
        connection.tenant_id = "tenant-123"
        connection.tenant_name = "Test Tenant"
        connection.tenant_type = "ORGANISATION"
        connection.creation_date_utc = datetime.now(timezone.utc)
        connection.auth_event_id = "auth-123"
        
        with patch('mcx3d_finance.auth.xero_oauth.IdentityApi') as mock_identity:
            mock_api = Mock()
            mock_api.get_connections.return_value = [connection]
            mock_identity.return_value = mock_api
            
            result = auth_manager._get_tenant_info(token)
        
        assert result["tenant_id"] == "tenant-123"
        assert result["tenant_name"] == "Test Tenant"
        assert result["tenant_type"] == "ORGANISATION"

    def test_get_tenant_info_no_connections(self, auth_manager):
        """Test tenant info retrieval with no connections."""
        token = {"access_token": "test_token"}
        
        with patch('mcx3d_finance.auth.xero_oauth.IdentityApi') as mock_identity:
            mock_api = Mock()
            mock_api.get_connections.return_value = []
            mock_identity.return_value = mock_api
            
            result = auth_manager._get_tenant_info(token)
        
        assert result is None

    def test_store_organization_token_new_org(self, auth_manager, db_session):
        """Test storing token for new organization."""
        token = {"access_token": "new_token"}
        tenant_info = {
            "tenant_id": "new-tenant",
            "tenant_name": "New Organization",
            "tenant_type": "ORGANISATION"
        }
        
        with patch.object(auth_manager, '_encrypt_token', return_value="encrypted_token"):
            org = auth_manager._store_organization_token(token, tenant_info, db_session)
        
        assert org.name == "New Organization"
        assert org.xero_tenant_id == "new-tenant"
        assert org.xero_token == "encrypted_token"
        
        # Verify it was saved
        saved_org = db_session.query(Organization).filter_by(
            xero_tenant_id="new-tenant"
        ).first()
        assert saved_org is not None

    def test_store_organization_token_existing_org(self, auth_manager, mock_organization, db_session):
        """Test updating token for existing organization."""
        token = {"access_token": "updated_token"}
        tenant_info = {
            "tenant_id": mock_organization.xero_tenant_id,
            "tenant_name": "Updated Name",
            "tenant_type": "ORGANISATION"
        }
        
        with patch.object(auth_manager, '_encrypt_token', return_value="new_encrypted_token"):
            org = auth_manager._store_organization_token(token, tenant_info, db_session)
        
        assert org.id == mock_organization.id
        assert org.name == "Updated Name"
        assert org.xero_token == "new_encrypted_token"