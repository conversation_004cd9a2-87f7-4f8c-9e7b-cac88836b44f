"""
Security test fixtures for MCX3D Finance application.

This module provides common fixtures for security testing, including mocked
dependencies and test utilities.
"""
import os
import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from typing import Dict, Any

from mcx3d_finance.utils.session_manager import SessionManager
from mcx3d_finance.utils.rate_limiter import RateLimiter
from mcx3d_finance.utils.input_validator import InputValidator
from mcx3d_finance.utils.audit_logger import AuditLogger
from mcx3d_finance.utils.data_protection import DataProtection
from mcx3d_finance.core.config import get_security_config


# Set test environment variables if not already set
os.environ.setdefault("SECRET_KEY", "test-secret-key-minimum-32-characters-long-for-testing")
os.environ.setdefault("DATABASE_URL", "postgresql://test:test@localhost:5432/testdb")
os.environ.setdefault("ENCRYPTION_KEY", "gAAAAABhZ0123456789012345678901234567890123456789012345678901234567890")


@pytest.fixture
def mock_redis():
    """
    Enhanced Redis mock for testing without actual Redis server.
    
    Features:
    - Persistent data storage across operations
    - Proper TTL handling with background expiration
    - Transaction/pipeline support
    - Full data type support (strings, sets, sorted sets, hashes)
    - Session state continuity for refresh token operations
    """
    from threading import Lock, Timer
    import threading
    import json
    
    class EnhancedRedisMock:
        def __init__(self):
            self.data = {}
            self.ttl_data = {}
            self.sets = {}
            self.zsets = {}
            self.hashes = {}
            self.lock = Lock()
            self.expiration_timers = {}
            
        def _expire_key(self, key):
            """Background expiration of keys."""
            with self.lock:
                if key in self.data:
                    del self.data[key]
                if key in self.ttl_data:
                    del self.ttl_data[key]
                if key in self.sets:
                    del self.sets[key]
                if key in self.zsets:
                    del self.zsets[key]
                if key in self.hashes:
                    del self.hashes[key]
                if key in self.expiration_timers:
                    del self.expiration_timers[key]
        
        def _set_expiration(self, key, seconds):
            """Set up automatic expiration for a key."""
            # Cancel existing timer if any
            if key in self.expiration_timers:
                self.expiration_timers[key].cancel()
            
            # Set new timer
            if seconds > 0:
                timer = Timer(seconds, self._expire_key, [key])
                timer.start()
                self.expiration_timers[key] = timer
                self.ttl_data[key] = datetime.utcnow() + timedelta(seconds=seconds)
        
        def get(self, key):
            """Get value by key with proper expiration check."""
            with self.lock:
                # Check if key has expired
                if key in self.ttl_data and self.ttl_data[key] < datetime.utcnow():
                    self._expire_key(key)
                    return None
                return self.data.get(key)
        
        def set(self, key, value, ex=None, px=None, nx=False, xx=False):
            """Set key-value with comprehensive options."""
            with self.lock:
                # Handle nx (only if not exists) and xx (only if exists) flags
                if nx and key in self.data:
                    return False
                if xx and key not in self.data:
                    return False
                
                # Store the value (handle different data types properly)
                if isinstance(value, (dict, list)):
                    self.data[key] = json.dumps(value)
                else:
                    self.data[key] = str(value) if value is not None else None
                
                # Handle expiration
                if ex is not None:
                    self._set_expiration(key, ex)
                elif px is not None:
                    self._set_expiration(key, px / 1000.0)
                
                return True
        
        def delete(self, *keys):
            """Delete multiple keys and return count of deleted keys."""
            with self.lock:
                deleted = 0
                for key in keys:
                    if key in self.data:
                        # Cancel expiration timer
                        if key in self.expiration_timers:
                            self.expiration_timers[key].cancel()
                            del self.expiration_timers[key]
                        
                        # Remove from all data structures
                        if key in self.data:
                            del self.data[key]
                        if key in self.ttl_data:
                            del self.ttl_data[key]
                        if key in self.sets:
                            del self.sets[key]
                        if key in self.zsets:
                            del self.zsets[key]
                        if key in self.hashes:
                            del self.hashes[key]
                        deleted += 1
                return deleted
        
        def exists(self, key):
            """Check if key exists (with expiration check)."""
            with self.lock:
                if key in self.ttl_data and self.ttl_data[key] < datetime.utcnow():
                    self._expire_key(key)
                return 1 if key in self.data else 0
        
        def incr(self, key, amount=1):
            """Increment key value."""
            with self.lock:
                if key not in self.data:
                    self.data[key] = "0"
                try:
                    current_value = int(self.data[key])
                    self.data[key] = str(current_value + amount)
                    return current_value + amount
                except ValueError:
                    raise ValueError("value is not an integer or out of range")
        
        def expire(self, key, seconds):
            """Set expiration for existing key."""
            with self.lock:
                if key in self.data:
                    self._set_expiration(key, seconds)
                    return True
                return False
        
        def ttl(self, key):
            """Get time to live for key."""
            with self.lock:
                if key not in self.data:
                    return -2  # Key doesn't exist
                if key not in self.ttl_data:
                    return -1  # Key exists but has no expiration
                
                remaining = (self.ttl_data[key] - datetime.utcnow()).total_seconds()
                return max(0, int(remaining))
        
        def keys(self, pattern="*"):
            """Get keys matching pattern."""
            import fnmatch
            with self.lock:
                return [k for k in self.data.keys() if fnmatch.fnmatch(k, pattern)]
        
        def scan_iter(self, match=None, count=None):
            """Iterate over keys."""
            keys = self.keys(match if match else "*")
            for key in keys:
                yield key
        
        # Set operations
        def sadd(self, key, *values):
            """Add members to set."""
            with self.lock:
                if key not in self.sets:
                    self.sets[key] = set()
                before_count = len(self.sets[key])
                self.sets[key].update(str(v) for v in values)
                return len(self.sets[key]) - before_count
        
        def smembers(self, key):
            """Get all members of set."""
            with self.lock:
                return self.sets.get(key, set())
        
        def srem(self, key, *values):
            """Remove members from set."""
            with self.lock:
                if key in self.sets:
                    str_values = {str(v) for v in values}
                    before_count = len(self.sets[key])
                    self.sets[key] -= str_values
                    return before_count - len(self.sets[key])
                return 0
        
        def scard(self, key):
            """Get set cardinality."""
            with self.lock:
                return len(self.sets.get(key, set()))
        
        # Sorted set operations
        def zadd(self, key, mapping, nx=False, xx=False):
            """Add members to sorted set."""
            with self.lock:
                if key not in self.zsets:
                    self.zsets[key] = {}
                
                added = 0
                for member, score in mapping.items():
                    member_str = str(member)
                    if nx and member_str in self.zsets[key]:
                        continue
                    if xx and member_str not in self.zsets[key]:
                        continue
                    
                    if member_str not in self.zsets[key]:
                        added += 1
                    self.zsets[key][member_str] = float(score)
                
                return added
        
        def zremrangebyscore(self, key, min_score, max_score):
            """Remove members by score range."""
            with self.lock:
                if key not in self.zsets:
                    return 0
                
                to_remove = []
                for member, score in self.zsets[key].items():
                    if min_score <= score <= max_score:
                        to_remove.append(member)
                
                for member in to_remove:
                    del self.zsets[key][member]
                
                return len(to_remove)
        
        def zcard(self, key):
            """Get sorted set cardinality."""
            with self.lock:
                return len(self.zsets.get(key, {}))
        
        def zrange(self, key, start, end, withscores=False):
            """Get range of members from sorted set."""
            with self.lock:
                if key not in self.zsets:
                    return []
                
                # Sort by score
                sorted_members = sorted(self.zsets[key].items(), key=lambda x: x[1])
                slice_members = sorted_members[start:end+1 if end != -1 else None]
                
                if withscores:
                    return [(member, score) for member, score in slice_members]
                else:
                    return [member for member, score in slice_members]
        
        # Hash operations
        def hset(self, key, field, value):
            """Set hash field."""
            with self.lock:
                if key not in self.hashes:
                    self.hashes[key] = {}
                self.hashes[key][field] = str(value)
                return 1
        
        def hget(self, key, field):
            """Get hash field value."""
            with self.lock:
                return self.hashes.get(key, {}).get(field)
        
        def hgetall(self, key):
            """Get all hash fields and values."""
            with self.lock:
                return self.hashes.get(key, {})
        
        def hdel(self, key, *fields):
            """Delete hash fields."""
            with self.lock:
                if key not in self.hashes:
                    return 0
                
                deleted = 0
                for field in fields:
                    if field in self.hashes[key]:
                        del self.hashes[key][field]
                        deleted += 1
                
                # Clean up empty hash
                if not self.hashes[key]:
                    del self.hashes[key]
                
                return deleted
        
        def pipeline(self, transaction=True):
            """Create pipeline for batch operations."""
            return EnhancedPipelineMock(self)
    
    class EnhancedPipelineMock:
        """Enhanced pipeline mock with proper transaction support."""
        
        def __init__(self, redis_mock):
            self.redis_mock = redis_mock
            self.commands = []
            self.transaction = True
        
        def __getattr__(self, name):
            """Capture all Redis commands for batch execution."""
            def command_wrapper(*args, **kwargs):
                self.commands.append((name, args, kwargs))
                return self
            return command_wrapper
        
        def execute(self):
            """Execute all queued commands atomically."""
            results = []
            
            # Execute all commands in lock for atomicity
            with self.redis_mock.lock:
                try:
                    for command_name, args, kwargs in self.commands:
                        method = getattr(self.redis_mock, command_name)
                        result = method(*args, **kwargs)
                        results.append(result)
                except Exception as e:
                    # In transaction mode, rollback on error
                    if self.transaction:
                        raise e
                    results.append(None)
            
            # Clear commands after execution
            self.commands = []
            return results
        
        def __enter__(self):
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            if exc_type is None:
                return self.execute()
    
    # Create the enhanced mock instance
    redis_mock = EnhancedRedisMock()
    return redis_mock


@pytest.fixture
def session_manager(mock_redis):
    """Create a SessionManager instance with mocked Redis."""
    manager = SessionManager(redis_client=mock_redis)
    return manager


@pytest.fixture
def rate_limiter(mock_redis):
    """Create a RateLimiter instance with mocked Redis."""
    limiter = RateLimiter(redis_client=mock_redis)
    return limiter


@pytest.fixture
def input_validator():
    """Create an InputValidator instance."""
    return InputValidator()


@pytest.fixture
def audit_logger(mock_redis):
    """Create an AuditLogger instance with mocked Redis."""
    logger = AuditLogger(redis_client=mock_redis)
    return logger


@pytest.fixture
def data_protection():
    """Create a DataProtection instance."""
    # Generate a valid Fernet key for testing
    from cryptography.fernet import Fernet
    test_key = Fernet.generate_key().decode()
    
    with patch('mcx3d_finance.utils.data_protection.get_security_config') as mock_config:
        mock_config.return_value = {
            "encryption_key": test_key,
            "data_retention_days": 365,
            "secure_delete_passes": 3
        }
        protection = DataProtection()
        return protection


@pytest.fixture
def mock_user():
    """Create a mock user for testing."""
    user = Mock()
    user.id = 1
    user.email = "<EMAIL>"
    user.hashed_password = "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewKyNiLXCJLwaEpi"  # "password123"
    user.is_active = True
    user.failed_login_attempts = 0
    user.last_failed_login = None
    user.organizations = []
    return user


@pytest.fixture
def mock_organization():
    """Create a mock organization for testing."""
    org = Mock()
    org.id = 1
    org.name = "Test Organization"
    org.is_active = True
    return org


@pytest.fixture
def security_config():
    """Get security configuration for testing."""
    return {
        "secret_key": os.environ.get("SECRET_KEY"),
        "algorithm": "HS256",
        "access_token_expire_minutes": 15,
        "refresh_token_expire_days": 30,
        "encryption_key": os.environ.get("ENCRYPTION_KEY"),
        "session_timeout_minutes": 60,
        "max_concurrent_sessions": 5,
        "password_min_length": 12,
        "password_require_uppercase": True,
        "password_require_lowercase": True,
        "password_require_numbers": True,
        "password_require_special": True,
        "password_history_count": 5,
        "account_lockout_threshold": 5,
        "account_lockout_duration_minutes": 30,
        "mfa_issuer": "MCX3D Finance Test",
        "rate_limit_enabled": True,
        "audit_log_enabled": True,
        "audit_log_retention_days": 365,
        "suspicious_activity_threshold": 10,
        "data_encryption_enabled": True,
        "data_retention_days": 365,
        "secure_delete_enabled": True,
        "secure_delete_passes": 3
    }


@pytest.fixture
def async_mock_redis():
    """Mock async Redis client for testing."""
    redis_mock = AsyncMock()
    
    # Storage for mocked Redis data
    redis_data = {}
    redis_ttl = {}
    
    async def mock_get(key):
        if key in redis_data:
            if key in redis_ttl and redis_ttl[key] < datetime.utcnow():
                del redis_data[key]
                del redis_ttl[key]
                return None
            return redis_data.get(key)
        return None
    
    async def mock_set(key, value, ex=None):
        redis_data[key] = value
        if ex:
            redis_ttl[key] = datetime.utcnow() + timedelta(seconds=ex)
        return True
    
    redis_mock.get = AsyncMock(side_effect=mock_get)
    redis_mock.set = AsyncMock(side_effect=mock_set)
    redis_mock.delete = AsyncMock(return_value=1)
    redis_mock.exists = AsyncMock(return_value=1)
    redis_mock.expire = AsyncMock(return_value=True)
    
    return redis_mock


@pytest.fixture
def test_client():
    """Create a test client for API testing."""
    from fastapi.testclient import TestClient
    from mcx3d_finance.api.main import app
    
    return TestClient(app)


@pytest.fixture
def test_user():
    """Create a test user for integration testing."""
    from mcx3d_finance.db.models import User
    user = User()
    user.id = 1
    user.email = "<EMAIL>"
    user.hashed_password = "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewKyNiLXCJLwaEpi"  # "password123"
    user.is_active = True
    user.full_name = "Test User"
    user.is_superuser = False
    return user


@pytest.fixture
def test_organization():
    """Create a test organization for integration testing."""
    from mcx3d_finance.db.models import Organization
    org = Organization()
    org.id = 1
    org.name = "Test Organization"
    org.xero_tenant_id = "test-tenant-123"
    org.xero_tenant_type = "ORGANISATION"
    org.base_currency = "USD"
    org.is_active = True
    return org


@pytest.fixture
def test_db_session():
    """Create a test database session."""
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker
    from mcx3d_finance.db.session import Base
    
    # Use in-memory SQLite for tests
    engine = create_engine("sqlite:///:memory:", echo=False)
    Base.metadata.create_all(engine)
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = SessionLocal()
    
    try:
        yield session
    finally:
        session.close()


@pytest.fixture
def populated_test_db(test_db_session, test_user, test_organization):
    """Create a populated test database with user and organization."""
    from mcx3d_finance.db.models import UserOrganization
    
    # Add user and organization to session
    test_db_session.add(test_user)
    test_db_session.add(test_organization)
    test_db_session.commit()
    
    # Create user-organization association
    user_org = UserOrganization()
    user_org.user_id = test_user.id
    user_org.organization_id = test_organization.id
    user_org.role = "admin"
    
    test_db_session.add(user_org)
    test_db_session.commit()
    
    return test_db_session


@pytest.fixture
def mock_xero_api():
    """Mock Xero API responses for integration testing."""
    from unittest.mock import Mock, patch
    
    mock_responses = {
        "organisations": [
            {
                "OrganisationID": "test-tenant-123",
                "Name": "Test Organization",
                "OrganisationType": "COMPANY",
                "BaseCurrency": "USD",
                "CountryCode": "US"
            }
        ],
        "accounts": [
            {
                "AccountID": "test-account-123",
                "Code": "200",
                "Name": "Sales",
                "Type": "REVENUE",
                "Status": "ACTIVE"
            }
        ],
        "contacts": [
            {
                "ContactID": "test-contact-123",
                "Name": "Test Customer",
                "EmailAddress": "<EMAIL>",
                "ContactStatus": "ACTIVE"
            }
        ]
    }
    
    with patch('mcx3d_finance.integrations.xero.XeroClient') as mock_client:
        # Setup mock methods
        mock_client.return_value.get_organisations.return_value = mock_responses["organisations"]
        mock_client.return_value.get_accounts.return_value = mock_responses["accounts"] 
        mock_client.return_value.get_contacts.return_value = mock_responses["contacts"]
        
        yield mock_client


@pytest.fixture
def authenticated_test_client(test_client, populated_test_db):
    """Create a test client with authenticated user."""
    # Mock the database dependency
    from mcx3d_finance.api.main import app
    from mcx3d_finance.db.session import get_db
    
    def override_get_db():
        return populated_test_db
    
    app.dependency_overrides[get_db] = override_get_db
    
    # Login to get access token
    response = test_client.post(
        "/api/auth/login",
        json={"email": "<EMAIL>", "password": "password123"}
    )
    
    if response.status_code == 200:
        access_token = response.json()["access_token"]
        # Add authentication header to test client
        test_client.headers = {"Authorization": f"Bearer {access_token}"}
    
    yield test_client
    
    # Cleanup
    app.dependency_overrides.clear()