#!/usr/bin/env python3
"""
Demo script to generate MCX3D LTD financial documentation.
This script creates comprehensive financial documents using the system we built.
"""

import os
import sys
import asyncio
from datetime import date, datetime
from pathlib import Path

# Add the project to Python path
sys.path.insert(0, '/app')

from mcx3d_finance.company import COMPANY_INFO, BRANDING_CONFIG
from mcx3d_finance.reporting.executive_summary import ExecutiveSummaryGenerator
from mcx3d_finance.reporting.notes_generator import NotesGenerator

def generate_demo_financial_package():
    """Generate financial documentation with demo data for MCX3D LTD."""
    
    print("🏢 MCX3D LTD Financial Documentation Generator")
    print("=" * 50)
    print(f"Company: {COMPANY_INFO['legal_name']}")
    print(f"Company Number: {COMPANY_INFO['company_number']}")
    print(f"Address: {COMPANY_INFO['registered_office']['address_line_1']}, {COMPANY_INFO['registered_office']['city']}")
    print(f"Report Date: {date.today().strftime('%d %B %Y')}")
    print()
    
    # Create output directory
    output_dir = Path("/app/reports")
    output_dir.mkdir(exist_ok=True, parents=True)
    
    # Initialize generators
    exec_summary = ExecutiveSummaryGenerator()
    notes_generator = NotesGenerator()
    
    # Generate executive summary with demo data
    print("📊 Generating Executive Summary...")
    
    demo_financial_data = {
        'revenue': 2850000,  # £2.85M revenue
        'gross_profit': 2420000,  # £2.42M gross profit  
        'net_income': 425000,  # £425K net income
        'total_assets': 1200000,  # £1.2M assets
        'total_liabilities': 350000,  # £350K liabilities
        'equity': 850000,  # £850K equity
        'cash_flow_operations': 520000,  # £520K operational cash flow
        'growth_rate': 0.18,  # 18% growth
        'key_metrics': {
            'arr': 3100000,  # £3.1M ARR
            'gross_margin': 85.0,  # 85% gross margin
            'customer_count': 245,
            'avg_contract_value': 12650
        }
    }
    
    try:
        # Generate executive summary PDF
        summary_file = output_dir / f"MCX3D_Executive_Summary_{date.today().strftime('%Y%m%d')}.pdf"
        exec_summary.generate_executive_summary(
            str(summary_file),
            demo_financial_data,
            report_date=date.today()
        )
        print(f"✅ Executive Summary: {summary_file.name}")
        
        # Generate notes to financial statements
        notes_file = output_dir / f"MCX3D_Notes_to_Financial_Statements_{date.today().strftime('%Y%m%d')}.pdf"
        notes_generator.generate_notes(
            str(notes_file),
            demo_financial_data,
            report_date=date.today()
        )
        print(f"✅ Notes to Statements: {notes_file.name}")
        
        print()
        print("📦 Financial Documentation Package Generated!")
        print(f"📁 Location: {output_dir.absolute()}")
        print()
        print("📋 Generated Documents:")
        
        for file in output_dir.glob("MCX3D_*.pdf"):
            size = file.stat().st_size / 1024
            print(f"   • {file.name} ({size:.1f} KB)")
            
        print()
        print("🎉 SUCCESS: MCX3D LTD financial documentation generated!")
        print()
        print("💡 Next Steps:")
        print("   1. Review generated documents in ./reports/")
        print("   2. Set up Xero OAuth tokens for real data")
        print("   3. Run: docker-compose exec worker python generate_docs_demo.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating documentation: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = generate_demo_financial_package()
    sys.exit(0 if success else 1)