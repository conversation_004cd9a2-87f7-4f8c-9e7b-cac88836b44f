name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Set up Python 3.9
        uses: actions/setup-python@v3
        with:
          python-version: 3.9

      - name: Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install isort autoflake mypy pylint bandit

      - name: Run Import Sorting Check
        run: |
          isort --check-only --diff .

      - name: Run Linters
        run: |
          flake8 .

      - name: Run Formatter Check
        run: |
          black --check .

      - name: Run Type Checking
        run: |
          mypy mcx3d_finance/

      - name: Check for Unused Imports
        run: |
          autoflake --check --recursive --remove-all-unused-imports --remove-unused-variables mcx3d_finance/

      - name: Run Security Check
        run: |
          bandit -r mcx3d_finance/ -f json -o bandit-report.json || true
          bandit -r mcx3d_finance/ --severity-level medium

      - name: Run Tests
        run: |
          pytest