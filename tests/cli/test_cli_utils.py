"""
Test suite for CLI utilities and standardized command structure.

Tests the CLI utilities module including decorators, error handling,
performance tracking, and output formatting.
"""

import pytest
import click
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
import json
import time
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock

from mcx3d_finance.cli.utils import (
    cli_command, cli_group, CLIFormatter, PerformanceTracker,
    handle_cli_errors, track_performance, standardize_output,
    validate_date, validate_positive_float, validate_percentage,
    confirm_action, handle_async_task, setup_logging
)


class TestCLIFormatter:
    """Test the CLIFormatter class."""
    
    def test_success_message(self, capsys):
        """Test success message formatting."""
        formatter = CLIFormatter()
        formatter.success("Operation completed")
        
        captured = capsys.readouterr()
        assert "✅ Operation completed" in captured.out
        assert "\x1b[32m" in captured.out  # Green color
    
    def test_success_message_with_data(self, capsys):
        """Test success message with data."""
        formatter = CLIFormatter()
        formatter.success("Operation completed", {"result": "data"})
        
        captured = capsys.readouterr()
        assert "✅ Operation completed" in captured.out
        assert '"result": "data"' in captured.out
    
    def test_error_message(self, capsys):
        """Test error message formatting."""
        formatter = CLIFormatter()
        formatter.error("Operation failed")
        
        captured = capsys.readouterr()
        assert "❌ Operation failed" in captured.err
        assert "\x1b[31m" in captured.err  # Red color
    
    def test_error_message_with_details(self, capsys):
        """Test error message with details."""
        formatter = CLIFormatter()
        formatter.error("Operation failed", "Invalid input")
        
        captured = capsys.readouterr()
        assert "❌ Operation failed" in captured.err
        assert "Details: Invalid input" in captured.err
    
    def test_warning_message(self, capsys):
        """Test warning message formatting."""
        formatter = CLIFormatter()
        formatter.warning("Caution advised")
        
        captured = capsys.readouterr()
        assert "⚠️  Caution advised" in captured.out
        assert "\x1b[33m" in captured.out  # Yellow color
    
    def test_info_message(self, capsys):
        """Test info message formatting."""
        formatter = CLIFormatter()
        formatter.info("Information")
        
        captured = capsys.readouterr()
        assert "ℹ️  Information" in captured.out
        assert "\x1b[34m" in captured.out  # Blue color
    
    def test_progress_message(self, capsys):
        """Test progress message formatting."""
        formatter = CLIFormatter()
        formatter.progress("Processing...")
        
        captured = capsys.readouterr()
        assert "🔄 Processing..." in captured.out
        assert "\x1b[36m" in captured.out  # Cyan color
    
    @patch('mcx3d_finance.cli.utils.tabulate')
    def test_table_formatting(self, mock_tabulate, capsys):
        """Test table formatting."""
        formatter = CLIFormatter()
        headers = ["Name", "Value"]
        rows = [["Item1", 100], ["Item2", 200]]
        
        mock_tabulate.return_value = "formatted_table"
        formatter.table(headers, rows)
        
        mock_tabulate.assert_called_once_with(rows, headers=headers, tablefmt="grid")
        captured = capsys.readouterr()
        assert "formatted_table" in captured.out


class TestPerformanceTracker:
    """Test the PerformanceTracker class."""
    
    def test_basic_tracking(self):
        """Test basic performance tracking."""
        tracker = PerformanceTracker()
        
        tracker.start()
        time.sleep(0.1)  # Simulate work
        tracker.end()
        
        metrics = tracker.get_metrics()
        assert "start_timestamp" in metrics
        assert "end_timestamp" in metrics
        assert "duration_seconds" in metrics
        assert metrics["duration_seconds"] >= 0.1
    
    def test_custom_metrics(self):
        """Test adding custom metrics."""
        tracker = PerformanceTracker()
        
        tracker.start()
        tracker.add_metric("records_processed", 1000)
        tracker.add_metric("errors", 0)
        tracker.end()
        
        metrics = tracker.get_metrics()
        assert metrics["records_processed"] == 1000
        assert metrics["errors"] == 0


class TestErrorHandling:
    """Test error handling decorator."""
    
    def test_handle_value_error(self):
        """Test handling ValueError."""
        @handle_cli_errors
        def command_with_value_error():
            raise ValueError("Invalid value")
        
        runner = CliRunner()
        result = runner.invoke(command_with_value_error)
        
        assert result.exit_code == 1
        assert "Invalid input: Invalid value" in result.output
    
    def test_handle_file_not_found(self):
        """Test handling FileNotFoundError."""
        @handle_cli_errors
        def command_with_file_error():
            raise FileNotFoundError("test.txt")
        
        runner = CliRunner()
        result = runner.invoke(command_with_file_error)
        
        assert result.exit_code == 1
        assert "File not found: test.txt" in result.output
    
    def test_handle_permission_error(self):
        """Test handling PermissionError."""
        @handle_cli_errors
        def command_with_permission_error():
            raise PermissionError("Access denied")
        
        runner = CliRunner()
        result = runner.invoke(command_with_permission_error)
        
        assert result.exit_code == 1
        assert "Permission denied: Access denied" in result.output
    
    def test_handle_connection_error(self):
        """Test handling ConnectionError."""
        @handle_cli_errors
        def command_with_connection_error():
            raise ConnectionError("Network error")
        
        runner = CliRunner()
        result = runner.invoke(command_with_connection_error)
        
        assert result.exit_code == 1
        assert "Connection error: Network error" in result.output
        assert "check your network connection" in result.output
    
    def test_handle_generic_exception(self):
        """Test handling generic exceptions."""
        @handle_cli_errors
        def command_with_generic_error():
            raise Exception("Something went wrong")
        
        runner = CliRunner()
        result = runner.invoke(command_with_generic_error)
        
        assert result.exit_code == 1
        assert "Unexpected error: Something went wrong" in result.output
        assert "check the logs" in result.output
    
    def test_click_exceptions_pass_through(self):
        """Test that Click exceptions pass through."""
        @handle_cli_errors
        def command_with_click_exception():
            raise click.ClickException("Click error")
        
        runner = CliRunner()
        result = runner.invoke(command_with_click_exception)
        
        # Click exceptions have their own handling
        assert "Click error" in result.output


class TestPerformanceTracking:
    """Test performance tracking decorator."""
    
    def test_performance_tracking(self):
        """Test basic performance tracking."""
        @click.command()
        @track_performance
        def test_command():
            time.sleep(0.1)
            return {"status": "success"}
        
        runner = CliRunner()
        with patch('mcx3d_finance.cli.utils.logger') as mock_logger:
            result = runner.invoke(test_command)
            
            # Check that performance was logged
            mock_logger.info.assert_called()
            call_args = str(mock_logger.info.call_args)
            assert "completed in" in call_args
    
    def test_performance_tracking_with_error(self):
        """Test performance tracking when command fails."""
        @click.command()
        @track_performance
        def failing_command():
            raise Exception("Command failed")
        
        runner = CliRunner()
        with pytest.raises(Exception):
            result = runner.invoke(failing_command, catch_exceptions=False)
    
    def test_performance_metrics_in_context(self):
        """Test that performance metrics are available in command."""
        @click.command()
        @track_performance
        def command_using_metrics(**kwargs):
            tracker = kwargs.get('_performance_tracker')
            assert tracker is not None
            tracker.add_metric("custom_metric", 42)
            return {"status": "success"}
        
        runner = CliRunner()
        result = runner.invoke(command_using_metrics)
        assert result.exit_code == 0


class TestOutputStandardization:
    """Test output standardization decorator."""
    
    def test_json_output(self):
        """Test JSON output format."""
        @click.command()
        @click.pass_context
        @standardize_output
        def json_command(ctx):
            ctx.obj = {"format": "json"}
            return {"result": "data", "value": 42}
        
        runner = CliRunner()
        result = runner.invoke(json_command)
        
        # Parse JSON output
        output_data = json.loads(result.output)
        assert output_data["result"] == "data"
        assert output_data["value"] == 42
    
    def test_table_output(self):
        """Test table output format."""
        @click.command()
        @click.pass_context
        @standardize_output
        def table_command(ctx):
            ctx.obj = {"format": "table"}
            return {
                "table": {
                    "headers": ["Name", "Value"],
                    "rows": [["Item1", 100], ["Item2", 200]]
                }
            }
        
        runner = CliRunner()
        with patch('mcx3d_finance.cli.utils.CLIFormatter.table') as mock_table:
            result = runner.invoke(table_command)
            mock_table.assert_called_once()
    
    def test_human_output(self):
        """Test human-readable output (default)."""
        @click.command()
        @click.pass_context
        @standardize_output
        def human_command(ctx):
            ctx.obj = {"format": "human"}
            click.echo("Human readable output")
            return {"internal": "data"}
        
        runner = CliRunner()
        result = runner.invoke(human_command)
        
        assert "Human readable output" in result.output
        # Internal data is not shown in human format
        assert "internal" not in result.output


class TestCLICommand:
    """Test the combined cli_command decorator."""
    
    def test_cli_command_integration(self):
        """Test that cli_command applies all decorators correctly."""
        @cli_command(name="test", help="Test command")
        @click.option("--value", type=int, default=42)
        def integrated_command(value):
            if value < 0:
                raise ValueError("Value must be positive")
            return {"result": value * 2}
        
        runner = CliRunner()
        
        # Test successful execution
        result = runner.invoke(integrated_command, ["--value", "21"])
        assert result.exit_code == 0
        
        # Test error handling
        result = runner.invoke(integrated_command, ["--value", "-1"])
        assert result.exit_code == 1
        assert "Invalid input" in result.output


class TestValidators:
    """Test validation callback functions."""
    
    def test_validate_date(self):
        """Test date validation."""
        ctx = Mock()
        param = Mock()
        
        # Valid date
        assert validate_date(ctx, param, "2023-12-31") == "2023-12-31"
        
        # Invalid date
        with pytest.raises(click.BadParameter):
            validate_date(ctx, param, "2023-13-01")
        
        # Invalid format
        with pytest.raises(click.BadParameter):
            validate_date(ctx, param, "31/12/2023")
        
        # None value
        assert validate_date(ctx, param, None) is None
    
    def test_validate_positive_float(self):
        """Test positive float validation."""
        ctx = Mock()
        param = Mock(name="test_param")
        
        # Valid positive float
        assert validate_positive_float(ctx, param, 42.5) == 42.5
        
        # Zero (invalid)
        with pytest.raises(click.BadParameter):
            validate_positive_float(ctx, param, 0)
        
        # Negative (invalid)
        with pytest.raises(click.BadParameter):
            validate_positive_float(ctx, param, -10.5)
        
        # None value
        assert validate_positive_float(ctx, param, None) is None
    
    def test_validate_percentage(self):
        """Test percentage validation."""
        ctx = Mock()
        param = Mock(name="test_param")
        
        # Valid percentages
        assert validate_percentage(ctx, param, 0) == 0
        assert validate_percentage(ctx, param, 50.5) == 50.5
        assert validate_percentage(ctx, param, 100) == 100
        
        # Invalid percentages
        with pytest.raises(click.BadParameter):
            validate_percentage(ctx, param, -10)
        
        with pytest.raises(click.BadParameter):
            validate_percentage(ctx, param, 150)
        
        # None value
        assert validate_percentage(ctx, param, None) is None


class TestAsyncHandling:
    """Test async task handling."""
    
    @patch('mcx3d_finance.cli.utils.celery_app')
    def test_handle_async_task_success(self, mock_celery_app):
        """Test successful async task handling."""
        # Mock async result
        mock_result = Mock()
        mock_result.ready.side_effect = [False, False, True]
        mock_result.successful.return_value = True
        mock_result.result = {"status": "completed"}
        mock_result.state = "SUCCESS"
        
        mock_celery_app.AsyncResult.return_value = mock_result
        
        with patch('mcx3d_finance.cli.utils.time.sleep'):
            result = handle_async_task("test-task-id", check_interval=0.1)
        
        assert result == {"status": "completed"}
    
    @patch('mcx3d_finance.cli.utils.celery_app')
    def test_handle_async_task_failure(self, mock_celery_app):
        """Test failed async task handling."""
        # Mock async result
        mock_result = Mock()
        mock_result.ready.side_effect = [False, True]
        mock_result.successful.return_value = False
        mock_result.info = "Task error message"
        mock_result.state = "FAILURE"
        
        mock_celery_app.AsyncResult.return_value = mock_result
        
        with patch('mcx3d_finance.cli.utils.time.sleep'):
            result = handle_async_task("test-task-id", check_interval=0.1)
        
        assert result is None
    
    @patch('mcx3d_finance.cli.utils.celery_app')
    def test_handle_async_task_progress(self, mock_celery_app):
        """Test async task with progress updates."""
        # Mock async result with progress
        mock_result = Mock()
        mock_result.ready.side_effect = [False, False, True]
        mock_result.successful.return_value = True
        mock_result.result = {"status": "completed"}
        mock_result.state = "PROGRESS"
        mock_result.info = {"current": 50, "total": 100}
        
        mock_celery_app.AsyncResult.return_value = mock_result
        
        with patch('mcx3d_finance.cli.utils.time.sleep'):
            result = handle_async_task("test-task-id", check_interval=0.1)
        
        assert result == {"status": "completed"}


class TestLoggingSetup:
    """Test logging setup functionality."""
    
    @patch('mcx3d_finance.cli.utils.logging.basicConfig')
    @patch('mcx3d_finance.cli.utils.logging.FileHandler')
    def test_setup_logging_debug(self, mock_file_handler, mock_basic_config):
        """Test logging setup with debug mode."""
        setup_logging(debug=True)
        
        mock_basic_config.assert_called_once()
        args, kwargs = mock_basic_config.call_args
        assert kwargs['level'] == logging.DEBUG
    
    @patch('mcx3d_finance.cli.utils.logging.basicConfig')
    @patch('mcx3d_finance.cli.utils.logging.FileHandler')
    def test_setup_logging_with_file(self, mock_file_handler_class, mock_basic_config):
        """Test logging setup with file output."""
        mock_handler = Mock()
        mock_file_handler_class.return_value = mock_handler
        
        setup_logging(log_file="test.log")
        
        mock_file_handler_class.assert_called_once_with("test.log")
        mock_handler.setLevel.assert_called_once()
        mock_handler.setFormatter.assert_called_once()


class TestCLIGroup:
    """Test the cli_group decorator."""
    
    def test_cli_group_creation(self):
        """Test creating a CLI group."""
        @cli_group(name="test-group", help="Test group")
        def test_group():
            pass
        
        # Add a command to the group
        @test_group.command()
        def subcommand():
            click.echo("Subcommand executed")
        
        runner = CliRunner()
        
        # Test help
        result = runner.invoke(test_group, ["--help"])
        assert "Test group" in result.output
        
        # Test subcommand
        result = runner.invoke(test_group, ["subcommand"])
        assert "Subcommand executed" in result.output
    
    def test_cli_group_context(self):
        """Test that cli_group sets up context correctly."""
        @cli_group(name="test-group", help="Test group")
        @click.pass_context
        def test_group(ctx):
            assert ctx.obj is not None
            assert "command_group" in ctx.obj
            assert ctx.obj["command_group"] == "test-group"
        
        runner = CliRunner()
        result = runner.invoke(test_group)
        assert result.exit_code == 0


class TestConfirmAction:
    """Test the confirm_action helper."""
    
    def test_confirm_action_yes(self):
        """Test confirmation with yes response."""
        with patch('click.confirm', return_value=True):
            result = confirm_action("Proceed with operation?")
            assert result is True
    
    def test_confirm_action_no(self):
        """Test confirmation with no response."""
        with patch('click.confirm', return_value=False):
            result = confirm_action("Proceed with operation?")
            assert result is False
    
    def test_confirm_action_default(self):
        """Test confirmation with default value."""
        with patch('click.confirm') as mock_confirm:
            confirm_action("Proceed?", default=True)
            mock_confirm.assert_called_once_with("Do you want to continue?", default=True)