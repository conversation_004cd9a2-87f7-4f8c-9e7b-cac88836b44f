# Staging Environment Configuration
# MCX3D Financials - Staging Settings
# Pre-production environment configuration
# Should closely mirror production settings with some debugging enabled

# Environment identification
environment: staging

# Database configuration for staging
database:
  # Production-like database but separate instance
  pool_size: 10
  timeout: 45
  echo_sql: false  # Disable SQL logging in staging

# Redis configuration for staging
redis:
  # Staging Redis instance with password
  db: 0
  # Password should be set via REDIS_PASSWORD environment variable

# Xero OAuth configuration
xero:
  # Use staging/sandbox Xero application
  # Set XERO_CLIENT_ID and XERO_CLIENT_SECRET in environment
  redirect_uri: "https://staging-api.mcx3d.com/api/auth/xero/callback"
  scopes: "accounting.transactions accounting.contacts accounting.reports.read accounting.settings offline_access"

# Security configuration for staging
security:
  # Production-like security with some debugging
  access_token_expire_minutes: 30
  max_login_attempts: 5
  lockout_duration_minutes: 15
  
  # Enhanced security for staging
  enable_audit_encryption: true
  enable_field_encryption: true
  key_rotation_days: 90

# Rate limiting - production-like
rate_limiting:
  api_default: 200
  auth_login: 5
  data_export: 10

# Logging configuration
logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  enable_sql_logging: false
  log_file: "/var/log/mcx3d_finance/staging.log"
  max_file_size_mb: 100
  backup_count: 5

# Reporting configuration
reporting:
  output_dir: "/var/reports/staging"
  default_format: "pdf"
  enable_charts: true
  chart_dpi: 300
  max_file_size_mb: 200

# Performance settings for staging
performance:
  memory_limit_mb: 1024
  timeout_seconds: 120
  parallel_workers: 4
  enable_caching: true

# Staging-specific features
staging:
  enable_debugging: true
  enable_performance_profiling: true
  enable_integration_testing: true
  mock_external_apis: false
  enable_load_testing: true

# CORS settings for staging
cors:
  origins:
    - "https://staging.mcx3d.com"
    - "https://staging-app.mcx3d.com"
  allow_credentials: true
  allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  allow_headers: ["Content-Type", "Authorization", "X-Request-ID"]

# Monitoring - comprehensive for staging validation
monitoring:
  enable_metrics: true
  enable_tracing: true
  enable_profiling: true
  enable_alerting: true
  
  # Monitoring endpoints
  prometheus_endpoint: "/metrics"
  health_check_interval_seconds: 30

# SSL/TLS configuration
ssl:
  enforce_https: true
  hsts_max_age: 31536000
  hsts_include_subdomains: true

# Data retention - production-like
data_retention:
  audit_logs_days: 365
  session_data_days: 30
  temp_files_hours: 48
  backup_retention_days: 90

# Backup configuration
backup:
  enable_automated_backups: true
  backup_schedule: "0 2 * * *"  # Daily at 2 AM
  retention_policy: "7d"
  encryption_enabled: true

# Integration testing
integration:
  enable_test_endpoints: true
  test_data_refresh_hours: 24
  enable_mock_responses: false