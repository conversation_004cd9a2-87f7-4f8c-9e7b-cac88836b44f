#!/usr/bin/env python3
"""
Performance testing script for MCX3D report generation in Docker.
"""

import time
import psutil
import os
import subprocess
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

def measure_memory_usage():
    """Get current memory usage."""
    process = psutil.Process()
    return process.memory_info().rss / 1024 / 1024  # MB

def run_dcf_valuation(org_id=1, export_format="pdf"):
    """Run DCF valuation and measure performance."""
    start_time = time.time()
    start_memory = measure_memory_usage()
    
    cmd = [
        "python", "-m", "mcx3d_finance.cli.main", 
        "valuate", "dcf", 
        "--organization-id", str(org_id),
        "--config", "sample.json",
        "--export", export_format
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        end_time = time.time()
        end_memory = measure_memory_usage()
        
        return {
            "success": result.returncode == 0,
            "duration": end_time - start_time,
            "memory_delta": end_memory - start_memory,
            "output_size": len(result.stdout) + len(result.stderr),
            "error": result.stderr if result.returncode != 0 else None
        }
    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "duration": 120,
            "memory_delta": 0,
            "output_size": 0,
            "error": "Timeout"
        }

def run_saas_valuation(org_id=1, export_format="pdf"):
    """Run SaaS valuation and measure performance."""
    start_time = time.time()
    start_memory = measure_memory_usage()
    
    cmd = [
        "python", "-m", "mcx3d_finance.cli.main", 
        "valuate", "saas", 
        "--organization-id", str(org_id),
        "--export", export_format
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        end_time = time.time()
        end_memory = measure_memory_usage()
        
        return {
            "success": result.returncode == 0,
            "duration": end_time - start_time,
            "memory_delta": end_memory - start_memory,
            "output_size": len(result.stdout) + len(result.stderr),
            "error": result.stderr if result.returncode != 0 else None
        }
    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "duration": 120,
            "memory_delta": 0,
            "output_size": 0,
            "error": "Timeout"
        }

def test_concurrent_generation(num_concurrent=3):
    """Test concurrent report generation."""
    print(f"Testing concurrent generation with {num_concurrent} workers...")
    
    start_time = time.time()
    results = []
    
    with ThreadPoolExecutor(max_workers=num_concurrent) as executor:
        # Submit different types of reports
        futures = []
        for i in range(num_concurrent):
            if i % 2 == 0:
                future = executor.submit(run_dcf_valuation, 1, "pdf")
            else:
                future = executor.submit(run_saas_valuation, 1, "pdf")
            futures.append(future)
        
        # Collect results
        for future in as_completed(futures):
            results.append(future.result())
    
    end_time = time.time()
    
    return {
        "total_duration": end_time - start_time,
        "individual_results": results,
        "success_rate": sum(1 for r in results if r["success"]) / len(results)
    }

def main():
    """Run performance tests."""
    print("=== MCX3D Docker Performance Test ===")
    print(f"Test started at: {datetime.now()}")
    
    # Single report generation tests
    print("\n1. Single DCF PDF Generation:")
    dcf_pdf_result = run_dcf_valuation(1, "pdf")
    print(f"   Duration: {dcf_pdf_result['duration']:.2f}s")
    print(f"   Memory Delta: {dcf_pdf_result['memory_delta']:.2f}MB")
    print(f"   Success: {dcf_pdf_result['success']}")
    
    print("\n2. Single SaaS PDF Generation:")
    saas_pdf_result = run_saas_valuation(1, "pdf")
    print(f"   Duration: {saas_pdf_result['duration']:.2f}s")
    print(f"   Memory Delta: {saas_pdf_result['memory_delta']:.2f}MB")
    print(f"   Success: {saas_pdf_result['success']}")
    
    # Concurrent generation test
    print("\n3. Concurrent Generation Test:")
    concurrent_result = test_concurrent_generation(3)
    print(f"   Total Duration: {concurrent_result['total_duration']:.2f}s")
    print(f"   Success Rate: {concurrent_result['success_rate']:.1%}")
    print(f"   Average Individual Duration: {sum(r['duration'] for r in concurrent_result['individual_results']) / len(concurrent_result['individual_results']):.2f}s")
    
    # System resource check
    print("\n4. System Resources:")
    print(f"   Available Memory: {psutil.virtual_memory().available / 1024 / 1024:.0f}MB")
    print(f"   CPU Usage: {psutil.cpu_percent(interval=1):.1f}%")
    print(f"   Disk Usage: {psutil.disk_usage('/').percent:.1f}%")
    
    # Summary
    print("\n=== Performance Summary ===")
    all_successful = all([
        dcf_pdf_result['success'],
        saas_pdf_result['success'],
        concurrent_result['success_rate'] > 0.8
    ])
    
    print(f"Overall Status: {'PASS' if all_successful else 'FAIL'}")
    print(f"Average Report Generation Time: {(dcf_pdf_result['duration'] + saas_pdf_result['duration']) / 2:.2f}s")
    
    return all_successful

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
