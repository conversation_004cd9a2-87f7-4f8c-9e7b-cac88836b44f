from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.pool import QueuePool
import logging
import time
from mcx3d_finance.db.config import get_database_url
from mcx3d_finance.core.config import detect_environment, load_config

logger = logging.getLogger(__name__)

def get_engine_config():
    """Get database engine configuration based on environment."""
    environment = detect_environment()
    config = load_config()
    
    # Get database configuration from environment-specific config
    db_config = config.get('database', {})
    
    engine_config = {
        'poolclass': QueuePool,
        'pool_size': db_config.get('pool_size', 10),
        'max_overflow': db_config.get('pool_size', 10),  # Allow double the pool size
        'pool_timeout': db_config.get('timeout', 30),
        'pool_recycle': db_config.get('pool_recycle', 3600),  # 1 hour
        'pool_pre_ping': db_config.get('pool_pre_ping', True),  # Validate connections
        'echo': db_config.get('echo_sql', False),
        'echo_pool': False,  # Set to True for pool debugging
    }
    
    # Add environment-specific optimizations
    if environment == 'production':
        engine_config.update({
            'pool_size': 20,
            'max_overflow': 30,
            'pool_timeout': 60,
            'echo': False,  # Never echo in production
        })
    elif environment == 'development':
        engine_config.update({
            'pool_size': 5,
            'max_overflow': 10,
            'pool_timeout': 30,
            'echo': db_config.get('echo_sql', False),
        })
    elif environment == 'testing':
        engine_config.update({
            'pool_size': 1,
            'max_overflow': 3,
            'pool_timeout': 10,
            'echo': False,  # Keep tests quiet
        })
    
    # Add connection arguments for SSL and timeouts
    connect_args = db_config.get('connect_args', {})
    if connect_args:
        engine_config['connect_args'] = connect_args
    
    logger.info(f"Database engine configured for {environment} environment: "
                f"pool_size={engine_config['pool_size']}, "
                f"max_overflow={engine_config['max_overflow']}")
    
    return engine_config

# Create engine with optimized connection pool configuration
engine_config = get_engine_config()
engine = create_engine(get_database_url(), **engine_config)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def check_database_connection() -> dict:
    """Check database connection health and return status."""
    try:
        # Test basic connectivity
        with engine.connect() as connection:
            # Execute a simple query to verify database is responsive
            from sqlalchemy import text
            result = connection.execute(text("SELECT 1 as test"))
            result.fetchone()
            
            # Check connection pool status
            pool = engine.pool
            pool_status = {
                'size': pool.size(),
                'checked_in': pool.checkedin(),
                'checked_out': pool.checkedout(),
                'overflow': pool.overflow()
                # Note: invalid() method not available in newer SQLAlchemy versions
            }
            
            return {
                'status': 'healthy',
                'message': 'Database connection successful',
                'pool_status': pool_status,
                'database_url_prefix': str(engine.url).split('@')[0] + '@***'  # Hide credentials
            }
            
    except Exception as e:
        logger.error(f"Database connection check failed: {e}")
        return {
            'status': 'unhealthy',
            'message': f'Database connection failed: {str(e)}',
            'error': str(e)
        }


def validate_database_startup() -> bool:
    """Validate database connection at application startup."""
    try:
        logger.info("Validating database connection at startup...")
        
        health_check = check_database_connection()
        
        if health_check['status'] == 'healthy':
            logger.info("✅ Database connection validation passed")
            logger.debug(f"Connection pool status: {health_check['pool_status']}")
            return True
        else:
            logger.error(f"❌ Database connection validation failed: {health_check['message']}")
            return False
            
    except Exception as e:
        logger.error(f"Database startup validation error: {e}")
        return False


def get_database_health() -> dict:
    """Get comprehensive database health information."""
    try:
        start_time = time.time()
        
        # Basic connection check
        connection_health = check_database_connection()
        response_time = (time.time() - start_time) * 1000
        
        if connection_health['status'] == 'healthy':
            # Additional health metrics
            with engine.connect() as connection:
                # Check if we can write (test transaction)
                try:
                    trans = connection.begin()
                    from sqlalchemy import text
                    connection.execute(text("SELECT 1"))
                    trans.rollback()  # Don't actually commit anything
                    write_status = 'healthy'
                except Exception as e:
                    write_status = f'unhealthy: {str(e)}'
                
                # Get database version and other info
                try:
                    from sqlalchemy import text
                    version_result = connection.execute(text("SELECT version()"))
                    db_version = version_result.fetchone()[0].split(' ')[0:2]
                    db_version = ' '.join(db_version)
                except:
                    db_version = 'unknown'
        else:
            write_status = 'unhealthy'
            db_version = 'unknown'
        
        return {
            'status': connection_health['status'],
            'response_time_ms': round(response_time, 2),
            'connection_health': connection_health,
            'write_status': write_status,
            'database_version': db_version,
            'engine_info': {
                'driver': engine.driver,
                'dialect': str(engine.dialect),
                'pool_class': str(engine.pool.__class__.__name__)
            }
        }
        
    except Exception as e:
        logger.error(f"Database health check error: {e}")
        return {
            'status': 'error',
            'error': str(e),
            'response_time_ms': 0
        }
