"""
Reporting-specific exceptions for the MCX3D financial system.

These exceptions handle all report generation errors including data validation,
output formatting, chart generation, and file operations.
"""

from typing import Dict, Any, Optional, List
from .base import MCX3DException


class MCX3DReportingException(MCX3DException):
    """Base exception for all reporting-related errors."""
    
    def _get_default_user_message(self) -> str:
        return "An error occurred while generating the report. Please check your data and try again."


class ReportGenerationError(MCX3DReportingException):
    """
    Exception raised when report generation fails due to data or system issues.
    
    Used for general report generation failures that cannot be categorized
    into more specific exception types.
    """
    
    def __init__(
        self,
        message: str,
        report_type: str,
        stage: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize report generation error.
        
        Args:
            message: Technical error message
            report_type: Type of report being generated (PDF, Excel, etc.)
            stage: Generation stage where error occurred (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.report_type = report_type
        self.stage = stage
        
        self.add_context("report_type", report_type)
        if stage:
            self.add_context("generation_stage", stage)
    
    def _get_default_user_message(self) -> str:
        return f"Failed to generate {self.report_type} report. Please verify your data and try again."


class ReportDataValidationError(MCX3DReportingException):
    """
    Exception raised when input data for report generation fails validation.
    
    Used when the data provided for report generation is invalid, incomplete,
    or doesn't meet business rules.
    """
    
    def __init__(
        self,
        message: str,
        validation_errors: List[str],
        data_type: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize report data validation error.
        
        Args:
            message: Technical error message
            validation_errors: List of specific validation error messages
            data_type: Type of data being validated (DCF, financial statements, etc.)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.validation_errors = validation_errors
        self.data_type = data_type
        
        self.add_context("validation_errors", validation_errors)
        if data_type:
            self.add_context("data_type", data_type)
    
    def _get_default_user_message(self) -> str:
        error_count = len(self.validation_errors)
        return f"Report data validation failed with {error_count} error(s). Please correct the data and try again."
    
    def get_validation_summary(self) -> str:
        """Get a formatted summary of validation errors."""
        if not self.validation_errors:
            return "No validation errors"
        
        summary = f"Validation failed with {len(self.validation_errors)} error(s):\n"
        for i, error in enumerate(self.validation_errors, 1):
            summary += f"{i}. {error}\n"
        
        return summary.strip()


class ReportOutputError(MCX3DReportingException):
    """
    Exception raised when report output operations fail.
    
    Used for file system errors, permissions issues, disk space problems,
    or other I/O related failures during report output.
    """
    
    def __init__(
        self,
        message: str,
        file_path: Optional[str] = None,
        operation: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize report output error.
        
        Args:
            message: Technical error message
            file_path: Path where output operation failed (optional)
            operation: Type of operation (write, create, delete, etc.)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.file_path = file_path
        self.operation = operation
        
        if file_path:
            self.add_context("file_path", file_path)
        if operation:
            self.add_context("operation", operation)
    
    def _get_default_user_message(self) -> str:
        return "Failed to save the report. Please check file permissions and available disk space."


class ChartGenerationError(MCX3DReportingException):
    """
    Exception raised when chart or visualization generation fails.
    
    Used for plotting library errors, data formatting issues for charts,
    or rendering problems with visualizations.
    """
    
    def __init__(
        self,
        message: str,
        chart_type: Optional[str] = None,
        chart_data: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """
        Initialize chart generation error.
        
        Args:
            message: Technical error message
            chart_type: Type of chart being generated (bar, line, pie, etc.)
            chart_data: Data used for chart generation (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.chart_type = chart_type
        self.chart_data = chart_data
        
        if chart_type:
            self.add_context("chart_type", chart_type)
        if chart_data:
            self.add_context("chart_data_keys", list(chart_data.keys()) if isinstance(chart_data, dict) else str(type(chart_data)))
    
    def _get_default_user_message(self) -> str:
        chart_desc = f" ({self.chart_type})" if self.chart_type else ""
        return f"Failed to generate chart{chart_desc}. The report will be created without visualizations."


class ReportTemplateError(MCX3DReportingException):
    """
    Exception raised when report template operations fail.
    
    Used for template loading, parsing, or rendering errors.
    """
    
    def __init__(
        self,
        message: str,
        template_name: Optional[str] = None,
        template_operation: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize report template error.
        
        Args:
            message: Technical error message
            template_name: Name of the template (optional)
            template_operation: Operation being performed (load, render, etc.)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.template_name = template_name
        self.template_operation = template_operation
        
        if template_name:
            self.add_context("template_name", template_name)
        if template_operation:
            self.add_context("template_operation", template_operation)
    
    def _get_default_user_message(self) -> str:
        return "Report template error. The system will attempt to generate a basic format."


class ReportFormatError(MCX3DReportingException):
    """
    Exception raised when report format operations fail.
    
    Used for PDF generation, Excel formatting, or other format-specific errors.
    """
    
    def __init__(
        self,
        message: str,
        format_type: str,
        format_operation: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize report format error.
        
        Args:
            message: Technical error message
            format_type: Type of format (PDF, Excel, CSV, etc.)
            format_operation: Specific formatting operation that failed
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.format_type = format_type
        self.format_operation = format_operation
        
        self.add_context("format_type", format_type)
        if format_operation:
            self.add_context("format_operation", format_operation)
    
    def _get_default_user_message(self) -> str:
        return f"Failed to format report as {self.format_type}. Please try a different format or contact support."


class ReportMemoryError(MCX3DReportingException):
    """
    Exception raised when report generation exceeds memory limits.
    
    Used specifically for memory-related issues during report generation.
    """
    
    def __init__(
        self,
        message: str,
        memory_usage_mb: Optional[float] = None,
        memory_limit_mb: Optional[float] = None,
        **kwargs
    ):
        """
        Initialize report memory error.
        
        Args:
            message: Technical error message
            memory_usage_mb: Current memory usage in MB (optional)
            memory_limit_mb: Memory limit in MB (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.memory_usage_mb = memory_usage_mb
        self.memory_limit_mb = memory_limit_mb
        
        if memory_usage_mb is not None:
            self.add_context("memory_usage_mb", memory_usage_mb)
        if memory_limit_mb is not None:
            self.add_context("memory_limit_mb", memory_limit_mb)
    
    def _get_default_user_message(self) -> str:
        return "Report generation requires too much memory. Please try reducing the data size or contact support."


class ReportProcessingTimeout(MCX3DReportingException):
    """
    Exception raised when report generation exceeds time limits.
    
    Used when report generation takes too long to complete.
    """
    
    def __init__(
        self,
        message: str,
        timeout_seconds: float,
        processing_stage: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize report processing timeout.
        
        Args:
            message: Technical error message
            timeout_seconds: Timeout duration in seconds
            processing_stage: Stage where timeout occurred (optional)
            **kwargs: Additional arguments for base exception
        """
        super().__init__(message, **kwargs)
        self.timeout_seconds = timeout_seconds
        self.processing_stage = processing_stage
        
        self.add_context("timeout_seconds", timeout_seconds)
        if processing_stage:
            self.add_context("processing_stage", processing_stage)
    
    def _get_default_user_message(self) -> str:
        return f"Report generation is taking longer than expected (>{self.timeout_seconds}s). Please try again or contact support."