"""Authentication-specific exceptions for the MCX3D finance application."""

from typing import Optional


class AuthenticationError(Exception):
    """Base class for authentication-related errors."""
    
    def __init__(self, message: str, error_code: Optional[str] = None):
        super().__init__(message)
        self.error_code = error_code


class InvalidCredentialsError(AuthenticationError):
    """Raised when provided credentials are invalid."""
    pass


class AccountLockedError(AuthenticationError):
    """Raised when an account is temporarily locked due to failed attempts."""
    
    def __init__(self, message: str, remaining_minutes: int):
        super().__init__(message)
        self.remaining_minutes = remaining_minutes


class MFARequiredError(AuthenticationError):
    """Raised when MFA is required but not provided."""
    
    def __init__(self, message: str, challenge: Optional[str] = None):
        super().__init__(message)
        self.challenge = challenge


class InvalidMFATokenError(AuthenticationError):
    """Raised when provided MFA token is invalid."""
    pass


class TokenExpiredError(AuthenticationError):
    """Raised when a token has expired."""
    pass


class TokenInvalidError(AuthenticationError):
    """Raised when a token is malformed or invalid."""
    pass


class SessionError(AuthenticationError):
    """Base class for session-related errors."""
    pass


class SessionExpiredError(SessionError):
    """Raised when a session has expired."""
    pass


class SessionNotFoundError(SessionError):
    """Raised when a session cannot be found."""
    pass


class XeroAuthError(AuthenticationError):
    """Base class for Xero OAuth-related errors."""
    pass


class XeroTokenRefreshError(XeroAuthError):
    """Raised when Xero token refresh fails."""
    pass


class XeroAuthorizationError(XeroAuthError):
    """Raised when Xero authorization fails."""
    pass


class InvalidStateError(XeroAuthError):
    """Raised when OAuth state parameter is invalid or expired."""
    pass