#!/usr/bin/env python3
"""
Database seeding script for MCX3D Financials v2.

This script initializes the database with sample data for testing and development.
It creates organizations, accounts, contacts, transactions, and other necessary data.
"""

import sys
import os
import json
import logging
from datetime import datetime, timedelta
from decimal import Decimal
import random
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mcx3d_finance.db.session import SessionLocal, engine
from mcx3d_finance.db.models import (
    Base,
    Organization,
    Account,
    Contact,
    Transaction,
    Invoice,
    BankTransaction,
    User
)
from enum import Enum

# Define UserRole enum if not available
class UserRole(str, Enum):
    ADMIN = "admin"
    ANALYST = "analyst"
    VIEWER = "viewer"
from mcx3d_finance.utils.generate_keys import generate_secret_key
from passlib.context import CryptContext

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    """Hash a password for storing."""
    return pwd_context.hash(password)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseSeeder:
    """Handle database initialization and sample data seeding."""
    
    def __init__(self):
        self.db = SessionLocal()
        self.sample_data_path = Path(__file__).parent.parent / "sample_data"
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.db.close()
        
    def create_tables(self):
        """Create all database tables."""
        logger.info("Creating database tables...")
        Base.metadata.create_all(bind=engine)
        logger.info("✅ Database tables created successfully")
        
    def load_json_data(self, filename: str) -> list:
        """Load data from JSON file in sample_data directory."""
        file_path = self.sample_data_path / filename
        if not file_path.exists():
            logger.warning(f"Sample data file not found: {file_path}")
            return []
            
        with open(file_path, 'r') as f:
            return json.load(f)
            
    def seed_organizations(self) -> list:
        """Create sample organizations."""
        logger.info("Seeding organizations...")
        
        organizations = []
        
        # Create test organizations
        org_data = [
            {
                "name": "Demo Company Inc",
                "xero_tenant_id": "demo-tenant-001",
                "xero_tenant_type": "ORGANISATION",
                "base_currency": "USD",
                "is_active": True,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            },
            {
                "name": "Test Corporation Ltd",
                "xero_tenant_id": "test-tenant-002",
                "xero_tenant_type": "ORGANISATION", 
                "base_currency": "EUR",
                "is_active": True,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            },
            {
                "name": "Sample Business LLC",
                "xero_tenant_id": None,  # Not connected to Xero
                "xero_tenant_type": None,
                "base_currency": "USD",
                "is_active": True,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
        ]
        
        for org_info in org_data:
            org = Organization(**org_info)
            self.db.add(org)
            organizations.append(org)
            
        self.db.commit()
        logger.info(f"✅ Created {len(organizations)} organizations")
        return organizations
        
    def seed_accounts(self, organizations: list) -> list:
        """Create chart of accounts for each organization."""
        logger.info("Seeding accounts...")
        
        accounts = []
        account_templates = [
            # Assets
            {"code": "1000", "name": "Cash and Cash Equivalents", "account_type": "BANK", "tax_type": "NONE", "is_bank_account": True},
            {"code": "1100", "name": "Accounts Receivable", "account_type": "CURRENT", "tax_type": "NONE", "is_bank_account": False},
            {"code": "1200", "name": "Inventory", "account_type": "INVENTORY", "tax_type": "NONE", "is_bank_account": False},
            {"code": "1500", "name": "Property and Equipment", "account_type": "FIXED", "tax_type": "NONE", "is_bank_account": False},
            
            # Liabilities
            {"code": "2000", "name": "Accounts Payable", "account_type": "CURRLIAB", "tax_type": "NONE", "is_bank_account": False},
            {"code": "2100", "name": "Credit Card", "account_type": "CREDITCARD", "tax_type": "NONE", "is_bank_account": False},
            {"code": "2500", "name": "Long Term Debt", "account_type": "TERMLIAB", "tax_type": "NONE", "is_bank_account": False},
            
            # Equity
            {"code": "3000", "name": "Common Stock", "account_type": "EQUITY", "tax_type": "NONE", "is_bank_account": False},
            {"code": "3100", "name": "Retained Earnings", "account_type": "EQUITY", "tax_type": "NONE", "is_bank_account": False},
            
            # Revenue
            {"code": "4000", "name": "Sales Revenue", "account_type": "REVENUE", "tax_type": "OUTPUT", "is_bank_account": False},
            {"code": "4100", "name": "Service Revenue", "account_type": "REVENUE", "tax_type": "OUTPUT", "is_bank_account": False},
            {"code": "4200", "name": "Interest Income", "account_type": "REVENUE", "tax_type": "NONE", "is_bank_account": False},
            
            # Expenses
            {"code": "5000", "name": "Cost of Goods Sold", "account_type": "DIRECTCOSTS", "tax_type": "INPUT", "is_bank_account": False},
            {"code": "6000", "name": "Salaries and Wages", "account_type": "EXPENSE", "tax_type": "NONE", "is_bank_account": False},
            {"code": "6100", "name": "Rent Expense", "account_type": "EXPENSE", "tax_type": "INPUT", "is_bank_account": False},
            {"code": "6200", "name": "Utilities", "account_type": "EXPENSE", "tax_type": "INPUT", "is_bank_account": False},
            {"code": "6300", "name": "Marketing and Advertising", "account_type": "EXPENSE", "tax_type": "INPUT", "is_bank_account": False},
            {"code": "6400", "name": "Office Supplies", "account_type": "EXPENSE", "tax_type": "INPUT", "is_bank_account": False},
            {"code": "6500", "name": "Professional Fees", "account_type": "EXPENSE", "tax_type": "INPUT", "is_bank_account": False},
            {"code": "6600", "name": "Insurance", "account_type": "EXPENSE", "tax_type": "INPUT", "is_bank_account": False},
            {"code": "6700", "name": "Depreciation", "account_type": "DEPRECIATN", "tax_type": "NONE", "is_bank_account": False},
            {"code": "6800", "name": "Interest Expense", "account_type": "EXPENSE", "tax_type": "NONE", "is_bank_account": False},
        ]
        
        for org in organizations:
            for template in account_templates:
                account = Account(
                    organization_id=org.id,
                    xero_account_id=f"{org.id}-{template['code']}",
                    code=template['code'],
                    name=template['name'],
                    type=template['account_type'],  # Changed from account_type to type
                    tax_type=template['tax_type'],
                    description=f"{template['name']} for {org.name}",
                    status="ACTIVE",
                    is_bank_account=template['is_bank_account'],
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                self.db.add(account)
                accounts.append(account)
                
        self.db.commit()
        logger.info(f"✅ Created {len(accounts)} accounts")
        return accounts
        
    def seed_contacts(self, organizations: list) -> list:
        """Create sample contacts for each organization."""
        logger.info("Seeding contacts...")
        
        contacts = []
        contact_templates = [
            {"name": "ABC Suppliers Inc", "is_supplier": True, "is_customer": False},
            {"name": "XYZ Corporation", "is_supplier": False, "is_customer": True},
            {"name": "Global Trading Co", "is_supplier": True, "is_customer": True},
            {"name": "Tech Solutions Ltd", "is_supplier": False, "is_customer": True},
            {"name": "Office Supplies Direct", "is_supplier": True, "is_customer": False},
            {"name": "Premium Services LLC", "is_supplier": False, "is_customer": True},
            {"name": "Industrial Equipment Co", "is_supplier": True, "is_customer": False},
            {"name": "Digital Marketing Agency", "is_supplier": True, "is_customer": False},
        ]
        
        for org in organizations:
            for template in contact_templates:
                contact = Contact(
                    organization_id=org.id,
                    xero_contact_id=f"{org.id}-{template['name'].replace(' ', '-').lower()}",
                    name=template['name'],
                    first_name=template['name'].split()[0],
                    last_name=template['name'].split()[-1] if len(template['name'].split()) > 1 else "",
                    email=f"contact@{template['name'].replace(' ', '').lower()}.com",
                    is_supplier=template['is_supplier'],
                    is_customer=template['is_customer'],
                    tax_number=f"TAX{random.randint(100000, 999999)}",
                    is_active=True,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                self.db.add(contact)
                contacts.append(contact)
                
        self.db.commit()
        logger.info(f"✅ Created {len(contacts)} contacts")
        return contacts
        
    def seed_transactions(self, organizations: list, accounts: list) -> list:
        """Create sample transactions."""
        logger.info("Seeding transactions...")
        
        transactions = []
        
        for org in organizations:
            # Get accounts for this organization
            org_accounts = [acc for acc in accounts if acc.organization_id == org.id]
            
            # Get specific account types
            bank_accounts = [acc for acc in org_accounts if acc.is_bank_account]
            revenue_accounts = [acc for acc in org_accounts if acc.type == "REVENUE"]
            expense_accounts = [acc for acc in org_accounts if acc.type == "EXPENSE"]
            
            if not (bank_accounts and revenue_accounts and expense_accounts):
                continue
                
            # Create transactions for the last 6 months
            for month in range(6):
                base_date = datetime.utcnow() - timedelta(days=30 * month)
                
                # Revenue transactions
                for _ in range(random.randint(5, 15)):
                    amount = Decimal(random.uniform(1000, 50000)).quantize(Decimal('0.01'))
                    transaction = Transaction(
                        organization_id=org.id,
                        xero_transaction_id=f"trans-{org.id}-{month}-{random.randint(1000, 9999)}",
                        date=base_date - timedelta(days=random.randint(0, 29)),
                        amount=amount,
                        currency_code=org.base_currency,
                        description=f"Sales revenue - Invoice #{random.randint(1000, 9999)}",
                        reference=f"INV-{random.randint(1000, 9999)}",
                        account_id=random.choice(revenue_accounts).id,
                        bank_account_id=random.choice(bank_accounts).id,
                        is_reconciled=random.choice([True, False]),
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                    self.db.add(transaction)
                    transactions.append(transaction)
                    
                # Expense transactions
                for _ in range(random.randint(10, 20)):
                    amount = Decimal(random.uniform(100, 10000)).quantize(Decimal('0.01'))
                    transaction = Transaction(
                        organization_id=org.id,
                        xero_transaction_id=f"trans-{org.id}-{month}-{random.randint(1000, 9999)}",
                        date=base_date - timedelta(days=random.randint(0, 29)),
                        amount=-amount,  # Negative for expenses
                        currency_code=org.base_currency,
                        description=f"{random.choice(expense_accounts).name} - {base_date.strftime('%B %Y')}",
                        reference=f"EXP-{random.randint(1000, 9999)}",
                        account_id=random.choice(expense_accounts).id,
                        bank_account_id=random.choice(bank_accounts).id,
                        is_reconciled=random.choice([True, False]),
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                    self.db.add(transaction)
                    transactions.append(transaction)
                    
        self.db.commit()
        logger.info(f"✅ Created {len(transactions)} transactions")
        return transactions
        
    def seed_users(self) -> list:
        """Create sample users for testing."""
        logger.info("Seeding users...")
        
        users = []
        user_data = [
            {
                "email": "<EMAIL>",
                "full_name": "Admin User",
                "is_superuser": True,
                "is_active": True
            },
            {
                "email": "<EMAIL>",
                "full_name": "Financial Analyst",
                "is_superuser": False,
                "is_active": True
            },
            {
                "email": "<EMAIL>",
                "full_name": "Report Viewer",
                "is_superuser": False,
                "is_active": True
            }
        ]
        
        for user_info in user_data:
            user = User(
                email=user_info["email"],
                full_name=user_info["full_name"],
                hashed_password=hash_password("password123"),  # Default password for testing
                is_superuser=user_info["is_superuser"],
                is_active=user_info["is_active"],
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            self.db.add(user)
            users.append(user)
            
        self.db.commit()
        logger.info(f"✅ Created {len(users)} users")
        logger.info("ℹ️  Default password for all users: password123")
        return users
        
    def seed_invoices(self, organizations: list, contacts: list, accounts: list) -> list:
        """Create sample invoices."""
        logger.info("Seeding invoices...")
        
        invoices = []
        
        for org in organizations:
            # Get contacts and accounts for this organization
            org_contacts = [c for c in contacts if c.organization_id == org.id and c.is_customer]
            org_revenue_accounts = [a for a in accounts if a.organization_id == org.id and a.type == "REVENUE"]
            
            if not (org_contacts and org_revenue_accounts):
                continue
                
            # Create invoices for the last 3 months
            for month in range(3):
                base_date = datetime.utcnow() - timedelta(days=30 * month)
                
                for _ in range(random.randint(3, 8)):
                    invoice_number = f"INV-{random.randint(1000, 9999)}"
                    due_date = base_date + timedelta(days=30)
                    
                    # Calculate totals
                    subtotal = Decimal(random.uniform(1000, 25000)).quantize(Decimal('0.01'))
                    tax_rate = Decimal('0.10')  # 10% tax
                    tax_amount = (subtotal * tax_rate).quantize(Decimal('0.01'))
                    total = subtotal + tax_amount
                    
                    invoice = Invoice(
                        organization_id=org.id,
                        xero_invoice_id=f"inv-{org.id}-{invoice_number}",
                        invoice_number=invoice_number,
                        contact_id=random.choice(org_contacts).id,
                        date=base_date,
                        due_date=due_date,
                        status=random.choice(["DRAFT", "SUBMITTED", "AUTHORISED", "PAID"]),
                        line_amount_types="Exclusive",
                        sub_total=subtotal,
                        tax_amount=tax_amount,
                        total=total,
                        total_tax=tax_amount,
                        currency_code=org.base_currency,
                        type="ACCREC",  # Accounts Receivable
                        reference=f"Project-{random.randint(100, 999)}",
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                    self.db.add(invoice)
                    invoices.append(invoice)
                    
                    # Create line items for the invoice
                    num_items = random.randint(1, 5)
                    for i in range(num_items):
                        line_amount = subtotal / num_items
                        # Note: InvoiceLineItem model not currently defined
                        # Would add line items here if model existed
                        pass
                        
        self.db.commit()
        logger.info(f"✅ Created {len(invoices)} invoices")
        return invoices
        
    def seed_bank_transactions(self, organizations: list, accounts: list) -> list:
        """Create sample bank transactions."""
        logger.info("Seeding bank transactions...")
        
        bank_transactions = []
        
        for org in organizations:
            # Get bank accounts for this organization
            org_bank_accounts = [a for a in accounts if a.organization_id == org.id and a.is_bank_account]
            
            if not org_bank_accounts:
                continue
                
            # Create bank transactions for the last 3 months
            for month in range(3):
                base_date = datetime.utcnow() - timedelta(days=30 * month)
                
                for _ in range(random.randint(20, 40)):
                    amount = Decimal(random.uniform(-5000, 10000)).quantize(Decimal('0.01'))
                    
                    bank_transaction = BankTransaction(
                        organization_id=org.id,
                        xero_bank_transaction_id=f"bank-{org.id}-{month}-{random.randint(10000, 99999)}",
                        bank_account_id=random.choice(org_bank_accounts).id,
                        type="SPEND" if amount < 0 else "RECEIVE",
                        reference=f"REF-{random.randint(10000, 99999)}",
                        amount=abs(amount),
                        currency_code=org.base_currency,
                        date=base_date - timedelta(days=random.randint(0, 29)),
                        status=random.choice(["AUTHORISED", "DELETED"]),
                        is_reconciled=random.choice([True, False]),
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                    self.db.add(bank_transaction)
                    bank_transactions.append(bank_transaction)
                    
        self.db.commit()
        logger.info(f"✅ Created {len(bank_transactions)} bank transactions")
        return bank_transactions
        
    def display_summary(self, organizations, accounts, contacts, transactions, users, invoices, bank_transactions):
        """Display summary of seeded data."""
        logger.info("\n" + "="*60)
        logger.info("DATABASE SEEDING COMPLETE")
        logger.info("="*60)
        logger.info(f"✅ Organizations: {len(organizations)}")
        logger.info(f"✅ Accounts: {len(accounts)}")
        logger.info(f"✅ Contacts: {len(contacts)}")
        logger.info(f"✅ Transactions: {len(transactions)}")
        logger.info(f"✅ Users: {len(users)}")
        logger.info(f"✅ Invoices: {len(invoices)}")
        logger.info(f"✅ Bank Transactions: {len(bank_transactions)}")
        logger.info("="*60)
        
        logger.info("\n📋 Test Credentials:")
        logger.info("  Admin: <EMAIL> / password123")
        logger.info("  Analyst: <EMAIL> / password123")
        logger.info("  Viewer: <EMAIL> / password123")
        
        logger.info("\n🏢 Organizations:")
        for org in organizations:
            logger.info(f"  - {org.name} (ID: {org.id}, Currency: {org.base_currency})")
    
    def load_company_sample_data(self) -> dict:
        """Load comprehensive company data from sample_data/company_data.json."""
        logger.info("Loading comprehensive company sample data...")
        
        company_data = self.load_json_data("company_data.json")
        if not company_data:
            logger.error("Failed to load company_data.json")
            return {}
            
        logger.info(f"✅ Loaded company data with {len(company_data.get('accounts', []))} accounts")
        logger.info(f"   • Contacts: {len(company_data.get('contacts', []))}")
        logger.info(f"   • Transactions: {len(company_data.get('transactions', []))}")
        logger.info(f"   • Invoices: {len(company_data.get('invoices', []))}")
        logger.info(f"   • Payments: {len(company_data.get('payments', []))}")
        
        return company_data
    
    def seed_mcx3d_organization(self) -> Organization:
        """Create MCX3D LTD organization with real company details."""
        logger.info("Creating MCX3D LTD organization...")
        
        # Create MCX3D organization matching company_info.py
        org = Organization(
            name="MCX3D LTD",
            xero_tenant_id="mcx3d-sample-data",
            xero_tenant_type="ORGANISATION",
            base_currency="GBP",
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.db.add(org)
        self.db.commit()
        
        logger.info(f"✅ Created MCX3D LTD organization (ID: {org.id})")
        return org
    
    def seed_company_accounts(self, organization: Organization, company_data: dict) -> list:
        """Import accounts from company sample data."""
        logger.info("Importing company accounts...")
        
        accounts = []
        account_data = company_data.get('accounts', [])
        
        for acc_data in account_data:
            try:
                account = Account(
                    organization_id=organization.id,
                    xero_account_id=acc_data.get('xero_account_id'),
                    code=acc_data.get('code', ''),
                    name=acc_data.get('name', ''),
                    type=acc_data.get('type', 'EXPENSE'),
                    tax_type=acc_data.get('tax_type', 'NONE'),
                    description=acc_data.get('description', ''),
                    class_type=acc_data.get('class_type', ''),
                    status=acc_data.get('status', 'ACTIVE'),
                    show_in_expense_claims=acc_data.get('show_in_expense_claims', False),
                    bank_account_number=acc_data.get('bank_account_number', ''),
                    bank_account_type=acc_data.get('bank_account_type', ''),
                    currency_code=acc_data.get('currency_code', 'GBP'),
                    reporting_code=acc_data.get('reporting_code', ''),
                    reporting_code_name=acc_data.get('reporting_code_name', ''),
                    has_attachments=acc_data.get('has_attachments', False),
                    updated_date_utc=datetime.fromisoformat(acc_data.get('updated_date_utc', '2023-01-01T00:00:00').replace('Z', '+00:00')) if acc_data.get('updated_date_utc') else datetime.utcnow(),
                    add_to_watchlist=acc_data.get('add_to_watchlist', False),
                    gaap_classification=acc_data.get('gaap_classification', 'other_revenue'),
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                self.db.add(account)
                accounts.append(account)
                
            except Exception as e:
                logger.warning(f"Failed to import account {acc_data.get('name', 'Unknown')}: {e}")
                continue
        
        self.db.commit()
        logger.info(f"✅ Imported {len(accounts)} accounts")
        return accounts
    
    def seed_company_contacts(self, organization: Organization, company_data: dict) -> list:
        """Import contacts from company sample data."""
        logger.info("Importing company contacts...")
        
        contacts = []
        contact_data = company_data.get('contacts', [])
        
        for cont_data in contact_data:
            try:
                contact = Contact(
                    organization_id=organization.id,
                    xero_contact_id=cont_data.get('xero_contact_id'),
                    name=cont_data.get('name', ''),
                    contact_number=cont_data.get('contact_number', ''),
                    account_number=cont_data.get('account_number', ''),
                    contact_status=cont_data.get('contact_status', 'ACTIVE'),
                    first_name=cont_data.get('first_name', ''),
                    last_name=cont_data.get('last_name', ''),
                    email_address=cont_data.get('email_address', ''),
                    bank_account_details=cont_data.get('bank_account_details', ''),
                    addresses=cont_data.get('addresses', ''),
                    phones=cont_data.get('phones', ''),
                    updated_date_utc=datetime.fromisoformat(cont_data.get('updated_date_utc', '2023-01-01T00:00:00').replace('Z', '+00:00')) if cont_data.get('updated_date_utc') else datetime.utcnow(),
                    is_supplier=cont_data.get('is_supplier', False),
                    is_customer=cont_data.get('is_customer', False),
                    default_currency=cont_data.get('default_currency', 'GBP'),
                    has_attachments=cont_data.get('has_attachments', False),
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                self.db.add(contact)
                contacts.append(contact)
                
            except Exception as e:
                logger.warning(f"Failed to import contact {cont_data.get('name', 'Unknown')}: {e}")
                continue
        
        self.db.commit()
        logger.info(f"✅ Imported {len(contacts)} contacts")
        return contacts
    
    def run_company_data_import(self):
        """Run the company sample data import process."""
        try:
            # Create tables
            self.create_tables()
            
            # Load company sample data
            company_data = self.load_company_sample_data()
            if not company_data:
                raise Exception("Failed to load company sample data")
            
            # Create MCX3D organization
            organization = self.seed_mcx3d_organization()
            
            # Import accounts and contacts from sample data
            accounts = self.seed_company_accounts(organization, company_data)
            contacts = self.seed_company_contacts(organization, company_data)
            
            # Create default users
            users = self.seed_users()
            
            # Display summary
            logger.info("\n" + "="*60)
            logger.info("MCX3D LTD SAMPLE DATA IMPORT SUMMARY")
            logger.info("="*60)
            logger.info(f"✅ Organization: {organization.name}")
            logger.info(f"✅ Accounts: {len(accounts)}")
            logger.info(f"✅ Contacts: {len(contacts)}")
            logger.info(f"✅ Users: {len(users)}")
            logger.info("="*60)
            
            logger.info("\n📋 Test Credentials:")
            logger.info("  Admin: <EMAIL> / password123")
            logger.info("  Analyst: <EMAIL> / password123")
            logger.info("  Viewer: <EMAIL> / password123")
            
            logger.info(f"\n🏢 Organization Details:")
            logger.info(f"  - {organization.name} (ID: {organization.id})")
            logger.info(f"  - Currency: {organization.base_currency}")
            logger.info(f"  - Tenant ID: {organization.xero_tenant_id}")
            
            logger.info("\n✅ Company sample data import completed successfully!")
            logger.info("🚀 Ready to generate financial documentation for MCX3D LTD!")
            
        except Exception as e:
            logger.error(f"❌ Error during company data import: {e}")
            self.db.rollback()
            raise
            
    def run(self):
        """Run the complete seeding process."""
        try:
            # Create tables
            self.create_tables()
            
            # Seed data in order
            organizations = self.seed_organizations()
            accounts = self.seed_accounts(organizations)
            contacts = self.seed_contacts(organizations)
            transactions = self.seed_transactions(organizations, accounts)
            users = self.seed_users()
            invoices = self.seed_invoices(organizations, contacts, accounts)
            bank_transactions = self.seed_bank_transactions(organizations, accounts)
            
            # Display summary
            self.display_summary(
                organizations, accounts, contacts, 
                transactions, users, invoices, bank_transactions
            )
            
            logger.info("\n✅ Database seeding completed successfully!")
            logger.info("You can now run the application and use the test data.")
            
        except Exception as e:
            logger.error(f"❌ Error during database seeding: {e}")
            self.db.rollback()
            raise


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Seed MCX3D Financials database with sample data")
    parser.add_argument("--reset", action="store_true", help="Drop and recreate all tables before seeding")
    parser.add_argument("--company-data", action="store_true", help="Import real company data from sample_data/company_data.json")
    args = parser.parse_args()
    
    if args.reset:
        logger.warning("⚠️  Resetting database - this will delete all existing data!")
        response = input("Are you sure you want to continue? (yes/no): ")
        if response.lower() != "yes":
            logger.info("Database reset cancelled.")
            return
            
        logger.info("Dropping all tables...")
        Base.metadata.drop_all(bind=engine)
        logger.info("✅ All tables dropped")
    
    with DatabaseSeeder() as seeder:
        if args.company_data:
            seeder.run_company_data_import()
        else:
            seeder.run()


if __name__ == "__main__":
    main()