groups:
  - name: mcx3d_financial_alerts
    rules:
      # Application Health Alerts
      - alert: ApplicationDown
        expr: up{job="mcx3d-finance"} == 0
        for: 1m
        labels:
          severity: critical
          component: application
        annotations:
          summary: "MCX3D Financial application is down"
          description: "The MCX3D Financial application has been down for more than 1 minute."

      - alert: HighErrorRate
        expr: rate(mcx3d_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
          component: application
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second, above the threshold of 0.1."

      # Business Metrics Alerts
      - alert: LowReportGeneration
        expr: rate(mcx3d_reports_generated_total[1h]) < 0.05
        for: 10m
        labels:
          severity: warning
          component: business
        annotations:
          summary: "Report generation rate is low"
          description: "Report generation rate is {{ $value }} per second, below expected threshold."

      - alert: SlowValuationProcessing
        expr: histogram_quantile(0.95, rate(mcx3d_valuation_processing_seconds_bucket[5m])) > 30
        for: 5m
        labels:
          severity: warning
          component: business
        annotations:
          summary: "Valuation processing is slow"
          description: "95th percentile valuation processing time is {{ $value }} seconds."

      - alert: CriticalValuationProcessing
        expr: histogram_quantile(0.95, rate(mcx3d_valuation_processing_seconds_bucket[5m])) > 120
        for: 2m
        labels:
          severity: critical
          component: business
        annotations:
          summary: "Valuation processing critically slow"
          description: "95th percentile valuation processing time is {{ $value }} seconds."

      # Database Alerts
      - alert: DatabaseDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
          component: database
        annotations:
          summary: "PostgreSQL database is down"
          description: "The PostgreSQL database has been down for more than 1 minute."

      - alert: HighDatabaseConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections * 100 > 80
        for: 5m
        labels:
          severity: warning
          component: database
        annotations:
          summary: "High database connection usage"
          description: "Database connection usage is {{ $value }}%, above 80% threshold."

      - alert: SlowDatabaseQueries
        expr: rate(pg_stat_database_tup_fetched[5m]) / rate(pg_stat_database_tup_returned[5m]) < 0.8
        for: 5m
        labels:
          severity: warning
          component: database
        annotations:
          summary: "Database query efficiency degraded"
          description: "Database query efficiency is below 80%."

      # Redis Alerts
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          component: cache
        annotations:
          summary: "Redis cache is down"
          description: "Redis cache has been down for more than 1 minute."

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 80
        for: 5m
        labels:
          severity: warning
          component: cache
        annotations:
          summary: "Redis memory usage is high"
          description: "Redis memory usage is {{ $value }}%, above 80% threshold."

      - alert: RedisLowHitRatio
        expr: rate(redis_keyspace_hits_total[5m]) / (rate(redis_keyspace_hits_total[5m]) + rate(redis_keyspace_misses_total[5m])) < 0.8
        for: 10m
        labels:
          severity: warning
          component: cache
        annotations:
          summary: "Redis cache hit ratio is low"
          description: "Redis cache hit ratio is {{ $value }}, below 80% threshold."

      # System Resource Alerts
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          component: system
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}."

      - alert: CriticalCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 95
        for: 2m
        labels:
          severity: critical
          component: system
        annotations:
          summary: "Critical CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}."

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
          component: system
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}."

      - alert: CriticalMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 95
        for: 2m
        labels:
          severity: critical
          component: system
        annotations:
          summary: "Critical memory usage"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}."

      - alert: HighDiskUsage
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes * 100 > 90
        for: 5m
        labels:
          severity: warning
          component: system
        annotations:
          summary: "High disk usage"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }} {{ $labels.mountpoint }}."

      - alert: CriticalDiskUsage
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes * 100 > 95
        for: 1m
        labels:
          severity: critical
          component: system
        annotations:
          summary: "Critical disk usage"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }} {{ $labels.mountpoint }}."

      # API Performance Alerts
      - alert: HighAPIResponseTime
        expr: histogram_quantile(0.95, rate(mcx3d_http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          component: api
        annotations:
          summary: "High API response time"
          description: "95th percentile API response time is {{ $value }} seconds."

      - alert: CriticalAPIResponseTime
        expr: histogram_quantile(0.95, rate(mcx3d_http_request_duration_seconds_bucket[5m])) > 5
        for: 2m
        labels:
          severity: critical
          component: api
        annotations:
          summary: "Critical API response time"
          description: "95th percentile API response time is {{ $value }} seconds."

      # User Activity Alerts
      - alert: LowUserActivity
        expr: mcx3d_active_users_current < 10
        for: 30m
        labels:
          severity: warning
          component: business
        annotations:
          summary: "Low user activity detected"
          description: "Only {{ $value }} active users, below expected threshold of 10."

      # Financial Data Quality Alerts
      - alert: DataQualityDegraded
        expr: mcx3d_data_quality_score < 80
        for: 10m
        labels:
          severity: warning
          component: data_quality
        annotations:
          summary: "Data quality score degraded"
          description: "Data quality score is {{ $value }}, below 80% threshold."

      - alert: DataValidationFailures
        expr: rate(mcx3d_data_validations_total{status="failed"}[10m]) > 0.1
        for: 5m
        labels:
          severity: error
          component: data_quality
        annotations:
          summary: "High data validation failure rate"
          description: "Data validation failure rate is {{ $value }} per second."