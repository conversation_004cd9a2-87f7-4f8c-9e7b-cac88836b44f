from sqlalchemy import Column, Inte<PERSON>, String, ForeignKey, DateTime, Float, Text, Boolean, JSON
from sqlalchemy.orm import relationship
from datetime import datetime, timezone
from mcx3d_finance.db.session import Base


def utc_now():
    """Return timezone-aware UTC datetime."""
    return datetime.now(timezone.utc)


class Organization(Base):
    __tablename__ = "organizations"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    xero_tenant_id = Column(String, unique=True, index=True)
    xero_tenant_type = Column(String)  # ORGANISATION, PRACTICE, etc.
    xero_token = Column(Text)  # Encrypted OAuth token
    token_expires_at = Column(DateTime)  # Token expiration timestamp
    base_currency = Column(String, default="USD")  # Base currency for reporting
    last_sync_at = Column(DateTime)  # Last successful sync timestamp
    sync_status = Column(String)  # current sync status
    created_at = Column(DateTime)
    updated_at = Column(DateTime)
    is_active = Column(Boolean, default=True)

    accounts = relationship("Account", back_populates="organization")
    contacts = relationship("Contact", back_populates="organization")
    transactions = relationship("Transaction", back_populates="organization")
    invoices = relationship("Invoice", back_populates="organization")
    bank_transactions = relationship("BankTransaction", back_populates="organization")
    users = relationship("User", secondary="user_organizations", back_populates="organizations")
    user_associations = relationship("UserOrganization", back_populates="organization", cascade="all, delete-orphan", overlaps="organizations,users")


class Account(Base):
    __tablename__ = "accounts"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    xero_account_id = Column(String, unique=True, index=True)  # Xero's AccountID
    code = Column(String, index=True)
    name = Column(String, index=True)
    type = Column(String)
    tax_type = Column(String)
    description = Column(Text)
    class_type = Column(String)  # ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE
    status = Column(String)
    show_in_expense_claims = Column(Boolean, default=False)
    bank_account_number = Column(String)
    bank_account_type = Column(String)
    currency_code = Column(String, default="GBP")
    reporting_code = Column(String)
    reporting_code_name = Column(String)
    has_attachments = Column(Boolean, default=False)
    updated_date_utc = Column(DateTime)
    add_to_watchlist = Column(Boolean, default=False)
    # GAAP classification for NASDAQ compliance
    gaap_classification = Column(String)
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    organization = relationship("Organization", back_populates="accounts")
    transactions = relationship("Transaction", back_populates="account")


class Contact(Base):
    __tablename__ = "contacts"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    xero_contact_id = Column(String, unique=True, index=True)  # Xero's ContactID
    contact_number = Column(String)
    account_number = Column(String)
    contact_status = Column(String, default="ACTIVE")
    name = Column(String, index=True)
    first_name = Column(String)
    last_name = Column(String)
    email_address = Column(String, index=True)
    bank_account_details = Column(String)
    tax_number = Column(String)
    accounts_receivable_tax_type = Column(String)
    accounts_payable_tax_type = Column(String)
    is_supplier = Column(Boolean, default=False)
    is_customer = Column(Boolean, default=False)
    default_currency = Column(String, default="GBP")
    updated_date_utc = Column(DateTime)
    has_attachments = Column(Boolean, default=False)
    has_validation_errors = Column(Boolean, default=False)
    # Phone numbers
    phone_default = Column(String)
    phone_mobile = Column(String)
    phone_fax = Column(String)
    # Addresses stored as JSON
    address_street = Column(JSON)
    address_postal = Column(JSON)
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    organization = relationship("Organization", back_populates="contacts")
    invoices = relationship("Invoice", back_populates="contact")
    bank_transactions = relationship("BankTransaction", back_populates="contact")


class Transaction(Base):
    __tablename__ = "transactions"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    xero_transaction_id = Column(String, unique=True, index=True)
    type = Column(String)  # SPEND, RECEIVE
    date = Column(DateTime, index=True)
    amount = Column(Float)
    description = Column(Text)
    reference = Column(String)
    is_reconciled = Column(Boolean, default=False)
    currency_code = Column(String, default="GBP")
    currency_rate = Column(Float, default=1.0)
    sub_total = Column(Float)
    total_tax = Column(Float)
    total = Column(Float)
    line_items = Column(JSON)  # Store line items as JSON
    status = Column(String)
    updated_date_utc = Column(DateTime)
    has_attachments = Column(Boolean, default=False)
    contact_id = Column(Integer, ForeignKey("contacts.id"))
    account_id = Column(Integer, ForeignKey("accounts.id"))
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    organization = relationship("Organization", back_populates="transactions")
    account = relationship("Account", back_populates="transactions")
    contact = relationship("Contact")


class Invoice(Base):
    __tablename__ = "invoices"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    xero_invoice_id = Column(String, unique=True, index=True)
    type = Column(String)  # ACCREC (sales) or ACCPAY (purchase)
    contact_id = Column(Integer, ForeignKey("contacts.id"))
    date = Column(DateTime, index=True)
    due_date = Column(DateTime)
    line_amount_types = Column(String)
    invoice_number = Column(String, index=True)
    reference = Column(String)
    branding_theme_id = Column(String)
    url = Column(String)
    currency_code = Column(String, default="GBP")
    currency_rate = Column(Float, default=1.0)
    status = Column(String, index=True)  # DRAFT, SUBMITTED, AUTHORISED, PAID, VOIDED
    sent_to_contact = Column(Boolean, default=False)
    expected_payment_date = Column(DateTime)
    planned_payment_date = Column(DateTime)
    sub_total = Column(Float)
    total_tax = Column(Float)
    total = Column(Float)
    total_discount = Column(Float)
    has_attachments = Column(Boolean, default=False)
    has_errors = Column(Boolean, default=False)
    is_discounted = Column(Boolean, default=False)
    payments = Column(JSON)  # Store payment details as JSON
    amount_due = Column(Float)
    amount_paid = Column(Float)
    amount_credited = Column(Float)
    updated_date_utc = Column(DateTime)
    line_items = Column(JSON)  # Store line items as JSON
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    organization = relationship("Organization")
    contact = relationship("Contact", back_populates="invoices")


class BankTransaction(Base):
    __tablename__ = "bank_transactions"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    xero_transaction_id = Column(String, unique=True, index=True)
    type = Column(String)  # SPEND, RECEIVE, etc.
    contact_id = Column(Integer, ForeignKey("contacts.id"))
    line_items = Column(JSON)
    bank_account = Column(JSON)  # Bank account details
    is_reconciled = Column(Boolean, default=False)
    date = Column(DateTime, index=True)
    reference = Column(String)
    currency_code = Column(String, default="GBP")
    currency_rate = Column(Float, default=1.0)
    url = Column(String)
    status = Column(String)
    line_amount_types = Column(String)
    sub_total = Column(Float)
    total_tax = Column(Float)
    total = Column(Float)
    updated_date_utc = Column(DateTime)
    has_attachments = Column(Boolean, default=False)
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    organization = relationship("Organization")
    contact = relationship("Contact", back_populates="bank_transactions")


class BankReconciliation(Base):
    """Track bank reconciliation history and matched transactions."""
    __tablename__ = "bank_reconciliations"
    
    id = Column(Integer, primary_key=True, index=True)
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    bank_account_id = Column(String)  # External bank account identifier
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    status = Column(String)  # in_progress, completed, failed
    
    # Summary statistics
    total_transactions = Column(Integer, default=0)
    matched_transactions = Column(Integer, default=0)
    unmatched_transactions = Column(Integer, default=0)
    total_amount = Column(Float, default=0.0)
    matched_amount = Column(Float, default=0.0)
    unmatched_amount = Column(Float, default=0.0)
    
    # Reconciliation results
    results = Column(JSON)  # Detailed matching results
    errors = Column(JSON)  # Any errors encountered
    
    # User who performed reconciliation
    user_id = Column(Integer, ForeignKey("users.id"))
    
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)
    completed_at = Column(DateTime)
    
    # Relationships
    organization = relationship("Organization")
    user = relationship("User")
    matched_transactions = relationship("ReconciliationMatch", back_populates="reconciliation")


class ReconciliationMatch(Base):
    """Individual transaction matches in a reconciliation."""
    __tablename__ = "reconciliation_matches"
    
    id = Column(Integer, primary_key=True, index=True)
    reconciliation_id = Column(Integer, ForeignKey("bank_reconciliations.id"))
    bank_transaction_id = Column(Integer, ForeignKey("bank_transactions.id"))
    
    # External bank statement data
    statement_date = Column(DateTime)
    statement_description = Column(String)
    statement_amount = Column(Float)
    statement_reference = Column(String)
    
    # Matching details
    match_type = Column(String)  # exact, partial, manual
    match_confidence = Column(Float)  # 0.0 to 1.0
    match_criteria = Column(JSON)  # What criteria were used for matching
    
    created_at = Column(DateTime, default=utc_now)
    
    # Relationships
    reconciliation = relationship("BankReconciliation", back_populates="matched_transactions")
    bank_transaction = relationship("BankTransaction")


class SyncStatus(Base):
    __tablename__ = "sync_status"

    id = Column(Integer, primary_key=True, index=True)
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    sync_type = Column(String)  # full, incremental
    status = Column(String)  # pending, in_progress, completed, failed
    records_synced = Column(JSON)  # JSON with counts per entity type
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    error_message = Column(Text)
    created_at = Column(DateTime)
    
    organization = relationship("Organization")


class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    full_name = Column(String)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now)
    
    # Many-to-many relationship with organizations
    organizations = relationship("Organization", secondary="user_organizations", back_populates="users")
    organization_associations = relationship("UserOrganization", back_populates="user", cascade="all, delete-orphan", overlaps="organizations,users")


class UserOrganization(Base):
    __tablename__ = "user_organizations"
    
    user_id = Column(Integer, ForeignKey("users.id"), primary_key=True)
    organization_id = Column(Integer, ForeignKey("organizations.id"), primary_key=True)
    role = Column(String)  # admin, user, viewer
    created_at = Column(DateTime, default=utc_now)
    
    # Relationships
    user = relationship("User", back_populates="organization_associations", overlaps="organizations,users")
    organization = relationship("Organization", back_populates="user_associations", overlaps="organizations,users")
