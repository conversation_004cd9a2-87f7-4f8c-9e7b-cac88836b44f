# MCX3D Financials Scripts Documentation

This directory contains utility scripts for managing, testing, and deploying the MCX3D Financial System.

## 📁 Directory Structure

```
scripts/
├── deployment/          # Production deployment and validation
├── testing/            # Test data generation and management
├── monitoring/         # System monitoring and health checks
├── development/        # Development utilities and helpers
├── start_backend.sh    # Quick backend startup script
└── setup_test_data.sh  # Test data setup script
```

## 🚀 Quick Start

### Starting the Backend Services
```bash
# From project root
./scripts/start_backend.sh
```

### Generating Test Data
```bash
# Generate basic test data
python scripts/testing/seed_test_data.py

# Generate enterprise scenario
python scripts/testing/seed_test_data.py --scenario enterprise
```

### Production Deployment
```bash
# Deploy to production
./scripts/deployment/production_deploy.sh --environment production
```

## 📂 Script Categories

### 1. Deployment Scripts (`deployment/`)

#### `production_deploy.sh`
Production deployment script with comprehensive health checks and rollback capabilities.

**Features:**
- Pre-deployment validation
- Database backup
- Rolling deployment support
- Health check verification
- Automatic rollback on failure

**Usage:**
```bash
./scripts/deployment/production_deploy.sh [OPTIONS]
Options:
  --environment    Environment to deploy to (staging/production)
  --skip-backup    Skip database backup
  --strategy       Deployment strategy (rolling/blue-green/recreate)
  --dry-run        Show what would be done without executing
```

#### `production_health_check.py`
Comprehensive health check script for production systems.

**Features:**
- Service availability checks
- Database connectivity validation
- Redis connection testing
- API endpoint verification

**Usage:**
```bash
python scripts/deployment/production_health_check.py
```

#### `validate_deployment.py`
Pre and post-deployment validation script.

**Features:**
- Environment configuration validation
- Security settings verification
- Performance baseline checks
- Dependency validation

**Usage:**
```bash
python scripts/deployment/validate_deployment.py --environment production
```

### 2. Testing Scripts (`testing/`)

#### `seed_test_data.py`
Comprehensive test data generation for all testing scenarios.

**Features:**
- Multiple organization types and industries
- Complete chart of accounts with GAAP classification
- Realistic transaction patterns
- Valuation projects with assumptions
- Report generation history

**Usage:**
```bash
# Basic usage - 3 organizations
python scripts/testing/seed_test_data.py

# Specific scenarios
python scripts/testing/seed_test_data.py --scenario minimal
python scripts/testing/seed_test_data.py --scenario growth
python scripts/testing/seed_test_data.py --scenario enterprise
python scripts/testing/seed_test_data.py --scenario valuation

# Custom configuration
python scripts/testing/seed_test_data.py --organizations 10 --output custom_data.json
```

#### `manage_test_data.py`
Test data lifecycle management including anonymization and validation.

**Features:**
- Multi-level data anonymization
- Performance dataset creation
- Data backup and restore
- Integrity validation

**Commands:**
```bash
# Anonymize data
python scripts/testing/manage_test_data.py anonymize --input test_data.json --level high

# Create performance dataset
python scripts/testing/manage_test_data.py performance --size 5000 --scaling 20

# Validate data
python scripts/testing/manage_test_data.py validate --file test_data.json

# Backup data
python scripts/testing/manage_test_data.py backup --name "before_major_test"
```

#### `establish_baselines.py`
Establish performance baselines for testing.

**Features:**
- API response time baselines
- Database query performance
- Resource utilization metrics
- Report generation benchmarks

**Usage:**
```bash
python scripts/testing/establish_baselines.py --output baselines.json
```

### 3. Monitoring Scripts (`monitoring/`)

#### `validate_monitoring_system.py`
Validate the monitoring and alerting system configuration.

**Features:**
- Prometheus metrics validation
- Grafana dashboard checks
- Alert rule verification
- Log aggregation testing

**Usage:**
```bash
python scripts/monitoring/validate_monitoring_system.py
```

### 4. Development Scripts (`development/`)

#### `xero_auth_helper.py`
Helper script for Xero OAuth authorization during development.

**Features:**
- OAuth flow initialization
- PKCE security implementation
- Configuration validation
- Troubleshooting guidance

**Usage:**
```bash
python scripts/development/xero_auth_helper.py
```

#### `generate_test_reports.py`
Generate sample financial reports for testing.

**Features:**
- Multiple report formats (PDF, Excel, JSON)
- Various report types (Balance Sheet, Income Statement, etc.)
- Customizable data ranges

**Usage:**
```bash
python scripts/development/generate_test_reports.py --type balance_sheet --format pdf
```

#### `test_data_import.py`
Import data from various sources for testing.

**Features:**
- Xero data import simulation
- CSV/Excel file import
- Data transformation testing

**Usage:**
```bash
python scripts/development/test_data_import.py --source xero
```

### 5. Utility Scripts (root level)

#### `start_backend.sh`
Quick startup script for all backend services.

**Features:**
- Docker dependency checking
- Environment validation
- Service health verification
- Database migration execution

**Usage:**
```bash
./scripts/start_backend.sh
```

#### `setup_test_data.sh`
Automated test data setup for new environments.

**Features:**
- Database initialization
- Test data seeding
- Configuration setup

**Usage:**
```bash
./scripts/setup_test_data.sh
```

## 🛠️ Common Tasks

### Setting Up a New Development Environment
1. Start backend services: `./scripts/start_backend.sh`
2. Generate test data: `python scripts/testing/seed_test_data.py`
3. Validate setup: `python scripts/deployment/validate_deployment.py --environment development`

### Preparing for Production Deployment
1. Validate deployment: `python scripts/deployment/validate_deployment.py --environment production`
2. Run health checks: `python scripts/deployment/production_health_check.py`
3. Deploy: `./scripts/deployment/production_deploy.sh --environment production`

### Creating Test Data for Performance Testing
```bash
# Create large dataset
python scripts/testing/manage_test_data.py performance --size 10000 --scaling 50

# Anonymize for sharing
python scripts/testing/manage_test_data.py anonymize --input perf_data.json --level high
```

## 📊 Performance Benchmarks

Based on our testing, the system should handle:
- **Small datasets** (< 1,000 records): < 1 second
- **Medium datasets** (1,000-10,000 records): < 5 seconds
- **Large datasets** (10,000-100,000 records): < 30 seconds
- **Performance datasets** (100,000+ records): < 2 minutes

## 🔧 Troubleshooting

### Common Issues

#### Docker not starting
```bash
# Check Docker status
docker info

# Reset Docker if needed
docker system prune -a
```

#### Database connection errors
```bash
# Verify database is running
docker-compose ps db

# Check connection
python scripts/deployment/production_health_check.py
```

#### Test data generation failures
```bash
# Validate environment
python scripts/deployment/validate_deployment.py --environment development

# Check logs
tail -f logs/test_data_generation.log
```

## 🤝 Contributing

When adding new scripts:
1. Place in appropriate subdirectory
2. Add comprehensive documentation
3. Include proper error handling
4. Use logging instead of print statements
5. Add entry to this README
6. Include usage examples

## 📚 Related Documentation

- [API Documentation](../docs/API.md)
- [Testing Guide](../tests/README.md)
- [Deployment Guide](../docs/DEPLOYMENT.md)
- [Database Schema](../mcx3d_finance/db/README.md)