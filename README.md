# MCX3D Financial Documentation & Valuation System

A comprehensive financial analytics platform providing NASDAQ-compliant reporting, advanced valuation models, and seamless Xero integration. Built with FastAPI, PostgreSQL, and modern Python architecture.

## 🚀 Quick Start

**Prerequisites**: Docker and Docker Compose

```bash
# Clone and start all services
git clone https://github.com/mcx3d/mcx3d-financials.git
cd mcx3d-financials/v2
docker-compose up --build
```

**Services Available**:
- 🌐 **API**: http://localhost:8000 (FastAPI application)
- 🗄️ **Database**: PostgreSQL with financial data models  
- ⚡ **Cache**: Redis for session management and task queuing
- 🔄 **Workers**: Celery for background processing

## ✨ Key Features

### 📊 Financial Reporting (NASDAQ-Compliant)
- **Balance Sheet**: GAAP-compliant with comparative periods
- **Income Statement**: Comprehensive earnings analysis
- **Cash Flow**: Direct and indirect methods supported
- **Multi-format Output**: PDF, Excel, HTML, JSON

### 💰 Valuation Models
- **DCF Analysis**: Discounted cash flow with sensitivity analysis
- **Multiples Valuation**: Industry comparable analysis
- **Scenario Modeling**: Multiple valuation scenarios

### 🔗 Xero Integration  
- **OAuth 2.0**: Secure authentication and token management
- **Real-time Sync**: Webhook-based data synchronization
- **Comprehensive API**: Chart of accounts, transactions, contacts

### 📈 SaaS Analytics
- **KPI Calculations**: MRR, ARR, churn rate, LTV/CAC
- **Dashboard Analytics**: Real-time business intelligence
- **Performance Metrics**: Growth and retention analysis

## CLI Commands

The project includes a command-line interface (CLI) for various operations. You can run these commands inside the `web` container.

### Generate Financial Reports

The `generate` command group allows you to create financial reports.

*   **Income Statement:**
    ```bash
    docker-compose exec web python -m mcx3d_finance.cli.main generate income-statement --organization-id <org_id> --period <period> --format <pdf|excel|html>
    ```

*   **Balance Sheet:**
    ```bash
    docker-compose exec web python -m mcx3d_finance.cli.main generate balance-sheet --organization-id <org_id> --date <date> --format <pdf|excel|html>
    ```

*   **Cash Flow Statement:**
    ```bash
    docker-compose exec web python -m mcx3d_finance.cli.main generate cash-flow --organization-id <org_id> --period <period> --format <pdf|excel|html>
    ```

### Run Valuation Models

The `valuate` command group allows you to run valuation models.

*   **Discounted Cash Flow (DCF):**
    ```bash
    docker-compose exec web python -m mcx3d_finance.cli.main valuate dcf --config <path_to_config.json>
    ```

*   **Multiples-Based Valuation:**
    ```bash
    docker-compose exec web python -m mcx3d_finance.cli.main valuate multiples --config <path_to_config.json>
    ```

### Synchronize Data

The `sync` command group allows you to synchronize data from external sources.

*   **Sync Xero Data:**
    ```bash
    docker-compose exec web python -m mcx3d_finance.cli.main sync xero --org-id <org_id> [--incremental]
    ```

## 🏗️ Architecture Overview

### Core Components
- **FastAPI**: Modern async web framework
- **SQLAlchemy**: Database ORM with PostgreSQL
- **Celery**: Background task processing
- **Redis**: Caching and task queue
- **Xero API**: Financial data integration

### Project Structure
```
mcx3d_finance/
├── api/           # REST API endpoints
├── core/          # Business logic & calculations
├── db/            # Database models & sessions
├── integrations/  # External API clients (Xero)
├── reporting/     # Multi-format report generation
├── tasks/         # Background processing
└── cli/           # Command-line interface
```

## 📖 Documentation

### 🚀 **[→ Complete Documentation Hub](docs/README.md)**
**Your starting point for all documentation** - organized by audience and use case.

### Quick Navigation

#### 👤 **For Users**
- **[Getting Started](docs/getting-started/README.md)** - Setup and configuration
- **[User Guide](docs/user-guide/README.md)** - Complete user documentation  
- **[Quick Reference](docs/user-guide/quick-reference.md)** - Common commands and workflows

#### 👨‍💻 **For Developers**
- **[Developer Guide](docs/developer/README.md)** - Development documentation hub
- **[API Reference](docs/developer/api-reference.md)** - Complete API documentation
- **[Testing Guide](docs/developer/testing.md)** - Comprehensive testing framework

#### ⚙️ **For Operations**
- **[Operations Guide](docs/operations/README.md)** - Deployment and monitoring
- **[Deployment Guide](docs/operations/deployment.md)** - Docker deployment setup
- **[Security Guide](docs/operations/security.md)** - Security configuration

### By Common Tasks
- **🔧 Setup**: [Getting Started](docs/getting-started/README.md) → [Configuration](docs/getting-started/configuration.md)
- **📊 Generate Reports**: [User Guide](docs/user-guide/overview.md) → [CLI Examples](#cli-commands)
- **🔗 Xero Integration**: [Xero Setup](docs/user-guide/xero-integration.md) → [Configuration](docs/getting-started/configuration.md)
- **🚀 Deploy**: [Operations Guide](docs/operations/README.md) → [Deployment](docs/operations/deployment.md)

## 🧪 Testing & Quality

```bash
# Run all tests
docker-compose exec web pytest

# Run specific test categories
docker-compose exec web pytest -m e2e        # End-to-end tests
docker-compose exec web pytest tests/core/   # Core business logic
docker-compose exec web pytest --cov=mcx3d_finance  # With coverage

# Code quality
docker-compose exec web black mcx3d_finance/  # Format code
docker-compose exec web flake8 mcx3d_finance/ # Lint code
```

## 🚀 Development Workflow

### Environment Setup
1. **Clone & Setup**: Follow [Quick Start](#-quick-start) above
2. **Configuration**: Copy `config.yml.example` to `config.yml` and configure
3. **Environment Variables**: Set required variables (see [Configuration Guide](docs/CONFIGURATION_GUIDE.md))
4. **Database**: Migrations run automatically on startup

### Making Changes
1. **Database Changes**: Use Alembic migrations (`alembic revision --autogenerate`)
2. **API Changes**: Update relevant documentation
3. **Testing**: Add tests for new functionality
4. **Quality**: Run linting and formatting before commits

## 🔒 Security & Compliance

- **GAAP Compliance**: Financial statements follow GAAP standards
- **NASDAQ Ready**: Reports meet NASDAQ listing requirements  
- **OAuth 2.0**: Secure Xero integration
- **Data Encryption**: Sensitive data encrypted at rest
- **API Security**: Rate limiting and input validation

## 🔧 Configuration

### Environment Variables
```bash
DATABASE_URL=**********************************/mcx3d_db
REDIS_URL=redis://redis:6379/0
XERO_CLIENT_ID=your_xero_client_id
XERO_CLIENT_SECRET=your_xero_client_secret
```

### Configuration Files
- `config.yml`: Application settings
- `alembic.ini`: Database migrations
- `pytest.ini`: Test configuration
- `pyproject.toml`: Python project metadata

## 📈 Current Status

- ✅ **Phase 1**: Core API, Xero integration, basic reporting
- 🔄 **Phase 2**: Enhanced data processing, validation, transformation
- 📋 **Phase 3**: Advanced analytics, ML-based insights (planned)

### Recent Updates
- Enhanced data validation and processing pipeline
- Improved GAAP compliance in financial statements
- Advanced transaction classification
- Comprehensive API documentation

## 🤝 Contributing

1. **Fork** the repository
2. **Create** feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** changes (`git commit -m 'Add amazing feature'`)
4. **Push** to branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

## 📞 Support

- **📚 Documentation**: [Complete Documentation Hub](docs/README.md) - your starting point for all guides
- **🐛 Issues**: GitHub Issues for bug reports and feature requests  
- **🔌 API Questions**: [API Reference](docs/developer/api-reference.md) - comprehensive endpoint documentation
- **⚙️ Setup Help**: [Getting Started Guide](docs/getting-started/README.md) - configuration and deployment
- **🧪 Testing**: [Testing Guide](docs/developer/testing.md) - development and validation
- **🚀 Operations**: [Operations Guide](docs/operations/README.md) - deployment and monitoring

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Built with ❤️ for modern financial analytics**