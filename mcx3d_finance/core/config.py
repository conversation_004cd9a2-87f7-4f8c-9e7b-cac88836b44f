import os
import sys
import yaml
from typing import Dict, Any, Optional
from pydantic_settings import BaseSettings as _BaseSettings
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


class Settings(_BaseSettings):
    """Application settings with environment variable support."""

    # Database
    database_url: Optional[str] = None  # Must be set via environment variable

    # Redis
    redis_url: str = "redis://localhost:6379/0"

    # Xero API
    xero_client_id: Optional[str] = None
    xero_client_secret: Optional[str] = None
    xero_webhook_key: Optional[str] = None
    xero_redirect_uri: str = "http://localhost:8000/api/auth/xero/callback"
    xero_scopes: str = "accounting.transactions accounting.contacts accounting.reports.read accounting.settings"
    
    # Security - Basic
    secret_key: Optional[str] = None
    encryption_key: Optional[str] = None
    jwt_algorithm: str = "HS256"
    access_token_expire_minutes: int = 15  # Reduced for security
    
    # Security - Session Management
    refresh_token_expire_days: int = 30
    max_sessions_per_user: int = 5
    session_idle_timeout_minutes: int = 60
    
    # Security - Account Lockout
    max_login_attempts: int = 5
    lockout_duration_minutes: int = 30
    
    # Security - MFA
    mfa_code_validity_minutes: int = 5
    mfa_issuer_name: str = "MCX3D Finance"
    
    # Security - Rate Limiting
    rate_limit_default: int = 100  # requests per minute
    rate_limit_auth: int = 5  # login attempts per 5 minutes
    
    # Security - Audit & Monitoring
    audit_log_file: str = "/var/log/mcx3d_audit.log"
    audit_retention_days: int = 365
    enable_audit_encryption: bool = True
    
    # Security - Data Protection
    enable_field_encryption: bool = True
    key_rotation_days: int = 90
    data_retention_days: int = 2555  # 7 years
    
    # Application
    debug: bool = False
    log_level: str = "INFO"
    environment: str = "production"

    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "allow"  # Allow extra fields from environment


def detect_environment() -> str:
    """Detect current environment from various sources."""
    # Check environment variable first
    env = os.getenv("ENVIRONMENT") or os.getenv("ENV") or os.getenv("APP_ENV")
    if env:
        return env.lower()
    
    # Check for common deployment indicators
    if os.getenv("KUBERNETES_SERVICE_HOST"):
        return "production"
    elif os.getenv("DOCKER_CONTAINER"):
        return "staging"
    elif os.path.exists(".git"):
        return "development"
    elif "pytest" in sys.modules:
        return "testing"
    
    # Default to production for safety
    return "production"


def load_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """Load configuration from YAML file with environment-specific support."""
    try:
        # Determine config file path
        if config_path is None:
            environment = detect_environment()
            
            # Try environment-specific config first
            env_config_path = f"config/{environment}.yml"
            if os.path.exists(env_config_path):
                config_path = env_config_path
                logger.info(f"Loading environment-specific config: {config_path}")
            else:
                # Fall back to main config file
                config_path = "config.yml"
                if os.path.exists(config_path):
                    logger.info(f"Loading default config: {config_path}")
                else:
                    logger.warning("No configuration file found, using defaults only")
                    return {}
        
        if os.path.exists(config_path):
            with open(config_path, "r") as f:
                config_data = yaml.safe_load(f) or {}
                logger.info(f"Successfully loaded config from {config_path}")
                return config_data
        else:
            logger.warning(f"Config file {config_path} not found, using defaults")
            return {}
    except yaml.YAMLError as e:
        logger.error(f"Error parsing config file {config_path}: {e}")
        return {}
    except Exception as e:
        logger.error(f"Unexpected error loading config from {config_path}: {e}")
        return {}


# Global settings instance
settings = Settings()
config = load_config()


def get_database_url() -> str:
    """Get database URL with proper precedence: Environment → Config → Test Default."""
    # Special case for testing
    if "pytest" in sys.modules:
        test_url = os.getenv("TEST_DATABASE_URL", "sqlite:///./test.db")
        logger.debug(f"Using test database URL: {test_url[:20]}...")
        return test_url

    # Precedence: Environment variable → Config file
    database_url = settings.database_url
    if not database_url:
        database_url = config.get("database", {}).get("url")
    
    if not database_url:
        raise ValueError(
            "DATABASE_URL must be set in environment variables or config file. "
            "Example: DATABASE_URL=postgresql://user:password@host:port/database"
        )
    
    # Enhanced security validation - check for insecure defaults and patterns
    insecure_patterns = [
        "user:password@localhost",
        "admin:admin@",
        "root:root@",
        "test:test@",
        "example.com",
        "password123",
        "123456",
        "admin@localhost",
        "postgres:postgres@",
        "mysql:mysql@",
        "user:user@",
        "default:default@",
        "changeme",
        "secret",
        "demo:demo@"
    ]
    
    for pattern in insecure_patterns:
        if pattern in database_url.lower():
            raise ValueError(
                f"Insecure database URL detected (contains '{pattern}'). "
                "Please set a secure DATABASE_URL with strong credentials."
            )
    
    # Additional validation for URL format and components
    try:
        from urllib.parse import urlparse
        parsed = urlparse(database_url)
        
        # Validate scheme
        if parsed.scheme not in ['postgresql', 'mysql', 'sqlite', 'mariadb', 'oracle']:
            raise ValueError(f"Unsupported database scheme: {parsed.scheme}")
        
        # For non-SQLite databases, ensure credentials are present
        if parsed.scheme != 'sqlite':
            if not parsed.username or not parsed.password:
                raise ValueError("Database URL must include username and password for non-SQLite databases")
            
            # Check password strength (basic validation)
            if len(parsed.password) < 8:
                raise ValueError("Database password must be at least 8 characters long")
            
            # Ensure hostname is specified
            if not parsed.hostname:
                raise ValueError("Database URL must include a hostname")
                
    except Exception as e:
        if "Unsupported database scheme" in str(e) or "Database URL must" in str(e) or "Database password must" in str(e):
            raise  # Re-raise our custom validation errors
        else:
            raise ValueError(f"Invalid database URL format: {e}")
    
    logger.debug(f"Using database URL: {database_url[:20]}...")
    return database_url


def get_xero_config() -> Dict[str, Any]:
    """Get Xero configuration with proper precedence: Environment → Config → Defaults."""
    xero_config_section = config.get("xero", {})
    
    xero_config = {
        "client_id": settings.xero_client_id or xero_config_section.get("client_id"),
        "client_secret": settings.xero_client_secret or xero_config_section.get("client_secret"),
        "webhook_key": settings.xero_webhook_key or xero_config_section.get("webhook_key"),
        "redirect_uri": settings.xero_redirect_uri or xero_config_section.get(
            "redirect_uri", "http://localhost:8000/api/auth/xero/callback"
        ),
        "scopes": settings.xero_scopes or xero_config_section.get(
            "scopes", "accounting.transactions accounting.contacts accounting.reports.read accounting.settings"
        ),
    }
    
    # Validate required fields
    if not xero_config["client_id"]:
        raise ValueError(
            "XERO_CLIENT_ID must be set in environment variables or config file. "
            "Get your client ID from https://developer.xero.com/myapps"
        )
    
    if not xero_config["client_secret"]:
        raise ValueError(
            "XERO_CLIENT_SECRET must be set in environment variables or config file. "
            "Get your client secret from https://developer.xero.com/myapps"
        )
    
    # Security validation - check for placeholder values
    placeholder_patterns = ["YOUR_CLIENT_ID", "YOUR_CLIENT_SECRET", "CHANGE_ME", "EXAMPLE"]
    
    for field_name, field_value in xero_config.items():
        if field_value and any(pattern.lower() in str(field_value).lower() for pattern in placeholder_patterns):
            raise ValueError(
                f"Placeholder value detected in xero.{field_name}: {field_value}. "
                "Please set actual Xero credentials."
            )
    
    return xero_config


def get_xero_webhook_key() -> Optional[str]:
    """Get Xero webhook key."""
    return settings.xero_webhook_key or config.get("xero", {}).get("webhook_key")


def get_security_config() -> Dict[str, Any]:
    """Get security configuration with proper precedence and validation."""
    security_config_section = config.get("security", {})
    
    # Get secret key with proper precedence: Environment → Config
    secret_key = settings.secret_key or security_config_section.get("secret_key")
    
    # Validate secret key
    if not secret_key:
        raise ValueError(
            "SECRET_KEY must be set in environment variables or config file. "
            "Use 'python -m mcx3d_finance.utils.generate_keys' to generate a secure key."
        )
    
    if len(secret_key) < 32:
        raise ValueError(
            "SECRET_KEY must be at least 32 characters long for security. "
            f"Current length: {len(secret_key)}. Use 'python -m mcx3d_finance.utils.generate_keys' to generate a secure key."
        )
    
    # Security validation - check for insecure defaults
    insecure_keys = [
        "your-secret-key-here",
        "changeme", 
        "secret", 
        "password", 
        "12345",
        "your-secret-key-here-minimum-32-characters-long"
    ]
    
    if any(insecure_key in secret_key.lower() for insecure_key in insecure_keys):
        raise ValueError(
            "Insecure SECRET_KEY detected. Please generate a secure key using: "
            "'python -m mcx3d_finance.utils.generate_keys'"
        )    
    
    # Get encryption key with validation
    encryption_key = settings.encryption_key or security_config_section.get("encryption_key")
    if not encryption_key:
        logger.warning(
            "ENCRYPTION_KEY not set. Sensitive data encryption will not be available. "
            "Use 'python -m mcx3d_finance.utils.generate_keys --type fernet' to generate one."
        )
    
    return {
        # Basic Security
        "secret_key": secret_key,
        "encryption_key": encryption_key,
        "algorithm": settings.jwt_algorithm or security_config_section.get("algorithm", "HS256"),
        "access_token_expire_minutes": settings.access_token_expire_minutes or security_config_section.get("access_token_expire_minutes", 15),
        
        # Session Management
        "refresh_token_expire_days": settings.refresh_token_expire_days or security_config_section.get("refresh_token_expire_days", 30),
        "max_sessions_per_user": settings.max_sessions_per_user or security_config_section.get("max_sessions_per_user", 5),
        "session_idle_timeout_minutes": settings.session_idle_timeout_minutes or security_config_section.get("session_idle_timeout_minutes", 60),
        
        # Account Lockout
        "max_login_attempts": settings.max_login_attempts or security_config_section.get("max_login_attempts", 5),
        "lockout_duration_minutes": settings.lockout_duration_minutes or security_config_section.get("lockout_duration_minutes", 30),
        
        # MFA
        "mfa_code_validity_minutes": settings.mfa_code_validity_minutes or security_config_section.get("mfa_code_validity_minutes", 5),
        "mfa_issuer_name": settings.mfa_issuer_name or security_config_section.get("mfa_issuer_name", "MCX3D Finance"),
        
        # Rate Limiting
        "rate_limits": {
            "api_default": {"calls": settings.rate_limit_default or 100, "period": 60},
            "api_authenticated": {"calls": 1000, "period": 60},
            "auth_login": {"calls": settings.rate_limit_auth or 5, "period": 300},
            "auth_register": {"calls": 3, "period": 3600},
            "password_reset": {"calls": 3, "period": 3600},
            "report_generation": {"calls": 10, "period": 300},
            "data_export": {"calls": 5, "period": 3600},
        },
        
        # Audit & Monitoring
        "audit_log_file": settings.audit_log_file or security_config_section.get("audit_log_file", "/var/log/mcx3d_audit.log"),
        "audit_retention_days": settings.audit_retention_days or security_config_section.get("audit_retention_days", 365),
        "audit_tamper_detection": settings.enable_audit_encryption or security_config_section.get("enable_audit_encryption", True),
        "audit_correlation": True,
        
        # Data Protection
        "enable_field_encryption": settings.enable_field_encryption or security_config_section.get("enable_field_encryption", True),
        "enable_key_rotation": settings.key_rotation_days > 0,
        "key_rotation_days": settings.key_rotation_days or security_config_section.get("key_rotation_days", 90),
        "data_retention_days": settings.data_retention_days or security_config_section.get("data_retention_days", 2555),
        
        # Redis Configuration  
        "redis_url": settings.redis_url or config.get("redis", {}).get("url", "redis://localhost:6379/0"),
        
        # Environment
        "environment": detect_environment(),
        "debug": settings.debug,
    }


def validate_startup_config() -> None:
    """Validate configuration at application startup."""
    try:
        from ..validation.config_validator import ConfigurationValidator
        
        logger.info("Validating application configuration...")
        
        # Create complete config for validation
        complete_config = {
            "database": {"url": get_database_url()},
            "xero": get_xero_config(),
            "security": get_security_config(),
            "redis": {"url": settings.redis_url or config.get("redis", {}).get("url", "redis://localhost:6379/0")},
            "environment": detect_environment()
        }
        
        # Add config file sections
        for section, section_config in config.items():
            if section not in complete_config:
                complete_config[section] = section_config
        
        validator = ConfigurationValidator()
        is_valid, errors, warnings = validator.validate_configuration(complete_config)
        
        # Log warnings
        for warning in warnings:
            logger.warning(f"Configuration warning: {warning}")
        
        # Fail on errors
        if not is_valid:
            error_msg = "Configuration validation failed:\n" + "\n".join(f"  - {error}" for error in errors)
            logger.error(error_msg)
            raise ValueError(f"Invalid configuration detected. {len(errors)} errors found.")
        
        logger.info("✅ Configuration validation passed")
        
    except ImportError as e:
        logger.warning(f"Configuration validator not available: {e}")
    except Exception as e:
        logger.error(f"Configuration validation failed: {e}")
        raise


def get_config_health_status() -> Dict[str, Any]:
    """Get configuration health status for monitoring."""
    try:
        environment = detect_environment()
        config_issues = []
        config_warnings = []
        
        # Check critical configuration items
        try:
            get_database_url()
        except Exception as e:
            config_issues.append(f"Database configuration: {e}")
        
        try:
            get_xero_config()
        except Exception as e:
            config_issues.append(f"Xero configuration: {e}")
        
        try:
            get_security_config()
        except Exception as e:
            config_issues.append(f"Security configuration: {e}")
        
        # Check environment-specific config file
        env_config_path = f"config/{environment}.yml"
        config_file_status = "environment-specific" if os.path.exists(env_config_path) else "default"
        
        if not os.path.exists(env_config_path) and environment == "production":
            config_warnings.append("No production-specific configuration file found")
        
        return {
            "status": "healthy" if not config_issues else "unhealthy",
            "environment": environment,
            "config_file": config_file_status,
            "issues": config_issues,
            "warnings": config_warnings,
            "checks_passed": len(config_issues) == 0,
            "total_issues": len(config_issues),
            "total_warnings": len(config_warnings)
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "checks_passed": False
        }
