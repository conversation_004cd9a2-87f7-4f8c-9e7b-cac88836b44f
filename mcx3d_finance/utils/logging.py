import logging
import sys

# Create a logger
logger = logging.getLogger("mcx3d_finance")
logger.setLevel(logging.INFO)

# Create a handler to write to standard output
handler = logging.StreamHandler(sys.stdout)

# Create a formatter and set it for the handler
formatter = logging.Formatter(
    "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
handler.setFormatter(formatter)

# Add the handler to the logger
# Avoid adding handlers if they already exist from a previous import
if not logger.handlers:
    logger.addHandler(handler)
