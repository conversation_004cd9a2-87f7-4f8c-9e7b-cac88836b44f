#!/usr/bin/env python3
"""
Explore xero_python package structure to understand available imports.
"""

def explore_xero_python():
    """Explore the xero_python package structure."""
    
    try:
        import xero_python
        print(f"✅ xero_python version: {getattr(xero_python, '__version__', 'unknown')}")
        
        # Check api_client
        print("✅ ApiClient import successful")
        
        # Check what's in api_client
        import xero_python.api_client as api_client_module
        print(f"api_client module contents: {dir(api_client_module)}")
        
        # Check configuration
        try:
            print("✅ Configuration import successful")
        except ImportError as e:
            print(f"❌ Configuration import failed: {e}")
        
        # Check for OAuth2 related classes
        try:
            from xero_python.api_client.oauth2 import OAuth2Token
            print("✅ OAuth2Token from api_client.oauth2 import successful")
        except ImportError:
            try:
                from xero_python.oauth2 import OAuth2Token
                print("✅ OAuth2Token from oauth2 import successful")
            except ImportError:
                try:
                    # Check if it's in the main api_client module
                    print("✅ OAuth2Token from api_client import successful")
                except ImportError as e:
                    print(f"❌ OAuth2Token import failed: {e}")
        
        # Check identity API
        try:
            print("✅ IdentityApi import successful")
        except ImportError as e:
            print(f"❌ IdentityApi import failed: {e}")
        
        # Check exceptions
        try:
            print("✅ Exceptions import successful")
        except ImportError as e:
            print(f"❌ Exceptions import failed: {e}")
            
        # List all available modules
        import pkgutil
        print("\nAvailable xero_python modules:")
        for importer, modname, ispkg in pkgutil.iter_modules(xero_python.__path__, xero_python.__name__ + "."):
            print(f"  - {modname}")
        
        return True
        
    except ImportError as e:
        print(f"❌ xero_python import failed: {e}")
        return False

if __name__ == "__main__":
    explore_xero_python()
