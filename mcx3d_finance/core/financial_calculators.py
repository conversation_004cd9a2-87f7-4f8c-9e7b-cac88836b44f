"""
Financial calculation engine for MCX3D Finance system, compliant with UK standards.
Implements core financial statement generation and KPI calculations.
"""

from typing import Dict, List
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime
import pandas as pd
import logging

logger = logging.getLogger(__name__)


class UKFinancialCalculator:
    """Core financial calculations for UK statements and KPIs."""

    def __init__(self):
        self.precision = Decimal("0.01")

    def _round_currency(self, value: float) -> Decimal:
        """Round currency values to 2 decimal places."""
        return Decimal(str(value)).quantize(self.precision, rounding=ROUND_HALF_UP)

    def generate_profit_and_loss(
        self,
        transactions_df: pd.DataFrame,
        period_start: datetime,
        period_end: datetime,
    ) -> Dict:
        """Generate a UK-compliant Profit and Loss account from transaction data."""
        try:
            period_transactions = transactions_df[
                (transactions_df["date"] >= period_start)
                & (transactions_df["date"] <= period_end)
            ]

            turnover_accounts = period_transactions[
                period_transactions["account_type"] == "REVENUE"
            ]
            total_turnover = self._round_currency(turnover_accounts["amount"].sum())

            expense_accounts = period_transactions[
                period_transactions["account_type"] == "EXPENSE"
            ]
            
            cost_of_sales_accounts = expense_accounts[
                expense_accounts["account_class"] == "COST_OF_SALES"
            ]
            cost_of_sales = self._round_currency(cost_of_sales_accounts["amount"].sum())

            distribution_costs_accounts = expense_accounts[
                expense_accounts["account_class"] == "DISTRIBUTION_COSTS"
            ]
            distribution_costs = self._round_currency(distribution_costs_accounts["amount"].sum())

            admin_expenses_accounts = expense_accounts[
                expense_accounts["account_class"] == "ADMINISTRATIVE_EXPENSES"
            ]
            admin_expenses = self._round_currency(admin_expenses_accounts["amount"].sum())

            gross_profit = total_turnover - cost_of_sales
            operating_profit = gross_profit - distribution_costs - admin_expenses
            profit_for_year = operating_profit  # Simplified for now

            return {
                "period_start": period_start.isoformat(),
                "period_end": period_end.isoformat(),
                "turnover": {
                    "total_turnover": float(total_turnover),
                },
                "cost_of_sales": float(cost_of_sales),
                "gross_profit": float(gross_profit),
                "distribution_costs": float(distribution_costs),
                "administrative_expenses": float(admin_expenses),
                "operating_profit": float(operating_profit),
                "profit_for_financial_year": float(profit_for_year),
            }
        except Exception as e:
            logger.error(f"Error generating Profit and Loss account: {e}")
            raise

    def calculate_saas_kpis(
        self,
        subscription_data: pd.DataFrame,
        period_start: datetime,
        period_end: datetime,
    ) -> Dict:
        """Calculate SaaS-specific KPIs."""
        try:
            active_subscriptions = subscription_data[
                subscription_data["status"] == "active"
            ]
            mrr = self._round_currency(active_subscriptions["monthly_value"].sum())
            arr = mrr * 12
            total_customers = len(active_subscriptions)
            arpu = mrr / total_customers if total_customers > 0 else Decimal("0")

            churned_customers = len(
                subscription_data[
                    (subscription_data["churn_date"] >= period_start)
                    & (subscription_data["churn_date"] <= period_end)
                ]
            )
            churn_rate = (
                (churned_customers / total_customers * 100)
                if total_customers > 0
                else 0
            )

            return {
                "period_start": period_start.isoformat(),
                "period_end": period_end.isoformat(),
                "revenue_metrics": {
                    "mrr": float(mrr),
                    "arr": float(arr),
                    "arpu": float(arpu),
                },
                "customer_metrics": {
                    "total_customers": total_customers,
                    "churned_customers": churned_customers,
                    "churn_rate": churn_rate,
                },
            }
        except Exception as e:
            logger.error(f"Error calculating SaaS KPIs: {e}")
            raise


class DCFCalculator:
    """Discounted Cash Flow valuation calculator."""

    def __init__(self, discount_rate: float = 0.10):
        self.discount_rate = discount_rate

    def calculate_dcf_valuation(
        self, cash_flows: List[float], terminal_growth_rate: float = 0.02
    ) -> Dict:
        """Calculate DCF valuation with terminal value."""
        try:
            if not cash_flows:
                raise ValueError("Cash flows list cannot be empty")

            pv_cash_flows = [
                cf / ((1 + self.discount_rate) ** i) for i, cf in enumerate(cash_flows, 1)
            ]

            terminal_cf = cash_flows[-1] * (1 + terminal_growth_rate)
            terminal_value = terminal_cf / (self.discount_rate - terminal_growth_rate)
            pv_terminal = terminal_value / ((1 + self.discount_rate) ** len(cash_flows))

            enterprise_value = sum(pv_cash_flows) + pv_terminal

            return {
                "projected_cash_flows": cash_flows,
                "pv_cash_flows": pv_cash_flows,
                "terminal_value": terminal_value,
                "pv_terminal_value": pv_terminal,
                "enterprise_value": enterprise_value,
                "discount_rate": self.discount_rate,
                "terminal_growth_rate": terminal_growth_rate,
            }
        except Exception as e:
            logger.error(f"Error calculating DCF valuation: {e}")
            raise
