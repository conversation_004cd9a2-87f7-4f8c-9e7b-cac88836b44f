# MCX3D Financial Platform - Production Monitoring Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the MCX3D Financial Platform with enterprise-grade production monitoring, achieving a 90%+ monitoring score through structured logging, metrics collection, alerting, and business intelligence.

## Monitoring Stack Components

### Core Monitoring Services
- **Prometheus**: Metrics collection and storage
- **Grafana**: Visualization dashboards and alerting
- **Loki**: Log aggregation and analysis
- **AlertManager**: Multi-channel alert routing
- **Node Exporter**: System resource metrics
- **cAdvisor**: Container performance metrics

### Application Monitoring Features
- **Structured Logging**: JSON logs with correlation IDs
- **Business KPI Tracking**: Financial metrics and anomaly detection
- **Real-time Alerting**: Slack, email, PagerDuty integration
- **Audit Trails**: Immutable financial transaction logging
- **Health Monitoring**: Comprehensive system health checks
- **Performance Tracking**: API response times and throughput

## Quick Start Deployment

### 1. Prerequisites

```bash
# Ensure Docker and Docker Compose are installed
docker --version
docker-compose --version

# Ensure required ports are available
# Main app: 8000, Metrics: 8001, Prometheus: 9090, Grafana: 3000
```

### 2. Environment Configuration

Create monitoring environment file:

```bash
# .env.monitoring
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL
PAGERDUTY_INTEGRATION_KEY=your_pagerduty_integration_key
SMTP_SERVER=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_smtp_password
ALERT_FROM_EMAIL=<EMAIL>
ALERT_TO_EMAILS=<EMAIL>,<EMAIL>
```

### 3. Install Dependencies

```bash
# Install monitoring dependencies
pip install -r requirements.txt

# Verify monitoring packages
python -c "import structlog, prometheus_client; print('Monitoring dependencies installed')"
```

### 4. Deploy Monitoring Stack

```bash
# Start the complete monitoring stack
docker-compose -f docker-compose.yml -f docker-compose.monitoring.yml up -d

# Verify all services are running
docker-compose ps

# Check service health
curl http://localhost:9090/api/v1/targets  # Prometheus targets
curl http://localhost:3000/api/health      # Grafana health
curl http://localhost:3100/ready           # Loki readiness
```

### 5. Start Application with Monitoring

```bash
# Option 1: Use the monitoring-enabled main application
python mcx3d_finance/main_with_monitoring.py

# Option 2: Use existing main.py with monitoring middleware
# (Requires integration of monitoring middleware)
python mcx3d_finance/main.py
```

## Service Endpoints

### Application Endpoints
- **Main Application**: http://localhost:8000
- **Metrics Endpoint**: http://localhost:8001/metrics
- **Health Checks**: http://localhost:8000/health/
- **Comprehensive Health**: http://localhost:8000/health/comprehensive
- **Business Health**: http://localhost:8000/health/business
- **Monitoring Status**: http://localhost:8000/monitoring/status

### Monitoring Services
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/mcx3d_admin_2024)
- **Loki**: http://localhost:3100
- **AlertManager**: http://localhost:9093
- **Node Exporter**: http://localhost:9100
- **cAdvisor**: http://localhost:8080

## Configuration Details

### Prometheus Configuration

Key metrics being collected:

```yaml
# Business Metrics
- mcx3d_reports_generated_total
- mcx3d_valuation_processing_seconds
- mcx3d_active_report_jobs
- mcx3d_valuation_amount_usd

# System Metrics
- mcx3d_http_request_duration_seconds
- mcx3d_db_connections_active
- mcx3d_memory_usage_bytes
- mcx3d_errors_total

# User Activity
- mcx3d_user_activities_total
- mcx3d_active_users_current
```

### Alert Rules

Critical alerts configured:

- **Application Down**: Application unavailable for >1 minute
- **High Error Rate**: >10% error rate for >2 minutes
- **Slow Valuation Processing**: >30 seconds P95 processing time
- **Database Issues**: Connection failures or high usage
- **System Resources**: CPU >80%, Memory >85%, Disk >90%

### Grafana Dashboards

Pre-configured dashboards available:

1. **MCX3D Business Dashboard**
   - Daily report generation metrics
   - Valuation processing performance
   - User activity and engagement
   - Revenue impact tracking

2. **System Performance Dashboard**
   - API response times and throughput
   - Database performance metrics
   - System resource utilization
   - Error rates and availability

3. **Financial Audit Dashboard**
   - Transaction audit trails
   - Compliance monitoring
   - Data quality metrics
   - Security event tracking

## Integration Examples

### Adding Monitoring to Existing Endpoints

```python
from mcx3d_finance.monitoring import (
    monitor_performance,
    reporting_logger,
    audit_report_creation
)

@monitor_performance('dcf_valuation')
async def generate_dcf_valuation(data: Dict[str, Any], user_id: str):
    """Generate DCF valuation with monitoring."""
    
    # Log business event
    reporting_logger.log_business_event(
        'dcf_valuation_started',
        organization_id=data.get('organization_id'),
        model_complexity=data.get('complexity', 'standard')
    )
    
    try:
        # Generate valuation
        result = perform_dcf_calculation(data)
        
        # Audit the valuation
        await audit_report_creation({
            'report_type': 'dcf_valuation',
            'organization_id': data.get('organization_id'),
            'valuation_amount': result.get('valuation'),
            'currency': 'USD',
            'duration_ms': result.get('processing_time_ms')
        }, user_id)
        
        return result
        
    except Exception as e:
        # Log error with context
        reporting_logger.log_error(
            e, 
            'dcf_valuation_failed',
            organization_id=data.get('organization_id'),
            user_id=user_id
        )
        raise
```

### Custom Business Metrics

```python
from mcx3d_finance.monitoring.metrics import (
    Counter, 
    Histogram,
    monitor_business_event
)

# Define custom metrics
CUSTOM_FINANCIAL_METRIC = Counter(
    'mcx3d_custom_financial_operations_total',
    'Custom financial operations',
    ['operation_type', 'status']
)

def track_custom_operation(operation_type: str, success: bool):
    """Track custom business operation."""
    CUSTOM_FINANCIAL_METRIC.labels(
        operation_type=operation_type,
        status='success' if success else 'failure'
    ).inc()
    
    monitor_business_event(
        'custom_operation',
        operation_type=operation_type,
        success=success
    )
```

### Custom Alerts

```python
from mcx3d_finance.monitoring import (
    alert_manager,
    AlertSeverity
)

async def check_business_thresholds():
    """Custom business threshold monitoring."""
    
    # Check daily report generation
    daily_reports = get_daily_report_count()
    
    if daily_reports < 50:
        await alert_manager.trigger_alert(
            AlertSeverity.WARNING,
            "Low Daily Report Generation",
            f"Only {daily_reports} reports generated today",
            {
                'component': 'business_intelligence',
                'daily_report_count': daily_reports,
                'threshold': 50
            },
            'low_daily_reports'
        )
```

## Troubleshooting

### Common Issues

**1. Metrics Not Appearing**
```bash
# Check metrics endpoint
curl http://localhost:8001/metrics | grep mcx3d

# Verify Prometheus targets
curl http://localhost:9090/api/v1/targets

# Check application logs
docker-compose logs web
```

**2. Alerts Not Firing**
```bash
# Check AlertManager configuration
curl http://localhost:9093/api/v1/alerts

# Verify webhook URLs
curl -X POST $SLACK_WEBHOOK_URL -d '{"text":"Test message"}'

# Check alert rules
curl http://localhost:9090/api/v1/rules
```

**3. Dashboard Data Missing**
```bash
# Verify Grafana data sources
curl http://localhost:3000/api/datasources

# Check Prometheus connectivity
curl http://localhost:3000/api/datasources/proxy/1/api/v1/query?query=up

# Restart Grafana if needed
docker-compose restart grafana
```

**4. High Resource Usage**
```bash
# Monitor container resources
docker stats

# Adjust resource limits in docker-compose.monitoring.yml
# Reduce metrics retention in prometheus.yml
# Optimize query frequency
```

### Performance Optimization

**Reduce Metrics Cardinality**:
```python
# Instead of this (high cardinality):
USER_ACTIONS.labels(user_id=user_id, action=action).inc()

# Use this (lower cardinality):
USER_ACTIONS.labels(action=action).inc()
```

**Optimize Alert Rules**:
```yaml
# Use longer evaluation windows for non-critical alerts
- alert: NonCriticalAlert
  expr: metric > threshold
  for: 10m  # Instead of 1m
```

## Production Deployment Checklist

### Pre-Deployment
- [ ] Environment variables configured
- [ ] Alert channels tested (Slack, email, PagerDuty)
- [ ] Grafana dashboards imported
- [ ] Prometheus targets validated
- [ ] Log aggregation working
- [ ] Resource limits set appropriately

### Post-Deployment
- [ ] All services healthy
- [ ] Metrics flowing to Prometheus
- [ ] Logs aggregating in Loki
- [ ] Dashboards displaying data
- [ ] Alert rules firing correctly
- [ ] Business KPIs collecting data
- [ ] Audit trails recording events

### Monitoring Validation
- [ ] Generate test report → Check metrics
- [ ] Trigger test alert → Verify delivery
- [ ] Create user activity → Verify audit logs
- [ ] Simulate error → Check error tracking
- [ ] Load test → Monitor performance metrics

## Maintenance

### Daily Tasks
- Monitor dashboard for anomalies
- Review critical alerts
- Check system resource usage
- Verify backup integrity

### Weekly Tasks
- Review business KPI trends
- Update alert thresholds based on data
- Clean up old logs and metrics
- Update monitoring configurations

### Monthly Tasks
- Generate compliance audit reports
- Review and optimize monitoring costs
- Update monitoring documentation
- Plan monitoring improvements

## Security Considerations

### Monitoring Data Security
- Encrypt logs and metrics in transit
- Secure Grafana with proper authentication
- Limit access to monitoring endpoints
- Regular security updates for monitoring stack

### Audit Compliance
- Immutable audit trail storage
- Regular audit trail integrity verification
- Compliance report generation
- Secure backup of audit data

## Scaling Considerations

### High Availability
- Deploy Prometheus in HA mode
- Use Grafana clustering
- Implement Loki clustering
- Set up monitoring data replication

### Performance Scaling
- Use Prometheus federation
- Implement metrics sharding
- Optimize dashboard queries
- Use caching for frequently accessed data

## Support and Maintenance

### Log Locations
- Application logs: `/var/log/mcx3d/`
- Container logs: `docker-compose logs [service]`
- Monitoring logs: Check individual service containers

### Backup Procedures
- Prometheus data: `/prometheus` volume
- Grafana configuration: `/var/lib/grafana` volume
- Loki logs: `/loki` volume
- Alert configuration: `./monitoring/` directory

### Monitoring the Monitoring
- Monitor Prometheus uptime
- Track Grafana performance
- Monitor log ingestion rates
- Alert on monitoring service failures

This comprehensive monitoring deployment provides enterprise-grade observability for the MCX3D Financial Platform, achieving the target 90%+ monitoring score through structured implementation of metrics, logging, alerting, and business intelligence capabilities.