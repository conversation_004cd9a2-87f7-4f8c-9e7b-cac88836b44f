"""
Streaming processors for large Xero API responses.

Provides memory-efficient processing of large datasets from Xero API
by implementing pagination, batching, and streaming patterns.
"""

import logging
from typing import Dict, List, Optional, Any, Generator, Iterator
from datetime import datetime, timedelta
from xero_python.api_client import ApiClient
from xero_python.accounting import AccountingApi

from mcx3d_finance.core.config import get_xero_config
from .xero_retry import xero_api_call
from .xero_rate_limiter import xero_rate_limiter

logger = logging.getLogger(__name__)


class XeroStreamingClient:
    """Memory-efficient Xero API client with streaming support."""
    
    def __init__(self, organization_id: int, api_client: ApiClient = None):
        self.organization_id = organization_id
        self.api_client = api_client
        self.accounting_api = AccountingApi(api_client) if api_client else None
        self.tenant_id = None  # Will be set when needed
        
        # Streaming configuration
        self.batch_size = 100  # Process 100 records at a time
        self.page_size = 100   # API page size
        self.max_memory_mb = 50  # Maximum memory usage per batch
    
    def set_client_details(self, api_client: ApiClient, tenant_id: str):
        """Set API client and tenant ID for streaming operations."""
        self.api_client = api_client
        self.accounting_api = AccountingApi(api_client)
        self.tenant_id = tenant_id
    
    @xero_api_call(max_retries=3, circuit_breaker_threshold=5)
    def stream_accounts(self, batch_size: Optional[int] = None) -> Generator[List[Dict[str, Any]], None, None]:
        """
        Stream chart of accounts in batches to reduce memory usage.
        
        Yields:
            List of account dictionaries in batches
        """
        batch_size = batch_size or self.batch_size
        
        try:
            xero_rate_limiter.check_and_track(self.tenant_id)
            logger.info(f"Streaming accounts for tenant {self.tenant_id} in batches of {batch_size}")
            
            page = 1
            total_processed = 0
            
            while True:
                # Get paginated accounts
                accounts_response = self.accounting_api.get_accounts(
                    xero_tenant_id=self.tenant_id,
                    where='Status=="ACTIVE"',
                    page=page
                )
                
                if not accounts_response.accounts:
                    break
                
                # Process accounts in current page
                batch = []
                for account in accounts_response.accounts:
                    account_data = self._transform_account_data(account)
                    batch.append(account_data)
                    
                    # Yield batch when it reaches the desired size
                    if len(batch) >= batch_size:
                        yield batch
                        total_processed += len(batch)
                        batch = []
                
                # Yield remaining accounts in the last batch
                if batch:
                    yield batch
                    total_processed += len(batch)
                
                # Move to next page
                page += 1
                
                # Break if we got fewer results than page size (last page)
                if len(accounts_response.accounts) < self.page_size:
                    break
            
            logger.info(f"Finished streaming {total_processed} accounts")
            
        except Exception as e:
            logger.error(f"Error streaming accounts: {e}")
            raise
    
    @xero_api_call(max_retries=3, circuit_breaker_threshold=5)
    def stream_contacts(self, batch_size: Optional[int] = None) -> Generator[List[Dict[str, Any]], None, None]:
        """
        Stream contacts in batches to reduce memory usage.
        
        Yields:
            List of contact dictionaries in batches
        """
        batch_size = batch_size or self.batch_size
        
        try:
            xero_rate_limiter.check_and_track(self.tenant_id)
            logger.info(f"Streaming contacts for tenant {self.tenant_id} in batches of {batch_size}")
            
            page = 1
            total_processed = 0
            
            while True:
                contacts_response = self.accounting_api.get_contacts(
                    xero_tenant_id=self.tenant_id,
                    where='ContactStatus=="ACTIVE"',
                    page=page
                )
                
                if not contacts_response.contacts:
                    break
                
                batch = []
                for contact in contacts_response.contacts:
                    contact_data = self._transform_contact_data(contact)
                    batch.append(contact_data)
                    
                    if len(batch) >= batch_size:
                        yield batch
                        total_processed += len(batch)
                        batch = []
                
                if batch:
                    yield batch
                    total_processed += len(batch)
                
                page += 1
                
                if len(contacts_response.contacts) < self.page_size:
                    break
            
            logger.info(f"Finished streaming {total_processed} contacts")
            
        except Exception as e:
            logger.error(f"Error streaming contacts: {e}")
            raise
    
    @xero_api_call(max_retries=3, circuit_breaker_threshold=5)
    def stream_transactions(
        self,
        from_date: Optional[datetime] = None,
        to_date: Optional[datetime] = None,
        batch_size: Optional[int] = None
    ) -> Generator[List[Dict[str, Any]], None, None]:
        """
        Stream bank transactions in batches with date filtering.
        
        Args:
            from_date: Start date for transactions
            to_date: End date for transactions  
            batch_size: Number of transactions per batch
            
        Yields:
            List of transaction dictionaries in batches
        """
        batch_size = batch_size or self.batch_size
        
        # Default to last 90 days if no dates provided
        if not to_date:
            to_date = datetime.now()
        if not from_date:
            from_date = to_date - timedelta(days=90)
        
        try:
            xero_rate_limiter.check_and_track(self.tenant_id)
            logger.info(f"Streaming transactions from {from_date.strftime('%Y-%m-%d')} "
                       f"to {to_date.strftime('%Y-%m-%d')} in batches of {batch_size}")
            
            # Process in date chunks to avoid large single queries
            current_date = from_date
            chunk_days = 30  # Process 30 days at a time
            total_processed = 0
            
            while current_date <= to_date:
                chunk_end = min(current_date + timedelta(days=chunk_days), to_date)
                
                # Process transactions for this date chunk
                for batch in self._stream_transactions_for_date_range(
                    current_date, chunk_end, batch_size
                ):
                    yield batch
                    total_processed += len(batch)
                
                current_date = chunk_end + timedelta(days=1)
            
            logger.info(f"Finished streaming {total_processed} transactions")
            
        except Exception as e:
            logger.error(f"Error streaming transactions: {e}")
            raise
    
    def _stream_transactions_for_date_range(
        self,
        from_date: datetime,
        to_date: datetime,
        batch_size: int
    ) -> Generator[List[Dict[str, Any]], None, None]:
        """Stream transactions for a specific date range."""
        where_clause = (f"Date >= DateTime({from_date.year}, {from_date.month}, {from_date.day}) "
                       f"AND Date <= DateTime({to_date.year}, {to_date.month}, {to_date.day})")
        
        page = 1
        
        while True:
            bank_transactions = self.accounting_api.get_bank_transactions(
                xero_tenant_id=self.tenant_id,
                where=where_clause,
                order="Date DESC",
                page=page
            )
            
            if not bank_transactions.bank_transactions:
                break
            
            batch = []
            for transaction in bank_transactions.bank_transactions:
                transaction_data = self._transform_transaction_data(transaction)
                batch.append(transaction_data)
                
                if len(batch) >= batch_size:
                    yield batch
                    batch = []
            
            if batch:
                yield batch
            
            page += 1
            
            if len(bank_transactions.bank_transactions) < self.page_size:
                break
    
    @xero_api_call(max_retries=3, circuit_breaker_threshold=5)
    def stream_invoices(
        self,
        from_date: Optional[datetime] = None,
        to_date: Optional[datetime] = None,
        batch_size: Optional[int] = None
    ) -> Generator[List[Dict[str, Any]], None, None]:
        """
        Stream invoices in batches with date filtering.
        
        Args:
            from_date: Start date for invoices
            to_date: End date for invoices
            batch_size: Number of invoices per batch
            
        Yields:
            List of invoice dictionaries in batches
        """
        batch_size = batch_size or self.batch_size
        
        if not to_date:
            to_date = datetime.now()
        if not from_date:
            from_date = to_date - timedelta(days=90)
        
        try:
            xero_rate_limiter.check_and_track(self.tenant_id)
            logger.info(f"Streaming invoices from {from_date.strftime('%Y-%m-%d')} "
                       f"to {to_date.strftime('%Y-%m-%d')} in batches of {batch_size}")
            
            where_clause = (f"Date >= DateTime({from_date.year}, {from_date.month}, {from_date.day}) "
                           f"AND Date <= DateTime({to_date.year}, {to_date.month}, {to_date.day})")
            
            page = 1
            total_processed = 0
            
            while True:
                invoices = self.accounting_api.get_invoices(
                    xero_tenant_id=self.tenant_id,
                    where=where_clause,
                    order="Date DESC",
                    page=page
                )
                
                if not invoices.invoices:
                    break
                
                batch = []
                for invoice in invoices.invoices:
                    invoice_data = self._transform_invoice_data(invoice)
                    batch.append(invoice_data)
                    
                    if len(batch) >= batch_size:
                        yield batch
                        total_processed += len(batch)
                        batch = []
                
                if batch:
                    yield batch
                    total_processed += len(batch)
                
                page += 1
                
                if len(invoices.invoices) < self.page_size:
                    break
            
            logger.info(f"Finished streaming {total_processed} invoices")
            
        except Exception as e:
            logger.error(f"Error streaming invoices: {e}")
            raise
    
    def process_stream_with_callback(
        self,
        stream_generator: Generator[List[Dict[str, Any]], None, None],
        callback: callable,
        progress_callback: Optional[callable] = None
    ) -> Dict[str, Any]:
        """
        Process a stream with a callback function, tracking progress and memory usage.
        
        Args:
            stream_generator: Generator yielding batches of data
            callback: Function to call for each batch
            progress_callback: Optional progress tracking function
            
        Returns:
            Processing statistics
        """
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        start_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        stats = {
            'batches_processed': 0,
            'total_records': 0,
            'start_memory_mb': start_memory,
            'peak_memory_mb': start_memory,
            'processing_time_seconds': 0,
            'errors': []
        }
        
        start_time = datetime.now()
        
        try:
            for batch_num, batch in enumerate(stream_generator, 1):
                try:
                    # Process batch with callback
                    callback(batch)
                    
                    # Update statistics
                    stats['batches_processed'] = batch_num
                    stats['total_records'] += len(batch)
                    
                    # Track memory usage
                    current_memory = process.memory_info().rss / 1024 / 1024
                    stats['peak_memory_mb'] = max(stats['peak_memory_mb'], current_memory)
                    
                    # Call progress callback if provided
                    if progress_callback:
                        progress_callback(batch_num, len(batch), stats)
                    
                    # Log progress periodically
                    if batch_num % 10 == 0:
                        logger.info(f"Processed {batch_num} batches, "
                                  f"{stats['total_records']} records, "
                                  f"memory: {current_memory:.1f}MB")
                
                except Exception as e:
                    error_info = f"Error processing batch {batch_num}: {e}"
                    logger.error(error_info)
                    stats['errors'].append(error_info)
        
        except Exception as e:
            error_info = f"Stream processing failed: {e}"
            logger.error(error_info)
            stats['errors'].append(error_info)
        
        finally:
            end_time = datetime.now()
            stats['processing_time_seconds'] = (end_time - start_time).total_seconds()
            final_memory = process.memory_info().rss / 1024 / 1024
            stats['final_memory_mb'] = final_memory
            stats['memory_increase_mb'] = final_memory - start_memory
        
        return stats
    
    def _transform_account_data(self, account) -> Dict[str, Any]:
        """Transform Xero account object to dictionary."""
        return {
            "account_id": account.account_id,
            "account_code": account.code,
            "account_name": account.name,
            "account_type": account.type.value if account.type else None,
            "account_class": account.class_.value if account.class_ else None,
            "status": account.status.value if account.status else None,
            "description": account.description,
            "tax_type": account.tax_type,
            "enable_payments": account.enable_payments_to_account,
            "show_in_expense_claims": account.show_in_expense_claims,
            "bank_account_number": account.bank_account_number,
            "bank_account_type": account.bank_account_type.value if account.bank_account_type else None,
            "currency_code": account.currency_code.value if account.currency_code else None,
        }
    
    def _transform_contact_data(self, contact) -> Dict[str, Any]:
        """Transform Xero contact object to dictionary."""
        return {
            "contact_id": contact.contact_id,
            "name": contact.name,
            "contact_number": contact.contact_number,
            "account_number": contact.account_number,
            "contact_status": contact.contact_status.value if contact.contact_status else None,
            "first_name": contact.first_name,
            "last_name": contact.last_name,
            "email_address": contact.email_address,
            "is_supplier": contact.is_supplier,
            "is_customer": contact.is_customer,
            "default_currency": contact.default_currency.value if contact.default_currency else None,
        }
    
    def _transform_transaction_data(self, transaction) -> Dict[str, Any]:
        """Transform Xero bank transaction object to dictionary."""
        return {
            "transaction_id": transaction.bank_transaction_id,
            "type": transaction.type.value if transaction.type else None,
            "date": transaction.date,
            "reference": transaction.reference,
            "amount": float(transaction.total) if transaction.total else 0.0,
            "currency_code": transaction.currency_code.value if transaction.currency_code else "USD",
            "bank_account": {
                "account_id": transaction.bank_account.account_id if transaction.bank_account else None,
                "account_code": transaction.bank_account.code if transaction.bank_account else None,
                "account_name": transaction.bank_account.name if transaction.bank_account else None,
            },
            "contact": {
                "contact_id": transaction.contact.contact_id if transaction.contact else None,
                "name": transaction.contact.name if transaction.contact else None,
            },
        }
    
    def _transform_invoice_data(self, invoice) -> Dict[str, Any]:
        """Transform Xero invoice object to dictionary."""
        return {
            "invoice_id": invoice.invoice_id,
            "invoice_number": invoice.invoice_number,
            "type": invoice.type.value if invoice.type else None,
            "status": invoice.status.value if invoice.status else None,
            "date": invoice.date,
            "due_date": invoice.due_date,
            "total": float(invoice.total) if invoice.total else 0.0,
            "amount_due": float(invoice.amount_due) if invoice.amount_due else 0.0,
            "amount_paid": float(invoice.amount_paid) if invoice.amount_paid else 0.0,
            "currency_code": invoice.currency_code.value if invoice.currency_code else "USD",
            "contact": {
                "contact_id": invoice.contact.contact_id if invoice.contact else None,
                "name": invoice.contact.name if invoice.contact else None,
            },
        }


def create_streaming_client(organization_id: int, api_client: ApiClient, tenant_id: str) -> XeroStreamingClient:
    """Factory function to create a configured streaming client."""
    client = XeroStreamingClient(organization_id)
    client.set_client_details(api_client, tenant_id)
    return client