server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  # MCX3D Application logs
  - job_name: mcx3d-application
    static_configs:
      - targets:
          - localhost
        labels:
          job: mcx3d-finance
          service: application
          __path__: /var/log/mcx3d/*.log

  # Docker container logs
  - job_name: docker-containers
    docker_sd_configs:
      - host: unix:///var/run/docker.sock
        refresh_interval: 5s
        filters:
          - name: label
            values: ["logging=promtail"]
    relabel_configs:
      - source_labels: ['__meta_docker_container_name']
        regex: '/(.*)'
        target_label: 'container'
      - source_labels: ['__meta_docker_container_log_stream']
        target_label: 'logstream'
      - source_labels: ['__meta_docker_container_label_logging_jobname']
        target_label: 'job'

  # System logs
  - job_name: syslog
    static_configs:
      - targets:
          - localhost
        labels:
          job: syslog
          service: system
          __path__: /var/log/syslog

  # PostgreSQL logs
  - job_name: postgresql
    static_configs:
      - targets:
          - localhost
        labels:
          job: postgresql
          service: database
          __path__: /var/log/postgresql/*.log

  # Nginx/Proxy logs (if applicable)
  - job_name: nginx
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx
          service: proxy
          __path__: /var/log/nginx/*log

pipeline_stages:
  - json:
      expressions:
        timestamp: timestamp
        level: level
        message: message
        correlation_id: correlation_id
        component: component
        service: service
        user_id: user_id
  - labels:
      level:
      component:
      service:
      correlation_id:
      user_id:
  - timestamp:
      source: timestamp
      format: RFC3339
  - output:
      source: message