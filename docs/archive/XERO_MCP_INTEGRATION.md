# Xero MCP Server Integration Guide

## Overview

This project now includes integration with the official Xero MCP (Model Context Protocol) server, providing direct access to Xero accounting data through Claude Code CLI.

## Installation Status

✅ **Xero MCP Server Installed**: `@xeroapi/xero-mcp-server@latest`
✅ **Configuration**: User scope (available in all projects)
✅ **Environment Variables**: Configured with project credentials

## Available MCP Commands

The Xero MCP server provides the following commands:

### Data Retrieval Commands

- `list-accounts`: Retrieve a list of accounts
- `list-contacts`: Retrieve a list of contacts from Xero
- `list-credit-notes`: Retrieve a list of credit notes
- `list-invoices`: Retrieve a list of invoices
- `list-items`: Retrieve a list of items
- `list-organisation-details`: Retrieve details about an organisation
- `list-quotes`: Retrieve a list of quotes
- `list-tax-rates`: Retrieve a list of tax rates
- `list-payments`: Retrieve a list of payments
- `list-bank-transactions`: Retrieve a list of bank account transactions

### Financial Reports

- `list-profit-and-loss`: Retrieve a profit and loss report
- `list-trial-balance`: Retrieve a trial balance report
- `list-report-balance-sheet`: Retrieve a balance sheet report
- `list-aged-receivables-by-contact`: Retrieves aged receivables for a contact
- `list-aged-payables-by-contact`: Retrieves aged payables for a contact

### Payroll Commands (NZ/UK regions)

- `list-payroll-employees`: Retrieve a list of Payroll Employees
- `list-payroll-employee-leave`: Retrieve a Payroll Employee's leave records
- `list-payroll-employee-leave-balances`: Retrieve a Payroll Employee's leave balances
- `list-payroll-employee-leave-types`: Retrieve a list of Payroll leave types
- `list-payroll-leave-periods`: Retrieve a list of a Payroll Employee's leave periods
- `list-payroll-leave-types`: Retrieve a list of all available leave types in Xero Payroll

### Creation Commands

- `create-contact`: Create a new contact
- `create-credit-note`: Create a new credit note
- `create-invoice`: Create a new invoice
- `create-payment`: Create a new payment
- `create-quote`: Create a new quote
- `create-payroll-timesheet`: Create a new Payroll Timesheet

### Update Commands

- `update-contact`: Update an existing contact
- `update-invoice`: Update an existing draft invoice
- `update-quote`: Update an existing draft quote
- `update-credit-note`: Update an existing draft credit note
- `update-payroll-timesheet-line`: Update a line on an existing Payroll Timesheet

### Other Commands

- `approve-payroll-timesheet`: Approve a Payroll Timesheet
- `revert-payroll-timesheet`: Revert an approved Payroll Timesheet
- `add-payroll-timesheet-line`: Add new line on an existing Payroll Timesheet
- `delete-payroll-timesheet`: Delete an existing Payroll Timesheet
- `get-payroll-timesheet`: Retrieve an existing Payroll Timesheet
- `list-contact-groups`: Retrieve a list of contact groups

## Usage in MCX3D Financials

### Integration with Existing OAuth Flow

The MCP server can work alongside our existing PKCE-enhanced OAuth2 implementation. Once a user authorizes through our web application, the MCP server can access the same Xero organization data.

### Data Pipeline Enhancement

The MCP server can enhance our existing data pipeline:

1. **Direct Data Access**: Bypass API rate limits for read operations
2. **Real-time Queries**: Get up-to-date financial data on demand
3. **Report Generation**: Generate financial reports without storing data locally
4. **Data Validation**: Verify our processed data against Xero's live data

### Example Usage

```bash
# Get organization details
claude "Using the Xero MCP server, get the organization details"

# Retrieve chart of accounts
claude "List all accounts from Xero and show their types and codes"

# Generate profit & loss report
claude "Generate a profit and loss report for the current period using Xero MCP"

# Get invoice data
claude "List all invoices from the last 30 days with their amounts and status"
```

## Configuration Details

- **Server**: `@xeroapi/xero-mcp-server@latest`
- **Scope**: User (available in all projects)
- **Authentication**: Custom Connection with OAuth2 credentials
- **Client ID**: D41A530E29674BDF833D94D15927BE91 (configured)
- **Redirect URI**: http://localhost:8000/auth/xero/callback

## Next Steps

1. **Test MCP Commands**: Verify connection and test basic commands
2. **Integration Scripts**: Create helper scripts for common financial data operations
3. **Data Synchronization**: Use MCP server to validate our processed data
4. **Report Generation**: Enhance existing reports with live Xero data

## Authentication Requirements

The MCP server uses the same Xero OAuth2 credentials as our web application. Ensure:

1. Your Xero app is configured with Custom Connection
2. The redirect URI matches our application: `http://localhost:8000/auth/xero/callback`
3. Required scopes are enabled in your Xero developer app

## Benefits

- **Direct API Access**: No need to implement additional Xero SDK calls
- **Standardized Interface**: MCP provides consistent command structure
- **Real-time Data**: Access live Xero data without caching concerns
- **Enhanced Reporting**: Combine processed data with live Xero information
- **Data Validation**: Verify our financial calculations against Xero's data

## Troubleshooting

If the MCP server doesn't work properly:

1. Verify Claude Code CLI is installed and updated
2. Check that Node.js is properly installed
3. Ensure Xero app credentials are correct
4. Restart Claude Code CLI after configuration changes
5. Check MCP server logs for authentication issues

For detailed API documentation, refer to:
- [Xero Public API Documentation](https://developer.xero.com/documentation/api/)
- [MCP Protocol Specification](https://modelcontextprotocol.io/)
- [Xero-Node Public API SDK Docs](https://xeroapi.github.io/xero-node/accounting)