"""
Integration tests for the security features.
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from mcx3d_finance.db.models import User

@pytest.mark.asyncio
class TestIntegrationSecurity:
    """
    Test suite for security feature integrations.
    """

    def test_login_access_logout(self, test_client: TestClient, test_user: User):
        """
        Test a full login, access a protected resource, and logout flow.
        """
        # Login
        response = test_client.post(
            "/api/auth/login",
            json={"email": test_user.email, "password": "password"},
        )
        assert response.status_code == 200
        access_token = response.json()["access_token"]

        # Access a protected resource
        response = test_client.get(
            "/api/auth/xero/status/1",  # Assuming org 1 exists
            headers={"Authorization": f"Bearer {access_token}"},
        )
        # This will fail because the user is not associated with the organization,
        # but it proves that the authentication part worked.
        assert response.status_code == 403

        # Logout
        response = test_client.post(
            "/api/auth/logout",
            headers={"Authorization": f"Bearer {access_token}"},
        )
        assert response.status_code == 200

    def test_failed_login_and_lockout(self, test_client: TestClient, test_user: User):
        """
        Test that multiple failed logins lead to an account lockout.
        """
        # 5 failed attempts
        for _ in range(5):
            response = test_client.post(
                "/api/auth/login",
                json={"email": test_user.email, "password": "wrong_password"},
            )
            assert response.status_code == 401

        # 6th attempt should be locked
        response = test_client.post(
            "/api/auth/login",
            json={"email": test_user.email, "password": "password"},
        )
        assert response.status_code == 403
        assert "Account locked" in response.text

    def test_rate_limiting_on_protected_endpoint(
        self, test_client: TestClient, test_user: User
    ):
        """
        Test that rate limiting is applied to a protected endpoint.
        """
        # Login
        response = test_client.post(
            "/api/auth/login",
            json={"email": test_user.email, "password": "password"},
        )
        access_token = response.json()["access_token"]

        # The default rate limit is 100 requests per minute.
        # We'll make 101 requests to trigger the rate limiter.
        for i in range(101):
            response = test_client.get(
                "/api/auth/xero/status/1",
                headers={"Authorization": f"Bearer {access_token}"},
            )
            if i < 100:
                # This will fail because of org access, but not rate limiting
                assert response.status_code == 403
            else:
                assert response.status_code == 429
