# Test environment configuration
# This file is for testing purposes only

# Test Database Configuration
DATABASE_URL=postgresql://test_user:test_password_123@localhost:5432/mcx3d_test

# Test Redis Configuration
REDIS_URL=redis://localhost:6379/1

# Test Security Configuration
SECRET_KEY=test_secret_key_for_testing_only_32_chars_long_with_Special123!
ENCRYPTION_KEY=zqDhO5z8W0pYqeBe0LmONLEfKmmJmRQJeOl9JFZN0lE=

# Test Xero Configuration
XERO_CLIENT_ID=test_client_id
XERO_CLIENT_SECRET=test_client_secret
XERO_REDIRECT_URI=http://localhost:8000/api/auth/xero/callback

# Test Application Configuration
DEBUG=True
LOG_LEVEL=DEBUG