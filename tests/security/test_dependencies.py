"""
Test to verify all security dependencies are available.
"""
import pytest


def test_security_dependencies():
    """Test that all required security dependencies can be imported."""
    
    # JWT and cryptography
    try:
        from jose import jwt
        import cryptography
        from cryptography.fernet import Fernet
    except ImportError as e:
        pytest.fail(f"JWT/Cryptography dependency missing: {e}")
    
    # Password hashing
    try:
        from passlib.context import CryptContext
        import bcrypt
    except ImportError as e:
        pytest.fail(f"Password hashing dependency missing: {e}")
    
    # XSS protection
    try:
        import bleach
    except ImportError as e:
        pytest.fail(f"Bleach dependency missing: {e}")
    
    # Email validation
    try:
        from email_validator import validate_email
    except ImportError as e:
        pytest.fail(f"Email validator dependency missing: {e}")
    
    # MFA support
    try:
        import pyotp
    except ImportError as e:
        pytest.fail(f"TOTP dependency missing: {e}")
    
    # Redis (fake for testing)
    try:
        import fakeredis
    except ImportError as e:
        pytest.fail(f"Fake Redis dependency missing: {e}")


def test_security_modules_importable():
    """Test that all security modules can be imported."""
    
    # Session manager
    try:
        from mcx3d_finance.utils.session_manager import SessionManager
    except ImportError as e:
        pytest.fail(f"Session manager import failed: {e}")
    
    # Rate limiter
    try:
        from mcx3d_finance.utils.rate_limiter import RateLimiter
    except ImportError as e:
        pytest.fail(f"Rate limiter import failed: {e}")
    
    # Input validator
    try:
        from mcx3d_finance.utils.input_validator import InputValidator, ValidationError
    except ImportError as e:
        pytest.fail(f"Input validator import failed: {e}")
    
    # Audit logger
    try:
        from mcx3d_finance.utils.audit_logger import AuditLogger
    except ImportError as e:
        pytest.fail(f"Audit logger import failed: {e}")
    
    # Data protection
    try:
        from mcx3d_finance.utils.data_protection import DataProtection
    except ImportError as e:
        pytest.fail(f"Data protection import failed: {e}")


def test_environment_variables():
    """Test that required environment variables are set."""
    import os
    
    required_vars = [
        "SECRET_KEY",
        "DATABASE_URL", 
        "ENCRYPTION_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        pytest.fail(f"Missing required environment variables: {', '.join(missing_vars)}")


def test_security_config_loading():
    """Test that security configuration can be loaded."""
    try:
        from mcx3d_finance.core.config import get_security_config
        config = get_security_config()
        
        # Check that essential config values are present
        assert config.get("secret_key"), "Secret key not configured"
        assert config.get("encryption_key"), "Encryption key not configured"
        assert len(config.get("secret_key", "")) >= 32, "Secret key too short"
        
    except Exception as e:
        pytest.fail(f"Security config loading failed: {e}")