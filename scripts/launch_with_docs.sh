#!/bin/bash

# Launch script for MCX3D Finance application with optional document generation
# This script will:
# 1. Start all Docker containers
# 2. Run database migrations
# 3. Seed initial data
# 4. Optionally generate financial documents

echo "🚀 MCX3D Finance Application Launcher"
echo "===================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if Docker is installed
if ! command_exists docker; then
    echo "❌ Error: Docker is not installed."
    echo "Please install Docker from https://www.docker.com/get-started"
    exit 1
fi

# Check if docker-compose is installed
if ! command_exists docker-compose; then
    echo "❌ Error: docker-compose is not installed."
    echo "Please install docker-compose"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ] && [ ! -f ".env.development" ]; then
    echo "⚠️  Warning: No .env file found."
    echo "Creating .env.development from .env.example..."
    cp .env.example .env.development
    echo ""
    echo "❗ IMPORTANT: Please edit .env.development and add your Xero credentials:"
    echo "   - XERO_CLIENT_ID"
    echo "   - XERO_CLIENT_SECRET"
    echo "   - XERO_ACCESS_TOKEN"
    echo "   - XERO_REFRESH_TOKEN"
    echo "   - XERO_TENANT_ID"
    echo ""
    echo "You can obtain these from https://developer.xero.com/myapps"
    echo ""
    read -p "Press Enter to continue after updating the credentials..."
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p reports logs

# Build Docker images
echo ""
echo "🔨 Building Docker images..."
docker-compose build

# Start containers
echo ""
echo "🐳 Starting Docker containers..."
docker-compose up -d

# Wait for services to be ready
echo ""
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check if PostgreSQL is ready
echo "🔍 Checking database connection..."
for i in {1..30}; do
    if docker-compose exec -T db pg_isready -U user -d mcx3d_db >/dev/null 2>&1; then
        echo "✅ Database is ready!"
        break
    fi
    echo -n "."
    sleep 1
done

# Run database migrations
echo ""
echo "🗄️  Running database migrations..."
docker-compose exec -T web alembic upgrade head

# Seed initial data
echo ""
echo "🌱 Seeding initial data..."
docker-compose exec -T web python -m mcx3d_finance.cli seed sample-data

# Check if user wants to generate financial documents
echo ""
read -p "📊 Would you like to generate financial documents now? (y/N) " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo ""
    echo "📄 Generating financial documentation..."
    ./scripts/generate_financial_docs.sh
fi

# Display status
echo ""
echo "✅ MCX3D Finance application is now running!"
echo ""
echo "🌐 Access the application at:"
echo "   - API: http://localhost:8000"
echo "   - API Docs: http://localhost:8000/docs"
echo "   - Health Check: http://localhost:8000/health/"
echo ""
echo "📋 Available CLI commands:"
echo "   - Generate financial docs: docker-compose exec web python -m mcx3d_finance.cli financial-docs generate"
echo "   - List generated docs: docker-compose exec web python -m mcx3d_finance.cli financial-docs list"
echo "   - View logs: docker-compose logs -f"
echo "   - Stop services: docker-compose down"
echo ""
echo "📚 For more information, see the documentation in the docs/ directory."