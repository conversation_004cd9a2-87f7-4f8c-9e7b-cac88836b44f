# Testing Environment Configuration
# MCX3D Financials - Testing Settings
# Configuration for automated testing, CI/CD, and test environments
# Optimized for test reliability, speed, and isolation

# Environment identification
environment: testing

# Database configuration for testing
database:
  # Use in-memory SQLite for fast, isolated tests
  # Override with TEST_DATABASE_URL for integration tests
  url: "sqlite:///:memory:"
  pool_size: 1
  timeout: 10
  echo_sql: false  # Disable for cleaner test output
  
  # Test database optimization
  connect_args:
    check_same_thread: false  # Allow SQLite threading for tests
  
  # Test data management
  enable_auto_rollback: true
  enable_test_fixtures: true

# Redis configuration for testing
redis:
  # Use separate Redis DB for testing or mock Redis
  url: "redis://localhost:6379/15"  # Use DB 15 for testing
  db: 15
  password: null
  
  # Testing optimizations
  socket_timeout: 1
  socket_connect_timeout: 1
  retry_on_timeout: false

# Xero OAuth configuration for testing
xero:
  # Use mock/test credentials - never real production data
  client_id: "TEST_CLIENT_ID"
  client_secret: "TEST_CLIENT_SECRET"
  redirect_uri: "http://localhost:8000/api/auth/xero/callback"
  scopes: "accounting.transactions accounting.contacts accounting.reports.read accounting.settings"
  
  # Testing-specific settings
  enable_mocking: true
  mock_responses: true
  timeout_seconds: 5

# Security configuration for testing
security:
  # Use fixed, known keys for predictable tests
  secret_key: "test-secret-key-32-chars-minimum-length-for-testing-only"
  encryption_key: "test-encryption-key-for-testing-only"
  algorithm: "HS256"
  
  # Relaxed security for testing speed
  access_token_expire_minutes: 120  # Longer for test stability
  max_login_attempts: 100  # No lockouts during tests
  lockout_duration_minutes: 0  # Disabled for tests
  
  # Disable expensive security features in tests
  enable_audit_encryption: false
  enable_field_encryption: false
  enable_key_rotation: false

# Rate limiting - disabled for testing
rate_limiting:
  api_default: 10000  # Very high limits for tests
  auth_login: 1000
  enable_rate_limiting: false  # Disable for test speed

# Logging configuration for testing
logging:
  level: WARNING  # Reduce log noise during tests
  format: "%(levelname)s - %(name)s - %(message)s"
  enable_sql_logging: false
  log_file: null  # No log files during tests
  
  # Test-specific logging
  enable_test_logging: true
  capture_test_output: true
  suppress_third_party_logs: true

# Reporting configuration for testing
reporting:
  output_dir: "/tmp/test_reports"
  default_format: "json"  # JSON is fastest for tests
  enable_charts: false  # Disable charts for speed
  max_file_size_mb: 10
  
  # Testing optimizations
  enable_minimal_mode: true
  skip_expensive_operations: true
  use_test_templates: true

# Performance settings for testing
performance:
  memory_limit_mb: 256  # Lower limits for test containers
  timeout_seconds: 30   # Shorter timeouts for faster test failure
  parallel_workers: 1   # Single worker for test predictability
  enable_caching: false # Disable caching for test isolation
  
  # Test-specific performance settings
  enable_fast_mode: true
  skip_non_essential_checks: true

# Testing-specific features
testing:
  enable_test_mode: true
  enable_fixtures: true
  enable_mocking: true
  enable_fast_tests: true
  
  # Test data configuration
  use_sample_data: true
  auto_cleanup_test_data: true
  isolate_test_runs: true
  
  # Mock configurations
  mock_external_apis: true
  mock_file_operations: false
  mock_database: false
  
  # Test reporting
  generate_coverage_reports: true
  enable_performance_profiling: false
  capture_test_metrics: true

# CORS settings for testing - permissive
cors:
  origins: ["*"]  # Allow all origins for testing
  allow_credentials: true
  allow_methods: ["*"]
  allow_headers: ["*"]

# Monitoring - minimal for testing
monitoring:
  enable_metrics: false
  enable_tracing: false
  enable_profiling: false
  enable_alerting: false
  
  # Test monitoring
  enable_test_metrics: true
  track_test_performance: true

# Test database fixtures
fixtures:
  # Sample organizations for testing
  organizations:
    - id: 1
      name: "Test Company 1"
      xero_tenant_id: "test-tenant-1"
    - id: 2
      name: "Test Company 2"
      xero_tenant_id: "test-tenant-2"
  
  # Sample users for testing
  users:
    - id: 1
      email: "<EMAIL>"
      password: "test-password"
      is_active: true
    - id: 2
      email: "<EMAIL>"
      password: "admin-password"
      is_superuser: true

# Mock data configuration
mock_data:
  # Xero API mock responses
  xero_responses:
    accounts: "fixtures/xero/accounts.json"
    contacts: "fixtures/xero/contacts.json"
    transactions: "fixtures/xero/transactions.json"
    invoices: "fixtures/xero/invoices.json"
  
  # Financial data for testing calculations
  financial_data:
    revenue_range: [10000, 100000]
    expense_range: [1000, 50000]
    account_count: 50
    transaction_count: 1000

# Test execution settings
execution:
  # Pytest configuration
  pytest_args: ["-v", "--tb=short", "--strict-markers"]
  
  # Coverage settings
  coverage_minimum: 80
  coverage_exclude:
    - "*/tests/*"
    - "*/migrations/*"
    - "*/venv/*"
  
  # Parallel testing
  enable_parallel_testing: false  # Set to true for faster CI
  max_parallel_workers: 2

# CI/CD specific settings
ci_cd:
  # GitHub Actions / CI environment detection
  detect_ci_environment: true
  
  # CI-specific optimizations
  use_faster_crypto: true  # Use less secure but faster crypto for tests
  skip_slow_tests: false   # Set to true to skip slow integration tests
  
  # Test result reporting
  junit_xml_output: "test-results.xml"
  coverage_xml_output: "coverage.xml"
  
# Cleanup configuration
cleanup:
  # Automatic cleanup after tests
  cleanup_temp_files: true
  cleanup_test_databases: true
  cleanup_generated_reports: true
  
  # Cleanup schedule
  cleanup_on_exit: true
  cleanup_between_tests: true

# Security testing
security_testing:
  # Enable security-focused tests
  enable_vulnerability_scanning: true
  enable_penetration_testing: false  # Enable for security test suites
  
  # Test security scenarios
  test_authentication_bypass: true
  test_authorization_failures: true
  test_input_validation: true
  test_sql_injection: true
  test_xss_protection: true

# Performance testing
performance_testing:
  # Load testing configuration
  enable_load_testing: false  # Enable for performance test suites
  concurrent_users: 10
  test_duration_seconds: 60
  
  # Performance thresholds
  max_response_time_ms: 2000
  max_memory_usage_mb: 512
  min_requests_per_second: 100