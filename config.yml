# MCX3D Financials - Main Configuration File
# 
# This is the fallback configuration file used when no environment-specific
# configuration is available. The application automatically detects the
# environment and loads the appropriate config file:
#
# - config/development.yml  (development environment)
# - config/staging.yml      (staging environment) 
# - config/production.yml   (production environment)
# - config/testing.yml      (testing environment)
# - config.yml              (fallback/default)
#
# SECURITY NOTICE:
# - All sensitive values MUST be stored in environment variables
# - Never commit secrets, API keys, or passwords to version control
# - Use 'python -m mcx3d_finance.utils.generate_keys' to generate secure keys
# - Review .env.example for complete configuration examples

# Environment detection
# Override with ENVIRONMENT, ENV, or APP_ENV environment variables
# Possible values: development, staging, production, testing
environment: production  # Default to production for safety

# Database configuration
# DATABASE_URL must be set in environment variables
# Example: postgresql://user:password@host:port/database
database:
  # These are fallback settings - use environment-specific configs
  pool_size: 10
  timeout: 30
  echo_sql: false

# Redis configuration
# REDIS_URL should be set in environment variables
redis:
  # Default Redis connection - override with REDIS_URL
  url: "redis://localhost:6379/0"

# Xero OAuth configuration
# XERO_CLIENT_ID and XERO_CLIENT_SECRET must be set in environment variables
xero:
  # Production-safe defaults
  redirect_uri: "https://api.mcx3d.com/api/auth/xero/callback"
  scopes: "accounting.transactions accounting.contacts accounting.reports.read accounting.settings offline_access"

# Security configuration
# SECRET_KEY and ENCRYPTION_KEY must be set in environment variables
# Use 'python -m mcx3d_finance.utils.generate_keys' to generate secure keys
security:
  # Production-safe defaults
  access_token_expire_minutes: 15
  max_login_attempts: 3
  lockout_duration_minutes: 30
  enable_audit_encryption: true
  enable_field_encryption: true

# Rate limiting defaults
rate_limiting:
  api_default: 100
  auth_login: 5
  enable_rate_limiting: true

# Logging defaults
logging:
  level: INFO
  enable_sql_logging: false
  sanitize_sensitive_data: true

# Reporting defaults  
reporting:
  output_dir: "./reports"
  default_format: "pdf"
  enable_charts: true
  max_file_size_mb: 100

# Performance defaults
performance:
  memory_limit_mb: 1024
  timeout_seconds: 120
  parallel_workers: 4
  enable_caching: true

# Monitoring defaults
monitoring:
  enable_metrics: true
  enable_alerting: true
  health_check_interval_seconds: 60

# Data retention defaults (7 years for financial data)
data_retention:
  financial_data_days: 2555
  audit_logs_days: 365
  session_data_days: 30

# Configuration validation
# The application validates configuration at startup
# Check /health/config endpoint for configuration status
validation:
  enforce_validation: true
  fail_on_missing_required: true
  warn_on_insecure_defaults: true