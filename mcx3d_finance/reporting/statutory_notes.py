"""
Generates statutory notes for UK financial statements in compliance with FRS 102.
"""

from typing import Dict, Any

def generate_all_statutory_notes(
    balance_sheet: Dict[str, Any], pnl: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Generate all required statutory notes.

    Args:
        balance_sheet: A UK-compliant balance sheet.
        pnl: A UK-compliant profit and loss account.

    Returns:
        A dictionary containing all statutory notes.
    """
    notes = {
        "accounting_policies": generate_accounting_policies_note(),
        "tangible_fixed_assets": generate_tangible_fixed_assets_note(balance_sheet),
        "stocks": generate_stocks_note(balance_sheet),
        "debtors": generate_debtors_note(balance_sheet),
        "creditors": generate_creditors_note(balance_sheet),
        "share_capital": generate_share_capital_note(balance_sheet),
        "related_party_transactions": generate_related_party_transactions_note(),
        "post_balance_sheet_events": generate_post_balance_sheet_events_note(),
        "ultimate_controlling_party": generate_ultimate_controlling_party_note(),
    }
    return notes

def generate_accounting_policies_note() -> Dict[str, str]:
    """Generate the accounting policies note."""
    return {
        "title": "Note 1: Accounting Policies",
        "reference": "FRS 102, Section 8",
        "content": "The financial statements have been prepared in accordance with Financial Reporting Standard 102, the Financial Reporting Standard applicable in the UK and Republic of Ireland. The company is a private company limited by shares and is registered in England and Wales."
    }

def generate_tangible_fixed_assets_note(balance_sheet: Dict[str, Any]) -> Dict[str, Any]:
    """Generate the tangible fixed assets note."""
    return {
        "title": "Note 2: Tangible Fixed Assets",
        "reference": "FRS 102, Section 17",
        "content": {
            "cost": {
                "at_start_of_period": 0, # Placeholder
                "additions": 0, # Placeholder
                "disposals": 0, # Placeholder
                "at_end_of_period": balance_sheet["fixed_assets"]["tangible"],
            },
            "depreciation": {
                "at_start_of_period": 0, # Placeholder
                "charge_for_year": 0, # Placeholder
                "on_disposals": 0, # Placeholder
                "at_end_of_period": 0, # Placeholder
            },
            "net_book_value": {
                "at_end_of_period": balance_sheet["fixed_assets"]["tangible"],
                "at_start_of_period": 0, # Placeholder
            }
        }
    }

def generate_stocks_note(balance_sheet: Dict[str, Any]) -> Dict[str, Any]:
    """Generate the stocks note."""
    return {
        "title": "Note 3: Stocks",
        "reference": "FRS 102, Section 13",
        "content": {
            "raw_materials": 0, # Placeholder
            "work_in_progress": 0, # Placeholder
            "finished_goods": balance_sheet["current_assets"]["stocks"],
            "total": balance_sheet["current_assets"]["stocks"],
        }
    }

def generate_debtors_note(balance_sheet: Dict[str, Any]) -> Dict[str, Any]:
    """Generate the debtors note."""
    return {
        "title": "Note 4: Debtors",
        "reference": "FRS 102, Section 11",
        "content": {
            "trade_debtors": balance_sheet["current_assets"]["debtors"],
            "other_debtors": 0, # Placeholder
            "prepayments_and_accrued_income": 0, # Placeholder
            "total": balance_sheet["current_assets"]["debtors"],
        }
    }

def generate_creditors_note(balance_sheet: Dict[str, Any]) -> Dict[str, Any]:
    """Generate the creditors note."""
    return {
        "title": "Note 5: Creditors",
        "reference": "FRS 102, Section 11",
        "content": {
            "due_within_one_year": {
                "trade_creditors": balance_sheet["creditors_due_within_one_year"]["total"],
                "other_creditors": 0, # Placeholder
                "total": balance_sheet["creditors_due_within_one_year"]["total"],
            },
            "due_after_one_year": {
                "bank_loans": balance_sheet["creditors_due_after_one_year"]["total"],
                "total": balance_sheet["creditors_due_after_one_year"]["total"],
            }
        }
    }

def generate_share_capital_note(balance_sheet: Dict[str, Any]) -> Dict[str, Any]:
    """Generate the share capital note."""
    return {
        "title": "Note 6: Share Capital",
        "reference": "FRS 102, Section 22",
        "content": {
            "allotted_called_up_and_fully_paid": {
                "ordinary_shares": balance_sheet["capital_and_reserves"]["share_capital"],
            },
            "total": balance_sheet["capital_and_reserves"]["share_capital"],
        }
    }

def generate_related_party_transactions_note() -> Dict[str, str]:
    """Generate the related party transactions note."""
    return {
        "title": "Note 7: Related Party Transactions",
        "reference": "FRS 102, Section 33",
        "content": "There were no related party transactions during the year." # Placeholder
    }

def generate_post_balance_sheet_events_note() -> Dict[str, str]:
    """Generate the post balance sheet events note."""
    return {
        "title": "Note 8: Post Balance Sheet Events",
        "reference": "FRS 102, Section 32",
        "content": "There were no significant post balance sheet events." # Placeholder
    }

def generate_ultimate_controlling_party_note() -> Dict[str, str]:
    """Generate the ultimate controlling party note."""
    return {
        "title": "Note 9: Ultimate Controlling Party",
        "reference": "FRS 102, Section 33",
        "content": "The company is controlled by its directors." # Placeholder
    }
