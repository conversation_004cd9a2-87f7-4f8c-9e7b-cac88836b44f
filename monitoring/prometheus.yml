global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alerts.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets: ["alertmanager:9093"]

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # MCX3D Financial Application
  - job_name: 'mcx3d-finance'
    static_configs:
      - targets: ['web:8001']  # Application metrics endpoint
    scrape_interval: 10s
    metrics_path: '/metrics'
    scrape_timeout: 10s

  # System metrics via Node Exporter
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s

  # Container metrics via cAdvisor
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s

  # PostgreSQL metrics
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 15s

  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 15s

  # FastAPI application health
  - job_name: 'mcx3d-health'
    static_configs:
      - targets: ['web:8000']
    metrics_path: '/health/detailed'
    scrape_interval: 30s
    scrape_timeout: 10s