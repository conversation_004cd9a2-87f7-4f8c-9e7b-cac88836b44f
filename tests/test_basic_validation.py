# tests/test_basic_validation.py

"""
Basic validation tests to ensure our testing infrastructure works correctly.
"""

import pytest
import tempfile
import os
from pathlib import Path

from mcx3d_finance.reporting.generator import ReportGenerator


class TestBasicValidation:
    """Basic tests to validate our testing setup."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test outputs."""
        temp_dir = tempfile.mkdtemp(prefix="mcx3d_basic_test_")
        yield temp_dir
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def report_generator(self):
        """Create ReportGenerator instance."""
        return ReportGenerator()
    
    def test_report_generator_creation(self, report_generator):
        """Test that ReportGenerator can be instantiated."""
        assert report_generator is not None
        assert hasattr(report_generator, 'generate_dcf_valuation_pdf')
        assert hasattr(report_generator, 'generate_saas_valuation_pdf')
    
    def test_temp_directory_creation(self, temp_dir):
        """Test temporary directory creation."""
        assert os.path.exists(temp_dir)
        assert os.path.isdir(temp_dir)
        
        # Test file creation in temp directory
        test_file = Path(temp_dir) / "test.txt"
        test_file.write_text("test content")
        assert test_file.exists()
        assert test_file.read_text() == "test content"
    
    @pytest.mark.integration
    def test_basic_dcf_pdf_generation(self, report_generator, temp_dir):
        """Test basic DCF PDF generation with minimal data."""
        # Simple test data that matches the actual ReportGenerator interface
        minimal_dcf_data = {
            "organization_id": 1,
            "company_name": "Test Company",
            "valuation_date": "2024-01-01",
            "enterprise_value": 10000000,
            "financial_projections": [
                {
                    "year": 1,
                    "revenue": 1000000,
                    "free_cash_flow": 300000,
                    "ebitda": 400000
                }
            ]
        }
        
        output_path = Path(temp_dir) / "basic_dcf_test.pdf"
        
        try:
            # This should not raise an exception
            report_generator.generate_dcf_valuation_pdf(
                dcf_data=minimal_dcf_data,
                output_path=str(output_path)
            )
            
            # Check that file was created
            assert output_path.exists(), "PDF file was not created"
            assert output_path.stat().st_size > 1000, "PDF file is too small"
            
            # Validate PDF header
            with open(output_path, 'rb') as f:
                header = f.read(4)
                assert header == b'%PDF', "Generated file is not a valid PDF"
                
        except Exception as e:
            pytest.fail(f"DCF PDF generation failed: {e}")
    
    @pytest.mark.integration
    def test_basic_saas_pdf_generation(self, report_generator, temp_dir):
        """Test basic SaaS PDF generation with minimal data."""
        # Simple test data that matches the actual ReportGenerator interface
        minimal_saas_data = {
            "organization_id": 1,
            "company_name": "Test SaaS Company",
            "valuation_date": "2024-01-01",
            "key_metrics": {
                "arr": 5000000,
                "mrr": 416667,
                "growth_rate": 25,
                "churn_rate": 3
            },
            "valuation_methods": {
                "arr_multiple": {
                    "adjusted_valuation": 45000000
                }
            }
        }
        
        output_path = Path(temp_dir) / "basic_saas_test.pdf"
        
        try:
            # This should not raise an exception
            report_generator.generate_saas_valuation_pdf(
                saas_data=minimal_saas_data,
                output_path=str(output_path)
            )
            
            # Check that file was created
            assert output_path.exists(), "PDF file was not created"
            assert output_path.stat().st_size > 1000, "PDF file is too small"
            
            # Validate PDF header
            with open(output_path, 'rb') as f:
                header = f.read(4)
                assert header == b'%PDF', "Generated file is not a valid PDF"
                
        except Exception as e:
            pytest.fail(f"SaaS PDF generation failed: {e}")
    
    def test_pytest_markers(self):
        """Test that our custom pytest markers are working."""
        # This test should run without marker warnings
        assert True
    
    @pytest.mark.pdf
    def test_pdf_marker(self):
        """Test PDF marker functionality."""
        assert True
    
    @pytest.mark.excel
    def test_excel_marker(self):
        """Test Excel marker functionality."""
        assert True
    
    @pytest.mark.performance
    def test_performance_marker(self):
        """Test performance marker functionality."""
        assert True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])