"""
Tests for the RateLimiter utility (Including Issue #6 verification).

Enhanced tests to verify that inadequate in-memory rate limiting has been
replaced with Redis-based distributed rate limiting that persists across restarts.
"""
import pytest
from unittest.mock import patch, MagicMock
import time
import asyncio
import redis
from typing import Dict, Any

from mcx3d_finance.utils.rate_limiter import RateLimiter, RateLimitStrategy

@pytest.mark.asyncio
class TestRateLimiter:
    """
    Test suite for the RateLimiter.
    """

    async def test_sliding_window_limit(self, rate_limiter: RateLimiter):
        """
        Test the sliding window algorithm.
        """
        rate_limiter.strategy = RateLimitStrategy.SLIDING_WINDOW
        key = "test_sliding"
        limit_type = "api_default"

        # Consume the limit
        for _ in range(100):
            allowed, _ = await rate_limiter.check_rate_limit(key, limit_type)
            assert allowed

        # The next request should be denied
        allowed, info = await rate_limiter.check_rate_limit(key, limit_type)
        assert not allowed
        assert "retry_after" in info

    async def test_token_bucket_limit(self, rate_limiter: RateLimiter):
        """
        Test the token bucket algorithm.
        """
        rate_limiter.strategy = RateLimitStrategy.TOKEN_BUCKET
        key = "test_token_bucket"
        limit_type = "report_generation"  # 10 calls per 5 minutes

        # Consume the limit
        for _ in range(10):
            allowed, _ = await rate_limiter.check_rate_limit(key, limit_type)
            assert allowed

        # The next request should be denied
        allowed, info = await rate_limiter.check_rate_limit(key, limit_type)
        assert not allowed
        assert "retry_after" in info

    async def test_fixed_window_limit(self, rate_limiter: RateLimiter):
        """
        Test the fixed window algorithm.
        """
        rate_limiter.strategy = RateLimitStrategy.FIXED_WINDOW
        key = "test_fixed_window"
        limit_type = "auth_login"  # 5 calls per 5 minutes

        # Consume the limit
        for _ in range(5):
            allowed, _ = await rate_limiter.check_rate_limit(key, limit_type)
            assert allowed

        # The next request should be denied
        allowed, info = await rate_limiter.check_rate_limit(key, limit_type)
        assert not allowed
        assert "retry_after" in info

    def test_progressive_delays(self, rate_limiter: RateLimiter):
        """
        Test that repeated failed authentication attempts result in
        progressively longer delays.
        """
        key = "test_progressive_delay"
        action = "login"

        # Simulate failed attempts
        delays = []
        for _ in range(len(rate_limiter.auth_delays)):
            delay = rate_limiter.get_progressive_delay(key, action)
            delays.append(delay)

        assert delays == rate_limiter.auth_delays

        # Resetting should clear the delay
        rate_limiter.reset_progressive_delay(key, action)
        delay = rate_limiter.get_progressive_delay(key, action)
        assert delay == rate_limiter.auth_delays[0]

    async def test_whitelist_blacklist(self, rate_limiter: RateLimiter):
        """
        Test that whitelisted keys bypass rate limits and blacklisted keys
        are always blocked.
        """
        whitelisted_key = "whitelisted_user"
        blacklisted_key = "blacklisted_user"
        limit_type = "api_default"

        # Add to whitelist/blacklist
        rate_limiter.add_to_whitelist(whitelisted_key)
        rate_limiter.add_to_blacklist(blacklisted_key)

        # Whitelisted user should always be allowed
        for _ in range(110):  # Exceed the limit
            allowed, _ = await rate_limiter.check_rate_limit(
                whitelisted_key, limit_type
            )
            assert allowed

        # Blacklisted user should always be denied
        allowed, _ = await rate_limiter.check_rate_limit(
            blacklisted_key, limit_type
        )
        assert not allowed

    @patch("redis.Redis.eval", side_effect=ConnectionError("Redis is down"))
    async def test_redis_fallback(self, redis_mock, rate_limiter: RateLimiter):
        """
        Test the system's behavior when the Redis connection fails.
        """
        # In case of Redis failure, the rate limiter should fail open (allow requests)
        allowed, info = await rate_limiter.check_rate_limit(
            "test_fallback", "api_default"
        )
        assert allowed
        assert info.get("error") == "rate_limit_check_failed"


@pytest.mark.asyncio
class TestRedisRateLimitingIssue6:
    """
    Test suite specifically for Issue #6 verification.
    
    Verifies that the inadequate in-memory rate limiting mentioned in Issue #6
    has been replaced with proper Redis-based distributed rate limiting.
    """
    
    def test_redis_client_configured(self):
        """
        Verify that RateLimiter is configured to use Redis, not in-memory storage.
        Issue #6: "In-memory rate limiting won't scale and loses data on restart"
        """
        rate_limiter = RateLimiter()
        
        # Should have Redis client configured
        assert hasattr(rate_limiter, 'redis_client'), \
            "RateLimiter should be configured with Redis client (Issue #6 fix)"
        
        # Redis client should be a proper Redis connection
        assert rate_limiter.redis_client is not None, \
            "Redis client should be configured, not None (Issue #6 fix)"
        
        # Should not be using in-memory dictionaries for rate limiting
        # Check that rate limiting data is not stored in instance variables
        instance_vars = vars(rate_limiter)
        memory_storage_indicators = ['_counters', '_memory_store', '_local_cache', '_in_memory']
        
        for indicator in memory_storage_indicators:
            assert indicator not in instance_vars, \
                f"RateLimiter should not use in-memory storage '{indicator}' (Issue #6 fix)"
    
    async def test_rate_limit_persistence_across_instances(self):
        """
        Verify that rate limiting data persists across different RateLimiter instances.
        This simulates the scenario where the application restarts.
        Issue #6: Rate limiting should not "lose data on restart"
        """
        key = "persistence_test_user"
        limit_type = "api_default"
        
        # Create first rate limiter instance and consume some limit
        rate_limiter_1 = RateLimiter()
        
        # Consume half the limit (50 out of 100)
        for _ in range(50):
            allowed, _ = await rate_limiter_1.check_rate_limit(key, limit_type)
            assert allowed, "Should be allowed within limit"
        
        # Create second rate limiter instance (simulating app restart)
        rate_limiter_2 = RateLimiter()
        
        # The rate limit state should persist - consume remaining 50
        consumed_count = 0
        for _ in range(60):  # Try to consume 60 more (should only get 50)
            allowed, _ = await rate_limiter_2.check_rate_limit(key, limit_type)
            if allowed:
                consumed_count += 1
            else:
                break
        
        # Should have consumed exactly 50 more (total 100), then been rate limited
        assert consumed_count == 50, \
            f"Rate limit state should persist across instances (got {consumed_count}, expected 50)"
        
        # Next request should be denied
        allowed, info = await rate_limiter_2.check_rate_limit(key, limit_type)
        assert not allowed, "Should be rate limited after consuming full quota"
        assert "retry_after" in info, "Should provide retry_after information"
    
    async def test_distributed_rate_limiting(self):
        """
        Verify that rate limiting works across multiple "distributed" instances.
        Issue #6: In-memory rate limiting "won't scale" - Redis-based should scale.
        """
        key = "distributed_test_user"
        limit_type = "api_default"  # 100 requests per minute
        
        # Create multiple rate limiter instances (simulating distributed servers)
        rate_limiters = [RateLimiter() for _ in range(3)]
        
        total_allowed = 0
        total_requests = 120  # More than the limit of 100
        
        # Distribute requests across all instances
        for i in range(total_requests):
            limiter = rate_limiters[i % len(rate_limiters)]
            allowed, _ = await limiter.check_rate_limit(key, limit_type)
            if allowed:
                total_allowed += 1
        
        # Should have allowed exactly 100 requests across all instances
        assert total_allowed == 100, \
            f"Distributed rate limiting should allow exactly 100 requests (got {total_allowed})"
    
    def test_redis_connection_details(self):
        """
        Verify that Redis connection is properly configured for production use.
        """
        rate_limiter = RateLimiter()
        redis_client = rate_limiter.redis_client
        
        # Should have a proper Redis client
        assert isinstance(redis_client, (redis.Redis, redis.StrictRedis)), \
            "Should use proper Redis client type"
        
        # Connection should be testable
        try:
            redis_client.ping()
            redis_available = True
        except (redis.ConnectionError, AttributeError):
            redis_available = False
            pytest.skip("Redis not available for testing")
        
        if redis_available:
            # Should be able to set and get values
            test_key = "rate_limiter_test_connection"
            redis_client.set(test_key, "test_value", ex=10)
            value = redis_client.get(test_key)
            assert value == b"test_value", "Redis should store and retrieve values"
            
            # Clean up
            redis_client.delete(test_key)
    
    async def test_no_memory_leaks_in_rate_limiting(self):
        """
        Verify that rate limiting doesn't cause memory leaks by storing data locally.
        Issue #6: In-memory storage can cause memory leaks in long-running applications.
        """
        rate_limiter = RateLimiter()
        initial_vars = set(vars(rate_limiter).keys())
        
        # Perform many rate limit checks
        for i in range(1000):
            key = f"memory_test_user_{i % 10}"  # Use 10 different users
            await rate_limiter.check_rate_limit(key, "api_default")
        
        # Instance variables should not grow with usage
        final_vars = set(vars(rate_limiter).keys())
        new_vars = final_vars - initial_vars
        
        # Should not have added new instance variables during operation
        assert len(new_vars) == 0, \
            f"Rate limiter should not accumulate instance variables (found: {new_vars})"
    
    async def test_rate_limit_accuracy_under_load(self):
        """
        Test rate limiting accuracy under concurrent load.
        Issue #6: Distributed Redis-based rate limiting should be accurate under load.
        """
        key = "load_test_user"
        limit_type = "auth_login"  # 5 requests per 5 minutes
        rate_limiter = RateLimiter()
        
        async def make_request():
            allowed, _ = await rate_limiter.check_rate_limit(key, limit_type)
            return allowed
        
        # Make many concurrent requests
        concurrent_requests = 20
        tasks = [make_request() for _ in range(concurrent_requests)]
        results = await asyncio.gather(*tasks)
        
        # Should allow exactly 5 requests (the limit)
        allowed_count = sum(results)
        assert allowed_count == 5, \
            f"Under load, should allow exactly 5 requests (got {allowed_count})"
    
    def test_rate_limiter_redis_key_format(self):
        """
        Verify that Redis keys follow a proper format for production use.
        """
        rate_limiter = RateLimiter()
        
        # Test key generation method exists
        assert hasattr(rate_limiter, '_generate_redis_key') or \
               hasattr(rate_limiter, 'get_key') or \
               callable(getattr(rate_limiter, 'redis_client', None)), \
               "RateLimiter should have proper key generation for Redis"
    
    async def test_rate_limit_strategies_use_redis(self):
        """
        Verify that all rate limiting strategies use Redis, not in-memory storage.
        """
        key = "strategy_test_user"
        limit_type = "api_default"
        
        # Test all available strategies
        strategies = [
            RateLimitStrategy.FIXED_WINDOW,
            RateLimitStrategy.SLIDING_WINDOW,
            RateLimitStrategy.TOKEN_BUCKET,
            RateLimitStrategy.LEAKY_BUCKET
        ]
        
        for strategy in strategies:
            rate_limiter = RateLimiter(strategy=strategy)
            
            # Make a request - this should use Redis
            allowed, info = await rate_limiter.check_rate_limit(key, limit_type)
            
            # Should succeed (basic functionality test)
            assert isinstance(allowed, bool), \
                f"Strategy {strategy.value} should return boolean result"
            assert isinstance(info, dict), \
                f"Strategy {strategy.value} should return info dict"


class TestRateLimitingIntegration:
    """Integration tests for rate limiting with the actual API."""
    
    def test_rate_limiting_middleware_integration(self):
        """
        Verify that rate limiting is properly integrated with the API middleware.
        """
        # Test that rate limiter is imported and used in auth middleware
        try:
            from mcx3d_finance.api.auth_middleware import auth_limiter
            assert auth_limiter is not None, \
                "auth_limiter should be available in middleware"
        except ImportError:
            pytest.fail("Rate limiter should be integrated with auth middleware")
    
    def test_rate_limiting_configuration(self):
        """
        Verify that rate limiting configuration is properly loaded.
        """
        from mcx3d_finance.core.config import get_security_config
        
        security_config = get_security_config()
        rate_limits = security_config.get("rate_limits", {})
        
        # Should have rate limit configurations
        assert len(rate_limits) > 0, "Rate limits should be configured"
        
        # Should have auth-specific rate limits (Issue #6 mentioned auth rate limiting)
        auth_limits = ["auth_login", "auth_register", "password_reset"]
        for limit_type in auth_limits:
            assert limit_type in rate_limits, \
                f"Should have rate limit configured for {limit_type}"
            
            limit_config = rate_limits[limit_type]
            assert "calls" in limit_config, f"{limit_type} should have calls limit"
            assert "period" in limit_config, f"{limit_type} should have period limit"
