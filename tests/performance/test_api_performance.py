"""
Performance and load testing for API endpoints (Phase 3.2).

Tests API performance under load, rate limiting effectiveness,
and concurrent request handling.
"""

import pytest
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
from typing import List, Dict, Any

from mcx3d_finance.main import app


class TestAPIPerformance:
    """Test suite for API performance under various conditions."""
    
    def test_basic_endpoint_response_time(self):
        """
        Test that basic endpoints respond within acceptable time limits.
        """
        client = TestClient(app)
        
        # Define performance thresholds
        performance_thresholds = {
            "/": 500,  # 500ms for root endpoint
            "/docs": 1000,  # 1s for documentation
        }
        
        for endpoint, threshold_ms in performance_thresholds.items():
            start_time = time.time()
            response = client.get(endpoint)
            end_time = time.time()
            
            response_time_ms = (end_time - start_time) * 1000
            
            assert response.status_code == 200, \
                f"Endpoint {endpoint} should respond successfully"
            
            assert response_time_ms < threshold_ms, \
                f"Endpoint {endpoint} should respond within {threshold_ms}ms " \
                f"(took {response_time_ms:.2f}ms)"
    
    def test_concurrent_request_handling(self):
        """
        Test API's ability to handle concurrent requests.
        """
        client = TestClient(app)
        
        def make_request():
            start_time = time.time()
            response = client.get("/")
            end_time = time.time()
            return {
                "status_code": response.status_code,
                "response_time": end_time - start_time,
                "success": response.status_code == 200
            }
        
        # Test with 10 concurrent requests
        concurrent_requests = 10
        
        with ThreadPoolExecutor(max_workers=concurrent_requests) as executor:
            futures = [executor.submit(make_request) for _ in range(concurrent_requests)]
            results = [future.result() for future in as_completed(futures)]
        
        # All requests should succeed
        successful_requests = sum(1 for result in results if result["success"])
        assert successful_requests == concurrent_requests, \
            f"All {concurrent_requests} concurrent requests should succeed " \
            f"(got {successful_requests})"
        
        # Average response time should be reasonable
        avg_response_time = sum(result["response_time"] for result in results) / len(results)
        assert avg_response_time < 2.0, \
            f"Average response time under load should be < 2s (got {avg_response_time:.2f}s)"
    
    def test_rate_limiting_performance(self):
        """
        Test that rate limiting doesn't significantly impact performance.
        """
        client = TestClient(app)
        
        # Test endpoint that should have rate limiting
        endpoint = "/api/auth/login"
        test_data = {"email": "<EMAIL>", "password": "testpassword"}
        
        # Make requests and measure performance
        response_times = []
        
        for i in range(5):  # Small number to avoid triggering rate limits
            start_time = time.time()
            response = client.post(endpoint, json=test_data)
            end_time = time.time()
            
            response_times.append(end_time - start_time)
            
            # Small delay to avoid rapid consecutive requests
            time.sleep(0.1)
        
        # Calculate performance metrics
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        
        # Performance should be consistent even with rate limiting
        assert avg_response_time < 1.0, \
            f"Rate limited endpoint average response time should be < 1s " \
            f"(got {avg_response_time:.2f}s)"
        
        assert max_response_time < 2.0, \
            f"Rate limited endpoint max response time should be < 2s " \
            f"(got {max_response_time:.2f}s)"
    
    @pytest.mark.slow
    def test_sustained_load_performance(self):
        """
        Test API performance under sustained load.
        """
        client = TestClient(app)
        
        def sustained_requests(duration_seconds=5):
            """Make requests for specified duration."""
            start_time = time.time()
            request_count = 0
            response_times = []
            errors = 0
            
            while time.time() - start_time < duration_seconds:
                request_start = time.time()
                try:
                    response = client.get("/")
                    request_end = time.time()
                    
                    response_times.append(request_end - request_start)
                    request_count += 1
                    
                    if response.status_code != 200:
                        errors += 1
                        
                except Exception:
                    errors += 1
                
                # Small delay to prevent overwhelming
                time.sleep(0.01)
            
            return {
                "request_count": request_count,
                "response_times": response_times,
                "errors": errors,
                "duration": duration_seconds
            }
        
        # Run sustained load test
        results = sustained_requests(duration_seconds=3)  # Shorter duration for tests
        
        # Calculate metrics
        avg_response_time = sum(results["response_times"]) / len(results["response_times"])
        requests_per_second = results["request_count"] / results["duration"]
        error_rate = results["errors"] / results["request_count"] if results["request_count"] > 0 else 0
        
        # Performance assertions
        assert avg_response_time < 0.5, \
            f"Average response time under sustained load should be < 0.5s " \
            f"(got {avg_response_time:.3f}s)"
        
        assert requests_per_second > 10, \
            f"Should handle > 10 requests/second (got {requests_per_second:.1f})"
        
        assert error_rate < 0.05, \
            f"Error rate should be < 5% (got {error_rate:.1%})"
    
    def test_memory_usage_stability(self):
        """
        Test that memory usage remains stable under load.
        """
        import psutil
        import os
        
        client = TestClient(app)
        process = psutil.Process(os.getpid())
        
        # Get initial memory usage
        initial_memory = process.memory_info().rss / (1024 * 1024)  # MB
        
        # Make many requests
        for i in range(50):
            response = client.get("/")
            if i % 10 == 0:  # Check memory every 10 requests
                current_memory = process.memory_info().rss / (1024 * 1024)
                memory_increase = current_memory - initial_memory
                
                # Memory shouldn't grow excessively
                assert memory_increase < 50, \
                    f"Memory usage should not increase by more than 50MB " \
                    f"(increased by {memory_increase:.1f}MB after {i+1} requests)"
        
        # Final memory check
        final_memory = process.memory_info().rss / (1024 * 1024)
        total_memory_increase = final_memory - initial_memory
        
        assert total_memory_increase < 100, \
            f"Total memory increase should be < 100MB " \
            f"(increased by {total_memory_increase:.1f}MB)"


class TestRateLimitingPerformance:
    """Test rate limiting performance and effectiveness."""
    
    def test_rate_limiting_triggers_correctly(self):
        """
        Test that rate limiting triggers at expected thresholds.
        """
        client = TestClient(app)
        
        # Use auth endpoint which should have rate limiting
        endpoint = "/api/auth/login"
        test_data = {"email": "<EMAIL>", "password": "testpass"}
        
        # Make requests until rate limited
        successful_requests = 0
        rate_limited_requests = 0
        
        for i in range(20):  # Try more requests than typical limit
            response = client.post(endpoint, json=test_data)
            
            if response.status_code == 429:  # Too Many Requests
                rate_limited_requests += 1
                
                # Should have rate limiting headers
                assert "Retry-After" in response.headers or \
                       "X-RateLimit-Reset" in response.headers, \
                       "Rate limited response should include retry information"
                
                break  # Stop after first rate limit
            elif response.status_code in [400, 401, 422]:  # Expected auth errors
                successful_requests += 1
            
            # Small delay between requests
            time.sleep(0.1)
        
        # Should have processed some requests before rate limiting
        assert successful_requests > 0, \
            "Should process some requests before rate limiting"
    
    def test_rate_limiting_reset_functionality(self):
        """
        Test that rate limits reset after the specified period.
        """
        client = TestClient(app)
        
        # This test is time-dependent and may be skipped in fast test runs
        endpoint = "/api/auth/login"
        test_data = {"email": "<EMAIL>", "password": "testpass"}
        
        # Make requests to approach rate limit
        for i in range(3):  # Conservative number
            response = client.post(endpoint, json=test_data)
            time.sleep(0.2)
        
        # Wait for potential rate limit reset (short wait for tests)
        time.sleep(2)
        
        # Should be able to make more requests after waiting
        response = client.post(endpoint, json=test_data)
        
        # Should not be rate limited immediately after reset period
        assert response.status_code != 429, \
            "Rate limit should reset after waiting period"
    
    def test_rate_limiting_per_ip_isolation(self):
        """
        Test that rate limiting is properly isolated per IP address.
        """
        client = TestClient(app)
        
        endpoint = "/api/auth/login"
        
        # Simulate different IP addresses
        ip_addresses = ["***********", "***********", "***********"]
        
        for ip in ip_addresses:
            # Mock the IP address
            with patch('mcx3d_finance.utils.rate_limiter.RateLimiter.get_client_ip') as mock_get_ip:
                mock_get_ip.return_value = ip
                
                # Each IP should have its own rate limit allowance
                response = client.post(endpoint, json={
                    "email": f"user@{ip.replace('.', '')}.com", 
                    "password": "test"
                })
                
                # Should not be immediately rate limited for new IP
                assert response.status_code != 429, \
                    f"New IP {ip} should not be immediately rate limited"


class TestAPIScalability:
    """Test API scalability characteristics."""
    
    def test_response_time_consistency_under_load(self):
        """
        Test that response times remain consistent as load increases.
        """
        client = TestClient(app)
        
        load_levels = [1, 5, 10]  # Different concurrent request levels
        response_time_data = {}
        
        for load_level in load_levels:
            def make_request():
                start_time = time.time()
                response = client.get("/")
                end_time = time.time()
                return end_time - start_time
            
            with ThreadPoolExecutor(max_workers=load_level) as executor:
                futures = [executor.submit(make_request) for _ in range(load_level)]
                response_times = [future.result() for future in as_completed(futures)]
            
            avg_response_time = sum(response_times) / len(response_times)
            response_time_data[load_level] = avg_response_time
        
        # Response times shouldn't degrade significantly under moderate load
        baseline_time = response_time_data[1]
        moderate_load_time = response_time_data[5]
        
        # Allow 2x degradation for 5x load increase
        assert moderate_load_time < baseline_time * 2, \
            f"Response time shouldn't degrade too much under load " \
            f"(baseline: {baseline_time:.3f}s, 5x load: {moderate_load_time:.3f}s)"
    
    def test_error_handling_performance(self):
        """
        Test that error handling doesn't significantly impact performance.
        """
        client = TestClient(app)
        
        # Test error endpoint performance
        error_endpoints = [
            ("/nonexistent", "GET", None, 404),
            ("/api/auth/login", "POST", {"invalid": "data"}, [400, 422]),
        ]
        
        for endpoint, method, data, expected_status in error_endpoints:
            start_time = time.time()
            
            if method == "GET":
                response = client.get(endpoint)
            elif method == "POST":
                response = client.post(endpoint, json=data)
            
            end_time = time.time()
            response_time = end_time - start_time
            
            # Verify expected error status
            if isinstance(expected_status, list):
                assert response.status_code in expected_status
            else:
                assert response.status_code == expected_status
            
            # Error responses should be fast
            assert response_time < 1.0, \
                f"Error response for {endpoint} should be fast " \
                f"(took {response_time:.3f}s)"


@pytest.mark.benchmark
class TestPerformanceBenchmarks:
    """Benchmark tests for establishing performance baselines."""
    
    def test_baseline_performance_metrics(self):
        """
        Establish baseline performance metrics for regression testing.
        """
        client = TestClient(app)
        
        # Run multiple iterations to get stable measurements
        iterations = 10
        response_times = []
        
        for _ in range(iterations):
            start_time = time.time()
            response = client.get("/")
            end_time = time.time()
            
            assert response.status_code == 200
            response_times.append(end_time - start_time)
        
        # Calculate statistics
        avg_time = sum(response_times) / len(response_times)
        min_time = min(response_times)
        max_time = max(response_times)
        
        # Log baseline metrics (for future regression testing)
        baseline_metrics = {
            "endpoint": "/",
            "iterations": iterations,
            "avg_response_time": avg_time,
            "min_response_time": min_time,
            "max_response_time": max_time,
            "timestamp": time.time()
        }
        
        # Assert reasonable performance
        assert avg_time < 0.1, f"Baseline average response time should be < 100ms (got {avg_time:.3f}s)"
        assert max_time < 0.5, f"Baseline max response time should be < 500ms (got {max_time:.3f}s)"
        
        # Could write baseline metrics to file for tracking
        print(f"Baseline metrics: {baseline_metrics}")


if __name__ == "__main__":
    # Run performance tests
    pytest.main([__file__, "-v", "--tb=short", "-m", "not slow"])