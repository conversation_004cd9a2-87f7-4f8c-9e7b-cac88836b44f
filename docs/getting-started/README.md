# Getting Started with MCX3D Financial System

Welcome to MCX3D Financial System! This section contains everything you need to get up and running with the system.

## 📋 **Prerequisites**

Before you begin, ensure you have:
- **Docker & Docker Compose** (for containerized deployment)
- **Python 3.9+** (for local development)
- **PostgreSQL 13+** (for database)
- **Redis 6+** (for caching and task queuing)

## 🚀 **Quick Start (5 minutes)**

### 1. <PERSON>lone and Start Services
```bash
git clone https://github.com/mcx3d/mcx3d-financials.git
cd mcx3d-financials/v2
docker-compose up --build
```

### 2. Verify Installation
- **API**: http://localhost:8000 (FastAPI application)
- **Database**: PostgreSQL on port 5432
- **Cache**: Redis on port 6379
- **Workers**: Celery background processing

### 3. Generate Your First Report
```bash
# Via CLI
docker-compose exec web python -m mcx3d_finance.cli.main generate balance-sheet --organization-id 1

# Via API
curl -X POST http://localhost:8000/api/reports/balance-sheet \
  -H "Content-Type: application/json" \
  -d '{"organization_id": 1, "as_of_date": "2024-12-31"}'
```

## 📚 **Available Guides**

### **System Configuration**
- **[Configuration Guide](./configuration.md)** - Complete system configuration including environment variables, database setup, and Xero integration

## 🔧 **Installation Options**

### **Option 1: Docker (Recommended)**
```bash
# Full stack with all services
docker-compose up --build

# Services available:
# - Web API: localhost:8000
# - Database: localhost:5432  
# - Redis: localhost:6379
# - Celery Worker: background processing
```

### **Option 2: Local Development**
```bash
# Install dependencies
pip install -r requirements.txt

# Set up database
createdb mcx3d_financial
alembic upgrade head

# Start services
uvicorn mcx3d_finance.main:app --reload
celery -A mcx3d_finance.tasks.celery_app worker --loglevel=info
```

## 🔐 **Initial Setup**

### **Environment Variables**
Create a `.env` file with required configuration:
```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/mcx3d_financial

# Redis
REDIS_URL=redis://localhost:6379/0

# Xero Integration
XERO_CLIENT_ID=your-xero-client-id
XERO_CLIENT_SECRET=your-xero-client-secret

# Security
SECRET_KEY=your-secret-key
```

### **Database Migration**
```bash
# Run database migrations
docker-compose exec web alembic upgrade head

# Or locally
alembic upgrade head
```

## 🧪 **Verify Installation**

### **Health Checks**
```bash
# API health check
curl http://localhost:8000/health

# Database connectivity
docker-compose exec web python -c "from mcx3d_finance.db.session import SessionLocal; print('DB OK')"

# Run basic tests
docker-compose exec web pytest -m smoke
```

### **Generate Test Data**
```bash
# Create sample organization and data
docker-compose exec web python scripts/setup_test_data.py

# Generate sample reports
docker-compose exec web python -m mcx3d_finance.cli.main generate income-statement --organization-id 1
```

## 🎯 **Next Steps**

### **For Users**
1. **[User Guide](../user-guide/overview.md)** - Learn how to use the system
2. **[Xero Integration](../user-guide/xero-integration.md)** - Connect your Xero account
3. **[Quick Reference](../user-guide/quick-reference.md)** - Common commands and workflows

### **For Developers**
1. **[Testing Guide](../developer/testing.md)** - Set up your development environment
2. **[API Reference](../developer/api-reference.md)** - Understand the API endpoints
3. **[Claude Integration](../developer/claude-integration.md)** - AI-assisted development

### **For Operations**
1. **[Deployment Guide](../operations/deployment.md)** - Production deployment
2. **[Monitoring Guide](../operations/monitoring.md)** - Set up monitoring and alerting
3. **[Security Guide](../operations/security.md)** - Security configuration

## ❓ **Troubleshooting**

### **Common Issues**

#### Port Conflicts
```bash
# If ports 8000, 5432, or 6379 are in use:
docker-compose down
# Edit docker-compose.yml to use different ports
docker-compose up
```

#### Permission Issues
```bash
# Fix Docker permissions
sudo chown -R $USER:$USER .
chmod +x scripts/*.sh
```

#### Database Connection Issues
```bash
# Reset database
docker-compose down -v  # WARNING: This removes all data
docker-compose up --build
```

### **Getting Help**
- **Documentation**: Check the relevant guide in this documentation
- **Logs**: Use `docker-compose logs [service]` to view service logs
- **Health Checks**: Run `curl http://localhost:8000/health` to verify API status
- **Testing**: Use `docker-compose exec web pytest -m smoke` for basic validation

## 📈 **System Architecture Overview**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Web Browser   │    │    API Client    │    │      CLI        │
└────────┬────────┘    └─────────┬────────┘    └────────┬────────┘
         │                       │                      │
         └───────────────────────┼──────────────────────┘
                                 │
                    ┌────────────▼────────────┐
                    │     FastAPI Server      │
                    │     (Port 8000)         │
                    └────────────┬────────────┘
                                 │
                ┌────────────────┼────────────────┐
                │                │                │
      ┌─────────▼─────────┐   ┌──▼──────┐   ┌────▼─────┐
      │   PostgreSQL      │   │  Redis  │   │  Celery  │
      │   (Port 5432)     │   │ (6379)  │   │ Worker   │
      └───────────────────┘   └─────────┘   └──────────┘
```

**Components:**
- **FastAPI Server**: REST API with automatic documentation
- **PostgreSQL**: Primary data storage with financial models
- **Redis**: Caching and task queue management
- **Celery Worker**: Background task processing
- **CLI Interface**: Command-line tools for batch operations

---

**Need help?** Check out our comprehensive guides above, or refer to the main [documentation hub](../README.md) for more resources.