"""
Advanced account mapping module with industry-specific chart of accounts for UK FRS 102.
Provides flexible mapping rules and intelligent account classification.
"""

from typing import Dict, Any, List
import logging
from enum import Enum
from dataclasses import dataclass
import re
from functools import lru_cache

from mcx3d_finance.core.account_classifications import FRS102AccountClassification

logger = logging.getLogger(__name__)


class IndustryType(Enum):
    """Industry classifications for specialized account mapping."""
    TECHNOLOGY = "technology"
    MANUFACTURING = "manufacturing"
    RETAIL = "retail"
    HEALTHCARE = "healthcare"
    FINANCIAL_SERVICES = "financial_services"
    REAL_ESTATE = "real_estate"
    CONSTRUCTION = "construction"
    PROFESSIONAL_SERVICES = "professional_services"
    HOSPITALITY = "hospitality"
    EDUCATION = "education"
    NON_PROFIT = "non_profit"
    GENERAL = "general"


class AccountType(Enum):
    """Extended account types for comprehensive classification."""
    ASSET = "asset"
    LIABILITY = "liability"
    EQUITY = "equity"
    REVENUE = "revenue"
    EXPENSE = "expense"
    CONTRA_ASSET = "contra_asset"
    CONTRA_LIABILITY = "contra_liability"
    CONTRA_EQUITY = "contra_equity"
    CONTRA_REVENUE = "contra_revenue"


@dataclass
class AccountMappingRule:
    """Account mapping rule definition."""
    pattern: str  # Regex pattern or exact match
    frs102_classification: FRS102AccountClassification
    account_type: AccountType
    industry_specific: bool = False
    confidence_score: float = 1.0
    description: str = ""
    
    def matches(self, account_code: str, account_name: str) -> bool:
        """Check if this rule matches the given account."""
        try:
            if self.pattern == account_code:
                return True
            if re.match(self.pattern, account_code, re.IGNORECASE):
                return True
            if re.search(self.pattern, account_name, re.IGNORECASE):
                return True
            return False
        except re.error:
            return (self.pattern.lower() in account_code.lower() or 
                    self.pattern.lower() in account_name.lower())


@dataclass
class MappingResult:
    """Result of account mapping operation."""
    original_code: str
    original_name: str
    frs102_classification: FRS102AccountClassification
    account_type: AccountType
    confidence_score: float
    rule_description: str
    industry_specific: bool = False


class AdvancedAccountMapper:
    """Advanced account mapper with industry-specific rules for FRS 102."""
    
    def __init__(self, industry: IndustryType = IndustryType.GENERAL):
        self.industry = industry
        self.mapping_rules = self._load_mapping_rules()
        self.custom_rules: List[AccountMappingRule] = []
        self._mapping_cache: Dict[str, MappingResult] = {}
    
    def _load_mapping_rules(self) -> List[AccountMappingRule]:
        """Load comprehensive mapping rules for FRS 102."""
        rules = []
        rules.extend(self._get_standard_asset_rules())
        rules.extend(self._get_standard_liability_rules())
        rules.extend(self._get_standard_equity_rules())
        rules.extend(self._get_standard_revenue_rules())
        rules.extend(self._get_standard_expense_rules())
        return rules
    
    def _get_standard_asset_rules(self) -> List[AccountMappingRule]:
        """Get standard asset account mapping rules for FRS 102."""
        return [
            AccountMappingRule(
                pattern=r"cash|checking|savings",
                frs102_classification=FRS102AccountClassification.CASH_AT_BANK_AND_IN_HAND,
                account_type=AccountType.ASSET,
                description="Cash and bank accounts"
            ),
            AccountMappingRule(
                pattern=r"debtors|receivable|a/r",
                frs102_classification=FRS102AccountClassification.DEBTORS,
                account_type=AccountType.ASSET,
                description="Debtors (Accounts Receivable)"
            ),
            AccountMappingRule(
                pattern=r"stock|inventory",
                frs102_classification=FRS102AccountClassification.STOCKS,
                account_type=AccountType.ASSET,
                description="Stocks (Inventory)"
            ),
            AccountMappingRule(
                pattern=r"equipment|machinery|building|land|vehicle",
                frs102_classification=FRS102AccountClassification.TANGIBLE_ASSETS,
                account_type=AccountType.ASSET,
                description="Tangible Fixed Assets"
            ),
            AccountMappingRule(
                pattern=r"patent|trademark|goodwill",
                frs102_classification=FRS102AccountClassification.INTANGIBLE_ASSETS,
                account_type=AccountType.ASSET,
                description="Intangible Fixed Assets"
            ),
        ]
    
    def _get_standard_liability_rules(self) -> List[AccountMappingRule]:
        """Get standard liability account mapping rules for FRS 102."""
        return [
            AccountMappingRule(
                pattern=r"creditors|payable|a/p",
                frs102_classification=FRS102AccountClassification.CREDITORS_DUE_WITHIN_ONE_YEAR,
                account_type=AccountType.LIABILITY,
                description="Creditors due within one year"
            ),
            AccountMappingRule(
                pattern=r"loan|mortgage|bond",
                frs102_classification=FRS102AccountClassification.CREDITORS_DUE_AFTER_ONE_YEAR,
                account_type=AccountType.LIABILITY,
                description="Creditors due after one year"
            ),
        ]
    
    def _get_standard_equity_rules(self) -> List[AccountMappingRule]:
        """Get standard equity account mapping rules for FRS 102."""
        return [
            AccountMappingRule(
                pattern=r"share capital|common stock",
                frs102_classification=FRS102AccountClassification.CALLED_UP_SHARE_CAPITAL,
                account_type=AccountType.EQUITY,
                description="Called up share capital"
            ),
            AccountMappingRule(
                pattern=r"retained earnings|profit and loss",
                frs102_classification=FRS102AccountClassification.PROFIT_AND_LOSS_ACCOUNT,
                account_type=AccountType.EQUITY,
                description="Profit and loss account"
            ),
        ]
    
    def _get_standard_revenue_rules(self) -> List[AccountMappingRule]:
        """Get standard revenue account mapping rules for FRS 102."""
        return [
            AccountMappingRule(
                pattern=r"turnover|revenue|sales",
                frs102_classification=FRS102AccountClassification.TURNOVER,
                account_type=AccountType.REVENUE,
                description="Turnover"
            ),
        ]
    
    def _get_standard_expense_rules(self) -> List[AccountMappingRule]:
        """Get standard expense account mapping rules for FRS 102."""
        return [
            AccountMappingRule(
                pattern=r"cost of sales|cogs",
                frs102_classification=FRS102AccountClassification.COST_OF_SALES,
                account_type=AccountType.EXPENSE,
                description="Cost of sales"
            ),
            AccountMappingRule(
                pattern=r"distribution|delivery|freight",
                frs102_classification=FRS102AccountClassification.DISTRIBUTION_COSTS,
                account_type=AccountType.EXPENSE,
                description="Distribution costs"
            ),
            AccountMappingRule(
                pattern=r"admin|administrative|office|salaries",
                frs102_classification=FRS102AccountClassification.ADMINISTRATIVE_EXPENSES,
                account_type=AccountType.EXPENSE,
                description="Administrative expenses"
            ),
            AccountMappingRule(
                pattern=r"interest payable",
                frs102_classification=FRS102AccountClassification.INTEREST_PAYABLE,
                account_type=AccountType.EXPENSE,
                description="Interest payable"
            ),
            AccountMappingRule(
                pattern=r"tax|corporation tax",
                frs102_classification=FRS102AccountClassification.TAXATION,
                account_type=AccountType.EXPENSE,
                description="Taxation"
            ),
        ]

    @lru_cache(maxsize=1000)
    def map_account(self, account_code: str, account_name: str = "") -> MappingResult:
        """Map an account to FRS 102 classification."""
        cache_key = f"{account_code}_{account_name}"
        if cache_key in self._mapping_cache:
            return self._mapping_cache[cache_key]

        for rule in self.custom_rules:
            if rule.matches(account_code, account_name):
                result = MappingResult(
                    original_code=account_code,
                    original_name=account_name,
                    frs102_classification=rule.frs102_classification,
                    account_type=rule.account_type,
                    confidence_score=rule.confidence_score,
                    rule_description=rule.description,
                    industry_specific=rule.industry_specific
                )
                self._mapping_cache[cache_key] = result
                return result

        best_match = None
        best_score = 0.0
        for rule in self.mapping_rules:
            if rule.matches(account_code, account_name):
                if rule.confidence_score > best_score:
                    best_match = rule
                    best_score = rule.confidence_score

        if best_match:
            result = MappingResult(
                original_code=account_code,
                original_name=account_name,
                frs102_classification=best_match.frs102_classification,
                account_type=best_match.account_type,
                confidence_score=best_match.confidence_score,
                rule_description=best_match.description,
                industry_specific=best_match.industry_specific
            )
            self._mapping_cache[cache_key] = result
            return result

        # Fallback if no rule matches
        return MappingResult(
            original_code=account_code,
            original_name=account_name,
            frs102_classification=FRS102AccountClassification.ADMINISTRATIVE_EXPENSES, # Default fallback
            account_type=AccountType.EXPENSE,
            confidence_score=0.1,
            rule_description="Fallback mapping"
        )

    def add_custom_rule(self, rule: AccountMappingRule):
        """Add a custom mapping rule."""
        self.custom_rules.append(rule)
        self._mapping_cache.clear()
        self.map_account.cache_clear()
