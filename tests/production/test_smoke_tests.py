# tests/production/test_smoke_tests.py

"""
Production smoke tests for MCX3D Financial System.

These tests validate core functionality in production-like environments
with minimal data and quick execution times.
"""

import pytest
import os
import tempfile
import json
from pathlib import Path
from datetime import datetime, timedelta
from decimal import Decimal

from mcx3d_finance.reporting.generator import ReportGenerator
from mcx3d_finance.core.financials.balance_sheet import BalanceSheetGenerator
from mcx3d_finance.core.financials.income_statement import IncomeStatementGenerator


@pytest.mark.smoke
@pytest.mark.production
class TestProductionSmokeTests:
    """
    Critical smoke tests that must pass in production environments.
    These tests use minimal data and focus on core functionality.
    """
    
    @pytest.fixture
    def temp_output_dir(self):
        """Create temporary directory for test outputs."""
        temp_dir = tempfile.mkdtemp(prefix="mcx3d_smoke_")
        yield temp_dir
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def minimal_dcf_data(self):
        """Minimal DCF data for smoke testing."""
        return {
            "organization_id": 1,
            "company_name": "Smoke Test Corp",
            "valuation_date": "2024-01-01",
            "enterprise_value": 5000000,
            "financial_projections": [
                {
                    "year": 1,
                    "revenue": 1000000,
                    "free_cash_flow": 200000,
                    "ebitda": 300000,
                    "revenue_growth_rate": 0.15
                }
            ],
            "assumptions": {
                "discount_rate": 0.12,
                "terminal_growth": 0.025,
                "tax_rate": 0.21
            }
        }
    
    @pytest.fixture
    def minimal_saas_data(self):
        """Minimal SaaS data for smoke testing."""
        return {
            "organization_id": 1,
            "company_name": "Smoke Test SaaS",
            "valuation_date": "2024-01-01",
            "key_metrics": {
                "arr": 2000000,
                "mrr": 166667,
                "growth_rate": 30,
                "churn_rate": 4,
                "ltv_cac_ratio": 4.5,
                "customer_acquisition_cost": 200,
                "customer_lifetime_value": 900,
                "active_customers": 500
            },
            "valuation_methods": {
                "arr_multiple": {
                    "adjusted_valuation": 18000000,
                    "base_arr": 2000000,
                    "adjusted_multiple": 9.0
                }
            }
        }
    
    def test_system_health_check(self):
        """Basic system health and import validation."""
        # Test that core modules can be imported
        try:
            from mcx3d_finance.reporting.generator import ReportGenerator
            from mcx3d_finance.core.financials.balance_sheet import BalanceSheetGenerator
            from mcx3d_finance.core.financials.income_statement import IncomeStatementGenerator
            
            # Test that classes can be instantiated
            report_gen = ReportGenerator()
            assert report_gen is not None
            
            balance_gen = BalanceSheetGenerator()
            assert balance_gen is not None
            
            income_gen = IncomeStatementGenerator()
            assert income_gen is not None
            
        except ImportError as e:
            pytest.fail(f"Critical import failure: {e}")
        except Exception as e:
            pytest.fail(f"System instantiation failure: {e}")
    
    def test_dcf_pdf_generation_smoke(self, minimal_dcf_data, temp_output_dir):
        """Smoke test for DCF PDF generation - must complete in under 10 seconds."""
        import time
        
        output_path = Path(temp_output_dir) / "smoke_dcf.pdf"
        report_generator = ReportGenerator()
        
        start_time = time.time()
        
        try:
            report_generator.generate_dcf_valuation_pdf(
                dcf_data=minimal_dcf_data,
                output_path=str(output_path)
            )
            
            end_time = time.time()
            generation_time = end_time - start_time
            
            # Validate output
            assert output_path.exists(), "PDF file was not created"
            assert output_path.stat().st_size > 1000, "PDF file is too small"
            
            # Performance requirement for production
            assert generation_time < 10.0, f"PDF generation too slow: {generation_time:.2f}s"
            
            # Validate PDF structure
            with open(output_path, 'rb') as f:
                header = f.read(4)
                assert header == b'%PDF', "Invalid PDF format"
                
        except Exception as e:
            pytest.fail(f"DCF PDF generation failed: {e}")
    
    def test_saas_pdf_generation_smoke(self, minimal_saas_data, temp_output_dir):
        """Smoke test for SaaS PDF generation - must complete in under 10 seconds."""
        import time
        
        output_path = Path(temp_output_dir) / "smoke_saas.pdf"
        report_generator = ReportGenerator()
        
        start_time = time.time()
        
        try:
            report_generator.generate_saas_valuation_pdf(
                saas_data=minimal_saas_data,
                output_path=str(output_path)
            )
            
            end_time = time.time()
            generation_time = end_time - start_time
            
            # Validate output
            assert output_path.exists(), "PDF file was not created"
            assert output_path.stat().st_size > 1000, "PDF file is too small"
            
            # Performance requirement for production
            assert generation_time < 10.0, f"PDF generation too slow: {generation_time:.2f}s"
            
            # Validate PDF structure
            with open(output_path, 'rb') as f:
                header = f.read(4)
                assert header == b'%PDF', "Invalid PDF format"
                
        except Exception as e:
            pytest.fail(f"SaaS PDF generation failed: {e}")
    
    def test_memory_usage_smoke(self, minimal_dcf_data, temp_output_dir):
        """Ensure memory usage stays within reasonable bounds."""
        try:
            import psutil
            process = psutil.Process()
            initial_memory = process.memory_info().rss
            
            # Generate multiple reports to test memory usage
            report_generator = ReportGenerator()
            
            for i in range(5):
                output_path = Path(temp_output_dir) / f"memory_test_{i}.pdf"
                report_generator.generate_dcf_valuation_pdf(
                    dcf_data=minimal_dcf_data,
                    output_path=str(output_path)
                )
            
            final_memory = process.memory_info().rss
            memory_increase = final_memory - initial_memory
            
            # Memory increase should be reasonable (less than 100MB for 5 reports)
            max_memory_increase = 100 * 1024 * 1024  # 100MB
            assert memory_increase < max_memory_increase, \
                f"Memory usage too high: {memory_increase / 1024 / 1024:.1f}MB increase"
                
        except ImportError:
            pytest.skip("psutil not available for memory testing")
        except Exception as e:
            pytest.fail(f"Memory usage smoke test failed: {e}")
    
    def test_concurrent_generation_smoke(self, minimal_dcf_data, temp_output_dir):
        """Test that concurrent report generation doesn't cause issues."""
        import concurrent.futures
        import time
        
        def generate_report(index):
            output_path = Path(temp_output_dir) / f"concurrent_{index}.pdf"
            report_generator = ReportGenerator()
            
            start_time = time.time()
            report_generator.generate_dcf_valuation_pdf(
                dcf_data=minimal_dcf_data,
                output_path=str(output_path)
            )
            end_time = time.time()
            
            return {
                'index': index,
                'success': output_path.exists(),
                'size': output_path.stat().st_size if output_path.exists() else 0,
                'time': end_time - start_time
            }
        
        # Test with 3 concurrent reports (moderate concurrency)
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(generate_report, i) for i in range(3)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # Validate all reports were generated successfully
        for result in results:
            assert result['success'], f"Report {result['index']} failed to generate"
            assert result['size'] > 1000, f"Report {result['index']} is too small"
            assert result['time'] < 15.0, f"Report {result['index']} took too long: {result['time']:.2f}s"
    
    def test_error_handling_smoke(self, temp_output_dir):
        """Test that the system handles errors gracefully."""
        report_generator = ReportGenerator()
        
        # Test with invalid data
        invalid_dcf_data = {
            "organization_id": None,  # Invalid
            "company_name": "",       # Empty
            "financial_projections": []  # Empty
        }
        
        output_path = Path(temp_output_dir) / "error_test.pdf"
        
        # Should not crash, but should handle error gracefully
        try:
            report_generator.generate_dcf_valuation_pdf(
                dcf_data=invalid_dcf_data,
                output_path=str(output_path)
            )
            # If it doesn't raise an exception, the file should either exist or not
            # but the system should not crash
        except Exception as e:
            # Exception is acceptable, but should be a meaningful error
            assert len(str(e)) > 0, "Error message should not be empty"
            # Should not be a system crash (ImportError, NameError, etc.)
            assert not isinstance(e, (ImportError, NameError, AttributeError)), \
                f"System error (not business logic error): {e}"
    
    def test_file_system_permissions_smoke(self, temp_output_dir):
        """Test file system operations work correctly."""
        report_generator = ReportGenerator()
        
        # Test creating files in different subdirectories
        subdirs = ["reports", "exports", "temp"]
        
        for subdir in subdirs:
            subdir_path = Path(temp_output_dir) / subdir
            subdir_path.mkdir(parents=True, exist_ok=True)
            
            output_path = subdir_path / "permission_test.pdf"
            
            # Use minimal valid data
            minimal_data = {
                "organization_id": 1,
                "company_name": "Permission Test",
                "valuation_date": "2024-01-01",
                "enterprise_value": 1000000,
                "financial_projections": [{
                    "year": 1,
                    "revenue": 500000,
                    "free_cash_flow": 100000,
                    "ebitda": 150000
                }]
            }
            
            try:
                report_generator.generate_dcf_valuation_pdf(
                    dcf_data=minimal_data,
                    output_path=str(output_path)
                )
                
                assert output_path.exists(), f"File not created in {subdir}"
                assert output_path.stat().st_size > 0, f"Empty file in {subdir}"
                
            except Exception as e:
                pytest.fail(f"File system permission error in {subdir}: {e}")
    
    def test_data_validation_smoke(self):
        """Test that basic data validation works."""
        report_generator = ReportGenerator()
        
        # Test various invalid data scenarios
        invalid_scenarios = [
            {
                "name": "missing_required_fields",
                "data": {"company_name": "Test"},
                "should_handle": True
            },
            {
                "name": "negative_financial_values",
                "data": {
                    "organization_id": 1,
                    "company_name": "Test",
                    "enterprise_value": -1000000,  # Negative value
                    "financial_projections": []
                },
                "should_handle": True
            },
            {
                "name": "empty_projections",
                "data": {
                    "organization_id": 1,
                    "company_name": "Test",
                    "enterprise_value": 1000000,
                    "financial_projections": []  # Empty
                },
                "should_handle": True
            }
        ]
        
        for scenario in invalid_scenarios:
            try:
                # This should either work (with default values) or raise a meaningful error
                temp_path = Path(tempfile.gettempdir()) / f"validation_{scenario['name']}.pdf"
                
                report_generator.generate_dcf_valuation_pdf(
                    dcf_data=scenario["data"],
                    output_path=str(temp_path)
                )
                
                # If it succeeds, clean up
                if temp_path.exists():
                    temp_path.unlink()
                    
            except Exception as e:
                # Errors are acceptable for invalid data, but should be meaningful
                if scenario["should_handle"]:
                    assert len(str(e)) > 0, f"Empty error message for {scenario['name']}"
                    # Should not be system errors
                    assert not isinstance(e, (ImportError, NameError, AttributeError)), \
                        f"System error for {scenario['name']}: {e}"


@pytest.mark.smoke
@pytest.mark.production
@pytest.mark.integration
class TestProductionIntegrationSmoke:
    """
    Integration smoke tests for production validation.
    These test end-to-end workflows with minimal setup.
    """
    
    def test_full_dcf_workflow_smoke(self):
        """Test complete DCF workflow from data to report."""
        import tempfile
        
        # Minimal but complete DCF data
        dcf_data = {
            "organization_id": 1,
            "company_name": "Integration Test Corp",
            "valuation_date": datetime.now().strftime("%Y-%m-%d"),
            "currency": "USD",
            "enterprise_value": 10000000,
            "financial_projections": [
                {
                    "year": 1,
                    "revenue": 2000000,
                    "operating_expenses": 1200000,
                    "free_cash_flow": 400000,
                    "ebitda": 600000,
                    "revenue_growth_rate": 0.20
                },
                {
                    "year": 2,
                    "revenue": 2400000,
                    "operating_expenses": 1320000,
                    "free_cash_flow": 520000,
                    "ebitda": 720000,
                    "revenue_growth_rate": 0.20
                }
            ],
            "assumptions": {
                "discount_rate": 0.12,
                "terminal_growth": 0.025,
                "tax_rate": 0.21,
                "years": 2
            }
        }
        
        # Test PDF generation
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_file:
            temp_path = temp_file.name
        
        try:
            report_generator = ReportGenerator()
            report_generator.generate_dcf_valuation_pdf(
                dcf_data=dcf_data,
                output_path=temp_path
            )
            
            # Validate output
            temp_file_path = Path(temp_path)
            assert temp_file_path.exists(), "Workflow did not produce output file"
            assert temp_file_path.stat().st_size > 5000, "Output file is too small for complete workflow"
            
        finally:
            # Cleanup
            if Path(temp_path).exists():
                Path(temp_path).unlink()
    
    def test_environment_variables_smoke(self):
        """Test that the system handles missing environment variables gracefully."""
        # Save original environment
        original_env = os.environ.copy()
        
        try:
            # Remove common environment variables that might be expected
            test_vars = ['DATABASE_URL', 'REDIS_URL', 'XERO_CLIENT_ID']
            
            for var in test_vars:
                if var in os.environ:
                    del os.environ[var]
            
            # The system should still be able to generate reports
            # (since we're testing the reporting module, not database integration)
            report_generator = ReportGenerator()
            
            minimal_data = {
                "organization_id": 1,
                "company_name": "Env Test",
                "valuation_date": "2024-01-01",
                "enterprise_value": 1000000,
                "financial_projections": [{
                    "year": 1,
                    "revenue": 500000,
                    "free_cash_flow": 100000,
                    "ebitda": 150000
                }]
            }
            
            with tempfile.NamedTemporaryFile(suffix=".pdf") as temp_file:
                # This should work even without environment variables
                # (for the reporting component)
                report_generator.generate_dcf_valuation_pdf(
                    dcf_data=minimal_data,
                    output_path=temp_file.name
                )
                
                assert Path(temp_file.name).stat().st_size > 1000, \
                    "Report generation failed without environment variables"
        
        finally:
            # Restore original environment
            os.environ.clear()
            os.environ.update(original_env)
    
    def test_python_version_compatibility_smoke(self):
        """Test basic Python version compatibility."""
        import sys
        
        # Verify we're running on a supported Python version
        major, minor = sys.version_info[:2]
        
        # MCX3D should support Python 3.8+
        assert major == 3, f"Unsupported Python major version: {major}"
        assert minor >= 8, f"Unsupported Python minor version: 3.{minor}"
        
        # Test that key language features work
        try:
            # f-strings (Python 3.6+)
            test_string = f"Python {major}.{minor}"
            assert "Python" in test_string
            
            # Walrus operator (Python 3.8+)
            if minor >= 8:
                test_data = [1, 2, 3, 4, 5]
                filtered = [x for x in test_data if (y := x * 2) > 4]
                assert len(filtered) > 0
            
            # Type hints should be available
            from typing import Dict, List, Optional
            
        except Exception as e:
            pytest.fail(f"Python compatibility issue: {e}")


if __name__ == "__main__":
    # Allow running smoke tests directly
    pytest.main([__file__, "-v", "-m", "smoke"])