"""
Tests for CORS configuration security (Issue #6 verification).

Verifies that overly permissive CORS settings have been fixed and that
CORS configuration follows security best practices.
"""

import pytest
import os
from unittest.mock import patch
from fastapi.testclient import TestClient
from typing import Dict, List

from mcx3d_finance.main import app


class TestCORSConfiguration:
    """Test suite for verifying CORS security fixes from Issue #6."""
    
    def test_cors_no_wildcard_methods(self):
        """
        Verify that CORS does not allow wildcard (*) methods.
        Issue #6 reported: allow_methods=["*"] was overly permissive.
        """
        client = TestClient(app)
        
        # Make a preflight request
        response = client.options(
            "/api/auth/login",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "POST"
            }
        )
        
        # Check that Access-Control-Allow-Methods header doesn't contain wildcard
        allow_methods = response.headers.get("Access-Control-Allow-Methods", "")
        
        assert "*" not in allow_methods, \
            "CORS should not allow wildcard (*) methods (Issue #6 fix)"
        
        # Should have specific methods listed
        expected_methods = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        for method in expected_methods:
            assert method in allow_methods, \
                f"CORS should explicitly allow {method} method"
    
    def test_cors_no_wildcard_headers(self):
        """
        Verify that CORS does not allow wildcard (*) headers.
        Issue #6 reported: allow_headers=["*"] was overly permissive.
        """
        client = TestClient(app)
        
        # Make a preflight request with specific headers
        response = client.options(
            "/api/auth/login",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type, Authorization"
            }
        )
        
        # Check that Access-Control-Allow-Headers header doesn't contain wildcard
        allow_headers = response.headers.get("Access-Control-Allow-Headers", "")
        
        assert "*" not in allow_headers, \
            "CORS should not allow wildcard (*) headers (Issue #6 fix)"
        
        # Should have specific headers listed
        expected_headers = ["Content-Type", "Authorization", "X-Request-ID", "Accept"]
        for header in expected_headers:
            assert header in allow_headers, \
                f"CORS should explicitly allow {header} header"
    
    def test_cors_restrictive_origins(self):
        """
        Verify that CORS origins are restrictive and configurable.
        Should not allow all origins (*).
        """
        client = TestClient(app)
        
        # Test with allowed origin
        allowed_origins = ["http://localhost:3000", "http://localhost:8080"]
        
        for origin in allowed_origins:
            response = client.options(
                "/api/auth/login",
                headers={
                    "Origin": origin,
                    "Access-Control-Request-Method": "POST"
                }
            )
            
            # Should allow these specific origins
            allow_origin = response.headers.get("Access-Control-Allow-Origin")
            assert allow_origin == origin or allow_origin == "*", \
                f"CORS should allow origin {origin}"
        
        # Test with disallowed origin (should not be allowed)
        disallowed_origin = "http://malicious-site.com"
        response = client.options(
            "/api/auth/login",
            headers={
                "Origin": disallowed_origin,
                "Access-Control-Request-Method": "POST"
            }
        )
        
        # Should not explicitly allow the malicious origin
        allow_origin = response.headers.get("Access-Control-Allow-Origin", "")
        if allow_origin != "*":  # If not using wildcard (which would be bad)
            assert allow_origin != disallowed_origin, \
                f"CORS should not allow origin {disallowed_origin}"
    
    def test_cors_credentials_handling(self):
        """
        Verify that CORS credentials are properly configured.
        Should allow credentials for authenticated requests.
        """
        client = TestClient(app)
        
        response = client.options(
            "/api/auth/login",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "POST"
            }
        )
        
        # Should allow credentials
        allow_credentials = response.headers.get("Access-Control-Allow-Credentials")
        assert allow_credentials == "true", \
            "CORS should allow credentials for authenticated requests"
    
    def test_cors_max_age_configured(self):
        """
        Verify that CORS max-age is properly configured.
        Issue #6 mentioned this should be configurable.
        """
        client = TestClient(app)
        
        response = client.options(
            "/api/auth/login",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "POST"
            }
        )
        
        # Should have max-age header
        max_age = response.headers.get("Access-Control-Max-Age")
        assert max_age is not None, "CORS should set Access-Control-Max-Age header"
        
        # Should be a reasonable value (e.g., 1 hour = 3600 seconds)
        max_age_value = int(max_age)
        assert 0 < max_age_value <= 86400, \
            f"CORS max-age should be reasonable (got {max_age_value} seconds)"
    
    @patch.dict(os.environ, {"CORS_ALLOWED_ORIGINS": "https://myapp.com,https://admin.myapp.com"})
    def test_cors_environment_configuration(self):
        """
        Verify that CORS origins can be configured via environment variables.
        """
        # This test would require reloading the app with new env vars
        # For now, we'll test that the environment variable is being read
        from mcx3d_finance.main import allowed_origins_str
        
        # The actual test would need to restart the app, but we can at least
        # verify the configuration mechanism exists
        assert hasattr(os, 'getenv'), "Environment configuration should be available"
        
        # Test that default origins are reasonable
        default_origins = os.getenv("CORS_ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:8080")
        origins_list = [origin.strip() for origin in default_origins.split(",")]
        
        # Should not be empty and should not contain wildcards
        assert len(origins_list) > 0, "CORS origins should be configured"
        assert "*" not in origins_list, "CORS origins should not contain wildcards"
        
        # Origins should be proper URLs
        for origin in origins_list:
            assert origin.startswith(("http://", "https://")), \
                f"CORS origin {origin} should be a proper URL"
    
    def test_cors_actual_requests(self):
        """
        Test CORS headers on actual requests (not just preflight).
        """
        client = TestClient(app)
        
        # Test with a simple GET request
        response = client.get(
            "/",
            headers={"Origin": "http://localhost:3000"}
        )
        
        # Should include CORS headers in actual response
        assert "Access-Control-Allow-Origin" in response.headers or \
               response.status_code == 200, \
               "Actual requests should handle CORS properly"
    
    def test_cors_security_headers_combination(self):
        """
        Verify that CORS works properly with other security headers.
        """
        client = TestClient(app)
        
        response = client.get(
            "/",
            headers={"Origin": "http://localhost:3000"}
        )
        
        # Should have both CORS and security headers
        headers = response.headers
        
        # Check for security headers (these should coexist with CORS)
        expected_security_headers = [
            "X-Content-Type-Options",
            "X-Frame-Options", 
            "X-XSS-Protection"
        ]
        
        # At least some security headers should be present
        security_headers_present = any(
            header in headers for header in expected_security_headers
        )
        
        assert security_headers_present, \
            "Security headers should coexist with CORS configuration"


class TestCORSEnvironmentVariables:
    """Test CORS configuration via environment variables."""
    
    @patch.dict(os.environ, {"CORS_MAX_AGE": "7200"})
    def test_cors_max_age_environment_override(self):
        """
        Test that CORS max-age can be overridden via environment variable.
        """
        # Note: This would require app restart to take effect in real scenario
        max_age_env = os.getenv("CORS_MAX_AGE", "3600")
        assert max_age_env == "7200", "Environment variable should override default"
    
    def test_cors_environment_validation(self):
        """
        Test that CORS environment variables are properly validated.
        """
        # Test default CORS configuration
        default_origins = os.getenv("CORS_ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:8080")
        origins = [origin.strip() for origin in default_origins.split(",")]
        
        # All origins should be valid URLs
        for origin in origins:
            assert origin.startswith(("http://", "https://")), \
                f"Invalid CORS origin format: {origin}"
            
            # Should not contain dangerous patterns
            dangerous_patterns = ["*", "null", "javascript:", "data:"]
            for pattern in dangerous_patterns:
                assert pattern not in origin.lower(), \
                    f"CORS origin contains dangerous pattern: {pattern}"


class TestCORSRegresssion:
    """Regression tests to ensure CORS fixes don't break functionality."""
    
    def test_legitimate_cors_requests_work(self):
        """
        Ensure that legitimate CORS requests still work after security fixes.
        """
        client = TestClient(app)
        
        # Test preflight request for a legitimate origin
        preflight_response = client.options(
            "/api/auth/xero/status/1",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "GET",
                "Access-Control-Request-Headers": "Authorization"
            }
        )
        
        # Preflight should succeed
        assert preflight_response.status_code == 200, \
            "Legitimate CORS preflight requests should succeed"
        
        # Should have proper CORS headers
        assert "Access-Control-Allow-Methods" in preflight_response.headers
        assert "Access-Control-Allow-Headers" in preflight_response.headers
    
    def test_api_functionality_preserved(self):
        """
        Ensure that API functionality is preserved after CORS security fixes.
        """
        client = TestClient(app)
        
        # Test that basic API endpoints still work
        response = client.get("/")
        assert response.status_code == 200, "Basic API functionality should be preserved"
        
        # Test that auth endpoints are accessible
        response = client.get("/api/auth/xero/authorize")
        # Should not be 404 (endpoint should exist)
        assert response.status_code != 404, "Auth endpoints should remain accessible"


if __name__ == "__main__":
    # Run CORS configuration tests
    pytest.main([__file__, "-v", "--tb=short"])