from typing import Dict, List


def get_account_balance(transactions: List[Dict], account_name: str) -> float:
    """
    Calculates the balance for a specific account from a list of transactions.

    Args:
        transactions: A list of dictionaries, where each dictionary
        represents a transaction.
        account_name: The name of the account to calculate the balance for.

    Returns:
        The balance of the specified account.
    """
    balance = 0.0
    for transaction in transactions:
        if transaction.get("account") == account_name:
            if transaction.get("type") == "debit":
                balance += transaction.get("amount", 0.0)
            elif transaction.get("type") == "credit":
                balance -= transaction.get("amount", 0.0)
    return balance


def filter_transactions_by_account_type(
    transactions: List[Dict], account_type: str
) -> List[Dict]:
    """
    Filters transactions by a specific account type.

    Args:
        transactions: A list of dictionaries representing transactions.
        account_type: The account type to filter by (e.g., 'revenue', 'expense').

    Returns:
        A list of transactions that match the specified account type.
    """
    return [
        transaction
        for transaction in transactions
        if transaction.get("account_type") == account_type
    ]
