"""
Production security configuration for MCX3D Financials.
"""
import os
from typing import List, Dict, Any
from datetime import timedelta


class SecurityConfig:
    """Security configuration settings for production deployment."""
    
    # CORS Settings
    CORS_ORIGINS: List[str] = os.getenv("CORS_ORIGINS", "https://mcx3d.com,https://app.mcx3d.com").split(",")
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: List[str] = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    CORS_ALLOW_HEADERS: List[str] = ["*"]
    
    # Security Headers
    SECURITY_HEADERS: Dict[str, str] = {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
        "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.xero.com",
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "Permissions-Policy": "camera=(), microphone=(), geolocation=()",
    }
    
    # Rate Limiting
    RATE_LIMIT_ENABLED: bool = os.getenv("RATE_LIMIT_ENABLED", "true").lower() == "true"
    RATE_LIMIT_DEFAULT: str = os.getenv("RATE_LIMIT_DEFAULT", "100/hour")
    RATE_LIMIT_STORAGE_URL: str = os.getenv("RATE_LIMIT_STORAGE_URL", "redis://redis:6379")
    
    # Authentication
    SECRET_KEY: str = os.getenv("SECRET_KEY", "")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    REFRESH_TOKEN_EXPIRE_DAYS: int = int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7"))
    PASSWORD_MIN_LENGTH: int = 8
    PASSWORD_REQUIRE_UPPERCASE: bool = True
    PASSWORD_REQUIRE_LOWERCASE: bool = True
    PASSWORD_REQUIRE_NUMBERS: bool = True
    PASSWORD_REQUIRE_SPECIAL: bool = True
    
    # Session Security
    SESSION_COOKIE_SECURE: bool = os.getenv("SESSION_COOKIE_SECURE", "true").lower() == "true"
    SESSION_COOKIE_HTTPONLY: bool = True
    SESSION_COOKIE_SAMESITE: str = "Lax"
    SESSION_COOKIE_NAME: str = "mcx3d_session"
    SESSION_EXPIRE_MINUTES: int = int(os.getenv("SESSION_EXPIRE_MINUTES", "60"))
    
    # CSRF Protection
    CSRF_ENABLED: bool = os.getenv("CSRF_ENABLED", "true").lower() == "true"
    CSRF_TOKEN_LENGTH: int = 32
    CSRF_HEADER_NAME: str = "X-CSRF-Token"
    CSRF_COOKIE_NAME: str = "mcx3d_csrf"
    CSRF_COOKIE_SECURE: bool = os.getenv("CSRF_COOKIE_SECURE", "true").lower() == "true"
    CSRF_COOKIE_HTTPONLY: bool = False  # Must be readable by JavaScript
    
    # API Security
    API_KEY_HEADER: str = "X-API-Key"
    API_KEY_LENGTH: int = 32
    API_KEY_EXPIRY_DAYS: int = 90
    
    # OAuth2 Settings (for Xero)
    OAUTH2_REDIRECT_HTTPS: bool = os.getenv("OAUTH2_REDIRECT_HTTPS", "true").lower() == "true"
    OAUTH2_STATE_LENGTH: int = 32
    OAUTH2_STATE_EXPIRY_MINUTES: int = 10
    
    # File Upload Security
    MAX_UPLOAD_SIZE: int = int(os.getenv("MAX_UPLOAD_SIZE", "10485760"))  # 10MB
    ALLOWED_UPLOAD_EXTENSIONS: List[str] = [".pdf", ".csv", ".xls", ".xlsx", ".json"]
    UPLOAD_DIRECTORY: str = os.getenv("UPLOAD_DIRECTORY", "/app/uploads")
    
    # Database Security
    DB_CONNECTION_TIMEOUT: int = int(os.getenv("DB_CONNECTION_TIMEOUT", "30"))
    DB_POOL_SIZE: int = int(os.getenv("DB_POOL_SIZE", "10"))
    DB_MAX_OVERFLOW: int = int(os.getenv("DB_MAX_OVERFLOW", "20"))
    DB_POOL_RECYCLE: int = int(os.getenv("DB_POOL_RECYCLE", "3600"))
    
    # Logging Security
    LOG_SENSITIVE_DATA: bool = False
    LOG_USER_ACTIVITY: bool = True
    LOG_FAILED_LOGINS: bool = True
    LOG_API_REQUESTS: bool = True
    
    # Encryption
    ENCRYPTION_KEY: str = os.getenv("ENCRYPTION_KEY", "")
    ENCRYPTION_ALGORITHM: str = "AES-256-GCM"
    
    # Brute Force Protection
    MAX_LOGIN_ATTEMPTS: int = 5
    LOGIN_LOCKOUT_MINUTES: int = 30
    
    # Environment
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "production")
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
    TESTING: bool = os.getenv("TESTING", "false").lower() == "true"
    
    @classmethod
    def validate(cls) -> None:
        """Validate security configuration."""
        errors = []
        
        if not cls.SECRET_KEY:
            errors.append("SECRET_KEY must be set in production")
            
        if not cls.ENCRYPTION_KEY:
            errors.append("ENCRYPTION_KEY must be set in production")
            
        if cls.ENVIRONMENT == "production":
            if cls.DEBUG:
                errors.append("DEBUG must be False in production")
            
            if not cls.SESSION_COOKIE_SECURE:
                errors.append("SESSION_COOKIE_SECURE must be True in production")
                
            if not cls.CSRF_COOKIE_SECURE:
                errors.append("CSRF_COOKIE_SECURE must be True in production")
                
            if not cls.OAUTH2_REDIRECT_HTTPS:
                errors.append("OAUTH2_REDIRECT_HTTPS must be True in production")
        
        if errors:
            raise ValueError(f"Security configuration errors: {'; '.join(errors)}")
    
    @classmethod
    def get_cors_config(cls) -> Dict[str, Any]:
        """Get CORS configuration for FastAPI."""
        return {
            "allow_origins": cls.CORS_ORIGINS,
            "allow_credentials": cls.CORS_ALLOW_CREDENTIALS,
            "allow_methods": cls.CORS_ALLOW_METHODS,
            "allow_headers": cls.CORS_ALLOW_HEADERS,
        }
    
    @classmethod
    def get_session_config(cls) -> Dict[str, Any]:
        """Get session configuration."""
        return {
            "secret_key": cls.SECRET_KEY,
            "session_cookie": cls.SESSION_COOKIE_NAME,
            "max_age": cls.SESSION_EXPIRE_MINUTES * 60,
            "same_site": cls.SESSION_COOKIE_SAMESITE.lower(),
            "https_only": cls.SESSION_COOKIE_SECURE,
            "httponly": cls.SESSION_COOKIE_HTTPONLY,
        }