"""
Comprehensive tests for the enhanced transformation engine.
Tests data quality scoring, transformation rules, batch processing, and performance.
"""

import pytest
import sys
import os
import time

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from mcx3d_finance.core.transformation_engine import (
    DataQualityScorer,
    DataQualityLevel,
    TransformationRuleEngine,
    BatchTransformationEngine,
    TransformationRule,
    TransformationRuleType,
    TransformationStatus
)


class TestDataQualityScorer:
    """Test data quality scoring system."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.scorer = DataQualityScorer()
    
    def test_empty_data_quality(self):
        """Test quality scoring with empty data."""
        result = self.scorer.calculate_quality_score([])
        
        assert result['overall_score'] == 0.0
        assert result['quality_level'] == DataQualityLevel.POOR
        assert 'No data to analyze' in result['recommendations']
    
    def test_high_quality_data(self):
        """Test quality scoring with high-quality data."""
        data = [
            {
                'id': '1',
                'name': '<PERSON>',
                'email': '<EMAIL>',
                'phone': '555-1234',
                'amount': 100.50,
                'date': '2024-01-01'
            },
            {
                'id': '2',
                'name': 'Jane Doe',
                'email': '<EMAIL>',
                'phone': '555-5678',
                'amount': 200.75,
                'date': '2024-01-02'
            }
        ]
        
        result = self.scorer.calculate_quality_score(data)
        
        assert result['overall_score'] > 0.8
        assert result['quality_level'] in [DataQualityLevel.EXCELLENT, DataQualityLevel.GOOD]
        assert result['record_count'] == 2
    
    def test_poor_quality_data(self):
        """Test quality scoring with poor-quality data."""
        data = [
            {
                'id': '1',
                'name': '',  # Missing name
                'email': 'invalid-email',  # Invalid email
                'phone': '123',  # Invalid phone
                'amount': 'not-a-number',  # Invalid amount
                'date': 'invalid-date'  # Invalid date
            },
            {
                'id': '1',  # Duplicate ID
                'name': 'Jane Doe',
                'email': '<EMAIL>',
                'phone': None,  # Missing phone
                'amount': None,  # Missing amount
                'date': None  # Missing date
            }
        ]
        
        result = self.scorer.calculate_quality_score(data)
        
        assert result['overall_score'] <= 0.7  # Allow exactly 0.7
        assert result['quality_level'] in [DataQualityLevel.POOR, DataQualityLevel.FAIR, DataQualityLevel.GOOD]
        assert len(result['recommendations']) > 0
    
    def test_completeness_calculation(self):
        """Test completeness score calculation."""
        # Data with some missing fields
        data = [
            {'field1': 'value1', 'field2': 'value2', 'field3': ''},
            {'field1': 'value1', 'field2': None, 'field3': 'value3'},
            {'field1': '', 'field2': 'value2', 'field3': 'value3'}
        ]
        
        completeness = self.scorer._calculate_completeness(data)
        
        # Should be 6 filled fields out of 9 total = 0.667
        assert 0.6 <= completeness <= 0.7
    
    def test_uniqueness_calculation(self):
        """Test uniqueness score calculation."""
        # Data with one duplicate
        data = [
            {'id': '1', 'name': 'John'},
            {'id': '2', 'name': 'Jane'},
            {'id': '1', 'name': 'John'}  # Duplicate
        ]
        
        uniqueness = self.scorer._calculate_uniqueness(data)
        
        # Should be 2 unique records out of 3 total = 0.667
        assert 0.6 <= uniqueness <= 0.7


class TestTransformationRuleEngine:
    """Test transformation rule engine."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.engine = TransformationRuleEngine()
    
    def test_add_and_remove_rules(self):
        """Test adding and removing transformation rules."""
        rule = TransformationRule(
            rule_id="test_rule",
            rule_type=TransformationRuleType.NORMALIZATION,
            name="Test Rule",
            description="Test rule description",
            source_fields=["email"],
            target_fields=["email"],
            transformation_function="normalize_email"
        )
        
        # Add rule
        self.engine.add_rule(rule)
        assert "test_rule" in self.engine.rules
        
        # Remove rule
        success = self.engine.remove_rule("test_rule")
        assert success
        assert "test_rule" not in self.engine.rules
        
        # Try to remove non-existent rule
        success = self.engine.remove_rule("non_existent")
        assert not success
    
    def test_get_applicable_rules(self):
        """Test getting applicable rules for data type and fields."""
        rule1 = TransformationRule(
            rule_id="email_rule",
            rule_type=TransformationRuleType.NORMALIZATION,
            name="Email Rule",
            description="Normalize emails",
            source_fields=["email"],
            target_fields=["email"],
            transformation_function="normalize_email",
            conditions={"data_type": "contact"}
        )
        
        rule2 = TransformationRule(
            rule_id="phone_rule",
            rule_type=TransformationRuleType.NORMALIZATION,
            name="Phone Rule",
            description="Normalize phones",
            source_fields=["phone"],
            target_fields=["phone"],
            transformation_function="normalize_phone",
            priority=5  # Higher priority
        )
        
        self.engine.add_rule(rule1)
        self.engine.add_rule(rule2)
        
        # Test with contact data type and both fields available
        applicable = self.engine.get_applicable_rules("contact", ["email", "phone", "name"])
        
        # Should get both rules, with phone_rule first (higher priority)
        assert len(applicable) == 2
        assert applicable[0].rule_id == "phone_rule"  # Higher priority (lower number)
        assert applicable[1].rule_id == "email_rule"
    
    def test_email_normalization(self):
        """Test built-in email normalization function."""
        rule = TransformationRule(
            rule_id="email_rule",
            rule_type=TransformationRuleType.NORMALIZATION,
            name="Email Rule",
            description="Normalize emails",
            source_fields=["email"],
            target_fields=["email"],
            transformation_function="normalize_email"
        )
        
        record = {"email": "  <EMAIL>  ", "name": "John"}
        result = self.engine._apply_transformation_function(rule, record)
        
        assert result["email"] == "<EMAIL>"
        assert result["name"] == "John"  # Other fields unchanged
    
    def test_phone_normalization(self):
        """Test built-in phone normalization function."""
        rule = TransformationRule(
            rule_id="phone_rule",
            rule_type=TransformationRuleType.NORMALIZATION,
            name="Phone Rule",
            description="Normalize phones",
            source_fields=["phone"],
            target_fields=["phone"],
            transformation_function="normalize_phone"
        )
        
        # Test 10-digit phone
        record = {"phone": "5551234567", "name": "John"}
        result = self.engine._apply_transformation_function(rule, record)
        
        assert result["phone"] == "(*************"
        
        # Test 11-digit phone with country code
        record = {"phone": "15551234567", "name": "John"}
        result = self.engine._apply_transformation_function(rule, record)
        
        assert result["phone"] == "+1 (*************"
    
    def test_currency_standardization(self):
        """Test built-in currency standardization function."""
        rule = TransformationRule(
            rule_id="currency_rule",
            rule_type=TransformationRuleType.NORMALIZATION,
            name="Currency Rule",
            description="Standardize currency",
            source_fields=["amount"],
            target_fields=["amount"],
            transformation_function="standardize_currency"
        )
        
        record = {"amount": "$1,234.56", "description": "Payment"}
        result = self.engine._apply_transformation_function(rule, record)
        
        assert result["amount"] == 1234.56
        assert result["description"] == "Payment"  # Other fields unchanged


class TestBatchTransformationEngine:
    """Test batch transformation engine."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.engine = BatchTransformationEngine(max_workers=2)
    
    def test_batch_processing_sequential(self):
        """Test sequential batch processing."""
        # Add a simple transformation rule
        rule = TransformationRule(
            rule_id="email_rule",
            rule_type=TransformationRuleType.NORMALIZATION,
            name="Email Rule",
            description="Normalize emails",
            source_fields=["email"],
            target_fields=["email"],
            transformation_function="normalize_email"
        )
        self.engine.add_transformation_rule(rule)
        
        # Test data
        data = [
            {"id": "1", "email": "<EMAIL>", "name": "John"},
            {"id": "2", "email": "<EMAIL>", "name": "Jane"},
            {"id": "3", "email": "<EMAIL>", "name": "Bob"}
        ]
        
        result = self.engine.process_batch(
            data, 
            data_type="contact", 
            batch_size=2, 
            parallel=False
        )
        
        assert result.status == TransformationStatus.SUCCESS
        assert result.total_records == 3
        assert result.processed_records >= 3
        assert result.overall_quality_score > 0
        assert len(result.batch_results) > 0
    
    def test_batch_processing_parallel(self):
        """Test parallel batch processing."""
        # Add a simple transformation rule
        rule = TransformationRule(
            rule_id="phone_rule",
            rule_type=TransformationRuleType.NORMALIZATION,
            name="Phone Rule",
            description="Normalize phones",
            source_fields=["phone"],
            target_fields=["phone"],
            transformation_function="normalize_phone"
        )
        self.engine.add_transformation_rule(rule)
        
        # Test data
        data = [
            {"id": f"{i}", "phone": f"555123456{i % 10}", "name": f"Person {i}"}
            for i in range(10)
        ]
        
        result = self.engine.process_batch(
            data, 
            data_type="contact", 
            batch_size=3, 
            parallel=True
        )
        
        assert result.status in [TransformationStatus.SUCCESS, TransformationStatus.PARTIAL_SUCCESS]
        assert result.total_records == 10
        assert len(result.batch_results) > 0
    
    def test_performance_with_large_dataset(self):
        """Test performance with large dataset."""
        # Add transformation rules
        email_rule = TransformationRule(
            rule_id="email_rule",
            rule_type=TransformationRuleType.NORMALIZATION,
            name="Email Rule",
            description="Normalize emails",
            source_fields=["email"],
            target_fields=["email"],
            transformation_function="normalize_email"
        )
        self.engine.add_transformation_rule(email_rule)
        
        # Generate large dataset
        data = [
            {
                "id": f"record_{i}",
                "email": f"USER{i}@EXAMPLE.COM",
                "name": f"User {i}",
                "amount": 100.0 + (i % 1000)
            }
            for i in range(5000)
        ]
        
        start_time = time.time()
        result = self.engine.process_batch(
            data, 
            data_type="contact", 
            batch_size=1000, 
            parallel=True
        )
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # Should complete within reasonable time (less than 30 seconds for 5K records)
        assert processing_time < 30, f"Processing took {processing_time:.2f} seconds, which is too long"
        
        assert result.status in [TransformationStatus.SUCCESS, TransformationStatus.PARTIAL_SUCCESS]
        assert result.total_records == 5000
        
        print(f"Processed {len(data)} records in {processing_time:.2f} seconds")
        print(f"Processing rate: {len(data) / processing_time:.0f} records/second")
    
    def test_quality_report_generation(self):
        """Test quality report generation."""
        data = [
            {"id": "1", "email": "<EMAIL>", "phone": "555-1234"},
            {"id": "2", "email": "<EMAIL>", "phone": "555-5678"},
            {"id": "3", "email": "invalid-email", "phone": "123"}  # Poor quality
        ]
        
        report = self.engine.get_quality_report(data)
        
        assert 'overall_score' in report
        assert 'quality_level' in report
        assert 'dimension_scores' in report
        assert 'recommendations' in report
        assert report['record_count'] == 3


if __name__ == "__main__":
    pytest.main([__file__])
