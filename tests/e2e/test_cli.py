import os
import subprocess
from pathlib import Path

import pytest
import openpyxl
from openpyxl import load_workbook


@pytest.mark.e2e
def test_generate_income_statement_success():
    """
    Tests successful generation of an income statement report via the CLI.
    """
    output_path = "income_statement.xlsx"
    if os.path.exists(output_path):
        os.remove(output_path)

    command = [
        "mcx3d-finance",
        "generate",
        "income-statement",
        "--organization-id",
        "test-org",
        "--period",
        "2023-Q4",
        "--format",
        "excel",
    ]
    result = subprocess.run(command, capture_output=True, text=True)

    assert result.returncode == 0
    assert os.path.exists(output_path)
    
    # Validate Excel file content
    _validate_excel_file(output_path)
    
    os.remove(output_path)


@pytest.mark.e2e
def test_generate_income_statement_missing_option():
    """
    Tests that the CLI returns an error when a required option is missing.
    """
    command = [
        "mcx3d-finance",
        "generate",
        "income-statement",
        "--organization-id",
        "test-org",
        # Missing --period
    ]
    result = subprocess.run(command, capture_output=True, text=True)

    assert result.returncode != 0
    assert "Error: Missing option '--period'" in result.stderr


def _validate_excel_file(file_path: str) -> None:
    """
    Validate the content and structure of a generated Excel file.
    
    Args:
        file_path: Path to the Excel file to validate
        
    Raises:
        AssertionError: If validation fails
        Exception: For file access or format issues
    """
    # Check file exists and has correct extension
    assert os.path.exists(file_path), f"Excel file does not exist: {file_path}"
    assert file_path.endswith(('.xlsx', '.xls')), f"Invalid Excel file extension: {file_path}"
    
    try:
        # Load the workbook
        workbook = load_workbook(file_path, read_only=True)
        
        # Validate workbook structure
        assert len(workbook.worksheets) > 0, "Excel file contains no worksheets"
        
        # Get the first worksheet (should contain the main report)
        worksheet = workbook.active
        assert worksheet is not None, "No active worksheet found"
        
        # Validate worksheet has data
        assert worksheet.max_row > 1, "Worksheet appears to be empty (only header row or less)"
        assert worksheet.max_column > 1, "Worksheet has insufficient columns"
        
        # Validate headers exist (check first row has content)
        first_row = list(worksheet.iter_rows(min_row=1, max_row=1, values_only=True))[0]
        assert any(cell is not None and str(cell).strip() for cell in first_row), \
            "First row appears to be empty or contain only whitespace"
        
        # Validate at least some data exists (check second row)
        if worksheet.max_row >= 2:
            second_row = list(worksheet.iter_rows(min_row=2, max_row=2, values_only=True))[0]
            has_data = any(cell is not None and str(cell).strip() for cell in second_row)
            assert has_data, "Second row appears to be empty - no data content found"
        
        # Validate file size is reasonable (not empty, not too large)
        file_size = os.path.getsize(file_path)
        assert file_size > 1024, f"Excel file is too small ({file_size} bytes) - may be corrupted"
        assert file_size < 10 * 1024 * 1024, f"Excel file is too large ({file_size} bytes) - may indicate an issue"
        
        # Validate the file can be read without corruption
        for row in worksheet.iter_rows(min_row=1, max_row=min(10, worksheet.max_row), values_only=True):
            # Just iterate through first 10 rows to ensure no corruption
            pass
        
        workbook.close()
        
    except openpyxl.utils.exceptions.InvalidFileException as e:
        raise AssertionError(f"Invalid Excel file format: {e}")
    except Exception as e:
        raise AssertionError(f"Error validating Excel file: {e}")


def _validate_chart_generation(file_path: str) -> None:
    """
    Validate chart generation in Excel files (when charts are implemented).
    
    Args:
        file_path: Path to the Excel file to validate
        
    Note:
        This function is prepared for future chart generation features.
        Currently validates the file structure is ready for charts.
    """
    try:
        workbook = load_workbook(file_path)
        worksheet = workbook.active
        
        # TODO: Once chart generation is implemented, add:
        # - Chart object validation
        # - Chart data series validation  
        # - Chart formatting validation
        # - Chart positioning validation
        
        # For now, validate the data structure supports charting
        if worksheet.max_row >= 3 and worksheet.max_column >= 2:
            # Check if data appears to be in a chart-friendly format
            # (numeric data in columns, labels in first column)
            first_col_data = [cell.value for cell in worksheet['A2:A10']]
            second_col_data = [cell.value for cell in worksheet['B2:B10']]
            
            # Basic validation that data exists for potential charting
            has_labels = any(isinstance(val, str) and val.strip() for val in first_col_data[:5])
            has_numeric = any(isinstance(val, (int, float)) for val in second_col_data[:5])
            
            # Note: This is preparatory validation - not failing if chart data isn't perfect
            # since charts aren't implemented yet
            if has_labels and has_numeric:
                pass  # Data structure looks good for future charting
        
        workbook.close()
        
    except Exception:
        # Don't fail tests for chart validation issues since charts aren't implemented yet
        pass
