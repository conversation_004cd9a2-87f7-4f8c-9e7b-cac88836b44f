#!/bin/bash
# setup_test_data.sh - Quick setup script for MCX3D test data

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SCRIPTS_DIR="$PROJECT_ROOT/scripts"
TEST_DATA_DIR="$PROJECT_ROOT/test_data"

echo -e "${BLUE}🚀 MCX3D Test Data Setup${NC}"
echo "=========================================="

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "$PROJECT_ROOT/pyproject.toml" ] && [ ! -f "$PROJECT_ROOT/requirements.txt" ]; then
    print_error "This doesn't appear to be the MCX3D project root directory"
    print_error "Please run this script from the project root or scripts directory"
    exit 1
fi

print_status "Project root: $PROJECT_ROOT"

# Create test data directory
mkdir -p "$TEST_DATA_DIR"
mkdir -p "$TEST_DATA_DIR/backups"
mkdir -p "$TEST_DATA_DIR/anonymized"
mkdir -p "$TEST_DATA_DIR/scenarios"
print_status "Created test data directories"

# Make scripts executable
chmod +x "$SCRIPTS_DIR/seed_test_data.py"
chmod +x "$SCRIPTS_DIR/manage_test_data.py"
print_status "Made scripts executable"

# Function to run Python scripts with proper path setup
run_python_script() {
    local script_name="$1"
    shift
    cd "$PROJECT_ROOT"
    python "$SCRIPTS_DIR/$script_name" "$@"
}

# Parse command line arguments
ACTION="${1:-help}"

case "$ACTION" in
    "quick")
        echo -e "\n${BLUE}📊 Generating Quick Test Dataset${NC}"
        echo "----------------------------------------"
        run_python_script "seed_test_data.py" --scenario minimal --output "$TEST_DATA_DIR/quick_test_data.json"
        print_status "Quick test data generated: $TEST_DATA_DIR/quick_test_data.json"
        ;;
    
    "standard")
        echo -e "\n${BLUE}📊 Generating Standard Test Dataset${NC}"
        echo "----------------------------------------"
        run_python_script "seed_test_data.py" --organizations 3 --output "$TEST_DATA_DIR/standard_test_data.json"
        print_status "Standard test data generated: $TEST_DATA_DIR/standard_test_data.json"
        ;;
    
    "enterprise")
        echo -e "\n${BLUE}🏢 Generating Enterprise Test Dataset${NC}"
        echo "----------------------------------------"
        run_python_script "seed_test_data.py" --scenario enterprise --output "$TEST_DATA_DIR/enterprise_test_data.json"
        print_status "Enterprise test data generated: $TEST_DATA_DIR/enterprise_test_data.json"
        ;;
    
    "performance")
        echo -e "\n${BLUE}⚡ Generating Performance Test Dataset${NC}"
        echo "----------------------------------------"
        run_python_script "manage_test_data.py" performance --size 1000 --scaling 10 --output "$TEST_DATA_DIR/performance_test_data.json"
        print_status "Performance test data generated: $TEST_DATA_DIR/performance_test_data.json"
        ;;
    
    "anonymize")
        if [ -z "$2" ]; then
            print_error "Please specify input file: ./setup_test_data.sh anonymize <input_file> [level]"
            exit 1
        fi
        
        INPUT_FILE="$2"
        LEVEL="${3:-medium}"
        OUTPUT_FILE="$TEST_DATA_DIR/anonymized_${LEVEL}_$(basename "$INPUT_FILE")"
        
        echo -e "\n${BLUE}🔒 Anonymizing Test Data (Level: $LEVEL)${NC}"
        echo "----------------------------------------"
        run_python_script "manage_test_data.py" anonymize --input "$INPUT_FILE" --level "$LEVEL" --output "$OUTPUT_FILE"
        print_status "Anonymized data saved: $OUTPUT_FILE"
        ;;
    
    "validate")
        if [ -z "$2" ]; then
            print_error "Please specify file to validate: ./setup_test_data.sh validate <file>"
            exit 1
        fi
        
        echo -e "\n${BLUE}🔍 Validating Test Data${NC}"
        echo "----------------------------------------"
        run_python_script "manage_test_data.py" validate --file "$2"
        ;;
    
    "scenarios")
        echo -e "\n${BLUE}📋 Exporting Test Scenarios${NC}"
        echo "----------------------------------------"
        run_python_script "manage_test_data.py" scenarios --output "$TEST_DATA_DIR/scenarios/test_scenarios.json"
        print_status "Test scenarios exported: $TEST_DATA_DIR/scenarios/test_scenarios.json"
        ;;
    
    "all")
        echo -e "\n${BLUE}🎯 Generating All Test Datasets${NC}"
        echo "=========================================="
        
        # Quick dataset
        echo -e "\n${YELLOW}Generating quick dataset...${NC}"
        run_python_script "seed_test_data.py" --scenario minimal --output "$TEST_DATA_DIR/quick_test_data.json"
        
        # Standard dataset
        echo -e "\n${YELLOW}Generating standard dataset...${NC}"
        run_python_script "seed_test_data.py" --organizations 3 --output "$TEST_DATA_DIR/standard_test_data.json"
        
        # SaaS dataset
        echo -e "\n${YELLOW}Generating SaaS growth dataset...${NC}"
        run_python_script "seed_test_data.py" --scenario growth --output "$TEST_DATA_DIR/saas_test_data.json"
        
        # Export scenarios
        echo -e "\n${YELLOW}Exporting test scenarios...${NC}"
        run_python_script "manage_test_data.py" scenarios --output "$TEST_DATA_DIR/scenarios/test_scenarios.json"
        
        print_status "All test datasets generated successfully!"
        echo -e "\n${GREEN}Available datasets:${NC}"
        echo "  • $TEST_DATA_DIR/quick_test_data.json (minimal)"
        echo "  • $TEST_DATA_DIR/standard_test_data.json (3 orgs)"
        echo "  • $TEST_DATA_DIR/saas_test_data.json (SaaS focused)"
        echo "  • $TEST_DATA_DIR/scenarios/test_scenarios.json (scenarios)"
        ;;
    
    "clean")
        echo -e "\n${BLUE}🧹 Cleaning Test Data${NC}"
        echo "----------------------------------------"
        
        if [ -d "$TEST_DATA_DIR" ]; then
            print_warning "This will remove all files in $TEST_DATA_DIR"
            read -p "Are you sure? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                rm -rf "$TEST_DATA_DIR"/*
                mkdir -p "$TEST_DATA_DIR/backups"
                mkdir -p "$TEST_DATA_DIR/anonymized"
                mkdir -p "$TEST_DATA_DIR/scenarios"
                print_status "Test data directory cleaned"
            else
                print_warning "Cleanup cancelled"
            fi
        else
            print_warning "Test data directory doesn't exist"
        fi
        ;;
    
    "status")
        echo -e "\n${BLUE}📊 Test Data Status${NC}"
        echo "----------------------------------------"
        
        if [ -d "$TEST_DATA_DIR" ]; then
            echo -e "${GREEN}Test data directory:${NC} $TEST_DATA_DIR"
            
            # Count files in each subdirectory
            for subdir in "." "backups" "anonymized" "scenarios"; do
                dir_path="$TEST_DATA_DIR/$subdir"
                if [ -d "$dir_path" ]; then
                    file_count=$(find "$dir_path" -maxdepth 1 -type f -name "*.json" | wc -l)
                    echo "  $subdir: $file_count JSON files"
                fi
            done
            
            # Show recent files
            echo -e "\n${GREEN}Recent files:${NC}"
            find "$TEST_DATA_DIR" -name "*.json" -type f -exec ls -la {} \; 2>/dev/null | head -5 | awk '{print "  " $9 " (" $5 " bytes, " $6 " " $7 " " $8 ")"}'
            
        else
            print_warning "Test data directory doesn't exist"
        fi
        ;;
    
    "help"|*)
        echo -e "\n${BLUE}📚 MCX3D Test Data Setup Help${NC}"
        echo "=========================================="
        echo
        echo -e "${GREEN}Usage:${NC} ./setup_test_data.sh <command> [options]"
        echo
        echo -e "${GREEN}Commands:${NC}"
        echo "  quick        Generate minimal test dataset (1 org, basic data)"
        echo "  standard     Generate standard test dataset (3 orgs, moderate data)"
        echo "  enterprise   Generate enterprise test dataset (5 orgs, complex data)"
        echo "  performance  Generate large dataset for performance testing"
        echo "  all          Generate all standard datasets"
        echo
        echo "  anonymize <file> [level]  Anonymize existing test data"
        echo "                            Levels: low, medium, high (default: medium)"
        echo "  validate <file>           Validate test data integrity"
        echo "  scenarios                 Export predefined test scenarios"
        echo
        echo "  status       Show test data directory status"
        echo "  clean        Remove all test data files"
        echo "  help         Show this help message"
        echo
        echo -e "${GREEN}Examples:${NC}"
        echo "  ./setup_test_data.sh quick"
        echo "  ./setup_test_data.sh anonymize test_data.json high"
        echo "  ./setup_test_data.sh validate test_data/standard_test_data.json"
        echo "  ./setup_test_data.sh performance"
        echo
        echo -e "${GREEN}Output Directory:${NC} $TEST_DATA_DIR"
        echo
        ;;
esac

echo -e "\n${GREEN}✅ Setup complete!${NC}"