"""
Enhanced logging system for financial report operations.

Provides structured logging with sanitization, performance metrics,
and audit trail capabilities for the MCX3D system.
"""

import json
import logging
import time
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from functools import wraps

from mcx3d_finance.exceptions import MCX3DException

logger = logging.getLogger(__name__)


class ReportLogger:
    """
    Enhanced logger for report generation operations with audit capabilities.
    
    Provides structured logging, performance tracking, and data sanitization
    for financial report operations.
    """
    
    # Sensitive fields that should be redacted in logs
    SENSITIVE_FIELDS = {
        'password', 'secret', 'token', 'key', 'credential', 'auth',
        'client_secret', 'webhook_key', 'api_key', 'access_token',
        'social_security', 'ssn', 'tax_id', 'account_number',
        'credit_card', 'bank_account', 'routing_number'
    }
    
    def __init__(self, logger_name: str):
        """
        Initialize the report logger.
        
        Args:
            logger_name: Name of the logger instance
        """
        self.logger = logging.getLogger(logger_name)
        self.operation_stack = []  # Track nested operations
        
    def log_report_request(
        self,
        report_type: str,
        config: Dict[str, Any],
        user_id: Optional[str] = None,
        organization_id: Optional[Union[str, int]] = None
    ):
        """
        Log report generation request with context.
        
        Args:
            report_type: Type of report being generated
            config: Report configuration (sanitized)
            user_id: User requesting the report (optional)
            organization_id: Organization ID (optional)
        """
        context = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_type": "report_request",
            "report_type": report_type,
            "user_id": user_id,
            "organization_id": organization_id,
            "config_summary": self._sanitize_config(config)
        }
        
        self.logger.info(
            f"Report generation requested: {report_type}",
            extra={"audit_data": context}
        )
        
        # Push operation onto stack
        self.operation_stack.append({
            "type": report_type,
            "start_time": time.time(),
            "context": context
        })
    
    def log_report_success(
        self,
        output_path: str,
        file_size_mb: Optional[float] = None,
        generation_time_seconds: Optional[float] = None,
        additional_metrics: Optional[Dict[str, Any]] = None
    ):
        """
        Log successful report generation.
        
        Args:
            output_path: Path to generated report file
            file_size_mb: Size of generated file in MB (optional)
            generation_time_seconds: Time taken to generate (optional)
            additional_metrics: Additional performance metrics (optional)
        """
        # Pop operation from stack
        operation = self.operation_stack.pop() if self.operation_stack else {}
        
        # Calculate generation time if not provided
        if generation_time_seconds is None and 'start_time' in operation:
            generation_time_seconds = time.time() - operation['start_time']
        
        # Get file size if not provided
        if file_size_mb is None:
            try:
                file_path = Path(output_path)
                if file_path.exists():
                    file_size_mb = file_path.stat().st_size / 1024 / 1024
            except Exception:
                file_size_mb = None
        
        context = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_type": "report_success",
            "report_type": operation.get('type', 'unknown'),
            "output_path": output_path,
            "file_size_mb": file_size_mb,
            "generation_time_seconds": generation_time_seconds,
            "metrics": additional_metrics or {}
        }
        
        self.logger.info(
            f"Report generated successfully: {operation.get('type', 'unknown')} "
            f"({generation_time_seconds:.2f}s, {file_size_mb:.2f}MB)",
            extra={"audit_data": context}
        )
    
    def log_report_error(
        self,
        error: Exception,
        context: Optional[Dict[str, Any]] = None,
        stage: Optional[str] = None
    ):
        """
        Log report generation error with detailed context.
        
        Args:
            error: Exception that occurred
            context: Additional context information (optional)
            stage: Stage where error occurred (optional)
        """
        # Pop operation from stack
        operation = self.operation_stack.pop() if self.operation_stack else {}
        
        # Calculate time to failure
        time_to_failure = None
        if 'start_time' in operation:
            time_to_failure = time.time() - operation['start_time']
        
        error_context = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_type": "report_error",
            "report_type": operation.get('type', 'unknown'),
            "error_type": type(error).__name__,
            "error_message": str(error),
            "stage": stage,
            "time_to_failure_seconds": time_to_failure,
            "additional_context": self._sanitize_context(context or {})
        }
        
        # Add MCX3D exception details if available
        if isinstance(error, MCX3DException):
            error_context.update({
                "error_code": error.error_code,
                "user_message": error.user_message,
                "severity": error.severity,
                "mcx3d_context": self._sanitize_context(error.context)
            })
        
        self.logger.error(
            f"Report generation failed: {operation.get('type', 'unknown')} - {error}",
            extra={"audit_data": error_context},
            exc_info=True
        )
    
    def log_performance_metric(
        self,
        metric_name: str,
        metric_value: Union[int, float],
        metric_unit: str,
        additional_data: Optional[Dict[str, Any]] = None
    ):
        """
        Log performance metrics for analysis.
        
        Args:
            metric_name: Name of the metric
            metric_value: Value of the metric
            metric_unit: Unit of measurement
            additional_data: Additional metric data (optional)
        """
        context = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_type": "performance_metric",
            "metric_name": metric_name,
            "metric_value": metric_value,
            "metric_unit": metric_unit,
            "additional_data": self._sanitize_context(additional_data or {})
        }
        
        self.logger.info(
            f"Performance metric: {metric_name} = {metric_value} {metric_unit}",
            extra={"metric_data": context}
        )
    
    def log_data_quality_issue(
        self,
        issue_type: str,
        description: str,
        data_context: Optional[Dict[str, Any]] = None,
        severity: str = "warning"
    ):
        """
        Log data quality issues encountered during processing.
        
        Args:
            issue_type: Type of data quality issue
            description: Description of the issue
            data_context: Context about the data (sanitized)
            severity: Severity level (info, warning, error)
        """
        context = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_type": "data_quality_issue",
            "issue_type": issue_type,
            "description": description,
            "severity": severity,
            "data_context": self._sanitize_context(data_context or {})
        }
        
        log_method = getattr(self.logger, severity, self.logger.warning)
        log_method(
            f"Data quality issue: {issue_type} - {description}",
            extra={"data_quality": context}
        )
    
    def _sanitize_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize configuration data for logging.
        
        Args:
            config: Configuration dictionary
            
        Returns:
            Sanitized configuration dictionary
        """
        sanitized = {}
        
        for key, value in config.items():
            if self._is_sensitive_field(key):
                sanitized[key] = "[REDACTED]"
            elif isinstance(value, dict):
                sanitized[key] = self._sanitize_config(value)
            elif isinstance(value, list):
                sanitized[key] = [
                    self._sanitize_config(item) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                sanitized[key] = value
        
        return sanitized
    
    def _sanitize_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize context data for logging.
        
        Args:
            context: Context dictionary
            
        Returns:
            Sanitized context dictionary
        """
        return self._sanitize_config(context)
    
    def _is_sensitive_field(self, field_name: str) -> bool:
        """
        Check if a field name indicates sensitive data.
        
        Args:
            field_name: Name of the field
            
        Returns:
            True if field appears to contain sensitive data
        """
        field_lower = field_name.lower()
        return any(sensitive in field_lower for sensitive in self.SENSITIVE_FIELDS)


class PerformanceTracker:
    """
    Context manager for tracking performance metrics of operations.
    """
    
    def __init__(
        self,
        logger: ReportLogger,
        operation_name: str,
        auto_log: bool = True
    ):
        """
        Initialize performance tracker.
        
        Args:
            logger: ReportLogger instance
            operation_name: Name of the operation being tracked
            auto_log: Automatically log performance metrics
        """
        self.logger = logger
        self.operation_name = operation_name
        self.auto_log = auto_log
        self.start_time = None
        self.metrics = {}
    
    def __enter__(self):
        """Start performance tracking."""
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """End performance tracking and log results."""
        if self.start_time is not None:
            duration = time.time() - self.start_time
            self.metrics['duration_seconds'] = duration
            
            if self.auto_log:
                self.logger.log_performance_metric(
                    f"{self.operation_name}_duration",
                    duration,
                    "seconds",
                    {"operation": self.operation_name}
                )
    
    def add_metric(self, name: str, value: Union[int, float], unit: str):
        """
        Add a custom metric to track.
        
        Args:
            name: Metric name
            value: Metric value
            unit: Unit of measurement
        """
        self.metrics[name] = {"value": value, "unit": unit}
        
        if self.auto_log:
            self.logger.log_performance_metric(
                f"{self.operation_name}_{name}",
                value,
                unit,
                {"operation": self.operation_name}
            )


def log_operation(
    operation_name: str,
    include_args: bool = False,
    include_result: bool = False,
    log_performance: bool = True
):
    """
    Decorator for automatic operation logging.
    
    Args:
        operation_name: Name of the operation
        include_args: Include function arguments in log
        include_result: Include function result in log
        log_performance: Log performance metrics
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            logger_instance = ReportLogger(func.__module__)
            
            # Log operation start
            context = {"operation": operation_name}
            if include_args:
                context["args"] = str(args)
                context["kwargs"] = logger_instance._sanitize_context(kwargs)
            
            logger_instance.logger.debug(f"Starting operation: {operation_name}")
            
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                
                # Log operation success
                duration = time.time() - start_time
                if log_performance:
                    logger_instance.log_performance_metric(
                        f"{operation_name}_duration",
                        duration,
                        "seconds"
                    )
                
                success_context = {"duration_seconds": duration}
                if include_result and result is not None:
                    success_context["result_type"] = type(result).__name__
                
                logger_instance.logger.debug(
                    f"Operation completed: {operation_name} ({duration:.3f}s)"
                )
                
                return result
                
            except Exception as e:
                # Log operation failure
                duration = time.time() - start_time
                logger_instance.log_report_error(
                    e,
                    context={"duration_seconds": duration},
                    stage=operation_name
                )
                raise
        
        return wrapper
    return decorator