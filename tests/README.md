# MCX3D Financial System - Test Suite

This directory contains the comprehensive test suite for the MCX3D Financial System, designed to ensure reliability, performance, and correctness across all system components.

## 🚀 Quick Start

```bash
# Run all tests
pytest

# Run specific test categories
pytest -m unit                    # Fast unit tests
pytest -m integration            # Integration tests
pytest -m e2e                   # End-to-end workflows
pytest -m smoke                 # Critical functionality
pytest -m performance           # Performance validation

# Generate test data
./scripts/setup_test_data.sh quick

# Run health checks
./scripts/production_health_check.py
```

## 📁 Test Organization

### Directory Structure

```
tests/
├── core/                          # Business logic tests
│   ├── test_data_validation.py    # Input validation and business rules
│   └── test_financial_calculations.py  # Core calculation logic
├── integration/                   # Component integration tests
│   ├── test_report_generation.py  # Report generation workflows
│   ├── test_output_quality.py     # PDF/Excel validation
│   └── test_xero_integration.py   # External API integration
├── e2e/                          # End-to-end workflow tests
│   ├── test_cli_exports.py       # CLI command workflows
│   └── test_api_workflows.py     # REST API workflows
├── performance/                   # Performance and load tests
│   ├── test_report_performance.py # Report generation performance
│   └── test_memory_usage.py      # Memory profiling and leaks
├── production/                    # Production validation tests
│   ├── test_smoke_tests.py       # Critical functionality validation
│   └── test_production_validation.py # Environment validation
├── fixtures/                     # Test data and utilities
│   ├── valuation_data.py         # Financial test data fixtures
│   └── test_utilities.py         # Helper functions and utilities
├── conftest.py                   # Global pytest configuration
└── test_basic_validation.py      # Basic system validation
```

## 🎯 Test Categories

### Unit Tests (`@pytest.mark.unit`)
- **Purpose**: Test individual functions/classes in isolation
- **Speed**: Fast (< 1 second each)
- **Coverage Target**: >90%
- **Dependencies**: None (mocked if needed)

### Integration Tests (`@pytest.mark.integration`)
- **Purpose**: Test component interactions and data flow
- **Speed**: Medium (1-10 seconds)
- **Coverage Target**: >85%
- **Dependencies**: Database, file system, temporary resources

### End-to-End Tests (`@pytest.mark.e2e`)
- **Purpose**: Test complete user workflows
- **Speed**: Slow (10-60 seconds)
- **Coverage Target**: Critical paths
- **Dependencies**: Full system integration

### Performance Tests (`@pytest.mark.performance`)
- **Purpose**: Validate performance characteristics
- **Speed**: Variable
- **Thresholds**: See [Performance Benchmarks](#performance-benchmarks)
- **Dependencies**: Large datasets, system monitoring

### Smoke Tests (`@pytest.mark.smoke`)
- **Purpose**: Critical functionality for production validation
- **Speed**: Fast (< 30 seconds total)
- **Reliability**: 100% must pass for deployment
- **Dependencies**: Minimal, production-safe

### Production Tests (`@pytest.mark.production`)
- **Purpose**: Production environment validation
- **Speed**: Medium (30-120 seconds)
- **Scope**: System health, configuration, security
- **Dependencies**: Production-like environment

## 📊 Test Execution

### Local Development

```bash
# Quick development cycle
pytest -m "unit and not slow" -x  # Stop on first failure

# Integration testing
pytest -m integration -v

# Full test suite
pytest --cov=mcx3d_finance --cov-report=html

# Performance validation
pytest -m performance --benchmark-compare
```

### CI/CD Pipeline

```bash
# GitHub Actions workflow runs:
pytest -m "not slow and not e2e"     # Fast tests first
pytest -m integration                # Integration validation
pytest -m smoke                      # Critical functionality
pytest -m performance --benchmark-save=ci  # Performance tracking
```

### Production Validation

```bash
# Pre-deployment checks
pytest -m smoke --tb=short -v
./scripts/validate_deployment.py --environment production

# Health monitoring
./scripts/production_health_check.py --json-output
pytest -m production -v
```

## 🔧 Test Configuration

### Environment Variables

```bash
# Test configuration
export TESTING=true
export DATABASE_URL=postgresql://test:test@localhost:5432/mcx3d_test
export REDIS_URL=redis://localhost:6379/1

# Production validation
export ENVIRONMENT=production
export SECRET_KEY=your-production-secret-key
```

### pytest.ini Settings

```ini
[tool:pytest]
testpaths = tests
addopts = 
    --cov=mcx3d_finance
    --cov-report=html
    --cov-fail-under=85
    --strict-markers
    --tb=short
```

## 📈 Performance Benchmarks

### Report Generation Thresholds

| Report Type | Single Report | Concurrent (3) | Memory Usage |
|-------------|---------------|----------------|--------------|
| DCF PDF | < 5 seconds | < 15 seconds | < 100MB |
| SaaS PDF | < 5 seconds | < 15 seconds | < 100MB |
| Excel Export | < 8 seconds | < 20 seconds | < 150MB |
| Complex Reports | < 30 seconds | < 60 seconds | < 200MB |

### Performance Test Results

Current baseline results (updated automatically):

```json
{
  "dcf_simple": {"average": 0.029, "success_rate": 100},
  "dcf_complex": {"average": 0.036, "success_rate": 100},
  "saas_simple": {"average": 0.022, "success_rate": 100},
  "saas_complex": {"average": 0.025, "success_rate": 100}
}
```

Run `python scripts/establish_baselines.py` to update benchmarks.

## 🔍 Test Data Management

### Test Data Categories

1. **Minimal Data** (`quick`): Basic functionality testing
2. **Standard Data** (`standard`): Realistic business scenarios  
3. **Enterprise Data** (`enterprise`): Large-scale, complex scenarios
4. **Performance Data** (`performance`): High-volume datasets
5. **Anonymized Data**: Production-safe test data

### Data Generation

```bash
# Generate test data sets
./scripts/setup_test_data.sh quick       # Minimal dataset
./scripts/setup_test_data.sh standard    # Standard dataset  
./scripts/setup_test_data.sh enterprise  # Large dataset
./scripts/setup_test_data.sh performance # High-volume dataset

# Manage test data
python scripts/manage_test_data.py anonymize --input test_data.json --level high
python scripts/manage_test_data.py validate --file test_data.json
```

### Using Test Data in Tests

```python
# Load generated test data
import json
from pathlib import Path

@pytest.fixture
def sample_test_data():
    data_file = Path("test_data/quick_test_data.json")
    with open(data_file, 'r') as f:
        return json.load(f)

# Use fixture data
def test_with_generated_data(sample_test_data):
    organizations = sample_test_data["data"]["organizations"]
    assert len(organizations) > 0
```

## 🏭 Production Testing

### Smoke Test Suite

Critical tests that must pass for production deployment:

- ✅ System health and imports
- ✅ Core report generation (< 10s)
- ✅ File system permissions
- ✅ Memory usage within limits
- ✅ Concurrent request handling
- ✅ Error handling gracefully

### Production Validation

```bash
# Complete deployment validation
./scripts/validate_deployment.py --environment production --exit-code

# Continuous health monitoring
./scripts/production_health_check.py --config production_config.json

# Validate system performance
pytest -m production --tb=short
```

### Deployment Checklist

- [ ] All smoke tests pass (`pytest -m smoke`)
- [ ] Production health checks pass
- [ ] Performance benchmarks met
- [ ] Security configuration validated
- [ ] Database connectivity confirmed
- [ ] External service integration working

## 🛠 Development Workflow

### Test-Driven Development (TDD)

1. **Red**: Write failing test for new functionality
2. **Green**: Write minimal code to make test pass
3. **Refactor**: Improve code while keeping tests green

```python
# 1. Write failing test
def test_new_calculation():
    result = calculate_irr([−1000, 300, 400, 500])
    assert result == pytest.approx(0.28, rel=1e-2)

# 2. Implement function
def calculate_irr(cash_flows):
    # Implementation here
    return 0.28

# 3. Refactor and optimize
```

### Adding New Tests

```python
# tests/core/test_new_feature.py
import pytest
from mcx3d_finance.core.new_feature import NewCalculator

@pytest.mark.unit
def test_new_calculation():
    """Test new calculation logic."""
    calculator = NewCalculator()
    result = calculator.calculate(100)
    assert result == 150

@pytest.mark.integration  
def test_new_integration():
    """Test integration with existing components."""
    # Integration test logic here
    pass
```

### Running Tests During Development

```bash
# Fast feedback loop
pytest -m "unit and not slow" --ff  # Failed first

# Watch mode (requires pytest-watch)
ptw -- -m unit

# Debug failing tests
pytest tests/failing_test.py --pdb -s
```

## 🔒 Security Testing

### Security Test Categories

- **Input Validation**: SQL injection, XSS prevention
- **Authentication**: OAuth flows, token validation  
- **Authorization**: Access control and permissions
- **Data Protection**: Encryption, PII handling
- **Infrastructure**: Security headers, HTTPS

### Security Testing Example

```python
@pytest.mark.security
def test_input_sanitization():
    """Test that malicious input is properly sanitized."""
    malicious_input = "<script>alert('xss')</script>"
    
    result = process_user_input(malicious_input)
    
    assert "<script>" not in result
    assert "alert" not in result
```

## 📊 Coverage and Quality

### Coverage Targets

- **Overall Coverage**: ≥85%
- **Core Business Logic**: ≥95%
- **Critical Path Functions**: 100%
- **New Code**: ≥90%

### Quality Metrics

```bash
# Generate coverage report
pytest --cov=mcx3d_finance --cov-report=html --cov-report=term

# Check code quality
flake8 mcx3d_finance/
black --check mcx3d_finance/

# Security scanning
bandit -r mcx3d_finance/
```

## 🚨 Troubleshooting

### Common Issues

#### Test Import Errors
```bash
# Solution: Install in development mode
pip install -e .
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

#### Database Connection Issues
```bash
# Solution: Set up test database
export DATABASE_URL=postgresql://test:test@localhost:5432/mcx3d_test
createdb mcx3d_test
```

#### Test Data Missing
```bash
# Solution: Generate test data
./scripts/setup_test_data.sh quick
ls -la test_data/  # Verify files exist
```

#### Slow Test Execution
```bash
# Solution: Run subset of tests
pytest -m "unit and not slow"
pytest -n auto  # Parallel execution
```

### Debug Information

```bash
# System health check
./scripts/production_health_check.py --verbose

# Test environment validation
pytest --collect-only  # List all tests
pytest --markers       # List all markers

# Performance analysis
pytest --durations=10  # Show slowest tests
```

## 📚 Documentation

### Comprehensive Guides
- [Testing Guide](../docs/TESTING_GUIDE.md) - Complete testing documentation
- [Developer Guide](../docs/DEVELOPER_TESTING_GUIDE.md) - Quick start for developers
- [Test Data Management](../scripts/README_test_data.md) - Data generation and management

### API Documentation
- [Report Generator API](../mcx3d_finance/reporting/README.md)
- [Core Calculations](../mcx3d_finance/core/README.md)
- [Integration APIs](../mcx3d_finance/integrations/README.md)

## 🤝 Contributing

### Adding New Tests

1. **Identify the appropriate test category** (unit, integration, e2e)
2. **Choose the correct directory** based on functionality
3. **Follow naming conventions** (`test_*.py`)
4. **Add appropriate markers** (`@pytest.mark.unit`)
5. **Update documentation** if needed

### Test Code Standards

- **Descriptive names**: `test_dcf_calculation_with_negative_cash_flows`
- **Clear structure**: Arrange-Act-Assert pattern
- **Good coverage**: Test both success and failure cases
- **Maintainable**: Use fixtures and helper functions
- **Well documented**: Docstrings for complex tests

### Review Guidelines

- [ ] Tests cover new functionality
- [ ] Appropriate test category and markers
- [ ] Performance impact considered
- [ ] Documentation updated
- [ ] No hardcoded values or paths

## 📞 Support

### Getting Help

- **Documentation**: Start with [Developer Testing Guide](../docs/DEVELOPER_TESTING_GUIDE.md)
- **System Issues**: Run `./scripts/production_health_check.py`
- **Test Failures**: Use `pytest --tb=long -vvv` for details
- **Performance Issues**: Check `pytest --durations=10`

### Resources

- **Log Files**: `/tmp/mcx3d_test.log`
- **Coverage Reports**: `htmlcov/index.html`
- **Performance Data**: `performance_baseline.json`
- **Health Check Results**: `/tmp/mcx3d_health_check_*.json`

---

## 🏆 Testing Excellence

The MCX3D test suite represents a comprehensive approach to software quality:

- **🔍 Comprehensive Coverage**: From unit tests to production validation
- **⚡ Performance Focus**: Continuous performance monitoring and optimization
- **🛡️ Production Safety**: Rigorous validation before deployment
- **👥 Developer Experience**: Clear documentation and helpful tools
- **🔄 Continuous Improvement**: Regular updates and enhancements

By maintaining high testing standards, we ensure the MCX3D Financial System delivers reliable, high-quality financial analysis and reporting capabilities to our users.

For detailed information on specific testing topics, see our comprehensive [Testing Guide](../docs/TESTING_GUIDE.md).