"""
User management CLI commands for MCX3D Finance.
Provides commands for creating, managing, and listing users.
"""

import click
import logging
import sys
import asyncio
from typing import Optional
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from sqlalchemy import func

from ..db.session import SessionLocal
from ..db.models import User, Organization, UserOrganization
from ..api.auth_middleware import hash_password
from ..utils.audit_logger import AuditEventType, audit_logger
from ..utils.input_validator import input_validator
from .error_handler import handle_cli_errors, display_success_message
from contextlib import contextmanager

logger = logging.getLogger(__name__)


@contextmanager
def get_db_session():
    """Context manager for database sessions in CLI."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@click.group()
def user():
    """User management commands."""
    pass


@user.command()
@click.option('--email', prompt=True, help='User email address')
@click.option('--password', prompt=True, hide_input=True, confirmation_prompt=True, help='User password')
@click.option('--name', '--full-name', prompt=True, help='User full name')
@click.option('--org-name', '--organization-name', help='Organization name (creates new org if provided)')
@click.option('--org-id', '--organization-id', type=int, help='Existing organization ID to associate user with')
@click.option('--role', type=click.Choice(['user', 'admin', 'viewer']), default='user', help='User role')
@click.option('--superuser', is_flag=True, help='Create superuser (admin privileges across all organizations)')
@handle_cli_errors()
def create(email: str, password: str, name: str, org_name: Optional[str], org_id: Optional[int], 
           role: str, superuser: bool):
    """Create a new user account.
    
    Examples:
        mcx3d user create --email <EMAIL> --name "John Doe"
        mcx3d user create --email <EMAIL> --name "Admin User" --superuser
        mcx3d user create --email <EMAIL> --name "User" --org-name "Acme Corp" --role admin
    """
    try:
        with get_db_session() as db:
            # Validate input
            validated_data = input_validator.validate_and_sanitize(
                {
                    "email": email,
                    "password": password,
                    "full_name": name,
                    "organization_name": org_name,
                    "role": role,
                },
                {
                    "email": {"type": "email", "required": True},
                    "password": {"type": "string", "required": True, "min_length": 8},
                    "full_name": {"type": "string", "required": True, "min_length": 2},
                    "organization_name": {"type": "string", "required": False, "min_length": 2},
                    "role": {"type": "string", "required": False, "allowed_values": ["user", "admin", "viewer"]},
                },
            )

            # Check if user already exists
            existing_user = db.query(User).filter(User.email == validated_data["email"]).first()
            if existing_user:
                click.echo(click.style(f"❌ User with email '{email}' already exists", fg="red"))
                sys.exit(1)

            # Validate organization
            organization = None
            if org_id:
                organization = db.query(Organization).filter(Organization.id == org_id).first()
                if not organization:
                    click.echo(click.style(f"❌ Organization with ID {org_id} not found", fg="red"))
                    sys.exit(1)
            elif org_name:
                # Check if organization exists
                organization = db.query(Organization).filter(Organization.name == org_name).first()
                if organization:
                    click.echo(click.style(f"ℹ️  Organization '{org_name}' already exists, associating user with it", fg="blue"))
                else:
                    # Create new organization
                    organization = Organization(
                        name=org_name,
                        base_currency="USD",
                        is_active=True,
                        created_at=datetime.now(timezone.utc),
                        updated_at=datetime.now(timezone.utc),
                    )
                    db.add(organization)
                    db.flush()  # Get organization ID
                    click.echo(click.style(f"✅ Created new organization: {org_name}", fg="green"))

            # Hash password
            hashed_password = hash_password(validated_data["password"])

            # Create user
            new_user = User(
                email=validated_data["email"],
                hashed_password=hashed_password,
                full_name=validated_data["full_name"],
                is_active=True,
                is_superuser=superuser,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
            )

            db.add(new_user)
            db.flush()  # Get user ID

            # Create user-organization association if organization specified
            if organization:
                user_org = UserOrganization(
                    user_id=new_user.id,
                    organization_id=organization.id,
                    role=role,
                    created_at=datetime.now(timezone.utc),
                )
                db.add(user_org)

            db.commit()

            # Log audit event
            asyncio.run(audit_logger.log_event(
                event_type=AuditEventType.USER_REGISTRATION,
                action="cli_create_user",
                outcome="success",
                user_id=str(new_user.id),
                details={
                    "email": new_user.email,
                    "organization_id": organization.id if organization else None,
                    "organization_name": organization.name if organization else None,
                    "role": role if organization else None,
                    "is_superuser": superuser,
                    "created_by": "cli",
                },
            ))

            # Display success message
            success_msg = f"User created successfully:\n"
            success_msg += f"  • Email: {new_user.email}\n"
            success_msg += f"  • Name: {new_user.full_name}\n"
            success_msg += f"  • User ID: {new_user.id}\n"
            if superuser:
                success_msg += f"  • Superuser: Yes\n"
            if organization:
                success_msg += f"  • Organization: {organization.name} (ID: {organization.id})\n"
                success_msg += f"  • Role: {role}\n"

            display_success_message(success_msg)

    except Exception as e:
        logger.error(f"Failed to create user: {e}")
        # Log audit event
        asyncio.run(audit_logger.log_event(
            event_type=AuditEventType.USER_REGISTRATION_FAILURE,
            action="cli_create_user",
            outcome="failure",
            details={"error": str(e), "email": email},
        ))
        click.echo(click.style(f"❌ Failed to create user: {e}", fg="red"))
        sys.exit(1)


@user.command()
@click.option('--email', prompt=True, help='Admin user email address')
@click.option('--password', prompt=True, hide_input=True, confirmation_prompt=True, help='Admin user password')
@click.option('--name', '--full-name', prompt=True, help='Admin user full name')
@handle_cli_errors()
def create_admin(email: str, password: str, name: str):
    """Create a superuser admin account.
    
    This creates a user with superuser privileges that can access all organizations
    and perform administrative functions.
    
    Example:
        mcx3d user create-admin --email <EMAIL> --name "System Administrator"
    """
    try:
        with get_db_session() as db:
            # Validate input
            validated_data = input_validator.validate_and_sanitize(
                {
                    "email": email,
                    "password": password,
                    "full_name": name,
                },
                {
                    "email": {"type": "email", "required": True},
                    "password": {"type": "string", "required": True, "min_length": 8},
                    "full_name": {"type": "string", "required": True, "min_length": 2},
                },
            )

            # Check if user already exists
            existing_user = db.query(User).filter(User.email == validated_data["email"]).first()
            if existing_user:
                click.echo(click.style(f"❌ User with email '{email}' already exists", fg="red"))
                sys.exit(1)

            # Hash password
            hashed_password = hash_password(validated_data["password"])

            # Create admin user
            admin_user = User(
                email=validated_data["email"],
                hashed_password=hashed_password,
                full_name=validated_data["full_name"],
                is_active=True,
                is_superuser=True,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
            )

            db.add(admin_user)
            db.commit()

            # Log audit event
            asyncio.run(audit_logger.log_event(
                event_type=AuditEventType.USER_REGISTRATION,
                action="cli_create_admin",
                outcome="success",
                user_id=str(admin_user.id),
                details={
                    "email": admin_user.email,
                    "is_superuser": True,
                    "created_by": "cli",
                },
            ))

            # Display success message
            success_msg = f"Admin user created successfully:\n"
            success_msg += f"  • Email: {admin_user.email}\n"
            success_msg += f"  • Name: {admin_user.full_name}\n"
            success_msg += f"  • User ID: {admin_user.id}\n"
            success_msg += f"  • Superuser: Yes\n"
            success_msg += f"  • Can access all organizations\n"

            display_success_message(success_msg)

    except Exception as e:
        logger.error(f"Failed to create admin user: {e}")
        # Log audit event
        asyncio.run(audit_logger.log_event(
            event_type=AuditEventType.USER_REGISTRATION_FAILURE,
            action="cli_create_admin",
            outcome="failure",
            details={"error": str(e), "email": email},
        ))
        click.echo(click.style(f"❌ Failed to create admin user: {e}", fg="red"))
        sys.exit(1)


@user.command()
@click.option('--org-id', '--organization-id', type=int, help='Filter by organization ID')
@click.option('--role', type=click.Choice(['user', 'admin', 'viewer']), help='Filter by role')
@click.option('--active/--all', default=True, help='Show only active users (default) or all users')
@click.option('--format', 'output_format', type=click.Choice(['table', 'json']), default='table', help='Output format')
@handle_cli_errors()
def list(org_id: Optional[int], role: Optional[str], active: bool, output_format: str):
    """List users in the system.
    
    Examples:
        mcx3d user list
        mcx3d user list --org-id 1 --role admin
        mcx3d user list --format json
    """
    try:
        with get_db_session() as db:
            # Build query
            query = db.query(User)
            
            if active:
                query = query.filter(User.is_active == True)

            # Join with organizations if filtering by org or role
            if org_id or role:
                query = query.join(UserOrganization).join(Organization)
                
                if org_id:
                    query = query.filter(Organization.id == org_id)
                
                if role:
                    query = query.filter(UserOrganization.role == role)

            users = query.all()

            if not users:
                click.echo(click.style("No users found matching the criteria", fg="yellow"))
                return

            if output_format == 'json':
                import json
                user_data = []
                for user in users:
                    user_info = {
                        "id": user.id,
                        "email": user.email,
                        "full_name": user.full_name,
                        "is_active": user.is_active,
                        "is_superuser": user.is_superuser,
                        "created_at": user.created_at.isoformat() if user.created_at else None,
                        "organizations": []
                    }
                    
                    for org_assoc in user.organization_associations:
                        user_info["organizations"].append({
                            "id": org_assoc.organization.id,
                            "name": org_assoc.organization.name,
                            "role": org_assoc.role
                        })
                    
                    user_data.append(user_info)
                
                click.echo(json.dumps(user_data, indent=2))
            else:
                # Table format
                click.echo(click.style("\n📋 Users List", fg="blue", bold=True))
                click.echo("=" * 80)
                
                for user in users:
                    status = "🟢 Active" if user.is_active else "🔴 Inactive"
                    superuser = " (Superuser)" if user.is_superuser else ""
                    
                    click.echo(f"\n👤 {user.full_name}{superuser}")
                    click.echo(f"   Email: {user.email}")
                    click.echo(f"   ID: {user.id}")
                    click.echo(f"   Status: {status}")
                    click.echo(f"   Created: {user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else 'Unknown'}")
                    
                    # Show organizations
                    if user.organization_associations:
                        click.echo("   Organizations:")
                        for org_assoc in user.organization_associations:
                            click.echo(f"     • {org_assoc.organization.name} ({org_assoc.role})")
                    else:
                        click.echo("   Organizations: None")

                # Summary
                total_users = len(users)
                active_users = sum(1 for user in users if user.is_active)
                superusers = sum(1 for user in users if user.is_superuser)
                
                click.echo("\n" + "=" * 80)
                click.echo(f"📊 Summary: {total_users} users total, {active_users} active, {superusers} superusers")

    except Exception as e:
        logger.error(f"Failed to list users: {e}")
        click.echo(click.style(f"❌ Failed to list users: {e}", fg="red"))
        sys.exit(1)


@user.command()
@click.argument('user_id', type=int)
@click.option('--org-id', '--organization-id', type=int, required=True, help='Organization ID to add user to')
@click.option('--role', type=click.Choice(['user', 'admin', 'viewer']), default='user', help='User role in organization')
@handle_cli_errors()
def add_to_org(user_id: int, org_id: int, role: str):
    """Add user to an organization with specified role.
    
    Example:
        mcx3d user add-to-org 123 --org-id 456 --role admin
    """
    try:
        with get_db_session() as db:
            # Validate user exists
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                click.echo(click.style(f"❌ User with ID {user_id} not found", fg="red"))
                sys.exit(1)

            # Validate organization exists
            organization = db.query(Organization).filter(Organization.id == org_id).first()
            if not organization:
                click.echo(click.style(f"❌ Organization with ID {org_id} not found", fg="red"))
                sys.exit(1)

            # Check if user is already in organization
            existing_assoc = db.query(UserOrganization).filter(
                UserOrganization.user_id == user_id,
                UserOrganization.organization_id == org_id
            ).first()
            
            if existing_assoc:
                click.echo(click.style(f"❌ User is already associated with organization '{organization.name}' with role '{existing_assoc.role}'", fg="red"))
                sys.exit(1)

            # Create association
            user_org = UserOrganization(
                user_id=user_id,
                organization_id=org_id,
                role=role,
                created_at=datetime.now(timezone.utc),
            )
            db.add(user_org)
            db.commit()

            success_msg = f"User added to organization successfully:\n"
            success_msg += f"  • User: {user.full_name} ({user.email})\n"
            success_msg += f"  • Organization: {organization.name}\n"
            success_msg += f"  • Role: {role}\n"

            display_success_message(success_msg)

    except Exception as e:
        logger.error(f"Failed to add user to organization: {e}")
        click.echo(click.style(f"❌ Failed to add user to organization: {e}", fg="red"))
        sys.exit(1)


if __name__ == '__main__':
    user()