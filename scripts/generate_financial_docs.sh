#!/bin/bash

# Script to generate MCX3D LTD financial documentation
# This script uses real Xero data to create comprehensive financial reports

echo "📊 MCX3D LTD Financial Documentation Generator"
echo "============================================="

# Check if Docker containers are running
if ! docker-compose ps | grep -q "Up"; then
    echo "❌ Error: Docker containers are not running."
    echo "Please run './scripts/launch_with_docs.sh' first to start the containers."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ] && [ ! -f ".env.development" ]; then
    echo "❌ Error: No .env file found."
    echo "Please copy .env.example to .env.development and configure your Xero credentials."
    exit 1
fi

# Create reports directory if it doesn't exist
mkdir -p reports

echo ""
echo "🔄 Generating financial documentation..."
echo ""

# Run the financial documentation generation command
docker-compose exec -T web python -m mcx3d_finance.cli financial-docs generate \
    --output-dir /app/reports \
    --formats pdf excel \
    --include-projections

# Check if generation was successful
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Financial documentation generated successfully!"
    echo ""
    echo "📁 Documents available in: ./reports/"
    echo ""
    
    # List generated files
    echo "Generated files:"
    ls -la reports/MCX3D_Financial_Package_*.zip 2>/dev/null || echo "No packages found. Check for individual files."
    
    echo ""
    echo "📄 You can also find individual documents in the reports directory."
else
    echo ""
    echo "❌ Error generating financial documentation."
    echo "Please check the logs for more details."
    exit 1
fi