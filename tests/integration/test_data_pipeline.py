import pytest
from unittest.mock import MagicMock

from sqlalchemy.orm import Session

from mcx3d_finance.reporting.generator import ReportGenerator
from mcx3d_finance.tasks.sync_tasks import sync_xero_data
from mcx3d_finance.api.webhook_routes import xero_webhook as process_xero_webhook
from mcx3d_finance.db.models import Transaction


@pytest.mark.integration
class TestDataPipeline:
    def test_full_data_sync_and_report_generation(self, db_session: Session, mocker):
        """
        Test the full data pipeline from Xero sync to report generation.
        """
        # Mock the Xero API client
        mock_xero_client = MagicMock()
        mocker.patch(
            "mcx3d_finance.integrations.xero_sync.XeroClient",
            return_value=mock_xero_client,
        )

        # Simulate a full data sync
        sync_xero_data(organization_id=1)

        # Generate a report from the synced data
        report_data = [
            ["Category", "Amount"],
            ["Revenue", 10000],
            ["Expenses", 5000],
            ["Net Income", 5000],
        ]
        report_generator = ReportGenerator(
            data=report_data, report_type="income_statement", format="pdf"
        )
        pdf_report = report_generator.generate()

        # Add assertions to verify the report
        assert pdf_report is not None
        assert pdf_report.getvalue().startswith(b"%PDF-1.4")

    def test_webhook_processing(self, db_session: Session, mocker):
        """
        Test the processing of a Xero webhook.
        """
        # Mock the Xero API client
        mock_xero_client = MagicMock()
        mocker.patch(
            "mcx3d_finance.integrations.xero_sync.XeroClient",
            return_value=mock_xero_client,
        )

        # Simulate a webhook payload
        webhook_payload = {
            "events": [
                {
                    "event": {
                        "eventType": "CREATE",
                        "resource": "transaction",
                        "resourceId": "12345",
                    }
                }
            ]
        }

        # Process the webhook
        process_xero_webhook(webhook_payload)

        # Verify that the data was updated in the database
        transaction = (
            db_session.query(Transaction).filter(Transaction.id == "12345").first()
        )
        assert transaction is not None
