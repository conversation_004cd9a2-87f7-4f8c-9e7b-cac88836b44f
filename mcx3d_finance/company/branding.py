"""
MCX3D LTD / ModularCX Branding Configuration

Defines the visual identity and branding elements for financial reports.
"""

from typing import Dict, Any, Tuple

# ModularCX brand colors based on their digital presence
BRAND_COLORS = {
    # Primary Colors
    "primary": "#2E3192",  # Deep Blue (main brand color)
    "primary_dark": "#1A1D5C",
    "primary_light": "#4A4FA8",
    
    # Secondary Colors
    "secondary": "#00D4FF",  # <PERSON> (accent)
    "secondary_dark": "#00A6CC",
    "secondary_light": "#66E0FF",
    
    # Supporting Colors
    "accent": "#FF6B35",  # Orange (for highlights)
    "success": "#28A745",
    "warning": "#FFC107",
    "danger": "#DC3545",
    "info": "#17A2B8",
    
    # Neutral Colors
    "black": "#000000",
    "dark_gray": "#333333",
    "gray": "#666666",
    "light_gray": "#999999",
    "lighter_gray": "#E0E0E0",
    "white": "#FFFFFF",
    "background": "#F8F9FA",
}

# RGB values for PDF generation
BRAND_COLORS_RGB: Dict[str, Tuple[int, int, int]] = {
    "primary": (46, 49, 146),
    "primary_dark": (26, 29, 92),
    "primary_light": (74, 79, 168),
    "secondary": (0, 212, 255),
    "secondary_dark": (0, 166, 204),
    "secondary_light": (102, 224, 255),
    "accent": (255, 107, 53),
    "success": (40, 167, 69),
    "warning": (255, 193, 7),
    "danger": (220, 53, 69),
    "info": (23, 162, 184),
    "black": (0, 0, 0),
    "dark_gray": (51, 51, 51),
    "gray": (102, 102, 102),
    "light_gray": (153, 153, 153),
    "lighter_gray": (224, 224, 224),
    "white": (255, 255, 255),
    "background": (248, 249, 250),
}

# Typography settings
TYPOGRAPHY = {
    "primary_font": "Helvetica",
    "secondary_font": "Arial",
    "monospace_font": "Courier",
    
    # Font sizes (in points)
    "sizes": {
        "h1": 24,
        "h2": 20,
        "h3": 16,
        "h4": 14,
        "h5": 12,
        "body": 10,
        "small": 9,
        "tiny": 8,
    },
    
    # Line heights
    "line_heights": {
        "tight": 1.2,
        "normal": 1.5,
        "relaxed": 1.8,
    }
}

# Report styling configuration
REPORT_STYLES = {
    # Page setup
    "page": {
        "margin_top": 72,  # 1 inch
        "margin_bottom": 72,
        "margin_left": 72,
        "margin_right": 72,
        "header_height": 100,
        "footer_height": 50,
    },
    
    # Header styling
    "header": {
        "background_color": BRAND_COLORS_RGB["primary"],
        "text_color": BRAND_COLORS_RGB["white"],
        "height": 80,
        "logo_height": 40,
        "padding": 20,
    },
    
    # Section styling
    "section": {
        "title_color": BRAND_COLORS_RGB["primary"],
        "title_size": TYPOGRAPHY["sizes"]["h2"],
        "spacing_before": 20,
        "spacing_after": 10,
    },
    
    # Table styling
    "table": {
        "header_bg": BRAND_COLORS_RGB["primary"],
        "header_text": BRAND_COLORS_RGB["white"],
        "row_alternate_bg": BRAND_COLORS_RGB["background"],
        "border_color": BRAND_COLORS_RGB["lighter_gray"],
        "border_width": 0.5,
        "cell_padding": 5,
    },
    
    # Financial highlights
    "highlights": {
        "positive_color": BRAND_COLORS_RGB["success"],
        "negative_color": BRAND_COLORS_RGB["danger"],
        "neutral_color": BRAND_COLORS_RGB["gray"],
        "kpi_box_bg": BRAND_COLORS_RGB["background"],
        "kpi_box_border": BRAND_COLORS_RGB["primary_light"],
    },
    
    # Charts and graphs
    "charts": {
        "primary_series": BRAND_COLORS["primary"],
        "secondary_series": BRAND_COLORS["secondary"],
        "accent_series": BRAND_COLORS["accent"],
        "grid_color": BRAND_COLORS["lighter_gray"],
        "axis_color": BRAND_COLORS["gray"],
    }
}

# Excel styling configuration
EXCEL_STYLES = {
    "header": {
        "font_name": TYPOGRAPHY["primary_font"],
        "font_size": 14,
        "font_color": BRAND_COLORS["white"],
        "bg_color": BRAND_COLORS["primary"],
        "bold": True,
        "align": "center",
        "valign": "vcenter",
        "border": 1,
    },
    "subheader": {
        "font_name": TYPOGRAPHY["primary_font"],
        "font_size": 12,
        "font_color": BRAND_COLORS["primary"],
        "bold": True,
        "border": 1,
    },
    "data": {
        "font_name": TYPOGRAPHY["primary_font"],
        "font_size": 10,
        "border": 1,
    },
    "total": {
        "font_name": TYPOGRAPHY["primary_font"],
        "font_size": 10,
        "bold": True,
        "top_border": 6,  # Double line
        "bottom_border": 1,
    },
    "currency": {
        "num_format": "£#,##0.00",
    },
    "percentage": {
        "num_format": "0.00%",
    },
    "date": {
        "num_format": "dd/mm/yyyy",
    }
}

# Branding configuration export
BRANDING_CONFIG: Dict[str, Any] = {
    "company_name": "MCX3D LTD",
    "trading_name": "ModularCX",
    "tagline": "Transform your digital experience",
    "colors": BRAND_COLORS,
    "colors_rgb": BRAND_COLORS_RGB,
    "typography": TYPOGRAPHY,
    "report_styles": REPORT_STYLES,
    "excel_styles": EXCEL_STYLES,
    
    # Logo configuration (placeholder - actual logo files would be referenced)
    "logo": {
        "primary": "assets/modularcx_logo.png",  # To be added
        "white": "assets/modularcx_logo_white.png",  # To be added
        "icon": "assets/modularcx_icon.png",  # To be added
    },
    
    # Report footer text
    "footer_text": "© 2024 MCX3D LTD trading as ModularCX. All rights reserved.",
    "confidentiality_notice": (
        "This document contains confidential and proprietary information of MCX3D LTD. "
        "It is furnished for evaluation purposes only and shall not be disclosed to any "
        "third party without the prior written consent of MCX3D LTD."
    ),
}