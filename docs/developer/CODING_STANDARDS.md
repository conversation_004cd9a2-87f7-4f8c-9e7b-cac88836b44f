# MCX3D Financials - Coding Standards

## Overview

This document outlines the coding standards and best practices for the MCX3D Financials project. Following these standards ensures consistency, maintainability, and code quality across the entire codebase.

## Table of Contents

1. [General Principles](#general-principles)
2. [Python Style Guide](#python-style-guide)
3. [Import Organization](#import-organization)
4. [Exception Handling](#exception-handling)
5. [Type Hints](#type-hints)
6. [Code Quality Tools](#code-quality-tools)
7. [Pre-commit Hooks](#pre-commit-hooks)
8. [Testing Standards](#testing-standards)

## General Principles

### Code Quality First
- Write self-documenting code that is easy to understand
- Prefer explicit over implicit behavior
- Follow the principle of least surprise
- Keep functions and classes focused on a single responsibility

### Security by Default
- Never hardcode secrets or API keys
- Use environment variables for configuration
- Validate all inputs and sanitize outputs
- Follow secure coding practices for authentication and authorization

### Performance Awareness
- Consider performance implications of design decisions
- Profile before optimizing
- Use appropriate data structures and algorithms
- Cache expensive operations when appropriate

## Python Style Guide

### Formatting
- **Line Length**: Maximum 88 characters (Black default)
- **Indentation**: 4 spaces (no tabs)
- **Quotes**: Double quotes for strings, single quotes for keys when appropriate
- **Trailing Commas**: Use trailing commas in multi-line data structures

### Naming Conventions
- **Variables and Functions**: `snake_case`
- **Classes**: `PascalCase`
- **Constants**: `UPPER_SNAKE_CASE`
- **Private Members**: Prefix with single underscore `_private_method`
- **File Names**: `snake_case.py`

### Documentation
- Use docstrings for all public functions, classes, and modules
- Follow Google-style docstring format
- Include type information, parameters, return values, and examples
- Write clear, concise comments for complex logic

Example:
```python
def calculate_tax_amount(gross_amount: float, tax_rate: float) -> float:
    """Calculate tax amount based on gross amount and tax rate.
    
    Args:
        gross_amount: The gross amount before tax
        tax_rate: Tax rate as a decimal (e.g., 0.20 for 20%)
        
    Returns:
        The calculated tax amount
        
    Raises:
        ValueError: If gross_amount or tax_rate is negative
        
    Example:
        >>> calculate_tax_amount(100.0, 0.20)
        20.0
    """
    if gross_amount < 0 or tax_rate < 0:
        raise ValueError("Amounts and rates must be non-negative")
    return gross_amount * tax_rate
```

## Import Organization

### Import Order (enforced by isort)
1. **Standard library imports**
2. **Third-party imports**  
3. **Local application imports** (relative imports)

### Import Style
- Use absolute imports when possible
- Group related imports together
- Use `from ... import ...` for frequently used items
- Avoid wildcard imports (`from module import *`)

Example:
```python
# Standard library imports
import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional

# Third party imports
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

# Local application imports
from ..db.models import User, Organization
from ..exceptions.auth import AuthenticationError
from ..utils.security import hash_password
```

## Exception Handling

### Custom Exception Hierarchy
- Use specific exception types instead of generic `Exception`
- Create custom exceptions for different error categories
- Include context information in exception messages
- Log exceptions appropriately based on severity

### Exception Categories
- **AuthenticationError**: Authentication and authorization failures
- **ValidationError**: Input validation and data validation errors
- **IntegrationError**: External service integration failures
- **BusinessLogicError**: Business rule violations

### Best Practices
```python
# Bad - generic exception handling
try:
    result = risky_operation()
except Exception as e:
    logger.error(f"Something went wrong: {e}")
    raise

# Good - specific exception handling
try:
    result = risky_operation()
except ValidationError as e:
    logger.warning(f"Validation failed: {e}")
    raise HTTPException(status_code=400, detail=str(e))
except IntegrationError as e:
    logger.error(f"External service error: {e}")
    raise HTTPException(status_code=502, detail="Service temporarily unavailable")
except Exception as e:
    logger.error(f"Unexpected error in operation: {e}")
    raise HTTPException(status_code=500, detail="Internal server error")
```

## Type Hints

### Required Type Hints
- **All function parameters** and **return types**
- **Class attributes** when not obvious
- **Variable annotations** for complex types

### Type Hint Guidelines
- Use `Optional[T]` for values that can be `None`
- Use `Union[T1, T2]` for values that can be multiple types
- Use `List[T]`, `Dict[K, V]` for collections
- Import types from `typing` module for Python < 3.9

Example:
```python
from typing import Dict, List, Optional, Union

def process_financial_data(
    data: List[Dict[str, Union[str, float]]], 
    organization_id: int,
    validation_rules: Optional[Dict[str, str]] = None
) -> Dict[str, List[str]]:
    """Process financial data with validation."""
    # Implementation here
    pass
```

## Code Quality Tools

### Required Tools (enforced in CI/CD)
1. **Black**: Code formatting
2. **isort**: Import sorting
3. **flake8**: Style and syntax checking
4. **mypy**: Static type checking
5. **pylint**: Advanced code analysis
6. **autoflake**: Remove unused imports
7. **bandit**: Security vulnerability scanning

### Tool Configuration
- Configuration files: `.isort.cfg`, `.pylintrc`, `mypy.ini`
- Line length: 88 characters (Black standard)
- Import style: `black` profile for isort

### Running Quality Checks
```bash
# Format code
black .
isort .

# Check for issues
flake8 .
mypy mcx3d_finance/
pylint mcx3d_finance/
bandit -r mcx3d_finance/

# Remove unused imports
autoflake --remove-all-unused-imports --recursive --in-place mcx3d_finance/
```

## Pre-commit Hooks

### Installation
```bash
pip install pre-commit
pre-commit install
```

### Hooks Configuration
The `.pre-commit-config.yaml` file includes all quality checks that run automatically before each commit.

### Manual Execution
```bash
# Run all hooks on all files
pre-commit run --all-files

# Run specific hook
pre-commit run black --all-files
```

## Testing Standards

### Test Organization
- **Unit Tests**: Test individual functions and classes
- **Integration Tests**: Test component interactions
- **End-to-End Tests**: Test complete user workflows

### Test Naming
- Test files: `test_*.py`
- Test functions: `test_function_description`
- Test classes: `TestClassName`

### Best Practices
- Write tests before or alongside implementation (TDD)
- Use descriptive test names that explain the scenario
- Test both happy path and edge cases
- Mock external dependencies
- Maintain test independence

## Code Review Checklist

### Before Submitting
- [ ] All tests pass
- [ ] Code quality tools pass (black, isort, flake8, mypy, pylint)
- [ ] Security scan passes (bandit)
- [ ] Documentation is updated
- [ ] Type hints are complete
- [ ] Exception handling is specific
- [ ] No hardcoded secrets or configurations

### During Review
- [ ] Code follows established patterns
- [ ] Business logic is correct
- [ ] Error cases are handled appropriately
- [ ] Performance implications are considered
- [ ] Security best practices are followed

## Continuous Integration

### CI Pipeline Stages
1. **Code Quality**: Black, isort, flake8 checks
2. **Type Checking**: mypy validation
3. **Security Scanning**: bandit analysis
4. **Testing**: Unit, integration, and E2E tests
5. **Documentation**: Check for documentation completeness

### Failure Policy
- All quality checks must pass before merge
- Any security vulnerabilities must be addressed
- Test coverage should be maintained or improved
- Breaking changes require documentation updates

## Tools and Resources

### Required Tools
- Python 3.9+
- Virtual environment (venv or conda)
- Git with pre-commit hooks
- Code editor with Python language server

### Recommended Extensions
- **VS Code**: Python, Pylance, Black Formatter, isort
- **PyCharm**: Built-in code quality tools
- **Vim/Neovim**: ALE, coc-python, or similar

### References
- [PEP 8](https://peps.python.org/pep-0008/) - Python Style Guide
- [PEP 484](https://peps.python.org/pep-0484/) - Type Hints
- [Google Python Style Guide](https://google.github.io/styleguide/pyguide.html)
- [Real Python Best Practices](https://realpython.com/python-code-quality/)

---

## Getting Help

If you have questions about these standards or need clarification on any point, please:

1. Check the existing codebase for examples
2. Review the tool documentation
3. Ask in code review comments
4. Update this document if standards evolve

Remember: These standards exist to help us write better, more maintainable code. When in doubt, prioritize readability and maintainability over cleverness.