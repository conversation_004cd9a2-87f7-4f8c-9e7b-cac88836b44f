# MCX3D Financials - Production Environment Configuration
# Generated on deployment for production use
# SECURITY NOTICE: This file contains sensitive production credentials

# Environment Configuration
ENVIRONMENT=production
DEBUG=False
LOG_LEVEL=INFO
APP_NAME=MCX3D Finance
APP_VERSION=2.0.0

# Database Configuration - PRODUCTION
# Replace with actual production database URL
DATABASE_URL=***************************************************/mcx3d_finance_prod

# Redis Configuration - PRODUCTION
# Replace with actual production Redis URL
REDIS_URL=redis://redis:6379/0

# CRITICAL SECURITY KEYS - Generated for Production
SECRET_KEY=C7VKA43ZDuTxUKLf#S2RNdcFJ^embDF-9kp_y5n3I2M-*DZCHOw@sy0mf2hm1IVe
ENCRYPTION_KEY=TUkzf1BrpwE1FgGoWIyQknFwpmzI3EwSLOD_W89T6C4=

# JWT Configuration
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=15
REFRESH_TOKEN_EXPIRE_DAYS=30

# Xero OAuth Configuration - PRODUCTION
XERO_CLIENT_ID=475EC4359EA4461DBDB16C4282A67410
XERO_CLIENT_SECRET=o7MuRbbBHjqgX_3IR7qO6P7AOUNLa6LLQUS0X1W9DBecbNGN
XERO_REDIRECT_URI=http://localhost:8000/api/auth/xero/callback
XERO_SCOPES=accounting.transactions accounting.contacts accounting.reports.read accounting.settings offline_access
XERO_WEBHOOK_KEY=your_production_webhook_key

# Session Management
MAX_SESSIONS_PER_USER=3
SESSION_IDLE_TIMEOUT_MINUTES=30

# Account Security
MAX_LOGIN_ATTEMPTS=3
LOCKOUT_DURATION_MINUTES=30

# Multi-Factor Authentication
MFA_CODE_VALIDITY_MINUTES=5
MFA_ISSUER_NAME=MCX3D Finance Production

# Rate Limiting - Production Levels
RATE_LIMIT_DEFAULT=100
RATE_LIMIT_AUTH=3
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_PER_HOUR=1000

# CORS Configuration - Production Origins
# Replace with actual production domains
CORS_ORIGINS=https://mcx3d.com,https://app.mcx3d.com,https://dashboard.mcx3d.com
CORS_MAX_AGE=3600

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# Celery Configuration
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# Data Protection & Encryption
ENABLE_FIELD_ENCRYPTION=true
ENABLE_AUDIT_ENCRYPTION=true
KEY_ROTATION_DAYS=90
DATA_RETENTION_DAYS=2555

# Audit & Monitoring
AUDIT_LOG_FILE=/var/log/mcx3d_audit.log
AUDIT_RETENTION_DAYS=365

# Performance Configuration
MEMORY_LIMIT_MB=2048
TIMEOUT_SECONDS=60
PARALLEL_WORKERS=4
ENABLE_CACHING=true

# Monitoring and Alerting
ENABLE_METRICS=true
ENABLE_ALERTING=true
HEALTH_CHECK_INTERVAL_SECONDS=30

# Email Configuration for Notifications - Production
# Replace with actual production SMTP credentials
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_production_smtp_password
EMAIL_FROM=<EMAIL>

# Production Monitoring Integration
# Uncomment and configure for production monitoring
# SENTRY_DSN=https://<EMAIL>/project
# DATADOG_API_KEY=your_datadog_api_key
# GRAFANA_URL=https://your-grafana-instance.com
# PROMETHEUS_GATEWAY=https://pushgateway.mcx3d.com

# SSL/TLS Configuration
FORCE_HTTPS=true
HSTS_MAX_AGE=31536000
SECURE_COOKIES=true

# Deployment Information
DEPLOYED_BY=automated_deployment
DEPLOYMENT_DATE=2025-07-24T16:10:00Z
BUILD_VERSION=production-20250724161000

# Production Security Headers
CSP_DEFAULT_SRC="self"
CSP_SCRIPT_SRC="self unsafe-inline"
CSP_STYLE_SRC="self unsafe-inline"
X_FRAME_OPTIONS=DENY
X_CONTENT_TYPE_OPTIONS=nosniff
REFERRER_POLICY=strict-origin-when-cross-origin