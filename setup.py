from setuptools import setup, find_packages

setup(
    name="mcx3d_finance",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "fastapi>=0.104.0",
        "uvicorn[standard]>=0.24.0",
        "click>=8.1.0",
        "sqlalchemy>=2.0.0",
        "psycopg2-binary>=2.9.0",
        "redis>=5.0.0",
        "celery>=5.3.0",
        "pandas>=2.1.0",
        "numpy>=1.24.0",
        "pydantic>=2.5.0",
        "reportlab>=4.0.0",
        "openpyxl>=3.1.0",
        "plotly>=5.17.0",
        # Phase 2 additions
        "pytest>=7.4.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.1.0",
        "Levenshtein>=0.23.0",
        "python-dateutil>=2.8.0",
        "requests>=2.31.0",
        "aiohttp>=3.9.0",
        "asyncio-throttle>=1.0.0",
    ],
    entry_points={
        "console_scripts": [
            "mcx3d-finance=mcx3d_finance.cli.main:cli",
        ],
    },
)
