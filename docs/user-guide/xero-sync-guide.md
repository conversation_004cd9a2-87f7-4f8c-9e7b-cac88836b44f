# Xero Data Sync Guide

## Prerequisites

1. **Add your Xero credentials to the .env file:**
   ```
   XERO_CLIENT_ID=your_actual_client_id
   XERO_CLIENT_SECRET=your_actual_client_secret
   ```

2. **Get these from:** https://developer.xero.com/myapps

## Step 1: Authorize with <PERSON>ero (One-time setup)

Since <PERSON><PERSON> uses OAuth 2.0, you need to authorize the app first:

1. **Start the API server:**
   ```bash
   source venv39/bin/activate
   uvicorn mcx3d_finance.main:app --reload --port 8000
   ```

2. **In a new terminal, initiate OAuth flow:**
   ```bash
   source venv39/bin/activate
   # First, you need to create an organization in the database
   python -c "
from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization
db = SessionLocal()
org = Organization(name='My Company', xero_tenant_id='')
db.add(org)
db.commit()
print(f'Created organization with ID: {org.id}')
db.close()
"
   ```

3. **Visit the authorization URL in your browser:**
   ```
   http://localhost:8000/api/auth/xero/authorize?organization_id=1
   ```

4. **Complete the Xero authorization flow**

## Step 2: Sync All Company Data

Once authorized, you can sync all data:

```bash
source venv39/bin/activate

# Full sync (downloads all data)
python -m mcx3d_finance.cli.main sync xero --org-id 1

# Or with progress display
python -m mcx3d_finance.cli.main sync xero --org-id 1 --show-progress

# Or run asynchronously (requires Celery)
python -m mcx3d_finance.cli.main sync xero --org-id 1 --async-mode
```

## Step 3: Generate Reports

After syncing, you can generate reports:

```bash
# Generate income statement
python -m mcx3d_finance.cli.main generate income-statement --org-id 1 --period 2024-Q1 --export pdf

# Generate balance sheet
python -m mcx3d_finance.cli.main generate balance-sheet --org-id 1 --period 2024-Q1 --export pdf

# Generate cash flow statement
python -m mcx3d_finance.cli.main generate cash-flow --org-id 1 --period 2024-Q1 --export pdf
```

## Step 4: Run Valuations

```bash
# DCF valuation
python -m mcx3d_finance.cli.main valuate dcf --org-id 1 --export pdf

# SaaS metrics and valuation
python -m mcx3d_finance.cli.main valuate saas --org-id 1 --comprehensive --export pdf
```

## What Gets Downloaded

The sync command downloads:
- ✅ Chart of Accounts (with GAAP classification)
- ✅ Contacts (with business enrichment)
- ✅ Invoices (sales and purchase)
- ✅ Bills
- ✅ Bank Transactions
- ✅ Financial Reports (Trial Balance, P&L)

## Troubleshooting

1. **Missing credentials error:**
   - Make sure XERO_CLIENT_ID and XERO_CLIENT_SECRET are set in .env

2. **Organization not found:**
   - Make sure you created an organization in the database first

3. **OAuth error:**
   - Check that XERO_REDIRECT_URI matches your app settings in Xero
   - Default: http://localhost:8000/api/auth/xero/callback

4. **Check sync status:**
   ```bash
   python -m mcx3d_finance.cli.main sync status <task_id>
   ```