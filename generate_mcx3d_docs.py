#!/usr/bin/env python3
"""
Direct script to generate MCX3D LTD financial documentation using sample data.
This bypasses the CLI module issue and runs the generation directly.
"""

import sys
import os
import asyncio
from datetime import date
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization
from mcx3d_finance.reporting.documentation_builder import FinancialDocumentationBuilder

def main():
    """Generate MCX3D LTD financial documentation."""
    
    print("🚀 MCX3D LTD Financial Documentation Generator")
    print("=" * 60)
    print("📊 Using sample database data")
    print("📅 Report Date: Today")
    print("📁 Output Directory: ./reports")
    print("📄 Formats: PDF, Excel")
    print("=" * 60)
    
    # Create output directory
    output_path = Path("./reports")
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Find MCX3D organization
    db = SessionLocal()
    try:
        print("\n🔍 Finding MCX3D LTD organization...")
        mcx3d_org = db.query(Organization).filter(
            Organization.name == "MCX3D LTD"
        ).first()
        
        if not mcx3d_org:
            print("❌ Error: MCX3D LTD organization not found in database.")
            print("Please run: python scripts/seed_database.py --company-data --reset")
            return 1
        
        org_id = mcx3d_org.id
        print(f"✅ Found MCX3D LTD organization (ID: {org_id})")
        
    finally:
        db.close()
    
    # Initialize documentation builder for sample data
    print("\n📋 Initializing documentation builder...")
    doc_builder = FinancialDocumentationBuilder.for_sample_data(org_id)
    print("✅ Documentation builder initialized")
    
    # Generate documentation
    print("\n💼 Generating comprehensive financial documentation...")
    try:
        # Run async generation
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            result = loop.run_until_complete(
                doc_builder.generate_complete_financial_package(
                    output_directory=str(output_path),
                    report_date=date.today(),
                    include_projections=True,
                    formats=['pdf', 'excel']
                )
            )
        finally:
            loop.close()
        
        # Display results
        print("\n" + "=" * 60)
        print("✅ MCX3D LTD Financial Documentation Generated Successfully!")
        print("=" * 60)
        
        # Show generated documents
        if 'documents' in result:
            print("\n📄 Generated Documents:")
            for category, files in result['documents'].items():
                if files:
                    category_name = category.replace('_', ' ').title()
                    print(f"   • {category_name}: {len(files)} file(s)")
        
        # Show package location
        if 'package_path' in result:
            print(f"\n📦 Complete Package: {result['package_path']}")
        
        # Show financial highlights
        if 'summary' in result:
            summary = result['summary']
            print(f"\n📊 Financial Highlights:")
            print(f"   • Revenue: £{summary.get('revenue', 0):,.0f}")
            print(f"   • Net Income: £{summary.get('net_income', 0):,.0f}")
            print(f"   • Total Assets: £{summary.get('total_assets', 0):,.0f}")
            
            if 'key_metrics' in summary:
                metrics = summary['key_metrics']
                if 'arr' in metrics and metrics['arr'] > 0:
                    print(f"   • Annual Recurring Revenue: £{metrics['arr']:,.0f}")
                if 'gross_margin' in metrics:
                    print(f"   • Gross Margin: {metrics['gross_margin']:.1f}%")
        
        print(f"\n📁 All documents saved to: {output_path.absolute()}")
        print("\n🎉 MCX3D LTD financial documentation generation completed successfully!")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Error generating documentation: {str(e)}")
        import traceback
        print(f"Details: {traceback.format_exc()}")
        return 1

if __name__ == "__main__":
    sys.exit(main())