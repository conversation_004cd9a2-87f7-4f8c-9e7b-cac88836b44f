"""
Test suite for reports CLI commands.

Tests the standardized report generation commands with the new CLI utilities.
"""

import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from unittest.mock import Mock, patch, MagicMock, mock_open
import json
from datetime import datetime
from pathlib import Path

from mcx3d_finance.cli.reports import (
    generate, income_statement_command, balance_sheet_command,
    cash_flow_command, parse_period, validate_organization_id,
    ensure_output_directory, check_data_availability
)
from mcx3d_finance.exceptions import ValidationError


class TestPeriodParsing:
    """Test period parsing functionality."""
    
    def test_parse_quarter(self):
        """Test parsing quarterly periods."""
        start, end = parse_period("2023-Q1")
        assert start == "2023-01-01"
        assert end == "2023-03-31"
        
        start, end = parse_period("2023-Q2")
        assert start == "2023-04-01"
        assert end == "2023-06-30"
        
        start, end = parse_period("2023-Q3")
        assert start == "2023-07-01"
        assert end == "2023-09-30"
        
        start, end = parse_period("2023-Q4")
        assert start == "2023-10-01"
        assert end == "2023-12-31"
    
    def test_parse_month(self):
        """Test parsing monthly periods."""
        start, end = parse_period("2023-01")
        assert start == "2023-01-01"
        assert end == "2023-01-31"
        
        start, end = parse_period("2023-02")
        assert start == "2023-02-01"
        assert end == "2023-02-28"
        
        start, end = parse_period("2023-12")
        assert start == "2023-12-01"
        assert end == "2023-12-31"
    
    def test_parse_year(self):
        """Test parsing yearly periods."""
        start, end = parse_period("2023")
        assert start == "2023-01-01"
        assert end == "2023-12-31"
    
    def test_parse_invalid_period(self):
        """Test parsing invalid periods."""
        with pytest.raises(ValidationError) as exc_info:
            parse_period("2023-Q5")
        assert "Invalid quarter: 5" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            parse_period("invalid")
        assert "Invalid period format" in str(exc_info.value)


class TestValidators:
    """Test validation functions."""
    
    def test_validate_organization_id_valid(self):
        """Test valid organization ID."""
        ctx = Mock()
        param = Mock()
        
        assert validate_organization_id(ctx, param, "123") == 123
        assert validate_organization_id(ctx, param, 1) == 1
    
    def test_validate_organization_id_invalid(self):
        """Test invalid organization ID."""
        ctx = Mock()
        param = Mock()
        
        # Non-integer
        with pytest.raises(click.BadParameter):
            validate_organization_id(ctx, param, "abc")
        
        # Zero
        with pytest.raises(click.BadParameter):
            validate_organization_id(ctx, param, "0")
        
        # Negative
        with pytest.raises(click.BadParameter):
            validate_organization_id(ctx, param, "-1")
        
        # None
        assert validate_organization_id(ctx, param, None) is None
    
    @patch('pathlib.Path.mkdir')
    @patch('pathlib.Path.touch')
    @patch('pathlib.Path.unlink')
    def test_ensure_output_directory(self, mock_unlink, mock_touch, mock_mkdir):
        """Test output directory creation and validation."""
        result = ensure_output_directory("/tmp/test/report.pdf")
        assert result == "/tmp/test/report.pdf"
        mock_mkdir.assert_called_once_with(parents=True, exist_ok=True)
    
    @patch('pathlib.Path.mkdir')
    @patch('pathlib.Path.touch', side_effect=PermissionError())
    def test_ensure_output_directory_no_permission(self, mock_touch, mock_mkdir):
        """Test output directory with no write permission."""
        with pytest.raises(ValidationError) as exc_info:
            ensure_output_directory("/tmp/test/report.pdf")
        assert "No write permission" in str(exc_info.value)


class TestReportGeneration:
    """Test report generation commands."""
    
    @pytest.fixture
    def runner(self):
        """Create a CLI runner."""
        return CliRunner()
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        with patch('mcx3d_finance.cli.reports.SessionLocal') as mock:
            yield mock
    
    @pytest.fixture
    def mock_generator(self):
        """Mock report generator."""
        with patch('mcx3d_finance.cli.reports.ReportGenerator') as mock:
            yield mock
    
    def test_generate_group(self, runner):
        """Test the generate group command."""
        result = runner.invoke(generate, ['--help'])
        assert result.exit_code == 0
        assert "Generate financial reports" in result.output
        assert "--debug" in result.output
        assert "--format" in result.output
        assert "--log-file" in result.output
    
    @patch('mcx3d_finance.cli.reports.setup_logging')
    def test_generate_group_with_options(self, mock_setup_logging, runner):
        """Test generate group with options."""
        result = runner.invoke(generate, ['--debug', '--format', 'json', '--log-file', 'test.log'])
        mock_setup_logging.assert_called_once_with(debug=True, log_file='test.log')
    
    @patch('mcx3d_finance.cli.reports.ensure_output_directory')
    @patch('mcx3d_finance.cli.reports.check_data_availability', return_value=True)
    def test_income_statement_sync(self, mock_check_data, mock_ensure_dir, 
                                  runner, mock_db, mock_generator):
        """Test synchronous income statement generation."""
        mock_ensure_dir.return_value = "/tmp/income_statement.pdf"
        
        # Mock the income statement generator
        with patch('mcx3d_finance.cli.reports.income_statement_calc.IncomeStatementGenerator') as mock_gen:
            mock_instance = Mock()
            mock_instance.generate_income_statement.return_value = {"revenues": {}, "expenses": {}}
            mock_gen.return_value = mock_instance
            
            result = runner.invoke(generate, [
                'income-statement',
                '--organization-id', '123',
                '--period', '2023-Q4',
                '--format', 'pdf'
            ])
            
            assert result.exit_code == 0
            assert "Generating income statement for organization 123" in result.output
            assert "Income statement generated successfully" in result.output
    
    @patch('mcx3d_finance.cli.reports.handle_async_task')
    @patch('mcx3d_finance.cli.reports.ensure_output_directory')
    def test_income_statement_async(self, mock_ensure_dir, mock_handle_async, 
                                   runner, mock_db):
        """Test asynchronous income statement generation."""
        mock_ensure_dir.return_value = "/tmp/income_statement.pdf"
        mock_handle_async.return_value = {"status": "success"}
        
        with patch('mcx3d_finance.cli.reports.generate_income_statement_async') as mock_async:
            mock_task = Mock()
            mock_task.id = "task-123"
            mock_async.delay.return_value = mock_task
            
            result = runner.invoke(generate, [
                'income-statement',
                '--organization-id', '123',
                '--period', '2023-Q4',
                '--async'
            ])
            
            assert result.exit_code == 0
            assert "Starting async income statement generation" in result.output
            assert "Income statement generated successfully" in result.output
            mock_handle_async.assert_called_once_with("task-123")
    
    @patch('mcx3d_finance.cli.reports.ensure_output_directory')
    @patch('mcx3d_finance.cli.reports.check_data_availability', return_value=False)
    @patch('mcx3d_finance.cli.reports.confirm_action', return_value=False)
    def test_income_statement_no_data(self, mock_confirm, mock_check_data, 
                                     mock_ensure_dir, runner, mock_db):
        """Test income statement with no data."""
        mock_ensure_dir.return_value = "/tmp/income_statement.pdf"
        
        result = runner.invoke(generate, [
            'income-statement',
            '--organization-id', '123',
            '--period', '2023-Q4'
        ])
        
        assert result.exit_code == 0
        assert "No transaction data found" in result.output
        mock_confirm.assert_called_once()
    
    @patch('mcx3d_finance.cli.reports.ensure_output_directory')
    def test_balance_sheet_sync(self, mock_ensure_dir, runner, mock_db, mock_generator):
        """Test synchronous balance sheet generation."""
        mock_ensure_dir.return_value = "/tmp/balance_sheet.pdf"
        
        result = runner.invoke(generate, [
            'balance-sheet',
            '--organization-id', '123',
            '--date', '2023-12-31',
            '--format', 'pdf'
        ])
        
        assert result.exit_code == 0
        assert "Generating balance sheet for organization 123" in result.output
        assert "Balance sheet generated successfully" in result.output
    
    @patch('mcx3d_finance.cli.reports.handle_async_task')
    @patch('mcx3d_finance.cli.reports.ensure_output_directory')
    def test_balance_sheet_async(self, mock_ensure_dir, mock_handle_async, runner):
        """Test asynchronous balance sheet generation."""
        mock_ensure_dir.return_value = "/tmp/balance_sheet.pdf"
        mock_handle_async.return_value = {"status": "success"}
        
        with patch('mcx3d_finance.cli.reports.generate_balance_sheet_async') as mock_async:
            mock_task = Mock()
            mock_task.id = "task-456"
            mock_async.delay.return_value = mock_task
            
            result = runner.invoke(generate, [
                'balance-sheet',
                '--organization-id', '123',
                '--date', '2023-12-31',
                '--async'
            ])
            
            assert result.exit_code == 0
            assert "Starting async balance sheet generation" in result.output
            assert "Balance sheet generated successfully" in result.output
    
    @patch('mcx3d_finance.cli.reports.ensure_output_directory')
    def test_cash_flow_sync(self, mock_ensure_dir, runner, mock_db, mock_generator):
        """Test synchronous cash flow generation."""
        mock_ensure_dir.return_value = "/tmp/cash_flow.pdf"
        
        # Mock cash flow generator
        with patch('mcx3d_finance.cli.reports.cash_flow_calc.CashFlowGenerator') as mock_gen:
            mock_instance = Mock()
            mock_instance.generate_cash_flow_statement.return_value = {
                "operating_activities": {},
                "investing_activities": {},
                "financing_activities": {}
            }
            mock_gen.return_value = mock_instance
            
            result = runner.invoke(generate, [
                'cash-flow',
                '--organization-id', '123',
                '--period', '2023-Q4',
                '--format', 'pdf'
            ])
            
            assert result.exit_code == 0
            assert "Generating cash flow statement for organization 123" in result.output
            assert "Cash flow statement generated successfully" in result.output
    
    def test_invalid_command(self, runner):
        """Test invalid command."""
        result = runner.invoke(generate, ['invalid-report'])
        assert result.exit_code != 0
        assert "Error" in result.output or "Usage" in result.output


class TestDataAvailability:
    """Test data availability checking."""
    
    def test_check_data_availability_with_data(self):
        """Test checking data availability when data exists."""
        mock_db = Mock()
        mock_query = Mock()
        mock_query.filter.return_value.count.return_value = 100
        mock_db.query.return_value = mock_query
        
        result = check_data_availability(mock_db, 123, "2023-01-01", "2023-12-31")
        assert result is True
    
    def test_check_data_availability_no_data(self):
        """Test checking data availability when no data exists."""
        mock_db = Mock()
        mock_query = Mock()
        mock_query.filter.return_value.count.return_value = 0
        mock_db.query.return_value = mock_query
        
        result = check_data_availability(mock_db, 123, "2023-01-01", "2023-12-31")
        assert result is False


class TestReportFormatGeneration:
    """Test different report format generation."""
    
    @patch('mcx3d_finance.cli.reports.SessionLocal')
    @patch('mcx3d_finance.cli.reports.income_statement_calc.IncomeStatementGenerator')
    def test_generate_income_statement_pdf(self, mock_gen_class, mock_db):
        """Test PDF income statement generation."""
        mock_generator = Mock()
        mock_gen_instance = Mock()
        mock_gen_instance.generate_income_statement.return_value = {"revenues": {}}
        mock_gen_class.return_value = mock_gen_instance
        
        from mcx3d_finance.cli.reports import generate_income_statement_pdf
        
        result = generate_income_statement_pdf(
            mock_generator, 123, "2023-01-01", "2023-12-31", "output.pdf"
        )
        
        mock_generator.generate_income_statement_pdf.assert_called_once()
        assert "transaction_count" in result
    
    @patch('mcx3d_finance.cli.reports.SessionLocal')
    @patch('mcx3d_finance.cli.reports.income_statement_calc.IncomeStatementGenerator')
    def test_generate_income_statement_excel(self, mock_gen_class, mock_db):
        """Test Excel income statement generation."""
        mock_generator = Mock()
        mock_gen_instance = Mock()
        mock_gen_instance.generate_income_statement.return_value = {"revenues": {}}
        mock_gen_class.return_value = mock_gen_instance
        
        from mcx3d_finance.cli.reports import generate_income_statement_excel
        
        result = generate_income_statement_excel(
            mock_generator, 123, "2023-01-01", "2023-12-31", "output.xlsx"
        )
        
        mock_generator.generate_income_statement_excel.assert_called_once()
        assert "transaction_count" in result
    
    @patch('mcx3d_finance.cli.reports.SessionLocal')
    @patch('mcx3d_finance.cli.reports.income_statement_calc.IncomeStatementGenerator')
    @patch('builtins.open', new_callable=mock_open)
    def test_generate_income_statement_json(self, mock_file, mock_gen_class, mock_db):
        """Test JSON income statement generation."""
        mock_generator = Mock()
        mock_gen_instance = Mock()
        mock_gen_instance.generate_income_statement.return_value = {"revenues": {"sales": 1000}}
        mock_gen_class.return_value = mock_gen_instance
        
        from mcx3d_finance.cli.reports import generate_income_statement_json
        
        result = generate_income_statement_json(
            mock_generator, 123, "2023-01-01", "2023-12-31", "output.json"
        )
        
        mock_file.assert_called_once_with("output.json", 'w')
        assert "transaction_count" in result


class TestOutputFormatting:
    """Test output formatting with different context formats."""
    
    def test_human_format_output(self, runner):
        """Test human-readable format output."""
        result = runner.invoke(generate, [
            '--format', 'human',
            '--help'
        ])
        assert result.exit_code == 0
        # Help text should be displayed normally
        assert "Generate financial reports" in result.output
    
    @patch('mcx3d_finance.cli.reports.ensure_output_directory')
    @patch('mcx3d_finance.cli.reports.check_data_availability', return_value=True)
    def test_json_format_output(self, mock_check_data, mock_ensure_dir, runner):
        """Test JSON format output."""
        mock_ensure_dir.return_value = "/tmp/report.pdf"
        
        # This would require a full command execution
        # For now, we test that the format option is accepted
        result = runner.invoke(generate, [
            '--format', 'json',
            '--help'
        ])
        assert result.exit_code == 0