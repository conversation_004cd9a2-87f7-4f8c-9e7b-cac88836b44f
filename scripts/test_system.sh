#!/bin/bash

# Test script to verify the financial documentation system

echo "🧪 Testing MCX3D Financial Documentation System"
echo "=============================================="

# Check Python
echo ""
echo "🐍 Checking Python environment..."
if command -v python3 >/dev/null 2>&1; then
    echo "✅ Python $(python3 --version) found"
else
    echo "❌ Python 3 is required but not found"
    exit 1
fi

# Check Docker
echo ""
echo "🐳 Checking Docker..."
if command -v docker >/dev/null 2>&1; then
    echo "✅ Docker $(docker --version | cut -d' ' -f3 | cut -d',' -f1) found"
else
    echo "❌ Docker is required but not found"
    exit 1
fi

# Check environment file
echo ""
echo "📄 Checking environment configuration..."
if [ -f ".env.development" ] || [ -f ".env" ]; then
    echo "✅ Environment file found"
    
    # Check for Xero credentials
    if grep -q "XERO_CLIENT_ID=." .env* 2>/dev/null; then
        echo "✅ Xero credentials appear to be configured"
    else
        echo "⚠️  Xero credentials not configured - real data generation will fail"
        echo "   Please add your Xero credentials to .env.development"
    fi
else
    echo "⚠️  No environment file found"
    echo "   Run: cp .env.example .env.development"
fi

# Check directories
echo ""
echo "📁 Checking directory structure..."
for dir in mcx3d_finance/company mcx3d_finance/reporting mcx3d_finance/cli scripts reports; do
    if [ -d "$dir" ]; then
        echo "✅ $dir exists"
    else
        echo "❌ Missing directory: $dir"
    fi
done

# Run Python test
echo ""
echo "🔧 Running system tests..."
if python3 test_financial_docs.py; then
    echo ""
    echo "✅ All tests passed!"
else
    echo ""
    echo "❌ Some tests failed"
fi

# Summary
echo ""
echo "📋 Summary"
echo "=========="
echo ""
echo "To generate financial documents:"
echo "1. Ensure Xero credentials are configured in .env.development"
echo "2. Run: ./scripts/launch_with_docs.sh"
echo "3. Or use: ./scripts/quick_start.sh for interactive setup"
echo ""
echo "For more information, see:"
echo "- FINANCIAL_DOCS_README.md - User guide"
echo "- IMPLEMENTATION_SUMMARY.md - Technical overview"