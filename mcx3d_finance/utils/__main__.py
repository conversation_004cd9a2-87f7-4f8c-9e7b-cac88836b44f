"""
Command-line utilities for MCX3D Finance.
"""
import sys


def main():
    """Main entry point for utilities."""
    if len(sys.argv) < 2:
        print("Usage: python -m mcx3d_finance.utils <command>")
        print("\nAvailable commands:")
        print("  generate_keys    - Generate secure cryptographic keys")
        print("  security_audit   - Run security configuration audit")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "generate_keys":
        from .generate_keys import main as generate_main
        sys.argv = [sys.argv[0]] + sys.argv[2:]  # Remove command from argv
        generate_main()
    elif command == "security_audit":
        from .security_validator import run_security_audit
        run_security_audit()
    else:
        print(f"Unknown command: {command}")
        sys.exit(1)


if __name__ == "__main__":
    main()