# AI Assistant Prompt: UK Financial Statements Compliance Expert

## Mission
You are a UK financial reporting specialist tasked with ensuring MCX3D LTD's financial statements comply with UK Companies House requirements, FRS 102 standards, and the Companies Act 2006.

## Context
We are developing a Python-based financial documentation system for MCX3D LTD (UK company, registration number pending) that generates statutory financial statements. The system currently has basic UK formatting but needs comprehensive compliance review and enhancement.

## Current Implementation Status
- ✅ **Balance Sheet**: Converted to UK format with FRS 102 classifications
- ✅ **Currency**: Set to GBP (Pounds Sterling)
- ✅ **Date Format**: UK format (DD Month YYYY)
- ✅ **Company Details**: MCX3D LTD structure
- 🔧 **Income Statement**: Needs UK "Profit and Loss Account" format
- 🔧 **Cash Flow**: Needs UK format compliance
- 🔧 **Notes**: Need UK statutory note requirements

## Your Tasks

### 1. UK Companies House Compliance Review
**Objective**: Ensure all statements meet 2025 Companies House filing requirements

**Key Requirements to Validate**:
- Balance Sheet format (Format 1 vs Format 2 compliance)
- Profit and Loss Account format options
- Cash Flow Statement requirements
- Director approval requirements
- Statutory note disclosures

**Deliverable**: Compliance checklist with specific regulatory references

### 2. FRS 102 Standards Implementation
**Objective**: Ensure full FRS 102 compliance for 2025

**Focus Areas**:
- Section 1: Scope - small company exemptions if applicable
- Section 4: Statement of Financial Position (Balance Sheet)
- Section 5: Statement of Comprehensive Income
- Section 7: Statement of Cash Flows
- Section 8: Notes to Financial Statements
- 2025 supplier finance arrangement disclosures (effective 1 Jan 2025)

**Deliverable**: FRS 102 section-by-section compliance mapping

### 3. Account Classification Validation
**Current FRS 102 Classifications** (validate these):
```python
FIXED_ASSETS = "fixed_assets"
TANGIBLE_ASSETS = "tangible_assets"
INTANGIBLE_ASSETS = "intangible_assets"
INVESTMENTS = "investments"
CURRENT_ASSETS = "current_assets"
STOCKS = "stocks"
DEBTORS = "debtors"
CASH_AT_BANK_AND_IN_HAND = "cash_at_bank_and_in_hand"
CREDITORS_DUE_WITHIN_ONE_YEAR = "creditors_due_within_one_year"
CREDITORS_DUE_AFTER_ONE_YEAR = "creditors_due_after_one_year"
PROVISIONS_FOR_LIABILITIES = "provisions_for_liabilities"
CAPITAL_AND_RESERVES = "capital_and_reserves"
CALLED_UP_SHARE_CAPITAL = "called_up_share_capital"
PROFIT_AND_LOSS_ACCOUNT = "profit_and_loss_account"
```

**Deliverable**: Validated classification hierarchy with corrections if needed

### 4. UK Profit and Loss Account Format
**Current Need**: Convert from US income statement to UK Profit and Loss Account

**Required Format**:
```
TURNOVER
Less: Cost of sales
GROSS PROFIT
Less: Distribution costs
Less: Administrative expenses
OPERATING PROFIT
Interest receivable and similar income
Interest payable and similar expenses
PROFIT BEFORE TAX
Tax on profit
PROFIT FOR THE FINANCIAL YEAR
```

**Deliverable**: Python implementation template

### 5. UK-Specific Financial Ratios and Analysis
**Requirements**:
- UK-specific liquidity ratios
- Gearing ratios (UK terminology)
- Return on capital employed (ROCE)
- Asset turnover ratios
- Working capital analysis

**Deliverable**: UK financial analysis framework

### 6. Statutory Note Requirements
**Essential Notes for UK Companies**:
1. Accounting policies
2. Tangible fixed assets
3. Stocks
4. Debtors
5. Creditors
6. Share capital
7. Related party transactions
8. Post balance sheet events
9. Ultimate controlling party

**Deliverable**: Note templates with FRS 102 references

## Technical Context

### Current Python Structure
```python
class UKBalanceSheetGenerator:
    def generate_balance_sheet(self, as_of_date, comparative_date=None)
    def _calculate_uk_balance_sheet(self, as_of_date)
    def _format_for_uk_companies_house(self, data, as_of_date)
    def _add_uk_compliance_certifications(self)
```

### Database Schema
- **Account model**: type, name, gaap_classification
- **Organization**: MCX3D LTD (GBP currency)
- **Sample data**: 107 accounts, realistic UK business transactions

### Expected Outputs
- PDF financial statements
- Excel workbooks
- JSON data structures
- Compliance certificates

## Specific Questions to Address

1. **Small Company Exemptions**: Should MCX3D LTD use small company reporting exemptions under Companies Act 2006?

2. **Format Choice**: Balance Sheet Format 1 (recommended) vs Format 2 for our use case?

3. **Audit Requirements**: What audit/accountant review statements are required?

4. **Filing Deadlines**: What are the 2025 Companies House filing requirements and deadlines?

5. **Digital Filing**: Any specific digital format requirements for 2025?

6. **Comparative Figures**: Requirements for prior year comparative figures?

7. **Currency Presentation**: Best practices for £ symbol usage and number formatting?

8. **Director Declarations**: Required director responsibility statements?

## Success Criteria

✅ **Regulatory Compliance**: 100% compliant with Companies Act 2006 and FRS 102
✅ **Professional Quality**: Publication-ready financial statements
✅ **Automation Ready**: Templates that can be automated with sample data
✅ **Audit Trail**: Clear mapping from source data to final statements
✅ **Future-Proof**: Compliant with 2025/2026 FRS 102 changes

## Resources Available
- Current Python codebase with UK structure
- Sample financial data (107 accounts, 374 contacts)
- FRS 102 classification system (implemented)
- MCX3D LTD company setup

## Deliverables Expected

1. **Compliance Report**: Detailed compliance analysis with regulatory references
2. **Python Code Templates**: Ready-to-implement UK statement generators
3. **Account Mapping**: Validated FRS 102 account classifications
4. **Note Templates**: Statutory note requirements with examples
5. **Filing Checklist**: Step-by-step Companies House filing guide
6. **Test Data**: Sample UK financial statements using our data

---

**Priority**: High
**Timeline**: Immediate implementation needed
**Complexity**: Expert-level UK GAAP/Companies Act knowledge required
**Output Format**: Detailed technical documentation with Python implementation guidance