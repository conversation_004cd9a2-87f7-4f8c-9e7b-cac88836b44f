# Operations Guide - MCX3D Financial System

This section contains comprehensive operations documentation for deploying, monitoring, and maintaining the MCX3D Financial System in production environments.

## 📚 **Available Guides**

### **[Production Deployment Guide](./production-deployment-guide.md)** ⭐ **NEW**
**Complete production deployment with validated lessons learned from successful deployment**

**What's included:**
- ✅ Step-by-step 40-minute deployment process
- ✅ Validated on January 24, 2025 - **WORKING SOLUTION**
- ✅ Docker Compose with 4 services (web, db, redis, worker)  
- ✅ Security-first configuration with production keys
- ✅ Comprehensive troubleshooting guide
- ✅ Performance benchmarks and health validation

### **[Deployment Guide](./deployment.md)**
General Docker deployment guide with production configuration, scaling, and optimization.

**What you'll learn:**
- Docker Compose setup and configuration
- Production environment preparation
- Service orchestration and scaling
- Load balancing and high availability
- Backup and disaster recovery

### **[Deployment Checklist](./deployment-checklist.md)**
Pre-deployment validation checklist ensuring system readiness and compliance.

**What you'll find:**
- Pre-deployment validation steps
- Environment configuration verification
- Security configuration checklist
- Performance validation requirements
- Post-deployment verification steps

### **[Monitoring Guide](./monitoring.md)**
Comprehensive monitoring and observability setup with metrics, logging, and alerting.

**What you'll learn:**
- Prometheus metrics collection
- Grafana dashboard configuration
- Structured logging with ELK stack
- Alert manager setup and rules
- Performance monitoring and analysis

### **[Security Guide](./security.md)**
Security configuration, hardening, and compliance for production deployments.

**What you'll find:**
- OAuth 2.0 configuration and management
- SSL/TLS certificate management
- Network security and firewall rules
- Data encryption and protection
- Security monitoring and incident response

## 🚀 **Quick Deployment**

### **Production Deployment (40 minutes) - VALIDATED WORKING**
Follow the complete [Production Deployment Guide](./production-deployment-guide.md) for the verified production deployment process:

```bash
# 1. Generate secure keys (5 min)
python3 -m mcx3d_finance.utils.generate_keys

# 2. Configure environment (10 min)
# Create .env.production with generated keys and Xero credentials

# 3. Deploy containers (10 min)  
docker-compose up -d

# 4. Run migrations (5 min)
docker-compose exec web alembic upgrade head

# 5. Validate deployment (10 min)
curl -s http://localhost:8000/ | python3 -m json.tool
python3 scripts/deployment/production_health_check.py
```

**Result**: ✅ All 4 services running, health checks passing, performance validated

### **Development Deployment**
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Scale services
docker-compose up -d --scale worker=3
```

## 🏗 **Architecture Overview**

### **Production Architecture**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Reverse Proxy │    │   Monitoring    │
│    (HAProxy)    │    │     (Nginx)     │    │ (Prometheus)    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
    ┌─────▼──────────────────────▼──────────────────────▼─────┐
    │                Application Layer                        │
    │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
    │  │   Web API   │  │   Web API   │  │   Web API   │     │
    │  │ (Container) │  │ (Container) │  │ (Container) │     │
    │  └─────────────┘  └─────────────┘  └─────────────┘     │
    └─────────────────────────┬─────────────────────────────┘
                              │
    ┌─────────────────────────▼─────────────────────────────┐
    │                Background Layer                       │
    │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐   │
    │  │   Celery    │  │   Celery    │  │   Celery    │   │
    │  │   Worker    │  │   Worker    │  │   Worker    │   │
    │  └─────────────┘  └─────────────┘  └─────────────┘   │
    └─────────────────────────┬─────────────────────────────┘
                              │
    ┌─────────────────────────▼─────────────────────────────┐
    │                  Data Layer                           │
    │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐   │
    │  │ PostgreSQL  │  │    Redis    │  │   Backup    │   │
    │  │  (Primary)  │  │   (Cache)   │  │  Storage    │   │
    │  └─────────────┘  └─────────────┘  └─────────────┘   │
    └───────────────────────────────────────────────────────┘
```

### **Service Components**
- **Web API**: FastAPI application servers (3+ instances)
- **Workers**: Celery background task processors (3+ instances)
- **Database**: PostgreSQL with read replicas
- **Cache**: Redis cluster for performance
- **Load Balancer**: HAProxy for traffic distribution
- **Reverse Proxy**: Nginx for SSL termination and static files
- **Monitoring**: Prometheus + Grafana + AlertManager

## 📊 **Monitoring & Observability**

### **Key Metrics**
- **Application**: Response times, error rates, throughput
- **Infrastructure**: CPU, memory, disk, network utilization
- **Business**: Report generation rates, user activity, system usage
- **Database**: Query performance, connection pools, replication lag
- **Cache**: Hit rates, memory usage, eviction rates

### **Alerting Rules**
- **Critical**: API response time >5s, error rate >5%, disk space <10%
- **Warning**: CPU usage >80%, memory usage >85%, queue depth >100
- **Info**: Deployment events, configuration changes, scheduled maintenance

### **Log Management**
- **Structured Logging**: JSON format with correlation IDs
- **Log Aggregation**: Centralized collection and indexing
- **Log Retention**: 30 days operational, 1 year compliance
- **Security Logs**: Authentication, authorization, data access

## 🔐 **Security Operations**

### **Security Monitoring**
- **Access Logs**: Authentication attempts and API access
- **Vulnerability Scanning**: Regular security assessments
- **Certificate Management**: Automated SSL/TLS renewal
- **Incident Response**: Security event handling procedures

### **Compliance Requirements**
- **Data Protection**: GDPR, CCPA compliance measures
- **Financial Regulations**: SOX, PCI DSS requirements
- **Audit Trails**: Comprehensive activity logging
- **Access Controls**: Role-based permissions and reviews

### **Backup & Recovery**
- **Database Backups**: Daily full, hourly incremental
- **Configuration Backups**: Infrastructure as Code
- **Disaster Recovery**: RTO <4 hours, RPO <1 hour
- **Testing**: Monthly recovery drills and validation

## ⚡ **Performance Operations**

### **Performance Targets**
- **API Response**: <200ms for 95th percentile
- **Report Generation**: <5 seconds for standard reports
- **Availability**: 99.9% uptime (8.7 hours/year downtime)
- **Throughput**: >1000 requests/minute peak capacity

### **Scaling Strategies**
- **Horizontal Scaling**: Auto-scaling groups for web and worker tiers
- **Database Scaling**: Read replicas and connection pooling
- **Cache Optimization**: Redis clustering and memory management
- **Load Distribution**: Geographic distribution and CDN integration

### **Performance Monitoring**
- **Real-time Metrics**: Application and infrastructure monitoring
- **Performance Testing**: Regular load testing and benchmarking
- **Capacity Planning**: Proactive resource allocation
- **Optimization**: Continuous performance improvement

## 🛠 **Operational Procedures**

### **Deployment Procedures**
1. **Pre-deployment**: Validation checklist and approval
2. **Blue-Green Deployment**: Zero-downtime deployment strategy
3. **Health Checks**: Automated post-deployment validation
4. **Rollback**: Automated rollback on failure detection
5. **Communication**: Stakeholder notification and status updates

### **Maintenance Procedures**
- **Scheduled Maintenance**: Monthly patching and updates
- **Database Maintenance**: Index optimization and cleanup
- **Log Rotation**: Automated log management and archival
- **Certificate Renewal**: Automated SSL/TLS certificate management
- **Backup Verification**: Regular backup integrity testing

### **Incident Response**
1. **Detection**: Automated monitoring and alerting
2. **Assessment**: Impact analysis and severity classification
3. **Response**: Immediate mitigation and stakeholder communication
4. **Resolution**: Root cause analysis and permanent fix
5. **Post-mortem**: Documentation and process improvement

## 📈 **Capacity Management**

### **Resource Planning**
- **Compute**: CPU and memory requirements per service
- **Storage**: Database growth patterns and retention policies
- **Network**: Bandwidth requirements and traffic patterns
- **Scaling**: Auto-scaling rules and manual intervention thresholds

### **Cost Optimization**
- **Resource Rightsizing**: Optimal instance types and sizes
- **Reserved Instances**: Long-term capacity planning
- **Spot Instances**: Cost-effective batch processing
- **Monitoring**: Cost tracking and optimization opportunities

## 🔧 **Troubleshooting**

### **Common Issues**

#### **Service Connectivity**
```bash
# Check service health
curl -f http://localhost:8000/health

# Verify database connection
docker-compose exec web python -c "from mcx3d_finance.db.session import SessionLocal; print('DB Connected')"

# Check Redis connectivity
docker-compose exec web python -c "import redis; r=redis.Redis(); r.ping(); print('Redis Connected')"
```

#### **Performance Issues**
```bash
# Monitor resource usage
docker stats

# Check application logs
docker-compose logs web | grep -i error

# Analyze slow queries
docker-compose exec db psql -U postgres -c "SELECT query, mean_exec_time FROM pg_stat_statements ORDER BY mean_exec_time DESC LIMIT 10;"
```

#### **Deployment Issues**
```bash
# Verify container status
docker-compose ps

# Check for port conflicts
netstat -tulpn | grep :8000

# Validate configuration
docker-compose config
```

### **Diagnostic Tools**
- **Health Endpoints**: `/health`, `/metrics`, `/status`
- **Log Analysis**: Structured log queries and correlation
- **Performance Profiling**: Application and database profiling
- **Network Diagnostics**: Connectivity and latency testing

## 📞 **Support & Escalation**

### **Support Tiers**
- **Level 1**: Basic troubleshooting and service restart
- **Level 2**: Advanced diagnostics and configuration changes
- **Level 3**: Code-level debugging and architecture changes
- **Vendor Support**: Third-party service and infrastructure support

### **Escalation Procedures**
1. **Initial Response**: <15 minutes for critical issues
2. **Status Updates**: Every 30 minutes during active incidents
3. **Resolution**: Target resolution times by severity
4. **Communication**: Stakeholder updates and post-incident reports

### **Documentation & Training**
- **Runbooks**: Step-by-step operational procedures
- **Training Materials**: System architecture and operational training
- **Knowledge Base**: Common issues and resolution procedures
- **On-call Procedures**: 24/7 support and escalation protocols

---

**Need help with operations?** Start with the [Deployment Guide](./deployment.md) for setup, then review the [Monitoring Guide](./monitoring.md) for observability, and the [Security Guide](./security.md) for hardening your production environment.