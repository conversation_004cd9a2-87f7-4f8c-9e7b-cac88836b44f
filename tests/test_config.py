#!/usr/bin/env python3
"""
Test script to verify configuration and database connection.
"""
import os
import sys
import sqlite3
from pathlib import Path

def test_env_loading():
    """Test if environment variables are loaded correctly."""
    print("🔍 Testing Environment Configuration")
    print("=" * 50)
    
    # Load .env manually
    env_path = Path(".env")
    if env_path.exists():
        print("✅ .env file found")
        with open(env_path) as f:
            for line in f:
                if line.strip() and not line.startswith('#') and '=' in line:
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
    else:
        print("❌ .env file not found")
        return False
    
    # Check required variables
    required_vars = ['DATABASE_URL', 'SECRET_KEY']
    for var in required_vars:
        value = os.environ.get(var)
        if value:
            print(f"✅ {var}: {'*' * min(len(value), 20)}...")
        else:
            print(f"❌ {var}: Not set")
            return False
    
    return True

def test_database_connection():
    """Test database connection."""
    print("\n🗄️  Testing Database Connection")
    print("=" * 50)
    
    database_url = os.environ.get('DATABASE_URL', '')
    
    if not database_url:
        print("❌ DATABASE_URL not set")
        return False
    
    if database_url.startswith('sqlite:///'):
        # Extract file path
        db_path = database_url.replace('sqlite:///', '')
        print(f"📁 Database file: {db_path}")
        
        try:
            # Test connection
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check if tables exist
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            if tables:
                print(f"✅ Database connected successfully")
                print(f"📊 Found {len(tables)} tables:")
                for table in tables[:5]:  # Show first 5 tables
                    print(f"   - {table[0]}")
                if len(tables) > 5:
                    print(f"   ... and {len(tables) - 5} more")
            else:
                print("⚠️  Database connected but no tables found")
                print("   This might be a fresh database")
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False
    else:
        print(f"⚠️  Non-SQLite database: {database_url}")
        print("   Cannot test connection without proper driver")
        return True

def test_imports():
    """Test if basic imports work."""
    print("\n📦 Testing Python Imports")
    print("=" * 50)
    
    try:
        # Test core imports
        sys.path.insert(0, '.')
        
        print("Testing core config import...")
        from mcx3d_finance.core.config import settings, get_security_config
        print("✅ Core config imported successfully")
        
        print("Testing security config...")
        security_config = get_security_config()
        print("✅ Security config loaded successfully")
        
        print("Testing database session...")
        from mcx3d_finance.db.session import get_db
        print("✅ Database session imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 MCX3D Finance Configuration Test")
    print("=" * 70)
    
    results = []
    
    # Test environment loading
    results.append(test_env_loading())
    
    # Test database connection
    results.append(test_database_connection())
    
    # Test imports
    results.append(test_imports())
    
    # Summary
    print("\n📋 Test Summary")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 All tests passed! ({passed}/{total})")
        print("✅ Configuration is ready for authentication testing")
        return 0
    else:
        print(f"⚠️  {passed}/{total} tests passed")
        print("❌ Configuration needs fixes before testing endpoints")
        return 1

if __name__ == "__main__":
    sys.exit(main())