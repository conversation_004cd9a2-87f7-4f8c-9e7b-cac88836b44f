"""
Performance optimization configuration and utilities for MCX3D Financials.
"""
import os
import time
import asyncio
import logging
from typing import Optional, Callable, Any
from functools import wraps
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class PerformanceConfig:
    """Performance optimization configuration."""
    
    # Thread pool settings
    MAX_WORKERS: int = int(os.getenv("MAX_WORKERS", "10"))
    THREAD_NAME_PREFIX: str = "mcx3d-worker"
    
    # Query optimization
    QUERY_TIMEOUT: int = int(os.getenv("QUERY_TIMEOUT", "30"))  # seconds
    MAX_QUERY_RESULTS: int = int(os.getenv("MAX_QUERY_RESULTS", "10000"))
    ENABLE_QUERY_CACHE: bool = os.getenv("ENABLE_QUERY_CACHE", "true").lower() == "true"
    
    # Batch processing
    DEFAULT_BATCH_SIZE: int = int(os.getenv("DEFAULT_BATCH_SIZE", "1000"))
    MAX_BATCH_SIZE: int = int(os.getenv("MAX_BATCH_SIZE", "5000"))
    
    # API response optimization
    ENABLE_RESPONSE_COMPRESSION: bool = os.getenv("ENABLE_RESPONSE_COMPRESSION", "true").lower() == "true"
    COMPRESSION_LEVEL: int = int(os.getenv("COMPRESSION_LEVEL", "6"))  # 1-9
    MIN_COMPRESSION_SIZE: int = int(os.getenv("MIN_COMPRESSION_SIZE", "1024"))  # bytes
    
    # Pagination defaults
    DEFAULT_PAGE_SIZE: int = int(os.getenv("DEFAULT_PAGE_SIZE", "100"))
    MAX_PAGE_SIZE: int = int(os.getenv("MAX_PAGE_SIZE", "1000"))
    
    # Performance monitoring
    ENABLE_PERFORMANCE_MONITORING: bool = os.getenv("ENABLE_PERFORMANCE_MONITORING", "true").lower() == "true"
    SLOW_QUERY_THRESHOLD: float = float(os.getenv("SLOW_QUERY_THRESHOLD", "1.0"))  # seconds
    SLOW_API_THRESHOLD: float = float(os.getenv("SLOW_API_THRESHOLD", "2.0"))  # seconds
    
    # Memory optimization
    MAX_MEMORY_CACHE_SIZE: int = int(os.getenv("MAX_MEMORY_CACHE_SIZE", "104857600"))  # 100MB
    ENABLE_MEMORY_PROFILING: bool = os.getenv("ENABLE_MEMORY_PROFILING", "false").lower() == "true"


# Global thread pool executor
_executor: Optional[ThreadPoolExecutor] = None


def get_executor() -> ThreadPoolExecutor:
    """Get global thread pool executor."""
    global _executor
    if _executor is None:
        _executor = ThreadPoolExecutor(
            max_workers=PerformanceConfig.MAX_WORKERS,
            thread_name_prefix=PerformanceConfig.THREAD_NAME_PREFIX
        )
    return _executor


def measure_performance(operation_name: Optional[str] = None):
    """Decorator to measure function performance."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                if PerformanceConfig.ENABLE_PERFORMANCE_MONITORING:
                    name = operation_name or f"{func.__module__}.{func.__name__}"
                    
                    if execution_time > PerformanceConfig.SLOW_API_THRESHOLD:
                        logger.warning(
                            f"Slow operation detected: {name} took {execution_time:.2f}s"
                        )
                    else:
                        logger.debug(f"Operation {name} completed in {execution_time:.2f}s")
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"Operation {operation_name or func.__name__} failed after {execution_time:.2f}s: {e}"
                )
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                if PerformanceConfig.ENABLE_PERFORMANCE_MONITORING:
                    name = operation_name or f"{func.__module__}.{func.__name__}"
                    
                    if execution_time > PerformanceConfig.SLOW_API_THRESHOLD:
                        logger.warning(
                            f"Slow operation detected: {name} took {execution_time:.2f}s"
                        )
                    else:
                        logger.debug(f"Operation {name} completed in {execution_time:.2f}s")
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"Operation {operation_name or func.__name__} failed after {execution_time:.2f}s: {e}"
                )
                raise
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


class BatchProcessor:
    """Batch processing utility for large datasets."""
    
    def __init__(self, batch_size: Optional[int] = None):
        self.batch_size = min(
            batch_size or PerformanceConfig.DEFAULT_BATCH_SIZE,
            PerformanceConfig.MAX_BATCH_SIZE
        )
    
    def process_in_batches(self, items: list, processor: Callable, **kwargs) -> list:
        """Process items in batches to optimize memory usage."""
        results = []
        total_items = len(items)
        
        for i in range(0, total_items, self.batch_size):
            batch = items[i:i + self.batch_size]
            batch_num = (i // self.batch_size) + 1
            total_batches = (total_items + self.batch_size - 1) // self.batch_size
            
            logger.debug(f"Processing batch {batch_num}/{total_batches} ({len(batch)} items)")
            
            try:
                batch_results = processor(batch, **kwargs)
                results.extend(batch_results)
            except Exception as e:
                logger.error(f"Error processing batch {batch_num}: {e}")
                raise
        
        return results
    
    async def process_in_batches_async(self, items: list, processor: Callable, **kwargs) -> list:
        """Process items in batches asynchronously."""
        results = []
        total_items = len(items)
        
        for i in range(0, total_items, self.batch_size):
            batch = items[i:i + self.batch_size]
            batch_num = (i // self.batch_size) + 1
            total_batches = (total_items + self.batch_size - 1) // self.batch_size
            
            logger.debug(f"Processing batch {batch_num}/{total_batches} ({len(batch)} items)")
            
            try:
                batch_results = await processor(batch, **kwargs)
                results.extend(batch_results)
            except Exception as e:
                logger.error(f"Error processing batch {batch_num}: {e}")
                raise
        
        return results


class QueryOptimizer:
    """Query optimization utilities."""
    
    @staticmethod
    def apply_pagination(query, page: int = 1, page_size: Optional[int] = None):
        """Apply pagination to a SQLAlchemy query."""
        page_size = min(
            page_size or PerformanceConfig.DEFAULT_PAGE_SIZE,
            PerformanceConfig.MAX_PAGE_SIZE
        )
        
        offset = (page - 1) * page_size
        return query.limit(page_size).offset(offset)
    
    @staticmethod
    def optimize_query_for_counting(query):
        """Optimize query for counting results."""
        # Remove unnecessary joins and order by clauses
        from sqlalchemy import func
        from sqlalchemy.orm import Query
        
        if isinstance(query, Query):
            # Remove order by for count queries
            query = query.order_by(None)
            
            # Use subquery for complex queries
            return query.from_self().count()
        
        return query.count()
    
    @staticmethod
    def add_query_hints(query, hints: dict):
        """Add database-specific query hints for optimization."""
        # PostgreSQL specific hints
        if hints.get('parallel'):
            query = query.execution_options(
                postgresql_parallel_workers=hints.get('parallel_workers', 4)
            )
        
        if hints.get('timeout'):
            query = query.execution_options(
                postgresql_statement_timeout=hints['timeout'] * 1000  # Convert to ms
            )
        
        return query


def optimize_json_response(data: Any) -> Any:
    """Optimize JSON response by removing null values and empty collections."""
    if isinstance(data, dict):
        return {
            k: optimize_json_response(v)
            for k, v in data.items()
            if v is not None and (not isinstance(v, (list, dict)) or v)
        }
    elif isinstance(data, list):
        return [optimize_json_response(item) for item in data]
    else:
        return data


def get_performance_metrics() -> dict:
    """Get current performance metrics."""
    import psutil
    import gc
    
    # Get process info
    process = psutil.Process()
    
    # Memory info
    memory_info = process.memory_info()
    memory_percent = process.memory_percent()
    
    # CPU info
    cpu_percent = process.cpu_percent(interval=0.1)
    cpu_count = psutil.cpu_count()
    
    # Thread pool info
    executor = get_executor()
    
    # Garbage collection stats
    gc_stats = {
        f"generation_{i}": {
            "collections": gc.get_count()[i],
            "collected": gc.get_stats()[i].get('collected', 0) if i < len(gc.get_stats()) else 0,
            "uncollectable": gc.get_stats()[i].get('uncollectable', 0) if i < len(gc.get_stats()) else 0,
        }
        for i in range(gc.get_count().__len__())
    }
    
    return {
        "memory": {
            "rss_mb": round(memory_info.rss / 1024 / 1024, 2),
            "vms_mb": round(memory_info.vms / 1024 / 1024, 2),
            "percent": round(memory_percent, 2),
            "available_mb": round(psutil.virtual_memory().available / 1024 / 1024, 2),
        },
        "cpu": {
            "percent": round(cpu_percent, 2),
            "count": cpu_count,
            "load_average": os.getloadavg() if hasattr(os, 'getloadavg') else None,
        },
        "thread_pool": {
            "max_workers": executor._max_workers,
            "threads": len(executor._threads),
            "pending_tasks": executor._work_queue.qsize() if hasattr(executor._work_queue, 'qsize') else None,
        },
        "gc_stats": gc_stats,
        "performance_config": {
            "query_cache_enabled": PerformanceConfig.ENABLE_QUERY_CACHE,
            "response_compression": PerformanceConfig.ENABLE_RESPONSE_COMPRESSION,
            "monitoring_enabled": PerformanceConfig.ENABLE_PERFORMANCE_MONITORING,
        }
    }


def cleanup_resources():
    """Cleanup performance-related resources."""
    global _executor
    
    if _executor:
        _executor.shutdown(wait=True)
        _executor = None
        logger.info("Thread pool executor shut down successfully")