"""
Xero-specific retry and error handling mechanisms.

Provides decorators and utilities specifically designed for handling
Xero API errors, rate limits, and authentication issues.
"""

import logging
import time
import json
from typing import Any, Callable, Type, Tuple, Optional, Dict
from functools import wraps
import random
from datetime import datetime, timezone

from requests.exceptions import <PERSON>quest<PERSON>x<PERSON>, HTTPError
from xero_python.exceptions import AccountingBadRequestException, ApiException

from ..utils.retry import Circuit<PERSON>reaker, RetryError
from ..exceptions.integration import (
    XeroIntegrationError,
    RateLimitError,
    AuthenticationError,
    APIConnectionError,
    ServiceUnavailableError
)
from ..auth.xero_oauth import XeroAuthManager
from ..db.session import SessionLocal
from ..db.models import Organization

logger = logging.getLogger(__name__)


class XeroCircuitBreaker(CircuitBreaker):
    """
    Xero-specific circuit breaker that understands Xero error patterns.
    """
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 300.0,  # 5 minutes for Xero
        rate_limit_threshold: int = 3  # Open circuit after 3 rate limit errors
    ):
        """
        Initialize Xero circuit breaker.
        
        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Seconds before attempting to close circuit
            rate_limit_threshold: Number of rate limit errors before opening
        """
        super().__init__(failure_threshold, recovery_timeout, Exception)
        self.rate_limit_threshold = rate_limit_threshold
        self.rate_limit_count = 0
        self.last_rate_limit_time = None
    
    def record_failure(self, error: Optional[Exception] = None):
        """
        Record a failed call with Xero-specific handling.
        
        Args:
            error: The exception that occurred
        """
        # Check if it's a rate limit error
        if error and (
            isinstance(error, RateLimitError) or
            (hasattr(error, 'response') and getattr(error.response, 'status_code', None) == 429)
        ):
            self.rate_limit_count += 1
            self.last_rate_limit_time = time.time()
            
            # Open circuit if too many rate limit errors
            if self.rate_limit_count >= self.rate_limit_threshold:
                self.state = "open"
                logger.warning(
                    f"Circuit breaker opened due to {self.rate_limit_count} rate limit errors"
                )
                return
        
        # Regular failure handling
        super().record_failure()
    
    def record_success(self):
        """Record a successful call and reset rate limit counter."""
        super().record_success()
        self.rate_limit_count = 0
        self.last_rate_limit_time = None


def xero_api_call(
    max_retries: int = 3,
    circuit_breaker_threshold: int = 5,
    handle_token_refresh: bool = True,
    respect_rate_limits: bool = True
) -> Callable:
    """
    Decorator for Xero API calls with specialized error handling.
    
    This decorator provides:
    - Xero-specific error handling (401, 403, 429, 503)
    - Automatic token refresh on 401 errors
    - Rate limit detection and backoff
    - Circuit breaker with Xero-aware thresholds
    - Proper exception mapping
    
    Args:
        max_retries: Maximum retry attempts
        circuit_breaker_threshold: Failures before opening circuit
        handle_token_refresh: Whether to automatically refresh tokens on 401
        respect_rate_limits: Whether to check rate limits before calls
        
    Returns:
        Decorated function with Xero-specific error handling
    """
    def decorator(func: Callable) -> Callable:
        # Create Xero-specific circuit breaker
        circuit_breaker = XeroCircuitBreaker(
            failure_threshold=circuit_breaker_threshold,
            recovery_timeout=300.0  # 5 minutes
        )
        
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            retry_count = 0
            
            # Extract organization_id from self if available
            organization_id = None
            if args and hasattr(args[0], 'organization_id'):
                organization_id = args[0].organization_id
            
            while retry_count <= max_retries:
                try:
                    # Check circuit breaker
                    if circuit_breaker.state == "open":
                        if (circuit_breaker.last_failure_time and 
                            time.time() - circuit_breaker.last_failure_time > circuit_breaker.recovery_timeout):
                            circuit_breaker.state = "half-open"
                            logger.info("Xero circuit breaker attempting half-open state")
                        else:
                            raise ServiceUnavailableError(
                                "Xero API circuit breaker is open",
                                service_name="Xero",
                                outage_type="circuit_breaker",
                                estimated_recovery=f"{int(circuit_breaker.recovery_timeout - (time.time() - circuit_breaker.last_failure_time))} seconds"
                            )
                    
                    # Log attempt
                    if retry_count > 0:
                        logger.info(
                            f"Xero API retry attempt {retry_count}/{max_retries} for {func.__name__}"
                        )
                    
                    # Execute function
                    result = func(*args, **kwargs)
                    
                    # Success - record in circuit breaker
                    circuit_breaker.record_success()
                    
                    return result
                    
                except Exception as e:
                    last_exception = e
                    retry_count += 1
                    
                    # Determine if error is retryable and calculate delay
                    is_retryable, delay, should_refresh_token = _analyze_xero_error(e)
                    
                    # Record failure in circuit breaker
                    circuit_breaker.record_failure(e)
                    
                    # Handle token refresh if needed
                    if should_refresh_token and handle_token_refresh and organization_id:
                        logger.info("Attempting token refresh due to 401 error")
                        if _refresh_token_for_organization(organization_id):
                            # Token refreshed, retry immediately
                            continue
                        else:
                            # Token refresh failed, raise authentication error
                            raise AuthenticationError(
                                "Failed to refresh Xero access token",
                                auth_type="OAuth2",
                                service_name="Xero",
                                auth_stage="token_refresh"
                            ) from e
                    
                    # If not retryable or no more retries, raise appropriate exception
                    if not is_retryable or retry_count > max_retries:
                        raise _convert_to_mcx3d_exception(e, func.__name__) from e
                    
                    # Log retry details
                    logger.warning(
                        f"Xero API error in {func.__name__} (attempt {retry_count}/{max_retries}): {e}. "
                        f"Retrying in {delay} seconds..."
                    )
                    
                    # Wait before retry with jitter
                    actual_delay = delay * (0.5 + random.random())
                    time.sleep(actual_delay)
            
            # This should not be reached, but just in case
            if last_exception:
                raise _convert_to_mcx3d_exception(last_exception, func.__name__)
        
        # Attach circuit breaker to function for external access
        wrapper._circuit_breaker = circuit_breaker
        return wrapper
    
    return decorator


def _analyze_xero_error(error: Exception) -> Tuple[bool, float, bool]:
    """
    Analyze Xero error to determine retry strategy.
    
    Args:
        error: The exception to analyze
        
    Returns:
        Tuple of (is_retryable, delay_seconds, should_refresh_token)
    """
    # Handle HTTP errors
    if isinstance(error, HTTPError) and hasattr(error, 'response'):
        status_code = error.response.status_code
        
        if status_code == 401:
            # Unauthorized - token might be expired
            return True, 0, True  # Retry immediately after token refresh
        
        elif status_code == 403:
            # Forbidden - likely permissions issue, not retryable
            return False, 0, False
        
        elif status_code == 429:
            # Rate limited - check for Retry-After header
            retry_after = error.response.headers.get('Retry-After', '60')
            try:
                delay = int(retry_after)
            except ValueError:
                # Might be HTTP date, default to 60 seconds
                delay = 60
            
            return True, delay, False
        
        elif status_code in (500, 502, 503, 504):
            # Server errors - exponential backoff
            return True, 30, False  # 30 second delay for server errors
        
        elif status_code == 400:
            # Bad request - check if it's a specific Xero error
            try:
                error_data = error.response.json()
                error_type = error_data.get('Type', '')
                
                # Some 400 errors are retryable (e.g., temporary validation issues)
                if 'temporary' in error_type.lower() or 'busy' in error_type.lower():
                    return True, 5, False
            except:
                pass
            
            return False, 0, False
    
    # Handle Xero-specific exceptions
    elif isinstance(error, AccountingBadRequestException):
        # Parse Xero error details
        error_details = getattr(error, 'error_data', {})
        if isinstance(error_details, dict):
            error_type = error_details.get('Type', '')
            if 'ratelimit' in error_type.lower():
                return True, 60, False  # Default 60 second delay for rate limits
        
        return False, 0, False
    
    elif isinstance(error, ApiException) and hasattr(error, 'status') and error.status == 403:
        # Forbidden - not retryable
        return False, 0, False
    
    # Handle network errors
    elif isinstance(error, (ConnectionError, TimeoutError, RequestException)):
        # Network errors are retryable with exponential backoff
        return True, 10, False
    
    # Unknown error - don't retry
    return False, 0, False


def _refresh_token_for_organization(organization_id: int) -> bool:
    """
    Refresh token for an organization.
    
    Args:
        organization_id: Organization ID
        
    Returns:
        True if token refreshed successfully, False otherwise
    """
    try:
        auth_manager = XeroAuthManager()
        new_token = auth_manager.get_valid_token(organization_id)
        return new_token is not None
    except Exception as e:
        logger.error(f"Failed to refresh token for organization {organization_id}: {e}")
        return False


def _convert_to_mcx3d_exception(error: Exception, operation: str) -> XeroIntegrationError:
    """
    Convert various exceptions to appropriate MCX3D exceptions.
    
    Args:
        error: The original exception
        operation: The operation that failed
        
    Returns:
        Appropriate MCX3D exception
    """
    # HTTP errors
    if isinstance(error, HTTPError) and hasattr(error, 'response'):
        status_code = error.response.status_code
        
        if status_code == 401:
            return AuthenticationError(
                f"Xero authentication failed during {operation}",
                auth_type="OAuth2",
                service_name="Xero",
                auth_stage="api_call"
            )
        
        elif status_code == 403:
            return XeroIntegrationError(
                f"Forbidden: Insufficient permissions for {operation}",
                operation=operation,
                xero_error_code="403",
                xero_error_message="Forbidden - check Xero app permissions"
            )
        
        elif status_code == 429:
            retry_after = error.response.headers.get('Retry-After', '60')
            return RateLimitError(
                f"Xero rate limit exceeded during {operation}",
                service_name="Xero",
                limit_type="api_calls",
                retry_after=int(retry_after) if retry_after.isdigit() else 60
            )
        
        elif status_code in (500, 502, 503, 504):
            return ServiceUnavailableError(
                f"Xero service unavailable during {operation}",
                service_name="Xero",
                outage_type="server_error"
            )
        
        else:
            # Extract error details from response
            try:
                error_data = error.response.json()
                error_message = error_data.get('Message', str(error))
                error_type = error_data.get('Type', 'Unknown')
            except:
                error_message = str(error)
                error_type = f"HTTP_{status_code}"
            
            return APIConnectionError(
                f"Xero API error during {operation}: {error_message}",
                service_name="Xero",
                endpoint=operation,
                status_code=status_code,
                response_body=error.response.text[:500] if hasattr(error.response, 'text') else None
            )
    
    # Xero-specific exceptions
    elif isinstance(error, AccountingBadRequestException):
        error_data = getattr(error, 'error_data', {})
        return XeroIntegrationError(
            f"Xero validation error during {operation}",
            operation=operation,
            xero_error_code=error_data.get('Type', 'ValidationError'),
            xero_error_message=error_data.get('Message', str(error))
        )
    
    elif isinstance(error, ApiException) and hasattr(error, 'status') and error.status == 403:
        return XeroIntegrationError(
            f"Xero forbidden error during {operation}",
            operation=operation,
            xero_error_code="Forbidden",
            xero_error_message="Check Xero app permissions and scopes"
        )
    
    # Network errors
    elif isinstance(error, (ConnectionError, TimeoutError, RequestException)):
        return APIConnectionError(
            f"Network error connecting to Xero during {operation}",
            service_name="Xero",
            endpoint=operation
        )
    
    # Fallback to generic Xero error
    return XeroIntegrationError(
        f"Unexpected error during {operation}: {str(error)}",
        operation=operation
    )