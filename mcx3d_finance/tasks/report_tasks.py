"""
Async report generation tasks using Celery.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from celery import shared_task
from sqlalchemy.orm import Session

from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.reporting.generator import ReportGenerator
from mcx3d_finance.core.financials import (
    income_statement as income_statement_calc,
    balance_sheet as balance_sheet_calc,
    cash_flow as cash_flow_calc,
)
from mcx3d_finance.db.models import Transaction, Account
from mcx3d_finance.utils.graceful_degradation import ReportComplexity

logger = logging.getLogger(__name__)

@shared_task(bind=True, name="generate_financial_package")
def generate_financial_package_task(
    self,
    task_id: str,
    report_date: Optional[str] = None,
    include_projections: bool = True,
    formats: List[str] = None,
    email_on_completion: bool = False
):
    """
    Background task to generate comprehensive financial documentation package.
    
    Args:
        task_id: Unique task identifier
        report_date: Report date in ISO format (optional)
        include_projections: Whether to include valuations
        formats: List of output formats (pdf, excel)
        email_on_completion: Whether to send email notification
    
    Returns:
        Dictionary with package information and download paths
    """
    from datetime import date
    from ..integrations.xero_client import XeroClient
    from ..reporting.documentation_builder import FinancialDocumentationBuilder
    from ..core.cache import cache_service
    import asyncio
    
    formats = formats or ["pdf", "excel"]
    
    try:
        # Update task state
        self.update_state(
            state="STARTED",
            meta={
                "progress": 0,
                "message": "Initializing financial documentation generation..."
            }
        )
        
        # Parse report date
        if report_date:
            report_date_obj = date.fromisoformat(report_date)
        else:
            report_date_obj = date.today()
            
        # Initialize clients
        logger.info(f"Starting financial package generation for task {task_id}")
        
        # Get organization ID from task context (would be passed in real implementation)
        organization_id = "default"  # This would come from user context
        
        xero_client = XeroClient(organization_id=organization_id)
        doc_builder = FinancialDocumentationBuilder(xero_client)
        
        # Update progress
        self.update_state(
            state="STARTED",
            meta={
                "progress": 10,
                "message": "Fetching data from Xero..."
            }
        )
        
        # Output directory
        output_dir = f"/tmp/financial_docs/{task_id}"
        
        # Run async generation
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            result = loop.run_until_complete(
                doc_builder.generate_complete_financial_package(
                    output_directory=output_dir,
                    report_date=report_date_obj,
                    include_projections=include_projections,
                    formats=formats
                )
            )
        finally:
            loop.close()
            
        # Update final state
        self.update_state(
            state="SUCCESS",
            meta={
                "progress": 100,
                "message": "Financial documentation generated successfully"
            }
        )
        
        # Send email notification if requested
        if email_on_completion:
            # This would send an email with download link
            logger.info(f"Sending completion email for task {task_id}")
            
        # Cache the result
        asyncio.run(cache_service.set(
            f"doc_result:{task_id}",
            result,
            ttl=86400  # 24 hours
        ))
        
        logger.info(f"Financial package generation completed for task {task_id}")
        
        return result
        
    except Exception as e:
        logger.error(f"Error in financial package generation task {task_id}: {str(e)}")
        
        # Update state with error
        self.update_state(
            state="FAILURE",
            meta={
                "progress": 0,
                "message": f"Generation failed: {str(e)}"
            }
        )
        
        raise


def get_transactions_for_report(db: Session, organization_id: int, start_date: str, end_date: str):
    """Retrieve transactions for report generation."""
    return (
        db.query(Transaction)
        .join(Account)
        .filter(
            Transaction.organization_id == organization_id,
            Transaction.date.between(start_date, end_date),
        )
        .all()
    )


@shared_task(bind=True)
def generate_income_statement_async(
    self,
    organization_id: int,
    period_start: str,
    period_end: str,
    output_format: str = "pdf",
    output_path: Optional[str] = None,
    complexity: Optional[str] = None
) -> Dict[str, Any]:
    """Generate income statement asynchronously."""
    try:
        # Update task state
        self.update_state(
            state="PROGRESS",
            meta={
                "progress": 10,
                "status": "Connecting to database...",
                "report_type": "income_statement",
                "organization_id": organization_id
            }
        )
        
        # Create database session
        db = SessionLocal()
        
        try:
            # Update progress
            self.update_state(
                state="PROGRESS",
                meta={
                    "progress": 20,
                    "status": "Retrieving transaction data...",
                    "report_type": "income_statement",
                    "organization_id": organization_id
                }
            )
            
            # Get transactions
            transactions = get_transactions_for_report(db, organization_id, period_start, period_end)
            
            # Update progress
            self.update_state(
                state="PROGRESS",
                meta={
                    "progress": 40,
                    "status": f"Processing {len(transactions)} transactions...",
                    "report_type": "income_statement",
                    "organization_id": organization_id
                }
            )
            
            # Process transaction data
            transaction_data = [
                {
                    "account_type": transaction.account.type,
                    "amount": float(transaction.amount),
                    "description": transaction.description or "No description",
                    "date": transaction.date.isoformat()
                }
                for transaction in transactions
            ]
            
            # Calculate income statement
            report_data = income_statement_calc.calculate_income_statement(transaction_data)
            
            # Add metadata
            report_data.update({
                "header": {
                    "organization_id": organization_id,
                    "company_name": f"Organization {organization_id}",
                    "statement_title": "INCOME STATEMENT",
                    "reporting_date": period_end,
                    "period_start": period_start,
                    "period_end": period_end,
                    "generated_at": datetime.now().isoformat(),
                    "currency": "USD"
                }
            })
            
            # Update progress
            self.update_state(
                state="PROGRESS",
                meta={
                    "progress": 60,
                    "status": f"Generating {output_format.upper()} report...",
                    "report_type": "income_statement",
                    "organization_id": organization_id
                }
            )
            
            # Generate report
            generator = ReportGenerator(
                enable_validation=True,
                enable_resource_monitoring=True,
                enable_degradation=True
            )
            
            # Convert complexity string to enum if provided
            complexity_level = None
            if complexity:
                complexity_level = ReportComplexity(complexity)
            
            # Generate output path if not provided
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                period_str = f"{period_start}_{period_end}".replace("-", "")
                output_path = f"income_statement_org{organization_id}_{period_str}_{timestamp}.{output_format}"
            
            result = generator.generate_income_statement(
                income_statement_data=report_data,
                output_path=output_path,
                output_format=output_format,
                complexity=complexity_level
            )
            
            # Update final progress
            self.update_state(
                state="PROGRESS",
                meta={
                    "progress": 100,
                    "status": "Report generation completed!",
                    "report_type": "income_statement",
                    "organization_id": organization_id
                }
            )
            
            return {
                "success": True,
                "output_path": result.get("output_path", output_path),
                "format": output_format,
                "organization_id": organization_id,
                "period": f"{period_start} to {period_end}",
                "transactions_processed": len(transactions),
                "generation_time": result.get("generation_time", 0),
                "file_size_mb": result.get("file_size_mb", 0)
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error generating income statement: {e}")
        return {
            "success": False,
            "error": str(e),
            "organization_id": organization_id
        }


@shared_task(bind=True)
def generate_balance_sheet_async(
    self,
    organization_id: int,
    as_of_date: str,
    output_format: str = "pdf",
    output_path: Optional[str] = None,
    complexity: Optional[str] = None
) -> Dict[str, Any]:
    """Generate balance sheet asynchronously."""
    try:
        # Update task state
        self.update_state(
            state="PROGRESS",
            meta={
                "progress": 10,
                "status": "Connecting to database...",
                "report_type": "balance_sheet",
                "organization_id": organization_id
            }
        )
        
        # Create database session
        db = SessionLocal()
        
        try:
            # Parse date for querying
            from mcx3d_finance.cli.reports import parse_date
            start_date, end_date = parse_date(as_of_date)
            
            # Update progress
            self.update_state(
                state="PROGRESS",
                meta={
                    "progress": 20,
                    "status": "Retrieving balance sheet data...",
                    "report_type": "balance_sheet",
                    "organization_id": organization_id
                }
            )
            
            # Get transactions
            transactions = get_transactions_for_report(db, organization_id, start_date, end_date)
            
            # Update progress
            self.update_state(
                state="PROGRESS",
                meta={
                    "progress": 40,
                    "status": f"Processing {len(transactions)} transactions...",
                    "report_type": "balance_sheet",
                    "organization_id": organization_id
                }
            )
            
            # Process transaction data
            transaction_data = [
                {
                    "account_type": transaction.account.type,
                    "amount": float(transaction.amount),
                    "account_name": transaction.account.name or f"Account {transaction.account_id}",
                    "date": transaction.date.isoformat()
                }
                for transaction in transactions
            ]
            
            # Calculate balance sheet
            report_data = balance_sheet_calc.calculate_balance_sheet(transaction_data, as_of_date=as_of_date)
            
            # Add metadata
            report_data.update({
                "header": {
                    "organization_id": organization_id,
                    "company_name": f"Organization {organization_id}",
                    "statement_title": "BALANCE SHEET",
                    "reporting_date": as_of_date,
                    "generated_at": datetime.now().isoformat(),
                    "currency": "USD"
                }
            })
            
            # Update progress
            self.update_state(
                state="PROGRESS",
                meta={
                    "progress": 60,
                    "status": f"Generating {output_format.upper()} report...",
                    "report_type": "balance_sheet",
                    "organization_id": organization_id
                }
            )
            
            # Generate report
            generator = ReportGenerator(
                enable_validation=True,
                enable_resource_monitoring=True,
                enable_degradation=True
            )
            
            # Convert complexity string to enum if provided
            complexity_level = None
            if complexity:
                complexity_level = ReportComplexity(complexity)
            
            # Generate output path if not provided
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                date_str = as_of_date.replace("-", "")
                output_path = f"balance_sheet_org{organization_id}_{date_str}_{timestamp}.{output_format}"
            
            result = generator.generate_balance_sheet(
                balance_sheet_data=report_data,
                output_path=output_path,
                output_format=output_format,
                complexity=complexity_level
            )
            
            # Update final progress
            self.update_state(
                state="PROGRESS",
                meta={
                    "progress": 100,
                    "status": "Report generation completed!",
                    "report_type": "balance_sheet",
                    "organization_id": organization_id
                }
            )
            
            return {
                "success": True,
                "output_path": result.get("output_path", output_path),
                "format": output_format,
                "organization_id": organization_id,
                "as_of_date": as_of_date,
                "transactions_processed": len(transactions),
                "generation_time": result.get("generation_time", 0),
                "file_size_mb": result.get("file_size_mb", 0)
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error generating balance sheet: {e}")
        return {
            "success": False,
            "error": str(e),
            "organization_id": organization_id
        }


@shared_task(bind=True)
def generate_cash_flow_async(
    self,
    organization_id: int,
    period_start: str,
    period_end: str,
    output_format: str = "pdf",
    output_path: Optional[str] = None,
    complexity: Optional[str] = None
) -> Dict[str, Any]:
    """Generate cash flow statement asynchronously."""
    try:
        # Update task state
        self.update_state(
            state="PROGRESS",
            meta={
                "progress": 10,
                "status": "Connecting to database...",
                "report_type": "cash_flow",
                "organization_id": organization_id
            }
        )
        
        # Create database session
        db = SessionLocal()
        
        try:
            # Update progress
            self.update_state(
                state="PROGRESS",
                meta={
                    "progress": 20,
                    "status": "Retrieving cash flow data...",
                    "report_type": "cash_flow",
                    "organization_id": organization_id
                }
            )
            
            # Get transactions
            transactions = get_transactions_for_report(db, organization_id, period_start, period_end)
            
            # Update progress
            self.update_state(
                state="PROGRESS",
                meta={
                    "progress": 40,
                    "status": f"Processing {len(transactions)} transactions...",
                    "report_type": "cash_flow",
                    "organization_id": organization_id
                }
            )
            
            # Process transaction data
            transaction_data = [
                {
                    "account_type": transaction.account.type,
                    "amount": float(transaction.amount),
                    "description": transaction.description or "No description",
                    "date": transaction.date.isoformat()
                }
                for transaction in transactions
            ]
            
            # Calculate cash flow
            report_data = cash_flow_calc.calculate_cash_flow(transaction_data)
            
            # Add metadata
            report_data.update({
                "header": {
                    "organization_id": organization_id,
                    "company_name": f"Organization {organization_id}",
                    "statement_title": "CASH FLOW STATEMENT",
                    "reporting_date": period_end,
                    "period_start": period_start,
                    "period_end": period_end,
                    "generated_at": datetime.now().isoformat(),
                    "currency": "USD"
                }
            })
            
            # Update progress
            self.update_state(
                state="PROGRESS",
                meta={
                    "progress": 60,
                    "status": f"Generating {output_format.upper()} report...",
                    "report_type": "cash_flow",
                    "organization_id": organization_id
                }
            )
            
            # Generate report
            generator = ReportGenerator(
                enable_validation=True,
                enable_resource_monitoring=True,
                enable_degradation=True
            )
            
            # Convert complexity string to enum if provided
            complexity_level = None
            if complexity:
                complexity_level = ReportComplexity(complexity)
            
            # Generate output path if not provided
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                period_str = f"{period_start}_{period_end}".replace("-", "")
                output_path = f"cash_flow_org{organization_id}_{period_str}_{timestamp}.{output_format}"
            
            result = generator.generate_cash_flow(
                cash_flow_data=report_data,
                output_path=output_path,
                output_format=output_format,
                complexity=complexity_level
            )
            
            # Update final progress
            self.update_state(
                state="PROGRESS",
                meta={
                    "progress": 100,
                    "status": "Report generation completed!",
                    "report_type": "cash_flow",
                    "organization_id": organization_id
                }
            )
            
            return {
                "success": True,
                "output_path": result.get("output_path", output_path),
                "format": output_format,
                "organization_id": organization_id,
                "period": f"{period_start} to {period_end}",
                "transactions_processed": len(transactions),
                "generation_time": result.get("generation_time", 0),
                "file_size_mb": result.get("file_size_mb", 0)
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error generating cash flow statement: {e}")
        return {
            "success": False,
            "error": str(e),
            "organization_id": organization_id
        }