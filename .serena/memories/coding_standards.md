# MCX3D Finance - Coding Standards & Conventions

## Code Style
- **Formatter**: Black (line length: 88 characters)
- **Import Organization**: isort with Black profile
- **Naming**: snake_case for variables/functions, PascalCase for classes, UPPER_SNAKE_CASE for constants
- **Quotes**: Double quotes for strings, single quotes for keys when appropriate

## Type Hints & Documentation
- **Required**: All function parameters and return types must have type hints
- **Docstrings**: Google-style docstrings for all public functions, classes, and modules
- **Type Imports**: Use `typing` module for Python < 3.9 compatibility

## Quality Tools (Required)
1. **Black**: Code formatting
2. **isort**: Import sorting  
3. **flake8**: Style and syntax checking
4. **mypy**: Static type checking
5. **pylint**: Advanced code analysis
6. **bandit**: Security vulnerability scanning

## Security Requirements
- Never hardcode secrets or API keys
- Use environment variables for configuration
- Validate all inputs and sanitize outputs
- Follow secure coding practices for auth/authorization

## Exception Handling
- Use specific exception types instead of generic `Exception`
- Create custom exceptions for different error categories:
  - `AuthenticationError`: Auth/authorization failures
  - `ValidationError`: Input validation errors
  - `IntegrationError`: External service failures
  - `BusinessLogicError`: Business rule violations