# MCX3D Finance Codebase Structure - Updated

## Root Directory Structure
```
mcx3d_finance/
├── api/              # REST API endpoints and middleware
├── auth/             # Authentication and OAuth handling  
├── core/             # Business logic & calculations
├── db/               # Database models & sessions
├── integrations/     # External API clients (Xero, MCP)
├── monitoring/       # Observability, metrics, and health checks
├── reporting/        # Multi-format report generation
├── tasks/            # Background processing (Celery)
├── utils/            # Utility functions and helpers
├── validation/       # Data validation modules
├── cli/              # Command-line interface
├── exceptions/       # Custom exception classes
├── config/           # Configuration files
├── main.py           # FastAPI application entry point
└── main_with_monitoring.py  # Production app with monitoring
```

## Supporting Directories
```
docs/                 # Comprehensive documentation
├── developer/        # Developer guides and references
├── user-guide/       # End-user documentation
├── technical-reports/# Technical analysis and reports
├── project-management/# Project planning and structure
├── operations/       # Deployment and operations guides
├── getting-started/  # Quick start guides
└── tutorials/        # Step-by-step tutorials

scripts/              # Automation and utility scripts
├── development/      # Development helper scripts
├── testing/          # Test automation and data management
├── deployment/       # Production deployment utilities
└── monitoring/       # Monitoring system validation

tests/                # Comprehensive test suite
├── core/            # Core business logic tests
├── security/        # Security-focused tests
├── auth/            # Authentication tests
├── integration/     # Integration tests
├── cli/             # CLI tests
├── production/      # Production validation tests
├── performance/     # Performance benchmarks
├── e2e/             # End-to-end tests
└── fixtures/        # Test data and fixtures
```

## API Structure (mcx3d_finance/api/)
- **auth.py**: Authentication endpoints (login, MFA, OAuth)
- **auth_middleware.py**: JWT authentication and security middleware
- **dashboard.py**: Dashboard analytics and KPI endpoints
- **error_middleware.py**: Global error handling middleware
- **health.py**: Health check endpoints (basic, comprehensive, business)
- **metrics.py**: SaaS KPI endpoints and monitoring metrics
- **reports.py**: Financial report generation endpoints
- **schemas.py**: Pydantic models for request/response validation
- **webhook_routes.py**: Xero webhook handling
- **xero_import_routes.py**: Xero data import and sync endpoints
- **middleware/**: Specialized middleware components

## Core Business Logic (mcx3d_finance/core/)
- Financial calculations and valuation models
- Configuration management
- Business rule implementations

## Database Layer (mcx3d_finance/db/)
- SQLAlchemy ORM models
- Database sessions and connections
- Migration scripts (Alembic)

## Integrations (mcx3d_finance/integrations/)
- **xero_client.py**: Core Xero API integration with retry logic
- **mcp_xero_client.py**: MCP-enhanced Xero client
- **mcp_bridge.py**: MCP integration bridge
- **mcp_data_validator.py**: MCP data validation utilities
- **xero_data_import.py**: Xero data import workflows
- **xero_data_storage.py**: Xero data persistence layer
- **xero_streaming.py**: Real-time Xero data streaming
- **xero_sync.py**: Xero synchronization processes
- **xero_rate_limiter.py**: Xero API rate limiting
- **xero_retry.py**: Xero-specific error handling and retry decorators

## Authentication (mcx3d_finance/auth/)
- **xero_oauth.py**: OAuth 2.0 flow for Xero integration
- JWT token management
- Session handling with Redis fallback

## Monitoring & Observability (mcx3d_finance/monitoring/)
- **alerting.py**: Alert management and anomaly detection
- **audit_trails.py**: Audit logging and compliance tracking
- **business_intelligence.py**: KPI collection and trend analysis
- **config_loader.py**: Monitoring configuration management
- **health_checker.py**: Comprehensive health checking system
- **metrics.py**: Prometheus metrics collection and exposure
- **performance_monitor.py**: Performance metrics and benchmarking
- **structured_logger.py**: Structured logging with correlation IDs

## Configuration Management (mcx3d_finance/config/)
- **monitoring.yml**: Environment-specific monitoring thresholds and settings
  - Health check thresholds for database, Redis, system resources
  - Alert configuration for email, Slack, PagerDuty
  - Monitoring intervals and rate limiting

## Utilities (mcx3d_finance/utils/)
- **audit_logger.py**: Security audit logging with tamper detection
- **data_protection.py**: Field-level encryption and GDPR compliance
- **distributed_lock.py**: Redis-based distributed locking
- **encryption.py**: Cryptographic utilities and key management
- **generate_keys.py**: Security key generation utilities
- **graceful_degradation.py**: Service degradation management
- **input_validator.py**: Comprehensive input validation
- **logging.py**: Centralized logging configuration
- **rate_limiter.py**: Advanced rate limiting (sliding window, token bucket)
- **redis_client.py**: Redis connection and operation utilities
- **report_logging.py**: Report generation logging
- **resource_monitor.py**: System resource monitoring
- **retry.py**: Robust retry mechanisms
- **robust_file_ops.py**: Fault-tolerant file operations
- **security_validator.py**: Security validation utilities
- **session_manager.py**: Session management with refresh token rotation

## Quality and Testing Setup
- **pytest.ini**: Test configuration with coverage requirements
- **requirements.txt**: Python dependencies
- **pyproject.toml**: Project metadata and tool configuration
- **.flake8**: Linting rules and style enforcement
- **.pylintrc**: Pylint configuration with custom rules
- **mypy.ini**: Static type checking settings
- **.pre-commit-config.yaml**: Git hooks for automated quality checks
- **CODING_STANDARDS.md**: Comprehensive coding guidelines and best practices