"""
UK GAAP/FRS 102 account classifications and financial statement categorization.
"""

from enum import Enum
from typing import Dict, Optional
import logging

logger = logging.getLogger(__name__)


class FRS102AccountClassification(Enum):
    """FRS 102-compliant account classifications for UK financial reporting."""

    # Balance Sheet
    FIXED_ASSETS = "fixed_assets"
    TANGIBLE_ASSETS = "tangible_assets"
    INTANGIBLE_ASSETS = "intangible_assets"
    INVESTMENTS = "investments"

    CURRENT_ASSETS = "current_assets"
    STOCKS = "stocks"
    DEBTORS = "debtors"
    CASH_AT_BANK_AND_IN_HAND = "cash_at_bank_and_in_hand"

    CREDITORS_DUE_WITHIN_ONE_YEAR = "creditors_due_within_one_year"
    CREDITORS_DUE_AFTER_ONE_YEAR = "creditors_due_after_one_year"
    PROVISIONS_FOR_LIABILITIES = "provisions_for_liabilities"

    CAPITAL_AND_RESERVES = "capital_and_reserves"
    CALLED_UP_SHARE_CAPITAL = "called_up_share_capital"
    PROFIT_AND_LOSS_ACCOUNT = "profit_and_loss_account"

    # Profit & Loss Account
    TURNOVER = "turnover"
    COST_OF_SALES = "cost_of_sales"
    GROSS_PROFIT = "gross_profit"

    DISTRIBUTION_COSTS = "distribution_costs"
    ADMINISTRATIVE_EXPENSES = "administrative_expenses"
    OPERATING_PROFIT = "operating_profit"

    INTEREST_RECEIVABLE = "interest_receivable"
    INTEREST_PAYABLE = "interest_payable"
    PROFIT_BEFORE_TAX = "profit_before_tax"

    TAXATION = "taxation"
    PROFIT_AFTER_TAX = "profit_after_tax"


class FinancialStatementCategory(Enum):
    """Financial statement categories for reporting."""
    BALANCE_SHEET = "balance_sheet"
    PROFIT_AND_LOSS_ACCOUNT = "profit_and_loss_account"
    CASH_FLOW_STATEMENT = "cash_flow_statement"


class AccountTypeMapping:
    """Maps Xero account types to FRS 102 classifications."""

    XERO_TO_FRS102_MAPPING = {
        # Assets
        "BANK": FRS102AccountClassification.CASH_AT_BANK_AND_IN_HAND,
        "CURRENT": FRS102AccountClassification.DEBTORS,
        "FIXED": FRS102AccountClassification.TANGIBLE_ASSETS,
        "INVENTORY": FRS102AccountClassification.STOCKS,
        "NONCURRENT": FRS102AccountClassification.TANGIBLE_ASSETS,
        "PREPAYMENT": FRS102AccountClassification.DEBTORS,

        # Liabilities
        "CURRLIAB": FRS102AccountClassification.CREDITORS_DUE_WITHIN_ONE_YEAR,
        "LIABILITY": FRS102AccountClassification.CREDITORS_DUE_AFTER_ONE_YEAR,
        "TERMLIAB": FRS102AccountClassification.CREDITORS_DUE_AFTER_ONE_YEAR,

        # Equity
        "EQUITY": FRS102AccountClassification.PROFIT_AND_LOSS_ACCOUNT,

        # Revenue
        "REVENUE": FRS102AccountClassification.TURNOVER,
        "SALES": FRS102AccountClassification.TURNOVER,
        "OTHERINCOME": FRS102AccountClassification.TURNOVER,

        # Expenses
        "DIRECTCOSTS": FRS102AccountClassification.COST_OF_SALES,
        "EXPENSE": FRS102AccountClassification.ADMINISTRATIVE_EXPENSES,
        "OVERHEADS": FRS102AccountClassification.ADMINISTRATIVE_EXPENSES,
        "DEPRECIATN": FRS102AccountClassification.ADMINISTRATIVE_EXPENSES,
    }

    @classmethod
    def get_frs102_classification(cls, xero_account_type: str) -> Optional[FRS102AccountClassification]:
        """Get FRS 102 classification for a Xero account type."""
        return cls.XERO_TO_FRS102_MAPPING.get(xero_account_type.upper())

    @classmethod
    def get_financial_statement_category(cls, frs102_classification: FRS102AccountClassification) -> FinancialStatementCategory:
        """Get financial statement category for an FRS 102 classification."""
        balance_sheet_classifications = {
            FRS102AccountClassification.FIXED_ASSETS,
            FRS102AccountClassification.TANGIBLE_ASSETS,
            FRS102AccountClassification.INTANGIBLE_ASSETS,
            FRS102AccountClassification.INVESTMENTS,
            FRS102AccountClassification.CURRENT_ASSETS,
            FRS102AccountClassification.STOCKS,
            FRS102AccountClassification.DEBTORS,
            FRS102AccountClassification.CASH_AT_BANK_AND_IN_HAND,
            FRS102AccountClassification.CREDITORS_DUE_WITHIN_ONE_YEAR,
            FRS102AccountClassification.CREDITORS_DUE_AFTER_ONE_YEAR,
            FRS102AccountClassification.PROVISIONS_FOR_LIABILITIES,
            FRS102AccountClassification.CAPITAL_AND_RESERVES,
            FRS102AccountClassification.CALLED_UP_SHARE_CAPITAL,
            FRS102AccountClassification.PROFIT_AND_LOSS_ACCOUNT,
        }

        profit_and_loss_classifications = {
            FRS102AccountClassification.TURNOVER,
            FRS102AccountClassification.COST_OF_SALES,
            FRS102AccountClassification.GROSS_PROFIT,
            FRS102AccountClassification.DISTRIBUTION_COSTS,
            FRS102AccountClassification.ADMINISTRATIVE_EXPENSES,
            FRS102AccountClassification.OPERATING_PROFIT,
            FRS102AccountClassification.INTEREST_RECEIVABLE,
            FRS102AccountClassification.INTEREST_PAYABLE,
            FRS102AccountClassification.PROFIT_BEFORE_TAX,
            FRS102AccountClassification.TAXATION,
            FRS102AccountClassification.PROFIT_AFTER_TAX,
        }

        if frs102_classification in balance_sheet_classifications:
            return FinancialStatementCategory.BALANCE_SHEET
        elif frs102_classification in profit_and_loss_classifications:
            return FinancialStatementCategory.PROFIT_AND_LOSS_ACCOUNT
        else:
            # Default, can be refined
            return FinancialStatementCategory.BALANCE_SHEET
