#!/usr/bin/env python3
"""
Performance baseline establishment script for MCX3D Financial System.

This script runs a series of performance tests to establish baseline metrics
for report generation functionality.
"""

import sys
import os
import time
import json
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mcx3d_finance.reporting.generator import ReportGenerator


class PerformanceBaseline:
    """Performance baseline establishment and measurement."""
    
    def __init__(self):
        self.report_generator = ReportGenerator()
        self.results = {}
        self.temp_dir = None
    
    def setup(self):
        """Setup temporary directory for test outputs."""
        self.temp_dir = tempfile.mkdtemp(prefix="mcx3d_baseline_")
        print(f"🔧 Created temporary directory: {self.temp_dir}")
    
    def cleanup(self):
        """Clean up temporary files."""
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
            print(f"🧹 Cleaned up temporary directory")
    
    def get_sample_dcf_data(self, complexity: str = "simple") -> Dict[str, Any]:
        """Generate sample DCF data based on complexity level."""
        
        if complexity == "simple":
            return {
                "organization_id": 1,
                "company_name": "Baseline Test Corp",
                "valuation_date": "2024-01-01",
                "enterprise_value": 5000000,
                "financial_projections": [
                    {
                        "year": 1,
                        "revenue": 1000000,
                        "free_cash_flow": 200000,
                        "ebitda": 300000,
                        "revenue_growth_rate": 0.15
                    },
                    {
                        "year": 2,
                        "revenue": 1150000,
                        "free_cash_flow": 250000,
                        "ebitda": 350000,
                        "revenue_growth_rate": 0.15
                    }
                ],
                "sensitivity_analysis": {
                    "discount_rate": {
                        "range": [-0.01, 0, 0.01],
                        "impact": [-8, 0, 8]
                    }
                }
            }
        
        elif complexity == "medium":
            projections = []
            for year in range(1, 6):  # 5 years
                projections.append({
                    "year": year,
                    "revenue": 1000000 * (1.2 ** (year - 1)),
                    "free_cash_flow": 200000 * (1.25 ** (year - 1)),
                    "ebitda": 300000 * (1.22 ** (year - 1)),
                    "revenue_growth_rate": max(0.05, 0.20 - (year * 0.03))
                })
            
            return {
                "organization_id": 1,
                "company_name": "Medium Complexity Corp",
                "valuation_date": "2024-01-01",
                "enterprise_value": 15000000,
                "financial_projections": projections,
                "sensitivity_analysis": {
                    "discount_rate": {
                        "range": [-0.02, -0.01, 0, 0.01, 0.02],
                        "impact": [-15, -8, 0, 8, 15]
                    },
                    "terminal_growth_rate": {
                        "range": [-0.01, -0.005, 0, 0.005, 0.01],
                        "impact": [-10, -5, 0, 5, 10]
                    }
                },
                "monte_carlo_simulation": {
                    "num_simulations": 1000,
                    "mean_valuation": 15000000,
                    "std_deviation": 3000000,
                    "percentile_10": 10000000,
                    "percentile_90": 20000000
                }
            }
        
        elif complexity == "complex":
            projections = []
            for year in range(1, 11):  # 10 years
                projections.append({
                    "year": year,
                    "revenue": 2000000 * (1.25 ** (year - 1)) if year <= 5 else 2000000 * (1.25 ** 4) * (1.08 ** (year - 5)),
                    "free_cash_flow": 400000 * (1.30 ** (year - 1)) if year <= 5 else 400000 * (1.30 ** 4) * (1.10 ** (year - 5)),
                    "ebitda": 600000 * (1.28 ** (year - 1)) if year <= 5 else 600000 * (1.28 ** 4) * (1.09 ** (year - 5)),
                    "revenue_growth_rate": max(0.05, 0.25 - (year * 0.02))
                })
            
            return {
                "organization_id": 1,
                "company_name": "Complex Enterprise Corp",
                "valuation_date": "2024-01-01",
                "enterprise_value": 50000000,
                "financial_projections": projections,
                "sensitivity_analysis": {
                    "discount_rate": {
                        "range": [-0.03, -0.02, -0.01, 0, 0.01, 0.02, 0.03],
                        "impact": [-25, -15, -8, 0, 8, 15, 25]
                    },
                    "terminal_growth_rate": {
                        "range": [-0.015, -0.01, -0.005, 0, 0.005, 0.01, 0.015],
                        "impact": [-18, -12, -6, 0, 6, 12, 18]
                    },
                    "revenue_growth": {
                        "range": [-0.05, -0.025, 0, 0.025, 0.05],
                        "impact": [-20, -10, 0, 10, 20]
                    }
                },
                "monte_carlo_simulation": {
                    "num_simulations": 10000,
                    "mean_valuation": 50000000,
                    "std_deviation": 12000000,
                    "percentile_5": 25000000,
                    "percentile_10": 30000000,
                    "percentile_25": 40000000,
                    "percentile_75": 60000000,
                    "percentile_90": 70000000,
                    "percentile_95": 75000000
                }
            }
    
    def get_sample_saas_data(self, complexity: str = "simple") -> Dict[str, Any]:
        """Generate sample SaaS data based on complexity level."""
        
        if complexity == "simple":
            return {
                "organization_id": 1,
                "company_name": "Baseline SaaS Inc",
                "valuation_date": "2024-01-01",
                "key_metrics": {
                    "arr": 2000000,
                    "mrr": 166667,
                    "growth_rate": 30,
                    "churn_rate": 4,
                    "ltv_cac_ratio": 4.5,
                    "customer_acquisition_cost": 200,
                    "customer_lifetime_value": 900,
                    "active_customers": 500
                },
                "valuation_methods": {
                    "arr_multiple": {
                        "adjusted_valuation": 18000000,
                        "base_arr": 2000000,
                        "adjusted_multiple": 9.0
                    }
                }
            }
        
        elif complexity == "medium":
            return {
                "organization_id": 1,
                "company_name": "Growth SaaS Corp",
                "valuation_date": "2024-01-01",
                "key_metrics": {
                    "arr": 10000000,
                    "mrr": 833333,
                    "growth_rate": 45,
                    "churn_rate": 2.5,
                    "ltv_cac_ratio": 6.2,
                    "customer_acquisition_cost": 350,
                    "customer_lifetime_value": 2170,
                    "active_customers": 2500,
                    "nrr": 115,
                    "gross_margin": 87
                },
                "valuation_methods": {
                    "arr_multiple": {
                        "adjusted_valuation": *********,
                        "base_arr": 10000000,
                        "adjusted_multiple": 12.0
                    },
                    "revenue_multiple": {
                        "valuation": *********,
                        "annual_revenue": 10000000,
                        "revenue_multiple": 10.0
                    },
                    "saas_dcf": {
                        "enterprise_value": 110000000,
                        "pv_operating_fcf": 75000000,
                        "discount_rate": 0.13
                    }
                },
                "weighted_valuation": {
                    "weighted_average_valuation": 110000000,
                    "valuation_range": {
                        "minimum": 95000000,
                        "maximum": 125000000
                    }
                }
            }
        
        elif complexity == "complex":
            # Complex SaaS with cohort analysis, advanced metrics
            cohort_data = []
            for month in range(1, 25):  # 24 months of cohort data
                cohort_data.append({
                    "month": month,
                    "customers_acquired": 100 + (month * 5),
                    "revenue_per_cohort": 50000 + (month * 2000),
                    "retention_rate": max(0.7, 0.95 - (month * 0.01))
                })
            
            return {
                "organization_id": 1,
                "company_name": "Enterprise SaaS Platform",
                "valuation_date": "2024-01-01",
                "key_metrics": {
                    "arr": 50000000,
                    "mrr": 4166667,
                    "growth_rate": 65,
                    "churn_rate": 1.8,
                    "ltv_cac_ratio": 8.5,
                    "customer_acquisition_cost": 850,
                    "customer_lifetime_value": 7225,
                    "active_customers": 12500,
                    "nrr": 125,
                    "gross_margin": 89,
                    "magic_number": 1.6,
                    "payback_period": 8
                },
                "cohort_analysis": cohort_data,
                "unit_economics": {
                    "monthly_churn_rates": [0.015, 0.018, 0.020, 0.018, 0.016],
                    "expansion_revenue": 1500000,
                    "contraction_revenue": 200000,
                    "upsell_rate": 25,
                    "cross_sell_rate": 15
                },
                "valuation_methods": {
                    "arr_multiple": {
                        "adjusted_valuation": 650000000,
                        "base_arr": 50000000,
                        "adjusted_multiple": 13.0
                    },
                    "revenue_multiple": {
                        "valuation": 600000000,
                        "annual_revenue": 50000000,
                        "revenue_multiple": 12.0
                    },
                    "saas_dcf": {
                        "enterprise_value": 580000000,
                        "pv_operating_fcf": 400000000,
                        "discount_rate": 0.11
                    }
                },
                "weighted_valuation": {
                    "weighted_average_valuation": 610000000,
                    "valuation_range": {
                        "minimum": 550000000,
                        "maximum": 680000000
                    }
                },
                "benchmarking": {
                    "industry_median_arr_multiple": 10.5,
                    "peer_companies": [
                        {"name": "Peer A", "arr_multiple": 11.2},
                        {"name": "Peer B", "arr_multiple": 14.8},
                        {"name": "Peer C", "arr_multiple": 9.5}
                    ]
                }
            }
    
    def measure_pdf_generation(self, data_type: str, complexity: str, iterations: int = 3) -> Dict[str, Any]:
        """Measure PDF generation performance."""
        print(f"📊 Measuring {data_type} PDF generation ({complexity} complexity, {iterations} iterations)")
        
        if data_type == "dcf":
            test_data = self.get_sample_dcf_data(complexity)
        else:  # saas
            test_data = self.get_sample_saas_data(complexity)
        
        times = []
        file_sizes = []
        
        for i in range(iterations):
            output_path = Path(self.temp_dir) / f"{data_type}_{complexity}_{i}.pdf"
            
            start_time = time.time()
            
            try:
                if data_type == "dcf":
                    self.report_generator.generate_dcf_valuation_pdf(
                        dcf_data=test_data,
                        output_path=str(output_path)
                    )
                else:  # saas
                    self.report_generator.generate_saas_valuation_pdf(
                        saas_data=test_data,
                        output_path=str(output_path)
                    )
                
                end_time = time.time()
                generation_time = end_time - start_time
                times.append(generation_time)
                
                if output_path.exists():
                    file_size = output_path.stat().st_size
                    file_sizes.append(file_size)
                else:
                    print(f"⚠️  Warning: File not created for iteration {i}")
                    file_sizes.append(0)
                
                print(f"   Iteration {i+1}: {generation_time:.3f}s, {file_sizes[-1]:,} bytes")
                
            except Exception as e:
                print(f"❌ Error in iteration {i}: {e}")
                times.append(float('inf'))
                file_sizes.append(0)
        
        # Calculate statistics
        valid_times = [t for t in times if t != float('inf')]
        valid_sizes = [s for s in file_sizes if s > 0]
        
        if valid_times:
            avg_time = sum(valid_times) / len(valid_times)
            min_time = min(valid_times)
            max_time = max(valid_times)
        else:
            avg_time = min_time = max_time = 0
        
        if valid_sizes:
            avg_size = sum(valid_sizes) / len(valid_sizes)
            min_size = min(valid_sizes)
            max_size = max(valid_sizes)
        else:
            avg_size = min_size = max_size = 0
        
        return {
            "data_type": data_type,
            "complexity": complexity,
            "iterations": iterations,
            "successful_iterations": len(valid_times),
            "times": {
                "average": avg_time,
                "minimum": min_time,
                "maximum": max_time,
                "all_times": times
            },
            "file_sizes": {
                "average": avg_size,
                "minimum": min_size,
                "maximum": max_size,
                "all_sizes": file_sizes
            }
        }
    
    def run_baseline_tests(self) -> Dict[str, Any]:
        """Run comprehensive baseline performance tests."""
        print("🚀 Starting Performance Baseline Tests")
        print("=" * 50)
        
        baseline_data = {
            "timestamp": datetime.now().isoformat(),
            "system_info": {
                "python_version": sys.version,
                "platform": os.name
            },
            "tests": {}
        }
        
        test_scenarios = [
            ("dcf", "simple"),
            ("dcf", "medium"),
            ("dcf", "complex"),
            ("saas", "simple"),
            ("saas", "medium"),
            ("saas", "complex")
        ]
        
        for data_type, complexity in test_scenarios:
            test_key = f"{data_type}_{complexity}"
            baseline_data["tests"][test_key] = self.measure_pdf_generation(
                data_type, complexity, iterations=3
            )
        
        return baseline_data
    
    def save_baseline(self, baseline_data: Dict[str, Any], output_file: str = "performance_baseline.json"):
        """Save baseline data to file."""
        output_path = Path(output_file)
        
        with open(output_path, 'w') as f:
            json.dump(baseline_data, f, indent=2, default=str)
        
        print(f"💾 Baseline data saved to: {output_path.absolute()}")
    
    def print_summary(self, baseline_data: Dict[str, Any]):
        """Print baseline summary."""
        print("\\n📈 Performance Baseline Summary")
        print("=" * 50)
        
        for test_name, test_data in baseline_data["tests"].items():
            data_type = test_data["data_type"].upper()
            complexity = test_data["complexity"].title()
            avg_time = test_data["times"]["average"]
            avg_size = test_data["file_sizes"]["average"]
            success_rate = test_data["successful_iterations"] / test_data["iterations"] * 100
            
            print(f"{data_type} ({complexity}): {avg_time:.3f}s avg, {avg_size:,.0f} bytes avg, {success_rate:.0f}% success")
        
        print("\\n✅ Baseline establishment completed!")


def main():
    """Main entry point for baseline establishment."""
    baseline = PerformanceBaseline()
    
    try:
        baseline.setup()
        baseline_data = baseline.run_baseline_tests()
        baseline.save_baseline(baseline_data)
        baseline.print_summary(baseline_data)
        
    except KeyboardInterrupt:
        print("\\n⚠️  Baseline testing interrupted by user")
    except Exception as e:
        print(f"❌ Error during baseline testing: {e}")
        import traceback
        traceback.print_exc()
    finally:
        baseline.cleanup()


if __name__ == "__main__":
    main()