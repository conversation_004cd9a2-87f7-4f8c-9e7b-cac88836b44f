"""
Structured Logging System for MCX3D Financial Platform

Provides production-grade structured logging with correlation IDs, business event tracking,
and comprehensive log aggregation capabilities for financial transaction monitoring.
"""

import json
import uuid
import logging
import structlog
from contextlib import contextmanager
from typing import Dict, Any, Optional, Union
from datetime import datetime
from contextvars import ContextVar
import traceback

# Context variable for correlation ID
correlation_id_context: ContextVar[Optional[str]] = ContextVar("correlation_id", default=None)

class StructuredLogger:
    """
    Production-grade structured logging with correlation IDs and business event tracking.
    
    Features:
    - Automatic correlation ID management for request tracing
    - Business-critical event logging with structured metadata
    - Financial transaction audit logging
    - Error tracking with contextual information
    - Performance metrics integration
    """
    
    def __init__(self, service_name: str, component: str = "unknown"):
        self.service_name = service_name
        self.component = component
        
        # Configure structlog
        structlog.configure(
            processors=[
                structlog.contextvars.merge_contextvars,
                structlog.processors.add_log_level,
                structlog.processors.StackInfoRenderer(),
                structlog.dev.set_exc_info,
                structlog.processors.JSONRenderer()
            ],
            wrapper_class=structlog.make_filtering_bound_logger(logging.INFO),
            context_class=dict,
            logger_factory=structlog.WriteLoggerFactory(),
            cache_logger_on_first_use=True,
        )
        
        self.logger = structlog.get_logger(service_name)
    
    @contextmanager
    def correlation_context(self, correlation_id: Optional[str] = None, user_id: Optional[str] = None):
        """
        Create correlation context for request tracing and user activity tracking.
        
        Args:
            correlation_id: Optional correlation ID, generates UUID if not provided
            user_id: Optional user ID for user activity tracking
        """
        correlation_id = correlation_id or str(uuid.uuid4())
        
        # Set context variables
        token = correlation_id_context.set(correlation_id)
        
        try:
            with structlog.contextvars.bound_contextvars(
                correlation_id=correlation_id,
                service=self.service_name,
                component=self.component,
                user_id=user_id
            ):
                yield correlation_id
        finally:
            correlation_id_context.reset(token)
    
    def log_business_event(self, event: str, **kwargs):
        """
        Log business-critical events with structured data.
        
        Args:
            event: Event type (e.g., 'report_generated', 'valuation_completed')
            **kwargs: Additional structured data
        """
        self.logger.info(
            "business_event",
            event_type=event,
            timestamp=datetime.utcnow().isoformat(),
            correlation_id=correlation_id_context.get(),
            **kwargs
        )
    
    def log_financial_transaction(self, transaction_type: str, amount: float, 
                                currency: str = "USD", **metadata):
        """
        Log financial transactions for audit and compliance.
        
        Args:
            transaction_type: Type of financial transaction
            amount: Transaction amount
            currency: Currency code
            **metadata: Additional transaction metadata
        """
        self.logger.info(
            "financial_transaction",
            transaction_type=transaction_type,
            amount=amount,
            currency=currency,
            timestamp=datetime.utcnow().isoformat(),
            correlation_id=correlation_id_context.get(),
            audit_trail=True,
            **metadata
        )
    
    def log_performance_metric(self, operation: str, duration_ms: float, 
                             success: bool = True, **context):
        """
        Log performance metrics for monitoring and optimization.
        
        Args:
            operation: Operation name
            duration_ms: Duration in milliseconds
            success: Whether operation succeeded
            **context: Additional performance context
        """
        self.logger.info(
            "performance_metric",
            operation=operation,
            duration_ms=duration_ms,
            success=success,
            timestamp=datetime.utcnow().isoformat(),
            correlation_id=correlation_id_context.get(),
            **context
        )
    
    def log_user_activity(self, action: str, user_id: str, resource: Optional[str] = None, **metadata):
        """
        Log user activities for engagement tracking and security monitoring.
        
        Args:
            action: User action (e.g., 'login', 'report_download', 'valuation_request')
            user_id: User identifier
            resource: Optional resource identifier
            **metadata: Additional activity metadata
        """
        self.logger.info(
            "user_activity",
            action=action,
            user_id=user_id,
            resource=resource,
            timestamp=datetime.utcnow().isoformat(),
            correlation_id=correlation_id_context.get(),
            **metadata
        )
    
    def log_security_event(self, event_type: str, severity: str = "medium", **details):
        """
        Log security events for compliance and threat monitoring.
        
        Args:
            event_type: Security event type
            severity: Event severity (low, medium, high, critical)
            **details: Security event details
        """
        self.logger.warning(
            "security_event",
            event_type=event_type,
            severity=severity,
            timestamp=datetime.utcnow().isoformat(),
            correlation_id=correlation_id_context.get(),
            requires_attention=severity in ["high", "critical"],
            **details
        )
    
    def log_error(self, error: Exception, operation: str, **context):
        """
        Log errors with full context and stack trace information.
        
        Args:
            error: Exception object
            operation: Operation that failed
            **context: Additional error context
        """
        self.logger.error(
            "error_occurred",
            operation=operation,
            error_type=type(error).__name__,
            error_message=str(error),
            stack_trace=traceback.format_exc(),
            timestamp=datetime.utcnow().isoformat(),
            correlation_id=correlation_id_context.get(),
            **context
        )
    
    def log_system_status(self, component: str, status: str, **metrics):
        """
        Log system component status for health monitoring.
        
        Args:
            component: System component name
            status: Component status (healthy, degraded, error)
            **metrics: Component metrics
        """
        self.logger.info(
            "system_status",
            component=component,
            status=status,
            timestamp=datetime.utcnow().isoformat(),
            correlation_id=correlation_id_context.get(),
            **metrics
        )

# Convenience functions
def get_correlation_id() -> Optional[str]:
    """Get the current correlation ID from context."""
    return correlation_id_context.get()

def set_correlation_id(correlation_id: str):
    """Set correlation ID in context."""
    return correlation_id_context.set(correlation_id)

# Pre-configured loggers for different components
main_logger = StructuredLogger("mcx3d-finance", "main")
api_logger = StructuredLogger("mcx3d-finance", "api")
reporting_logger = StructuredLogger("mcx3d-finance", "reporting")
valuation_logger = StructuredLogger("mcx3d-finance", "valuation")
integration_logger = StructuredLogger("mcx3d-finance", "integration")
auth_logger = StructuredLogger("mcx3d-finance", "auth")

# Business event convenience functions
def log_report_generated(report_type: str, output_format: str, organization_id: str, 
                        duration_ms: float, file_size_bytes: int, success: bool = True):
    """Log report generation event."""
    reporting_logger.log_business_event(
        "report_generated",
        report_type=report_type,
        output_format=output_format,
        organization_id=organization_id,
        duration_ms=duration_ms,
        file_size_bytes=file_size_bytes,
        success=success
    )

def log_valuation_completed(model_type: str, organization_id: str, valuation_amount: float,
                          currency: str, duration_ms: float, data_points: int):
    """Log valuation completion event."""
    valuation_logger.log_business_event(
        "valuation_completed",
        model_type=model_type,
        organization_id=organization_id,
        valuation_amount=valuation_amount,
        currency=currency,
        duration_ms=duration_ms,
        data_points=data_points
    )

def log_user_login(user_id: str, login_method: str, success: bool = True, **metadata):
    """Log user authentication event."""
    auth_logger.log_user_activity(
        "user_login",
        user_id=user_id,
        resource="authentication",
        login_method=login_method,
        success=success,
        **metadata
    )

def log_data_sync(source: str, record_count: int, duration_ms: float, 
                 success: bool = True, **metadata):
    """Log data synchronization event."""
    integration_logger.log_business_event(
        "data_sync_completed",
        source=source,
        record_count=record_count,
        duration_ms=duration_ms,
        success=success,
        **metadata
    )