#!/usr/bin/env python3
"""
MCX3D Financial Platform - Monitoring System Validation Script

This script validates that all monitoring components are working correctly
and that Issue #9 has been fully addressed.
"""

import asyncio
import time
import sys
import os
import logging
from typing import Dict, Any, List
from datetime import datetime, timezone

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mcx3d_finance.monitoring.health_checker import HealthChecker, HealthStatus
from mcx3d_finance.monitoring.metrics import (
    update_celery_metrics, record_celery_queue_metrics, 
    record_external_service_metrics, get_metrics_data
)
from mcx3d_finance.monitoring.alerting import AlertManager, AlertSeverity
from mcx3d_finance.monitoring.config_loader import MonitoringConfigLoader, get_monitoring_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MonitoringSystemValidator:
    """Comprehensive monitoring system validation."""
    
    def __init__(self):
        self.results = {
            'passed': 0,
            'failed': 0,
            'warnings': 0,
            'details': []
        }
        self.start_time = time.time()
    
    def log_result(self, test_name: str, passed: bool, message: str, warning: bool = False):
        """Log test result."""
        if warning:
            self.results['warnings'] += 1
            status = 'WARNING'
            logger.warning(f"{test_name}: {message}")
        elif passed:
            self.results['passed'] += 1
            status = 'PASS'
            logger.info(f"{test_name}: {message}")
        else:
            self.results['failed'] += 1
            status = 'FAIL'
            logger.error(f"{test_name}: {message}")
        
        self.results['details'].append({
            'test': test_name,
            'status': status,
            'message': message,
            'timestamp': datetime.now(timezone.utc).isoformat()
        })
    
    async def test_configuration_loading(self) -> bool:
        """Test that monitoring configuration loads correctly."""
        try:
            config_loader = get_monitoring_config()
            config = config_loader.load_config()
            
            # Test required sections exist
            required_sections = ['health_thresholds', 'alert_thresholds', 'monitoring_intervals']
            for section in required_sections:
                if section not in config:
                    self.log_result(
                        'Configuration Loading',
                        False,
                        f"Missing required section: {section}"
                    )
                    return False
            
            # Test environment detection
            environment = config['_metadata']['environment']
            if environment not in ['development', 'testing', 'staging', 'production']:
                self.log_result(
                    'Configuration Loading',
                    False,
                    f"Invalid environment detected: {environment}",
                    warning=True
                )
            
            # Test configuration validation
            validation = config_loader.validate_config()
            if not validation['valid']:
                self.log_result(
                    'Configuration Validation',
                    False,
                    f"Configuration validation failed: {validation['issues']}"
                )
                return False
            
            if validation['warnings']:
                self.log_result(
                    'Configuration Validation',
                    True,
                    f"Configuration loaded with warnings: {validation['warnings']}",
                    warning=True
                )
            else:
                self.log_result(
                    'Configuration Loading',
                    True,
                    f"Configuration loaded successfully for environment: {environment}"
                )
            
            return True
            
        except Exception as e:
            self.log_result(
                'Configuration Loading',
                False,
                f"Configuration loading failed: {str(e)}"
            )
            return False
    
    async def test_health_checker_initialization(self) -> bool:
        """Test health checker initialization."""
        try:
            # Test with default initialization
            health_checker = HealthChecker()
            
            # Verify thresholds are loaded
            if not health_checker.health_thresholds:
                self.log_result(
                    'Health Checker Initialization',
                    False,
                    "Health thresholds not loaded"
                )
                return False
            
            # Check that thresholds contain expected components
            expected_components = ['database', 'redis', 'system', 'application', 'celery']
            for component in expected_components:
                if component not in health_checker.health_thresholds:
                    self.log_result(
                        'Health Checker Initialization',
                        False,
                        f"Missing health thresholds for component: {component}",
                        warning=True
                    )
            
            self.log_result(
                'Health Checker Initialization',
                True,
                f"Health checker initialized with {len(health_checker.health_thresholds)} component thresholds"
            )
            return True
            
        except Exception as e:
            self.log_result(
                'Health Checker Initialization',
                False,
                f"Health checker initialization failed: {str(e)}"
            )
            return False
    
    async def test_celery_monitoring(self) -> bool:
        """Test Celery monitoring implementation."""
        try:
            health_checker = HealthChecker()
            
            # Test Celery health check
            start_time = time.time()
            result = await health_checker._check_celery_workers()
            duration_ms = (time.time() - start_time) * 1000
            
            # Verify result structure
            required_fields = ['status', 'message', 'response_time_ms', 'metrics', 'timestamp']
            for field in required_fields:
                if field not in result:
                    self.log_result(
                        'Celery Monitoring',
                        False,
                        f"Missing field in Celery health check result: {field}"
                    )
                    return False
            
            # Verify metrics contain real data (not hardcoded zeros)
            metrics = result['metrics']
            required_metrics = ['worker_count', 'total_queue_length', 'failed_tasks']
            for metric in required_metrics:
                if metric not in metrics:
                    self.log_result(
                        'Celery Monitoring',
                        False,
                        f"Missing Celery metric: {metric}"
                    )
                    return False
            
            # Test that metrics are not hardcoded (Issue #9 requirement)
            if ('active_workers' in metrics and 
                isinstance(metrics.get('total_queue_length'), int) and
                isinstance(metrics.get('failed_tasks'), int)):
                self.log_result(
                    'Celery Monitoring',
                    True,
                    f"Celery monitoring implemented with real metrics (response: {duration_ms:.1f}ms)"
                )
            else:
                self.log_result(
                    'Celery Monitoring',
                    False,
                    "Celery monitoring still using mock data instead of real metrics"
                )
                return False
            
            # Test configurable thresholds
            celery_thresholds = health_checker.health_thresholds.get('celery', {})
            if not celery_thresholds:
                self.log_result(
                    'Celery Thresholds',
                    False,
                    "Celery thresholds not configured",
                    warning=True
                )
            else:
                self.log_result(
                    'Celery Thresholds',
                    True,
                    f"Configurable Celery thresholds loaded: {list(celery_thresholds.keys())}"
                )
            
            return True
            
        except Exception as e:
            self.log_result(
                'Celery Monitoring',
                False,
                f"Celery monitoring test failed: {str(e)}"
            )
            return False
    
    async def test_external_services_monitoring(self) -> bool:
        """Test external services monitoring implementation."""
        try:
            health_checker = HealthChecker()
            
            # Test external services health check
            start_time = time.time()
            result = await health_checker._check_external_services()
            duration_ms = (time.time() - start_time) * 1000
            
            # Verify result structure
            if 'services' not in result:
                self.log_result(
                    'External Services Monitoring',
                    False,
                    "External services result missing 'services' field"
                )
                return False
            
            services = result['services']
            expected_services = ['xero_api', 'internet_connectivity', 'dns_resolution']
            
            for service in expected_services:
                if service not in services:
                    self.log_result(
                        'External Services Monitoring',
                        False,
                        f"Missing external service check: {service}"
                    )
                    return False
            
            # Test Xero API health check specifically
            xero_result = services.get('xero_api', {})
            if 'endpoints' in xero_result:
                self.log_result(
                    'External Services Monitoring',
                    True,
                    f"Comprehensive external services monitoring implemented ({len(services)} services, {duration_ms:.1f}ms)"
                )
            else:
                self.log_result(
                    'External Services Monitoring',
                    False,
                    "External services monitoring still using basic implementation"
                )
                return False
            
            return True
            
        except Exception as e:
            self.log_result(
                'External Services Monitoring',
                False,
                f"External services monitoring test failed: {str(e)}"
            )
            return False
    
    async def test_metrics_collection(self) -> bool:
        """Test metrics collection implementation."""
        try:
            # Test Celery metrics update
            update_celery_metrics(
                worker_count=2,
                active_tasks=5,
                queue_length=10,
                failed_tasks=1
            )
            
            # Test queue metrics recording
            queue_metrics = {
                'total_queue_length': 15,
                'failed_tasks': 2,
                'queues': {'celery': 10, 'high_priority': 5}
            }
            record_celery_queue_metrics(queue_metrics)
            
            # Test external service metrics
            record_external_service_metrics(
                service_name='test_service',
                endpoint='test_endpoint',
                is_healthy=True,
                response_time_seconds=0.5
            )
            
            # Test metrics data retrieval
            metrics_data = get_metrics_data()
            if metrics_data:
                self.log_result(
                    'Metrics Collection',
                    True,
                    f"Metrics collection implemented successfully ({len(metrics_data)} bytes)"
                )
            else:
                self.log_result(
                    'Metrics Collection',
                    False,
                    "Metrics collection not producing data"
                )
                return False
            
            return True
            
        except Exception as e:
            self.log_result(
                'Metrics Collection',
                False,
                f"Metrics collection test failed: {str(e)}"
            )
            return False
    
    async def test_alerting_system(self) -> bool:
        """Test alerting system with configuration."""
        try:
            alert_manager = AlertManager()
            
            # Test that alert manager loaded configuration
            if not hasattr(alert_manager, 'alert_config'):
                self.log_result(
                    'Alerting System',
                    False,
                    "Alert manager did not load configuration"
                )
                return False
            
            # Test alert triggering (without actually sending alerts)
            with patch_alert_handlers(alert_manager):
                await alert_manager.trigger_alert(
                    AlertSeverity.WARNING,
                    'Test Alert',
                    'This is a test alert for validation',
                    {'component': 'validation_test'},
                    'test_alert_key'
                )
            
            # Test rate limiting configuration
            if hasattr(alert_manager, 'max_alerts_per_window'):
                self.log_result(
                    'Alerting System',
                    True,
                    f"Alerting system configured (rate limit: {alert_manager.max_alerts_per_window} alerts)"
                )
            else:
                self.log_result(
                    'Alerting System',
                    False,
                    "Alerting system not properly configured"
                )
                return False
            
            return True
            
        except Exception as e:
            self.log_result(
                'Alerting System',
                False,
                f"Alerting system test failed: {str(e)}"
            )
            return False
    
    async def test_performance_requirements(self) -> bool:
        """Test performance requirements."""
        try:
            health_checker = HealthChecker()
            
            # Test individual component response times
            component_tests = [
                ('Database', health_checker._check_database_health),
                ('Redis', health_checker._check_redis_health),
                ('System Resources', health_checker._check_system_resources),
            ]
            
            performance_results = []
            
            for component_name, test_method in component_tests:
                start_time = time.time()
                try:
                    result = await test_method()
                    duration_ms = (time.time() - start_time) * 1000
                    performance_results.append((component_name, duration_ms, True))
                    
                    if duration_ms > 1000:  # 1 second threshold for validation
                        self.log_result(
                            f'{component_name} Performance',
                            False,
                            f"Response time too slow: {duration_ms:.1f}ms",
                            warning=True
                        )
                except Exception as e:
                    duration_ms = (time.time() - start_time) * 1000
                    performance_results.append((component_name, duration_ms, False))
                    self.log_result(
                        f'{component_name} Performance',
                        False,
                        f"Component test failed: {str(e)}"
                    )
            
            # Calculate average performance
            avg_performance = sum(p[1] for p in performance_results) / len(performance_results)
            
            if avg_performance < 500:  # 500ms average threshold
                self.log_result(
                    'Performance Requirements',
                    True,
                    f"Performance requirements met (avg: {avg_performance:.1f}ms)"
                )
            else:
                self.log_result(
                    'Performance Requirements',
                    False,
                    f"Performance requirements not met (avg: {avg_performance:.1f}ms)",
                    warning=True
                )
            
            return True
            
        except Exception as e:
            self.log_result(
                'Performance Requirements',
                False,
                f"Performance testing failed: {str(e)}"
            )
            return False
    
    async def run_comprehensive_validation(self) -> Dict[str, Any]:
        """Run all validation tests."""
        print("🔍 Starting MCX3D Monitoring System Validation")
        print("=" * 60)
        
        # Define test sequence
        tests = [
            ('Configuration System', self.test_configuration_loading),
            ('Health Checker', self.test_health_checker_initialization),
            ('Celery Monitoring', self.test_celery_monitoring),
            ('External Services', self.test_external_services_monitoring),
            ('Metrics Collection', self.test_metrics_collection),
            ('Alerting System', self.test_alerting_system),
            ('Performance', self.test_performance_requirements),
        ]
        
        print(f"Running {len(tests)} validation tests...\n")
        
        # Run all tests
        for test_name, test_method in tests:
            print(f"Testing {test_name}...")
            try:
                await test_method()
            except Exception as e:
                self.log_result(test_name, False, f"Unexpected error: {str(e)}")
            print("")
        
        # Calculate final results
        total_duration = time.time() - self.start_time
        total_tests = self.results['passed'] + self.results['failed']
        success_rate = (self.results['passed'] / total_tests * 100) if total_tests > 0 else 0
        
        # Generate summary
        summary = {
            'total_tests': total_tests,
            'passed': self.results['passed'],
            'failed': self.results['failed'],
            'warnings': self.results['warnings'],
            'success_rate': success_rate,
            'duration_seconds': total_duration,
            'details': self.results['details']
        }
        
        return summary
    
    def print_summary(self, summary: Dict[str, Any]):
        """Print validation summary."""
        print("=" * 60)
        print("📊 VALIDATION SUMMARY")
        print("=" * 60)
        
        print(f"Total Tests: {summary['total_tests']}")
        print(f"✅ Passed: {summary['passed']}")
        print(f"❌ Failed: {summary['failed']}")
        print(f"⚠️  Warnings: {summary['warnings']}")
        print(f"Success Rate: {summary['success_rate']:.1f}%")
        print(f"Duration: {summary['duration_seconds']:.2f} seconds")
        
        print("\n" + "=" * 60)
        
        if summary['failed'] == 0:
            print("🎉 ALL TESTS PASSED!")
            print("✅ Issue #9 has been successfully addressed!")
            print("✅ Monitoring system is production-ready!")
        else:
            print("❌ SOME TESTS FAILED")
            print("⚠️  Issue #9 may not be fully addressed")
            print("\nFailed tests:")
            for detail in summary['details']:
                if detail['status'] == 'FAIL':
                    print(f"  - {detail['test']}: {detail['message']}")
        
        if summary['warnings'] > 0:
            print(f"\n⚠️  {summary['warnings']} warnings detected:")
            for detail in summary['details']:
                if detail['status'] == 'WARNING':
                    print(f"  - {detail['test']}: {detail['message']}")


def patch_alert_handlers(alert_manager):
    """Context manager to patch alert handlers for testing."""
    class AlertHandlerPatcher:
        def __enter__(self):
            # Mock alert handlers to prevent actual alerts during testing
            for severity in alert_manager.alert_handlers:
                alert_manager.alert_handlers[severity] = [lambda x: None]
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            pass
    
    return AlertHandlerPatcher()


async def main():
    """Main validation script entry point."""
    validator = MonitoringSystemValidator()
    
    try:
        summary = await validator.run_comprehensive_validation()
        validator.print_summary(summary)
        
        # Exit with appropriate code
        exit_code = 0 if summary['failed'] == 0 else 1
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n🛑 Validation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Validation failed with unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    asyncio.run(main())