"""
FastAPI Monitoring Middleware for MCX3D Financial Platform

Provides comprehensive request/response monitoring, correlation ID management,
performance tracking, and business event logging for all API endpoints.
"""

import time
import uuid
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
import logging

from mcx3d_finance.monitoring.structured_logger import api_logger, set_correlation_id
from mcx3d_finance.monitoring.metrics import (
    HTTP_REQUEST_COUNT,
    HTTP_REQUEST_DURATION,
    record_api_metrics,
    USER_ACTIVITY_COUNT,
    ACTIVE_USERS
)

logger = logging.getLogger(__name__)

class MonitoringMiddleware(BaseHTTPMiddleware):
    """
    Comprehensive monitoring middleware for FastAPI application.
    
    Features:
    - Automatic correlation ID generation and injection
    - Request/response performance tracking
    - Business event logging
    - User activity monitoring
    - Error tracking and alerting
    - Prometheus metrics collection
    """
    
    def __init__(self, app: ASGIApp, service_name: str = "mcx3d-finance"):
        super().__init__(app)
        self.service_name = service_name
        self.active_requests = set()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with comprehensive monitoring."""
        start_time = time.time()
        correlation_id = self._get_or_create_correlation_id(request)
        
        # Set correlation context
        set_correlation_id(correlation_id)
        request.state.correlation_id = correlation_id
        request.state.start_time = start_time
        
        # Track active requests
        request_id = str(uuid.uuid4())
        self.active_requests.add(request_id)
        
        # Extract user information
        user_id = self._extract_user_id(request)
        if user_id:
            request.state.user_id = user_id
        
        try:
            # Log request start
            api_logger.log_business_event(
                'api_request_started',
                method=request.method,
                path=request.url.path,
                user_agent=request.headers.get('user-agent', 'unknown'),
                user_id=user_id,
                correlation_id=correlation_id
            )
            
            # Process request
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Log successful request completion
            api_logger.log_business_event(
                'api_request_completed',
                method=request.method,
                path=request.url.path,
                status_code=response.status_code,
                duration_ms=round(duration * 1000, 2),
                user_id=user_id,
                correlation_id=correlation_id
            )
            
            # Record metrics
            self._record_request_metrics(request, response, duration)
            
            # Track user activity
            if user_id:
                self._track_user_activity(request, user_id, response.status_code)
            
            # Add correlation ID to response headers
            response.headers["X-Correlation-ID"] = correlation_id
            
            return response
            
        except Exception as e:
            # Calculate duration for failed requests
            duration = time.time() - start_time
            
            # Log request error
            api_logger.log_error(
                e, 
                'api_request_failed',
                method=request.method,
                path=request.url.path,
                duration_ms=round(duration * 1000, 2),
                user_id=user_id,
                correlation_id=correlation_id
            )
            
            # Record error metrics
            HTTP_REQUEST_COUNT.labels(
                method=request.method,
                endpoint=self._normalize_endpoint(request.url.path),
                status_code='500'
            ).inc()
            
            # Re-raise the exception
            raise
            
        finally:
            # Clean up active requests tracking
            self.active_requests.discard(request_id)
    
    def _get_or_create_correlation_id(self, request: Request) -> str:
        """Get correlation ID from headers or create new one."""
        # Check for existing correlation ID in headers
        correlation_id = request.headers.get('X-Correlation-ID')
        if not correlation_id:
            correlation_id = request.headers.get('X-Request-ID')
        if not correlation_id:
            correlation_id = str(uuid.uuid4())
        
        return correlation_id
    
    def _extract_user_id(self, request: Request) -> str:
        """Extract user ID from request (authentication, session, etc.)."""
        # Check for user in request state (set by auth middleware)
        if hasattr(request.state, 'user') and request.state.user:
            return str(request.state.user.get('id', 'unknown'))
        
        # Check for user ID in headers
        user_id = request.headers.get('X-User-ID')
        if user_id:
            return user_id
        
        # Check for JWT token and extract user ID
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            try:
                # This would decode JWT token to extract user ID
                # Simplified for now
                return 'authenticated_user'
            except Exception:
                pass
        
        return None
    
    def _record_request_metrics(self, request: Request, response: Response, duration: float):
        """Record Prometheus metrics for the request."""
        endpoint = self._normalize_endpoint(request.url.path)
        
        # Record request count
        HTTP_REQUEST_COUNT.labels(
            method=request.method,
            endpoint=endpoint,
            status_code=str(response.status_code)
        ).inc()
        
        # Record request duration
        HTTP_REQUEST_DURATION.labels(
            method=request.method,
            endpoint=endpoint
        ).observe(duration)
        
        # Use the centralized metrics recording function
        record_api_metrics(
            request.method,
            endpoint,
            response.status_code,
            duration
        )
    
    def _track_user_activity(self, request: Request, user_id: str, status_code: int):
        """Track user activity metrics."""
        # Determine activity type based on endpoint
        activity_type = self._determine_activity_type(request.url.path, request.method)
        resource = self._extract_resource_from_path(request.url.path)
        
        # Record user activity
        USER_ACTIVITY_COUNT.labels(
            user_id=user_id,
            action=activity_type,
            resource=resource
        ).inc()
        
        # Log user activity event
        api_logger.log_user_activity(
            activity_type,
            user_id,
            resource,
            endpoint=request.url.path,
            method=request.method,
            status_code=status_code
        )
    
    def _normalize_endpoint(self, path: str) -> str:
        """Normalize endpoint path for metrics (replace IDs with placeholders)."""
        import re
        
        # Replace UUIDs with placeholder
        path = re.sub(r'/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', '/{uuid}', path)
        
        # Replace numeric IDs with placeholder
        path = re.sub(r'/\d+', '/{id}', path)
        
        # Replace other common patterns
        path = re.sub(r'/[^/]+\.(pdf|xlsx|csv|json)', '/{file}', path)
        
        return path
    
    def _determine_activity_type(self, path: str, method: str) -> str:
        """Determine user activity type based on endpoint and method."""
        if '/auth/' in path:
            return 'authentication'
        elif '/reports/' in path:
            if method == 'POST':
                return 'report_generation'
            elif method == 'GET':
                return 'report_download'
        elif '/valuations/' in path:
            if method == 'POST':
                return 'valuation_request'
            elif method == 'GET':
                return 'valuation_view'
        elif '/sync/' in path:
            return 'data_sync'
        elif '/health/' in path:
            return 'health_check'
        else:
            return f'{method.lower()}_request'
    
    def _extract_resource_from_path(self, path: str) -> str:
        """Extract resource name from API path."""
        path_parts = path.strip('/').split('/')
        
        if len(path_parts) >= 2:
            # Remove 'api' prefix if present
            if path_parts[0] == 'api':
                path_parts = path_parts[1:]
            
            # Return the main resource
            return path_parts[0] if path_parts else 'unknown'
        
        return 'root'


class MetricsMiddleware(BaseHTTPMiddleware):
    """
    Lightweight metrics-only middleware for high-performance metric collection.
    """
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Collect basic metrics with minimal overhead."""
        start_time = time.time()
        
        try:
            response = await call_next(request)
            duration = time.time() - start_time
            
            # Record basic metrics
            endpoint = self._normalize_path(request.url.path)
            
            HTTP_REQUEST_COUNT.labels(
                method=request.method,
                endpoint=endpoint,
                status_code=str(response.status_code)
            ).inc()
            
            HTTP_REQUEST_DURATION.labels(
                method=request.method,
                endpoint=endpoint
            ).observe(duration)
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            endpoint = self._normalize_path(request.url.path)
            
            HTTP_REQUEST_COUNT.labels(
                method=request.method,
                endpoint=endpoint,
                status_code='500'
            ).inc()
            
            raise
    
    def _normalize_path(self, path: str) -> str:
        """Simple path normalization for metrics."""
        import re
        path = re.sub(r'/\d+', '/{id}', path)
        path = re.sub(r'/[0-9a-f-]{36}', '/{uuid}', path)
        return path


# Convenience function to add monitoring to FastAPI app
def add_monitoring_middleware(app, full_monitoring: bool = True):
    """
    Add monitoring middleware to FastAPI application.
    
    Args:
        app: FastAPI application instance
        full_monitoring: If True, adds full monitoring; if False, adds metrics-only
    """
    if full_monitoring:
        app.add_middleware(MonitoringMiddleware)
        logger.info("Full monitoring middleware added to FastAPI application")
    else:
        app.add_middleware(MetricsMiddleware)
        logger.info("Metrics-only middleware added to FastAPI application")
    
    # Add metrics endpoint
    from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
    from fastapi import Response
    
    @app.get("/metrics")
    async def get_metrics():
        """Prometheus metrics endpoint."""
        return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)
    
    logger.info("Metrics endpoint added at /metrics")