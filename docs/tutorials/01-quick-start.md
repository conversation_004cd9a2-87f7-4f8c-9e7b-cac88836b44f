# Tutorial 1: Quick Start Guide
**Get MCX3D Financial System Running in 5 Minutes**

Welcome to MCX3D Financial System! This tutorial will get you up and running with the system in just 5 minutes. By the end, you'll have the system running and will have generated your first financial report.

## 🎯 **What You'll Accomplish**

- ✅ Start all MCX3D services with Docker
- ✅ Verify system health and connectivity
- ✅ Connect to Xero (optional)
- ✅ Generate your first balance sheet report
- ✅ Understand the basic system architecture

**Estimated Time**: 5-10 minutes  
**Prerequisites**: Docker and Docker Compose installed

---

## 🚀 **Step 1: Clone and Start Services**

### 1.1 Clone the Repository
```bash
git clone https://github.com/mcx3d/mcx3d-financials.git
cd mcx3d-financials/v2
```

### 1.2 Start All Services
```bash
# Start all services (this may take 2-3 minutes the first time)
docker-compose up --build -d

# Check that all services are running
docker-compose ps
```

**Expected Output:**
```
NAME                COMMAND                  SERVICE    STATUS      PORTS
v2-db-1            "docker-entrypoint.s…"   db         running     0.0.0.0:5432->5432/tcp
v2-redis-1         "docker-entrypoint.s…"   redis      running     0.0.0.0:6379->6379/tcp
v2-web-1           "uvicorn mcx3d_finan…"   web        running     0.0.0.0:8000->8000/tcp
v2-worker-1        "celery -A mcx3d_fin…"   worker     running
```

**💡 Tip**: If you see any services with "Exited" status, run `docker-compose logs [service-name]` to check for errors.

---

## 🏥 **Step 2: Verify System Health**

### 2.1 Check API Health
```bash
# Basic health check
curl http://localhost:8000/health

# Check if API is responding
curl http://localhost:8000/
```

**Expected Response:**
```json
{
  "message": "Welcome to the MCX3D Financials API"
}
```

### 2.2 Access Interactive Documentation
Open your browser and navigate to:
- **API Documentation**: http://localhost:8000/docs
- **Alternative Docs**: http://localhost:8000/redoc

You should see the FastAPI interactive documentation with all available endpoints.

---

## 🗄️ **Step 3: Initialize the Database**

### 3.1 Apply Database Migrations
```bash
# Apply database migrations to set up tables
docker-compose exec web alembic upgrade head
```

### 3.2 Verify Database Connection
```bash
# Test database connectivity
docker-compose exec web python -c "
from mcx3d_finance.db.session import SessionLocal
db = SessionLocal()
print('✅ Database connection successful')
db.close()
"
```

---

## 📊 **Step 4: Generate Your First Report**

Since we don't have real data yet, we'll generate a report with sample data or create a basic organization entry.

### 4.1 Create Sample Organization (Optional)
```bash
# Create a sample organization for testing
docker-compose exec web python -c "
from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization
from datetime import datetime

db = SessionLocal()
org = Organization(
    name='Sample Company',
    base_currency='USD',
    created_at=datetime.utcnow(),
    updated_at=datetime.utcnow(),
    is_active=True
)
db.add(org)
db.commit()
print(f'✅ Created organization with ID: {org.id}')
db.close()
"
```

### 4.2 Generate Balance Sheet Report
```bash
# Generate a balance sheet in JSON format
curl -X GET "http://localhost:8000/reports/balance-sheet?organization_id=1&date=2024-12-31&format=json"
```

**Expected Response:**
```json
{
  "report_name": "Balance Sheet",
  "organization_id": 1,
  "as_of_date": "2024-12-31",
  "data": {
    "assets": {...},
    "liabilities": {...},
    "equity": {...}
  },
  "generated_at": "2025-07-24T..."
}
```

### 4.3 Generate PDF Report (Advanced)
```bash
# Generate a PDF report (save to file)
curl -X GET "http://localhost:8000/reports/balance-sheet?organization_id=1&date=2024-12-31&format=pdf" \
     --output balance_sheet_sample.pdf

# Verify the file was created
ls -la balance_sheet_sample.pdf
```

---

## 🔗 **Step 5: Connect to Xero (Optional)**

If you have a Xero account and want to connect real data:

### 5.1 Set Up Xero Credentials
```bash
# Copy environment template
cp .env.example .env

# Edit .env file to add your Xero credentials
# XERO_CLIENT_ID=your_client_id_here
# XERO_CLIENT_SECRET=your_client_secret_here
```

### 5.2 Restart Services with New Configuration
```bash
# Restart to load new environment variables
docker-compose restart web worker
```

### 5.3 Authorize Xero Connection
1. **Open your browser** and go to: http://localhost:8000/auth/xero/authorize
2. **Log in to Xero** and authorize the application
3. **You'll be redirected back** with a success message

### 5.4 Import Xero Data
```bash
# Trigger data import from Xero
curl -X POST "http://localhost:8000/api/v1/xero/import" \
     -H "Content-Type: application/json" \
     -d '{"organization_id": 1, "incremental": false}'

# Check import status
curl "http://localhost:8000/api/v1/xero/sync-status/1"
```

---

## 🧐 **Step 6: Explore the System**

### 6.1 Available Services
- **Web API**: http://localhost:8000 (FastAPI application)
- **Database**: PostgreSQL on port 5432
- **Cache**: Redis on port 6379
- **Background Workers**: Celery for async tasks

### 6.2 Available Report Types
```bash
# Try different report types
curl "http://localhost:8000/reports/income-statement?organization_id=1&start_date=2024-01-01&end_date=2024-12-31&format=json"
curl "http://localhost:8000/reports/cash-flow?organization_id=1&start_date=2024-01-01&end_date=2024-12-31&format=json"
curl "http://localhost:8000/reports/dcf-valuation?organization_id=1&date=2024-12-31&format=json"
```

### 6.3 Check System Components
```bash
# View service logs
docker-compose logs web --tail=20     # API logs
docker-compose logs worker --tail=20  # Background task logs
docker-compose logs db --tail=20      # Database logs

# Check service resource usage
docker stats
```

---

## 🎉 **Congratulations!**

You've successfully:
- ✅ Set up MCX3D Financial System
- ✅ Verified all services are running
- ✅ Generated your first financial report
- ✅ (Optional) Connected to Xero for real data

## 🔄 **What's Next?**

### Immediate Next Steps
1. **[Tutorial 2: Generate Your First Report](./02-first-report.md)** - Detailed report generation
2. **[Tutorial 3: Xero Integration](./03-xero-integration.md)** - Complete Xero setup
3. **[User Guide](../user-guide/overview.md)** - Comprehensive system usage

### Learning Path
- **Beginner**: Complete tutorials 1-3, then explore the User Guide
- **Developer**: Review the [API Reference](../developer/api-reference.md)
- **Operations**: Check the [Deployment Guide](../operations/deployment.md)

### Useful Resources
- **[Quick Reference](../user-guide/quick-reference.md)**: Commands and shortcuts
- **[API Documentation](http://localhost:8000/docs)**: Interactive endpoint testing
- **[Troubleshooting](../user-guide/overview.md#troubleshooting-common-issues)**: Common issues and solutions

---

## 🆘 **Troubleshooting**

### Service Won't Start
```bash
# Check for port conflicts
lsof -i :8000  # Should show nothing if port is free

# View detailed error logs
docker-compose logs web

# Complete restart
docker-compose down && docker-compose up --build
```

### Database Connection Issues
```bash
# Check if database is accessible
docker-compose exec db psql -U user -d mcx3d_db -c "SELECT 1;"

# Reset database if needed (⚠️ this removes all data)
docker-compose down -v
docker-compose up -d db
docker-compose exec web alembic upgrade head
```

### Permission Issues
```bash
# Fix Docker permissions (Linux/Mac)
sudo chown -R $USER:$USER .
chmod +x scripts/*.sh
```

---

**🎯 Ready for more?** Continue with [Tutorial 2: Generate Your First Report](./02-first-report.md) to learn advanced report generation techniques, or jump to the [Complete User Guide](../user-guide/overview.md) for comprehensive system usage.