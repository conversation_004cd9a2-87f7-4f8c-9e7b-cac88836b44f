FROM nginx:1.25-alpine

# Install certbot for Let's Encrypt SSL certificates
RUN apk add --no-cache certbot certbot-nginx

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Create directories
RUN mkdir -p /etc/nginx/ssl /var/log/nginx /app/static /app/media

# Copy SSL renewal script
COPY renew-ssl.sh /usr/local/bin/renew-ssl.sh
RUN chmod +x /usr/local/bin/renew-ssl.sh

# Add cron job for SSL renewal
RUN echo "0 0,12 * * * /usr/local/bin/renew-ssl.sh >> /var/log/cron.log 2>&1" | crontab -

# Expose ports
EXPOSE 80 443

# Start nginx and cron
CMD crond && nginx -g "daemon off;"