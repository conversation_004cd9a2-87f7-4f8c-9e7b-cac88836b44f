"""
Tests for Xero integration reliability improvements.

Tests distributed locking, rate limiting, and error handling.
"""

import pytest
import time
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta, timezone
import redis
from requests.exceptions import HTTPError

from mcx3d_finance.utils.distributed_lock import Distributed<PERSON>ock, TokenRefreshLock
from mcx3d_finance.integrations.xero_retry import xero_api_call, XeroCircuitBreaker
from mcx3d_finance.integrations.xero_rate_limiter import XeroRateLimiter
from mcx3d_finance.exceptions.integration import (
    RateLimitError,
    AuthenticationError,
    ServiceUnavailableError
)


class TestDistributedLock:
    """Test distributed lock functionality."""
    
    @pytest.fixture
    def redis_mock(self):
        """Mock Redis client."""
        mock = Mock()
        mock.set.return_value = True
        mock.get.return_value = None
        mock.eval.return_value = 1
        return mock
    
    def test_lock_acquisition(self, redis_mock):
        """Test basic lock acquisition and release."""
        lock = DistributedLock(redis_mock, "test_resource", timeout=10)
        
        # Test acquisition
        assert lock.acquire() is True
        assert lock._lock_acquired is True
        
        # Verify Redis calls
        redis_mock.set.assert_called_once()
        args = redis_mock.set.call_args[0]
        assert args[0] == "lock:test_resource"
        
        # Test release
        assert lock.release() is True
        redis_mock.eval.assert_called_once()
    
    def test_lock_context_manager(self, redis_mock):
        """Test lock as context manager."""
        with DistributedLock(redis_mock, "test_resource") as lock:
            assert lock._lock_acquired is True
        
        # Verify release was called
        redis_mock.eval.assert_called_once()
    
    def test_lock_blocking(self, redis_mock):
        """Test lock blocking behavior."""
        # Simulate lock already held
        redis_mock.set.return_value = False
        
        lock = DistributedLock(
            redis_mock, 
            "test_resource", 
            blocking=True,
            blocking_timeout=1
        )
        
        start_time = time.time()
        acquired = lock.acquire(retry_interval=0.1)
        elapsed = time.time() - start_time
        
        assert acquired is False
        assert elapsed >= 0.9  # Should wait approximately 1 second


class TestXeroRateLimiter:
    """Test Xero rate limiter functionality."""
    
    @pytest.fixture
    def redis_mock(self):
        """Mock Redis client."""
        mock = Mock()
        mock.get.return_value = "10"  # Current count
        mock.incr.return_value = 11
        mock.ttl.return_value = -1
        mock.zcard.return_value = 100  # Daily count
        mock.zadd.return_value = 1
        mock.zremrangebyscore.return_value = 0
        return mock
    
    def test_rate_limit_check_allowed(self, redis_mock):
        """Test rate limit check when under limits."""
        limiter = XeroRateLimiter(redis_mock)
        
        allowed, info = limiter.check_rate_limit("test_tenant")
        
        assert allowed is True
        assert info["minute_used"] == 10
        assert info["minute_remaining"] == 45  # 55 limit - 10 used
        assert info["daily_used"] == 100
        assert info["daily_remaining"] == 4800  # 4900 limit - 100 used
    
    def test_rate_limit_check_minute_exceeded(self, redis_mock):
        """Test rate limit check when minute limit exceeded."""
        redis_mock.get.return_value = "60"  # Over minute limit
        
        limiter = XeroRateLimiter(redis_mock)
        allowed, info = limiter.check_rate_limit("test_tenant")
        
        assert allowed is False
        assert info["minute_remaining"] == 0
        assert info["retry_after"] is not None
    
    def test_rate_limit_check_daily_exceeded(self, redis_mock):
        """Test rate limit check when daily limit exceeded."""
        redis_mock.get.return_value = "10"  # Under minute limit
        redis_mock.zcard.return_value = 5000  # Over daily limit
        
        limiter = XeroRateLimiter(redis_mock)
        allowed, info = limiter.check_rate_limit("test_tenant")
        
        assert allowed is False
        assert info["daily_remaining"] == 0
    
    def test_track_api_call(self, redis_mock):
        """Test tracking API calls."""
        limiter = XeroRateLimiter(redis_mock)
        
        result = limiter.track_api_call("test_tenant")
        
        assert result is True
        redis_mock.incr.assert_called()
        redis_mock.zadd.assert_called()


class TestXeroRetryDecorator:
    """Test Xero-specific retry decorator."""
    
    def test_successful_call(self):
        """Test decorator with successful API call."""
        @xero_api_call(max_retries=3)
        def mock_api_call():
            return {"success": True}
        
        result = mock_api_call()
        assert result == {"success": True}
    
    def test_retry_on_401_error(self):
        """Test retry on 401 authentication error."""
        call_count = 0
        
        @xero_api_call(max_retries=3, handle_token_refresh=False)
        def mock_api_call(self):
            nonlocal call_count
            call_count += 1
            
            if call_count == 1:
                # First call fails with 401
                response = Mock()
                response.status_code = 401
                raise HTTPError(response=response)
            
            return {"success": True}
        
        # Create mock self with organization_id
        mock_self = Mock()
        mock_self.organization_id = 1
        
        with pytest.raises(AuthenticationError):
            mock_api_call(mock_self)
    
    def test_retry_on_429_rate_limit(self):
        """Test retry on 429 rate limit error."""
        call_count = 0
        
        @xero_api_call(max_retries=3)
        def mock_api_call():
            nonlocal call_count
            call_count += 1
            
            if call_count == 1:
                # First call fails with 429
                response = Mock()
                response.status_code = 429
                response.headers = {"Retry-After": "1"}
                raise HTTPError(response=response)
            
            return {"success": True}
        
        start_time = time.time()
        result = mock_api_call()
        elapsed = time.time() - start_time
        
        assert result == {"success": True}
        assert call_count == 2
        assert elapsed >= 0.5  # Should have waited with jitter
    
    def test_circuit_breaker_integration(self):
        """Test circuit breaker integration."""
        @xero_api_call(max_retries=1, circuit_breaker_threshold=2)
        def mock_api_call():
            response = Mock()
            response.status_code = 503
            raise HTTPError(response=response)
        
        # First few calls should fail normally
        for _ in range(2):
            with pytest.raises(ServiceUnavailableError):
                mock_api_call()
        
        # Circuit should now be open
        with pytest.raises(ServiceUnavailableError) as exc_info:
            mock_api_call()
        
        assert "circuit breaker is open" in str(exc_info.value).lower()


class TestXeroCircuitBreaker:
    """Test Xero-specific circuit breaker."""
    
    def test_rate_limit_threshold(self):
        """Test circuit breaker opens on rate limit errors."""
        breaker = XeroCircuitBreaker(
            failure_threshold=5,
            rate_limit_threshold=3
        )
        
        # Record rate limit errors
        for _ in range(3):
            error = RateLimitError(
                "Rate limited",
                service_name="Xero",
                retry_after=60
            )
            breaker.record_failure(error)
        
        assert breaker.state == "open"
        assert breaker.rate_limit_count == 3
    
    def test_success_resets_rate_limit_count(self):
        """Test successful call resets rate limit counter."""
        breaker = XeroCircuitBreaker()
        
        # Record some rate limit errors
        breaker.rate_limit_count = 2
        
        # Success should reset
        breaker.record_success()
        
        assert breaker.rate_limit_count == 0
        assert breaker.state == "closed"


class TestTokenRefreshLock:
    """Test token refresh lock functionality."""
    
    @pytest.fixture
    def redis_mock(self):
        """Mock Redis client."""
        mock = Mock()
        mock.set.return_value = True
        mock.eval.return_value = 1
        return mock
    
    def test_token_refresh_lock(self, redis_mock):
        """Test token refresh lock with organization ID."""
        with TokenRefreshLock(redis_mock, organization_id=123) as lock:
            assert lock.lock._lock_acquired is True
            assert lock.lock.key == "lock:xero_token_refresh:123"
            assert lock.lock.timeout == 30  # 30 seconds for token refresh
            assert lock.lock.auto_extend is True
    
    def test_token_refresh_lock_contention_logging(self, redis_mock, caplog):
        """Test logging when lock acquisition takes time."""
        # Simulate delayed acquisition
        def delayed_set(*args, **kwargs):
            time.sleep(1.5)
            return True
        
        redis_mock.set = delayed_set
        
        with TokenRefreshLock(redis_mock, organization_id=123):
            pass
        
        # Check for contention logging
        assert any(
            "Token refresh lock acquired" in record.message and "after waiting" in record.message
            for record in caplog.records
        )


@pytest.mark.integration
class TestIntegration:
    """Integration tests for Xero reliability improvements."""
    
    @pytest.fixture
    def setup_mocks(self):
        """Setup common mocks for integration tests."""
        with patch('mcx3d_finance.utils.redis_client.get_redis_client') as redis_mock:
            redis_client = Mock()
            redis_client.set.return_value = True
            redis_client.eval.return_value = 1
            redis_client.get.return_value = "10"
            redis_client.incr.return_value = 11
            redis_client.zcard.return_value = 100
            redis_mock.return_value = redis_client
            
            yield {
                'redis_client': redis_client,
                'redis_mock': redis_mock
            }
    
    def test_concurrent_token_refresh(self, setup_mocks):
        """Test concurrent token refresh with distributed locking."""
        # This would require more complex setup with threading
        # Placeholder for demonstration
        pass
    
    def test_rate_limited_api_calls(self, setup_mocks):
        """Test API calls with rate limiting."""
        # This would test the full flow with rate limiting
        # Placeholder for demonstration
        pass