# MCX3D Financial System - Complete API Reference

## Overview

MCX3D Financial System provides comprehensive financial analytics through RESTful APIs with NASDAQ compliance and GAAP standards. This reference covers all endpoints, authentication, data models, and integration patterns.

---

## Authentication

All API endpoints require authentication. The system supports two authentication methods:

### JWT Token Authentication
For API endpoints, include the <PERSON><PERSON><PERSON> token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

### Xero OAuth 2.0 Integration
For Xero-specific endpoints, OAuth 2.0 tokens are handled automatically after authorization.

---

## API Endpoints

### Authentication & Authorization

#### `/api/auth/register` ⭐ **NEW**
- **Method**: `POST`
- **Purpose**: Register new user account with optional organization creation
- **Content-Type**: `application/json`
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "full_name": "<PERSON>",
    "organization_name": "Acme Corporation",  // Optional
    "role": "admin"  // Optional: user, admin, viewer
  }
  ```
- **Response**: 
  ```json
  {
    "user_id": 123,
    "email": "<EMAIL>",
    "full_name": "<PERSON> Doe",
    "organization_id": 456,
    "organization_name": "Acme Corporation",
    "role": "admin",
    "message": "User registered successfully"
  }
  ```
- **Error Codes**:
  - `409`: User with email already exists
  - `400`: Invalid input data
  - `500`: Registration failed
- **Security Features**: Rate limiting, input validation, audit logging
- **Implementation**: `mcx3d_finance.api.auth.register_user`

#### `/api/auth/login`
- **Method**: `POST`
- **Purpose**: User login with MFA support
- **Content-Type**: `application/json`
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "mfa_token": "123456"  // Optional MFA token
  }
  ```
- **Response**:
  ```json
  {
    "access_token": "jwt_token_here",
    "refresh_token": "refresh_token_here",
    "token_type": "bearer",
    "user": {
      "id": 123,
      "email": "<EMAIL>",
      "full_name": "John Doe"
    },
    "mfa_required": false
  }
  ```
- **Implementation**: `mcx3d_finance.api.auth.login`

#### `/auth/xero/authorize`
- **Method**: `GET`
- **Purpose**: Initiate Xero OAuth 2.0 authentication flow
- **Response**: Redirect URL to Xero authorization server
- **Implementation**: `mcx3d_finance.api.auth`

#### `/auth/xero/callback`
- **Method**: `GET` 
- **Purpose**: Handle OAuth callback and token exchange
- **Parameters**: `code`, `state`, `scope`
- **Response**: Authentication success with tenant information
- **Implementation**: `mcx3d_finance.api.auth`

### Financial Reports

#### `/reports/income-statement`
- **Method**: `GET`
- **Purpose**: Generate NASDAQ-compliant income statements
- **Parameters**:
  - `organization_id` (int): Target organization ID
  - `start_date` (str): Period start date (YYYY-MM-DD)  
  - `end_date` (str): Period end date (YYYY-MM-DD)
  - `format` (enum): Output format [json, pdf, excel, html]
- **Response**: Income statement data or file stream
- **Implementation**: `mcx3d_finance.api.reports`

#### `/reports/balance-sheet`
- **Method**: `GET`
- **Purpose**: Generate NASDAQ-compliant balance sheets
- **Parameters**:
  - `organization_id` (int): Target organization ID
  - `date` (str): Balance sheet date (YYYY-MM-DD)
  - `format` (enum): Output format [json, pdf, excel, html]
- **Response**: Balance sheet data or file stream  
- **Implementation**: `mcx3d_finance.api.reports`

#### `/reports/cash-flow`
- **Method**: `GET`
- **Purpose**: Generate cash flow statements (indirect method)
- **Parameters**:
  - `organization_id` (int): Target organization ID
  - `start_date` (str): Period start date (YYYY-MM-DD)
  - `end_date` (str): Period end date (YYYY-MM-DD)
  - `format` (enum): Output format [json, pdf, excel, html]
- **Response**: Cash flow statement data or file stream
- **Implementation**: `mcx3d_finance.api.reports`

### Valuation Models

#### `/reports/dcf-valuation`
- **Method**: `GET`
- **Purpose**: Execute Discounted Cash Flow valuation
- **Parameters**: `organization_id`, `date`, `format`
- **Response**: Enterprise value, equity value, per-share value
- **Implementation**: `mcx3d_finance.api.reports`

#### `/reports/multiples-valuation`  
- **Method**: `GET`
- **Purpose**: Execute multiples-based valuation
- **Parameters**: `organization_id`, `date`, `format`
- **Response**: Valuation ranges based on industry multiples
- **Implementation**: `mcx3d_finance.api.reports`

### Xero Data Import

#### `/api/v1/xero/import`
- **Method**: `POST`
- **Purpose**: Import data from Xero for a specific organization
- **Request Body**:
  ```json
  {
    "organization_id": 2,
    "incremental": false,
    "force_refresh": false
  }
  ```
- **Parameters**:
  - `organization_id` (int, required): The ID of the organization to import data for
  - `incremental` (bool, optional): If true, only import changes since last sync. Default: false
  - `force_refresh` (bool, optional): If true, force refresh of all data. Default: false
- **Response**:
  ```json
  {
    "success": true,
    "organization_id": 2,
    "import_stats": {},
    "processed_data_summary": {},
    "timestamp": "2025-07-22T15:45:00.000Z"
  }
  ```
- **Status Codes**:
  - `200`: Import started successfully
  - `404`: Organization not found
  - `409`: Import already in progress
  - `401`: Authentication required
  - `500`: Server error

#### `/api/v1/xero/auth-status/{organization_id}`
- **Method**: `GET`
- **Purpose**: Check the Xero authentication status for an organization
- **Path Parameters**: `organization_id` (int): The organization ID
- **Response**:
  ```json
  {
    "organization_id": 2,
    "is_authenticated": true,
    "expires_at": "2025-07-23T15:45:00.000Z",
    "scopes": ["accounting.transactions", "accounting.contacts.read"],
    "tenant_name": "Modular CX"
  }
  ```

#### `/api/v1/xero/sync-status/{organization_id}`
- **Method**: `GET`
- **Purpose**: Get the current sync status for an organization
- **Path Parameters**: `organization_id` (int): The organization ID
- **Response**:
  ```json
  {
    "organization_id": 2,
    "sync_status": "completed",
    "last_sync_at": "2025-07-22T14:30:00.000Z",
    "next_sync_at": "2025-07-23T14:30:00.000Z",
    "records_synced": {
      "accounts": {"total": 107, "imported": 107, "errors": 0},
      "contacts": {"total": 374, "imported": 374, "errors": 0},
      "invoices": {"total": 120, "imported": 120, "errors": 0},
      "transactions": {"total": 4851, "imported": 4851, "errors": 0}
    },
    "errors": []
  }
  ```
- **Possible sync_status values**:
  - `never_synced`: No sync has been performed
  - `running`: Sync is currently in progress
  - `completed`: Last sync completed successfully
  - `failed`: Last sync failed with errors

#### `/api/v1/xero/sync/{organization_id}`
- **Method**: `POST`
- **Purpose**: Trigger a sync using the Celery task system (recommended for production)
- **Path Parameters**: `organization_id` (int): The organization ID
- **Query Parameters**: `force_full` (bool, optional): If true, perform full sync instead of incremental. Default: false
- **Response**:
  ```json
  {
    "task_id": "celery-task-uuid-123",
    "organization_id": 2,
    "status": "queued",
    "message": "Sync task has been queued and will start shortly"
  }
  ```

#### `/api/v1/xero/task-status/{task_id}`
- **Method**: `GET`
- **Purpose**: Get the status of a Celery task
- **Path Parameters**: `task_id` (string): The Celery task ID
- **Response**:
  ```json
  {
    "task_id": "celery-task-uuid-123",
    "status": "SUCCESS",
    "result": {
      "success": true,
      "organization_id": 2,
      "records_processed": 5452
    },
    "info": null
  }
  ```
- **Possible task status values**:
  - `PENDING`: Task is waiting to be processed
  - `STARTED`: Task has been started
  - `SUCCESS`: Task completed successfully  
  - `FAILURE`: Task failed
  - `RETRY`: Task is being retried
  - `REVOKED`: Task was revoked

### Metrics & KPIs

#### `/api/metrics/saas-kpis`
- **Method**: `GET`
- **Purpose**: Calculate SaaS-specific KPIs
- **Parameters**: `organization_id`, `period`
- **Response**: MRR, ARR, churn rate, LTV/CAC, NPS
- **Implementation**: `mcx3d_finance.api.metrics`

### Health & Monitoring

#### `/health`
- **Method**: `GET`
- **Purpose**: System health check
- **Response**: Health status of all system components
- **Implementation**: `mcx3d_finance.api.health`

### Webhooks & Integration

#### `/webhooks/xero`
- **Method**: `POST`
- **Purpose**: Receive Xero webhook notifications
- **Security**: HMAC-SHA256 signature verification
- **Response**: Processing acknowledgment
- **Implementation**: `mcx3d_finance.api.webhook_routes`

---

## Core Business Logic Classes

### Financial Statement Generators

#### `BalanceSheetGenerator`
- **Module**: `mcx3d_finance.core.financials.balance_sheet`
- **Purpose**: NASDAQ-compliant balance sheet generation
- **Key Methods**:
  - `generate_balance_sheet()`: Main generation method
  - `_classify_accounts()`: GAAP classification
  - `_calculate_totals()`: Balance validation

#### `IncomeStatementGenerator`  
- **Module**: `mcx3d_finance.core.financials.income_statement`
- **Purpose**: GAAP income statement with earnings calculations
- **Key Methods**:
  - `generate_income_statement()`: Main generation method
  - `_calculate_earnings()`: EPS calculations
  - `_categorize_revenue()`: Revenue recognition

#### `CashFlowGenerator`
- **Module**: `mcx3d_finance.core.financials.cash_flow`  
- **Purpose**: Cash flow statements (direct/indirect methods)
- **Key Methods**:
  - `generate_cash_flow_statement()`: Main generation method
  - `_calculate_operating_cf()`: Operating activities
  - `_calculate_investing_cf()`: Investing activities
  - `_calculate_financing_cf()`: Financing activities

### Valuation Models

#### `DCFValuation`
- **Module**: `mcx3d_finance.core.valuation.dcf`
- **Purpose**: Discounted cash flow modeling with sensitivity analysis
- **Key Methods**:
  - `calculate_dcf_valuation()`: Core DCF calculation
  - `_project_cash_flows()`: Cash flow projections
  - `_calculate_terminal_value()`: Terminal value calculation
  - `_sensitivity_analysis()`: Scenario modeling

#### `MultiplesValuation`
- **Module**: `mcx3d_finance.core.valuation.multiples`
- **Purpose**: Comparable company analysis
- **Key Methods**:
  - `calculate_multiples_valuation()`: Main valuation method
  - `_find_comparables()`: Peer identification
  - `_calculate_multiples()`: Multiple calculations

### Data Processing & Validation

#### `XeroDataProcessor`
- **Module**: `mcx3d_finance.core.data_processors`
- **Purpose**: Xero data processing and enrichment
- **Key Methods**:
  - `process_accounts()`: Account data processing
  - `process_transactions()`: Transaction processing
  - `enrich_data()`: Data enrichment

#### `DataValidationEngine`
- **Module**: `mcx3d_finance.core.data_validation`
- **Purpose**: Multi-layer data validation system
- **Validators**:
  - `FinancialIntegrityValidator`: Balance checks
  - `BusinessRuleValidator`: Business logic validation
  - `RegulatoryComplianceValidator`: GAAP/NASDAQ compliance

### Utility & Infrastructure

#### `CurrencyConverter`
- **Module**: `mcx3d_finance.core.currency_converter`
- **Purpose**: Multi-currency support and conversion
- **Currencies**: 50+ supported currency codes
- **Features**: Real-time rates, historical conversion

#### `SaaSKPICalculator`
- **Module**: `mcx3d_finance.core.metrics.saas_kpis`
- **Purpose**: SaaS-specific KPI calculations
- **Metrics**: MRR, ARR, Churn, LTV/CAC, Growth rates

---

## Data Models & Schema

### Core Entities

#### `Organization`
- **Table**: `organizations`
- **Key Fields**: `id`, `name`, `xero_tenant_id`, `xero_token`
- **Relationships**: accounts, contacts, transactions

#### `Account`  
- **Table**: `accounts`
- **Key Fields**: `id`, `name`, `type`, `status`
- **GAAP Mapping**: Automated classification to GAAP standards

#### `Transaction`
- **Table**: `transactions`  
- **Key Fields**: `id`, `amount`, `date`, `description`
- **Classification**: Automated categorization and validation

#### `Contact`
- **Table**: `contacts`
- **Key Fields**: `id`, `name`, `email`, `is_supplier`, `is_customer`
- **Integration**: Xero contact synchronization

---

## Usage Examples

### Python (using requests)

```python
import requests

# Authentication
headers = {
    'Authorization': 'Bearer YOUR_JWT_TOKEN',
    'Content-Type': 'application/json'
}

# Generate balance sheet
response = requests.get(
    'http://localhost:8000/reports/balance-sheet',
    params={
        'organization_id': 2,
        'date': '2025-07-24',
        'format': 'json'
    },
    headers=headers
)

# Start Xero import
import_response = requests.post(
    'http://localhost:8000/api/v1/xero/import',
    json={
        'organization_id': 2,
        'incremental': False,
        'force_refresh': False
    },
    headers=headers
)

# Check sync status
status_response = requests.get(
    'http://localhost:8000/api/v1/xero/sync-status/2',
    headers=headers
)

print(f"Balance sheet: {response.json()}")
print(f"Import response: {import_response.json()}")
print(f"Sync status: {status_response.json()}")
```

### curl

```bash
# Set your JWT token
TOKEN="your_jwt_token_here"

# Generate income statement
curl -X GET "http://localhost:8000/reports/income-statement?organization_id=2&start_date=2025-01-01&end_date=2025-07-24&format=json" \
     -H "Authorization: Bearer $TOKEN"

# Start Xero import
curl -X POST "http://localhost:8000/api/v1/xero/import" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"organization_id": 2, "incremental": false}'

# Check auth status
curl -X GET "http://localhost:8000/api/v1/xero/auth-status/2" \
     -H "Authorization: Bearer $TOKEN"

# Trigger sync task
curl -X POST "http://localhost:8000/api/v1/xero/sync/2?force_full=false" \
     -H "Authorization: Bearer $TOKEN"
```

---

## Error Handling & Validation

### HTTP Status Codes
- `200`: Success with data
- `201`: Resource created successfully  
- `400`: Bad request (validation errors)
- `401`: Unauthorized (invalid/expired token)
- `404`: Resource not found
- `422`: Unprocessable entity (business rule violations)
- `429`: Rate limit exceeded
- `500`: Internal server error

### Error Response Format
```json
{
  "detail": "Organization 999 not found",
  "error": "Not Found",
  "timestamp": "2025-07-22T15:45:00.000Z",
  "request_id": "req-123"
}
```

### Common Error Scenarios
- **401 Unauthorized**: Invalid or missing JWT token
- **403 Forbidden**: User doesn't have access to the organization
- **404 Not Found**: Organization not found
- **409 Conflict**: Import already in progress
- **500 Internal Server Error**: Server-side error during import

---

## Best Practices

### Xero Integration
1. **Check auth status first** before attempting imports to ensure Xero authentication is valid
2. **Use the Celery task endpoint** (`/sync/{organization_id}`) for production imports as it handles long-running operations better
3. **Monitor task progress** using the task status endpoint when using Celery tasks
4. **Handle rate limits** - Xero has API rate limits, so avoid triggering multiple imports simultaneously
5. **Use incremental sync** when possible to reduce API calls and improve performance
6. **Check sync status** to avoid triggering imports when one is already running

### Report Generation
1. **Use appropriate formats** - JSON for programmatic access, PDF/Excel for human consumption
2. **Cache frequently requested reports** to improve performance
3. **Handle large datasets** - Use pagination for large result sets
4. **Validate date ranges** before making requests

### Security
1. **Always include authentication tokens** in API requests
2. **Use HTTPS** in production environments
3. **Implement proper error handling** to avoid exposing sensitive information
4. **Monitor API usage** to detect unusual patterns

---

## Development Notes

- The API uses FastAPI with automatic OpenAPI documentation available at `/docs`
- Background tasks use Celery for reliable processing
- All times are in UTC ISO format
- The API supports CORS for local development
- Rate limiting is implemented to prevent abuse
- Comprehensive logging and monitoring are built-in