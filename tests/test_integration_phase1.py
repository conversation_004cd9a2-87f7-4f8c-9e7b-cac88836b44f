"""
Integration tests for Phase 1 components of MCX3D Financial System.
Tests data flow and interaction between all core financial modules.
"""

import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock
from typing import Optional

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import Phase 1 components
from mcx3d_finance.core.financials.cash_flow import CashFlowGenerator
from mcx3d_finance.core.valuation.dcf import DCFValuation
from mcx3d_finance.core.valuation.multiples import MultiplesValuation
from mcx3d_finance.core.metrics.saas_kpis import SaaSKPICalculator


class IntegratedFinancialAnalyzer:
    """Integrated analyzer that uses all Phase 1 components together."""
    
    def __init__(self, db_session=None):
        self.db = db_session
        self.cash_flow_generator = CashFlowGenerator(db_session)
        self.dcf_valuation = DCFValuation()
        self.multiples_valuation = MultiplesValuation()
        self.saas_calculator = SaaSKPICalculator(db_session)
    
    def generate_comprehensive_financial_analysis(
        self, 
        organization_id: int, 
        analysis_date: datetime,
        comparable_companies: Optional[list] = None
    ):
        """Generate comprehensive financial analysis using all components."""
        
        # Define analysis period
        from_date = analysis_date - timedelta(days=365)  # Last 12 months
        to_date = analysis_date
        
        results = {
            "analysis_date": analysis_date.isoformat(),
            "organization_id": organization_id,
            "period": {"from": from_date.isoformat(), "to": to_date.isoformat()},
        }
        
        try:
            # 1. Generate cash flow statements
            print("   📊 Generating cash flow statements...")
            cash_flow_indirect = self.cash_flow_generator.generate_cash_flow_statement(
                organization_id, from_date, to_date, method="indirect"
            )
            cash_flow_direct = self.cash_flow_generator.generate_cash_flow_statement(
                organization_id, from_date, to_date, method="direct"
            )
            
            results["cash_flow"] = {
                "indirect": cash_flow_indirect,
                "direct": cash_flow_direct,
            }
            
            # 2. Calculate SaaS KPIs
            print("   🚀 Calculating SaaS KPIs...")
            saas_kpis = self.saas_calculator.calculate_comprehensive_kpis(
                organization_id, from_date, to_date
            )
            results["saas_kpis"] = saas_kpis
            
            # 3. Extract financial metrics for valuation
            operating_cash_flow = cash_flow_indirect["operating_activities"]["net_cash_from_operating"]
            revenue_metrics = saas_kpis["kpis"]["revenue_metrics"]
            
            # Create financial projections based on current metrics
            projections = self._create_financial_projections(revenue_metrics, operating_cash_flow)
            
            # 4. Perform DCF valuation
            print("   💰 Performing DCF valuation...")
            dcf_results = self.dcf_valuation.calculate_dcf_valuation(
                financial_projections=projections,
                discount_rate=0.12,  # 12% discount rate
                terminal_growth_rate=0.03,  # 3% terminal growth
                scenarios=["base", "upside", "downside"]
            )
            results["dcf_valuation"] = dcf_results
            
            # 5. Perform multiples valuation if comparables provided
            if comparable_companies:
                print("   📈 Performing multiples valuation...")
                target_metrics = self._extract_target_metrics(revenue_metrics, operating_cash_flow)
                
                multiples_results = self.multiples_valuation.calculate_comprehensive_multiples_valuation(
                    target_metrics=target_metrics,
                    comparable_companies=comparable_companies
                )
                results["multiples_valuation"] = multiples_results
            
            # 6. Generate integrated insights
            results["integrated_insights"] = self._generate_integrated_insights(results)
            
            return results
            
        except Exception as e:
            print(f"   ❌ Integration analysis failed: {e}")
            raise
    
    def _create_financial_projections(self, revenue_metrics, operating_cash_flow):
        """Create financial projections based on current metrics."""
        current_arr = revenue_metrics.get("annual_recurring_revenue", 1000000)
        growth_rate = revenue_metrics.get("revenue_growth_rate", {}).get("monthly", 10) / 100
        
        projections = []
        for year in range(1, 6):  # 5-year projections
            revenue = current_arr * ((1 + growth_rate) ** year)
            free_cash_flow = revenue * 0.25  # Assume 25% FCF margin
            
            projections.append({
                "year": year,
                "revenue": revenue,
                "free_cash_flow": free_cash_flow,
                "ebitda": revenue * 0.30,  # Assume 30% EBITDA margin
            })
        
        return projections
    
    def _extract_target_metrics(self, revenue_metrics, operating_cash_flow):
        """Extract target company metrics for multiples valuation."""
        return {
            "revenue": revenue_metrics.get("annual_recurring_revenue", 1000000),
            "ebitda": revenue_metrics.get("annual_recurring_revenue", 1000000) * 0.30,
            "free_cash_flow": operating_cash_flow,
            "net_income": operating_cash_flow * 0.8,  # Assume 80% of OCF is net income
        }
    
    def _generate_integrated_insights(self, results):
        """Generate insights by combining all analysis results."""
        insights = []
        
        # Cash flow insights
        cash_flow = results.get("cash_flow", {}).get("indirect", {})
        operating_cash = cash_flow.get("operating_activities", {}).get("net_cash_from_operating", 0)
        
        if operating_cash > 0:
            insights.append(f"Positive operating cash flow of ${operating_cash:,.2f} indicates healthy operations")
        else:
            insights.append(f"Negative operating cash flow of ${operating_cash:,.2f} requires attention")
        
        # SaaS KPIs insights
        saas_kpis = results.get("saas_kpis", {}).get("kpis", {})
        health_score = saas_kpis.get("health_score", {})
        
        if health_score:
            score = health_score.get("overall_score", 0)
            grade = health_score.get("health_grade", "Unknown")
            insights.append(f"SaaS health score: {score:.1f}/100 (Grade {grade})")
        
        # Valuation insights
        dcf_results = results.get("dcf_valuation", {})
        if dcf_results:
            base_value = dcf_results.get("valuation_results", {}).get("base", {}).get("enterprise_value", 0)
            insights.append(f"DCF enterprise value: ${base_value:,.2f}")
        
        multiples_results = results.get("multiples_valuation", {})
        if multiples_results:
            weighted_val = multiples_results.get("weighted_valuation", {}).get("weighted_valuation", 0)
            insights.append(f"Multiples-based valuation: ${weighted_val:,.2f}")
        
        return insights


def test_integrated_financial_analysis():
    """Test integrated financial analysis using all components."""
    print("\n🔗 Testing Integrated Financial Analysis...")
    
    try:
        # Create mock database session
        mock_session = Mock()
        mock_org = Mock()
        mock_org.name = "Integrated Test Company"
        mock_org.id = 1
        mock_session.query().filter().first.return_value = mock_org
        mock_session.query().join().filter().scalar.return_value = 100000
        
        # Create integrated analyzer
        analyzer = IntegratedFinancialAnalyzer(mock_session)
        
        # Sample comparable companies
        comparable_companies = [
            {"company": "Comp A", "ev_revenue": 5.0, "ev_ebitda": 15.0, "pe_ratio": 25.0},
            {"company": "Comp B", "ev_revenue": 6.0, "ev_ebitda": 18.0, "pe_ratio": 30.0},
            {"company": "Comp C", "ev_revenue": 4.5, "ev_ebitda": 12.0, "pe_ratio": 22.0},
        ]
        
        # Run comprehensive analysis
        analysis_date = datetime(2024, 12, 31)
        results = analyzer.generate_comprehensive_financial_analysis(
            organization_id=1,
            analysis_date=analysis_date,
            comparable_companies=comparable_companies
        )
        
        # Verify integrated results structure
        required_sections = ["cash_flow", "saas_kpis", "dcf_valuation", "multiples_valuation", "integrated_insights"]
        for section in required_sections:
            assert section in results, f"Missing section: {section}"
        
        # Verify data consistency between components
        cash_flow_data = results["cash_flow"]["indirect"]
        saas_data = results["saas_kpis"]
        dcf_data = results["dcf_valuation"]
        multiples_data = results["multiples_valuation"]
        
        # Check that all components produced valid results
        assert cash_flow_data["header"]["company_name"] == "Integrated Test Company"
        assert "kpis" in saas_data
        assert "valuation_results" in dcf_data
        assert "weighted_valuation" in multiples_data
        
        # Verify insights were generated
        insights = results["integrated_insights"]
        assert len(insights) > 0
        
        print("   ✅ Integrated analysis structure is correct")
        print(f"   📊 Generated {len(insights)} integrated insights")
        print(f"   💰 DCF Base Value: ${dcf_data['valuation_results']['base']['enterprise_value']:,.2f}")
        print(f"   📈 Multiples Value: ${multiples_data['weighted_valuation']['weighted_valuation']:,.2f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Integrated analysis test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_consistency():
    """Test data consistency between components."""
    print("\n🔄 Testing Data Consistency...")
    
    try:
        # Test that all components handle the same data types consistently
        test_value = 123456.789
        
        # Create instances
        mock_session = Mock()
        cash_flow = CashFlowGenerator(mock_session)
        multiples = MultiplesValuation()
        saas = SaaSKPICalculator()
        
        # Test currency rounding consistency
        cf_rounded = cash_flow._round_currency(test_value)
        mult_rounded = multiples._round_currency(test_value)
        saas_rounded = saas._round_currency(test_value)
        
        assert cf_rounded == mult_rounded == saas_rounded
        print("   ✅ Currency rounding is consistent across components")
        
        # Test date handling consistency
        test_date = datetime(2024, 6, 15)
        period_desc = cash_flow._format_period(test_date, test_date + timedelta(days=180))
        assert isinstance(period_desc, str)
        assert len(period_desc) > 0
        print("   ✅ Date handling is consistent")
        
        # Test error handling consistency
        try:
            cash_flow._get_organization(-1)  # Invalid ID
        except Exception:
            pass  # Expected to fail
        
        print("   ✅ Error handling is consistent")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Data consistency test failed: {e}")
        return False


def test_performance_integration():
    """Test performance of integrated operations."""
    print("\n⚡ Testing Performance Integration...")
    
    try:
        import time
        
        # Create mock session with larger dataset simulation
        mock_session = Mock()
        mock_org = Mock()
        mock_org.name = "Performance Test Company"
        mock_org.id = 1
        mock_session.query().filter().first.return_value = mock_org
        mock_session.query().join().filter().scalar.return_value = 500000  # Larger amounts
        
        analyzer = IntegratedFinancialAnalyzer(mock_session)
        
        # Time the integrated analysis
        start_time = time.time()
        
        results = analyzer.generate_comprehensive_financial_analysis(
            organization_id=1,
            analysis_date=datetime(2024, 12, 31),
            comparable_companies=[
                {"company": f"Comp {i}", "ev_revenue": 4.0 + i*0.5, "ev_ebitda": 12.0 + i*2, "pe_ratio": 20.0 + i*3}
                for i in range(10)  # 10 comparable companies
            ]
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Verify results were generated
        assert "integrated_insights" in results
        assert len(results["integrated_insights"]) > 0
        
        print(f"   ✅ Integrated analysis completed in {execution_time:.2f} seconds")
        print(f"   📊 Generated analysis with {len(results)} main sections")
        
        # Performance should be under 5 seconds for this test
        if execution_time < 5.0:
            print("   ⚡ Performance meets requirements (< 5 seconds)")
        else:
            print("   ⚠️  Performance slower than expected")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Performance integration test failed: {e}")
        return False


def test_edge_cases():
    """Test edge cases and error conditions."""
    print("\n🛡️  Testing Edge Cases...")
    
    try:
        mock_session = Mock()
        analyzer = IntegratedFinancialAnalyzer(mock_session)
        
        # Test with invalid organization ID
        mock_session.query().filter().first.return_value = None
        
        try:
            analyzer.generate_comprehensive_financial_analysis(
                organization_id=999,  # Non-existent
                analysis_date=datetime(2024, 12, 31)
            )
            print("   ⚠️  Should have failed with invalid organization ID")
        except Exception:
            print("   ✅ Properly handles invalid organization ID")
        
        # Test with empty comparable companies
        mock_org = Mock()
        mock_org.name = "Edge Case Company"
        mock_session.query().filter().first.return_value = mock_org
        
        results = analyzer.generate_comprehensive_financial_analysis(
            organization_id=1,
            analysis_date=datetime(2024, 12, 31),
            comparable_companies=[]  # Empty list
        )
        
        # Should still work without multiples valuation
        assert "cash_flow" in results
        assert "saas_kpis" in results
        assert "dcf_valuation" in results
        print("   ✅ Handles empty comparable companies list")
        
        # Test with extreme dates
        future_date = datetime(2030, 1, 1)
        results = analyzer.generate_comprehensive_financial_analysis(
            organization_id=1,
            analysis_date=future_date
        )
        assert "integrated_insights" in results
        print("   ✅ Handles future analysis dates")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Edge cases test failed: {e}")
        return False


def run_integration_tests():
    """Run all integration tests."""
    print("🔗 MCX3D Financial System - Phase 1 Integration Testing")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_results = {}
    
    # Run integration tests
    test_results["integrated_analysis"] = test_integrated_financial_analysis()
    test_results["data_consistency"] = test_data_consistency()
    test_results["performance"] = test_performance_integration()
    test_results["edge_cases"] = test_edge_cases()
    
    # Generate summary
    print("\n" + "=" * 60)
    print("📋 INTEGRATION TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for result in test_results.values() if result)
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name.replace('_', ' ').title():<25} {status}")
    
    print("-" * 60)
    print(f"Integration Result: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL INTEGRATION TESTS PASSED - Components work together seamlessly!")
    else:
        print("⚠️  Some integration tests failed - review component interactions")
    
    print(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return test_results


if __name__ == "__main__":
    run_integration_tests()
