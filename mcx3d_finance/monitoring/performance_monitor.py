"""
Performance monitoring system for the MCX3D financial platform.

Provides real-time performance tracking, benchmarking, and optimization
recommendations for database queries, API calls, and system resources.
"""

import time
import psutil
import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from contextlib import contextmanager
from collections import defaultdict, deque
from functools import wraps
import threading
import json

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """Individual performance measurement."""
    operation: str
    duration_ms: float
    timestamp: datetime
    memory_usage_mb: float
    cpu_percent: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    success: bool = True
    error: Optional[str] = None


@dataclass
class PerformanceBenchmark:
    """Performance benchmark thresholds."""
    operation: str
    target_ms: float
    warning_ms: float
    critical_ms: float
    description: str


class PerformanceMonitor:
    """Real-time performance monitoring and analysis."""
    
    def __init__(self, max_metrics: int = 1000):
        self.metrics: deque = deque(maxlen=max_metrics)
        self.operation_stats = defaultdict(list)
        self.lock = threading.Lock()
        
        # System monitoring
        self.process = psutil.Process()
        
        # Performance benchmarks
        self.benchmarks = {
            'database_query': PerformanceBenchmark(
                'database_query', 100, 200, 1000,
                'Database query performance'
            ),
            'xero_api_call': PerformanceBenchmark(
                'xero_api_call', 500, 1000, 5000,
                'Xero API call performance'
            ),
            'redis_operation': PerformanceBenchmark(
                'redis_operation', 10, 50, 200,
                'Redis cache operation performance'
            ),
            'report_generation': PerformanceBenchmark(
                'report_generation', 2000, 5000, 15000,
                'Report generation performance'
            ),
            'health_check': PerformanceBenchmark(
                'health_check', 100, 500, 2000,
                'System health check performance'
            )
        }
    
    def record_metric(self, metric: PerformanceMetric):
        """Record a performance metric."""
        with self.lock:
            self.metrics.append(metric)
            self.operation_stats[metric.operation].append(metric)
            
            # Limit per-operation metrics
            if len(self.operation_stats[metric.operation]) > 100:
                self.operation_stats[metric.operation].pop(0)
    
    @contextmanager
    def measure_operation(self, operation: str, metadata: Optional[Dict[str, Any]] = None):
        """Context manager to measure operation performance."""
        start_time = time.time()
        start_memory = self.process.memory_info().rss / 1024 / 1024
        start_cpu = self.process.cpu_percent()
        
        error = None
        success = True
        
        try:
            yield
        except Exception as e:
            error = str(e)
            success = False
            raise
        finally:
            duration_ms = (time.time() - start_time) * 1000
            end_memory = self.process.memory_info().rss / 1024 / 1024
            end_cpu = self.process.cpu_percent()
            
            metric = PerformanceMetric(
                operation=operation,
                duration_ms=duration_ms,
                timestamp=datetime.utcnow(),
                memory_usage_mb=end_memory - start_memory,
                cpu_percent=(start_cpu + end_cpu) / 2,
                metadata=metadata or {},
                success=success,
                error=error
            )
            
            self.record_metric(metric)
            
            # Log performance warnings
            benchmark = self.benchmarks.get(operation)
            if benchmark:
                if duration_ms > benchmark.critical_ms:
                    logger.error(f"CRITICAL: {operation} took {duration_ms:.1f}ms "
                               f"(threshold: {benchmark.critical_ms}ms)")
                elif duration_ms > benchmark.warning_ms:
                    logger.warning(f"SLOW: {operation} took {duration_ms:.1f}ms "
                                 f"(threshold: {benchmark.warning_ms}ms)")
    
    def get_operation_stats(self, operation: str, time_window_minutes: int = 60) -> Dict[str, Any]:
        """Get statistics for a specific operation."""
        cutoff_time = datetime.utcnow() - timedelta(minutes=time_window_minutes)
        
        with self.lock:
            recent_metrics = [
                m for m in self.operation_stats[operation]
                if m.timestamp >= cutoff_time
            ]
        
        if not recent_metrics:
            return {"operation": operation, "metrics_count": 0}
        
        durations = [m.duration_ms for m in recent_metrics]
        memory_usage = [m.memory_usage_mb for m in recent_metrics]
        cpu_usage = [m.cpu_percent for m in recent_metrics]
        success_rate = sum(1 for m in recent_metrics if m.success) / len(recent_metrics)
        
        stats = {
            "operation": operation,
            "time_window_minutes": time_window_minutes,
            "metrics_count": len(recent_metrics),
            "success_rate": round(success_rate * 100, 2),
            "duration_ms": {
                "min": round(min(durations), 2),
                "max": round(max(durations), 2),
                "avg": round(sum(durations) / len(durations), 2),
                "p50": round(sorted(durations)[len(durations) // 2], 2),
                "p95": round(sorted(durations)[int(len(durations) * 0.95)], 2),
                "p99": round(sorted(durations)[int(len(durations) * 0.99)], 2) if len(durations) > 10 else round(max(durations), 2)
            },
            "memory_usage_mb": {
                "avg": round(sum(memory_usage) / len(memory_usage), 2),
                "max": round(max(memory_usage), 2)
            },
            "cpu_percent": {
                "avg": round(sum(cpu_usage) / len(cpu_usage), 2),
                "max": round(max(cpu_usage), 2)
            }
        }
        
        # Add benchmark comparison
        benchmark = self.benchmarks.get(operation)
        if benchmark:
            stats["benchmark"] = {
                "target_ms": benchmark.target_ms,
                "warning_ms": benchmark.warning_ms,
                "critical_ms": benchmark.critical_ms,
                "performance_score": self._calculate_performance_score(stats["duration_ms"]["avg"], benchmark)
            }
        
        return stats
    
    def get_system_performance(self) -> Dict[str, Any]:
        """Get current system performance metrics."""
        try:
            # CPU and memory
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Process-specific metrics
            process_memory = self.process.memory_info()
            process_cpu = self.process.cpu_percent()
            
            # Network I/O
            net_io = psutil.net_io_counters()
            
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "system": {
                    "cpu_percent": round(cpu_percent, 2),
                    "memory_percent": round(memory.percent, 2),
                    "memory_available_mb": round(memory.available / 1024 / 1024, 2),
                    "disk_percent": round(disk.percent, 2),
                    "disk_free_gb": round(disk.free / 1024 / 1024 / 1024, 2),
                    "load_average": list(psutil.getloadavg()) if hasattr(psutil, 'getloadavg') else None
                },
                "process": {
                    "memory_mb": round(process_memory.rss / 1024 / 1024, 2),
                    "memory_percent": round(self.process.memory_percent(), 2),
                    "cpu_percent": round(process_cpu, 2),
                    "threads": self.process.num_threads(),
                    "connections": len(self.process.connections()),
                    "open_files": len(self.process.open_files())
                },
                "network": {
                    "bytes_sent": net_io.bytes_sent,
                    "bytes_recv": net_io.bytes_recv,
                    "packets_sent": net_io.packets_sent,
                    "packets_recv": net_io.packets_recv
                }
            }
        except Exception as e:
            logger.error(f"Error getting system performance: {e}")
            return {"error": str(e)}
    
    def get_performance_report(self, time_window_minutes: int = 60) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        report = {
            "timestamp": datetime.utcnow().isoformat(),
            "time_window_minutes": time_window_minutes,
            "system_performance": self.get_system_performance(),
            "operations": {},
            "recommendations": []
        }
        
        # Get stats for all monitored operations
        for operation in self.operation_stats.keys():
            report["operations"][operation] = self.get_operation_stats(operation, time_window_minutes)
        
        # Generate recommendations
        report["recommendations"] = self._generate_recommendations(report["operations"])
        
        return report
    
    def _calculate_performance_score(self, avg_duration: float, benchmark: PerformanceBenchmark) -> int:
        """Calculate performance score (0-100) based on benchmark."""
        if avg_duration <= benchmark.target_ms:
            return 100
        elif avg_duration <= benchmark.warning_ms:
            # Linear scale from 100 to 70
            ratio = (avg_duration - benchmark.target_ms) / (benchmark.warning_ms - benchmark.target_ms)
            return int(100 - (ratio * 30))
        elif avg_duration <= benchmark.critical_ms:
            # Linear scale from 70 to 30
            ratio = (avg_duration - benchmark.warning_ms) / (benchmark.critical_ms - benchmark.warning_ms)
            return int(70 - (ratio * 40))
        else:
            # Below 30 for critical performance
            return max(10, int(30 - ((avg_duration - benchmark.critical_ms) / benchmark.critical_ms * 20)))
    
    def _generate_recommendations(self, operations: Dict[str, Any]) -> List[str]:
        """Generate performance recommendations based on metrics."""
        recommendations = []
        
        for operation, stats in operations.items():
            if stats.get("metrics_count", 0) == 0:
                continue
            
            # Check success rate
            success_rate = stats.get("success_rate", 100)
            if success_rate < 95:
                recommendations.append(
                    f"⚠️  {operation}: Low success rate ({success_rate:.1f}%) - investigate error patterns"
                )
            
            # Check performance against benchmarks
            benchmark_info = stats.get("benchmark")
            if benchmark_info:
                score = benchmark_info.get("performance_score", 100)
                avg_duration = stats["duration_ms"]["avg"]
                
                if score < 30:
                    recommendations.append(
                        f"🚨 {operation}: Critical performance issue ({avg_duration:.1f}ms avg, "
                        f"target: {benchmark_info['target_ms']}ms) - immediate optimization needed"
                    )
                elif score < 70:
                    recommendations.append(
                        f"⚠️  {operation}: Performance degraded ({avg_duration:.1f}ms avg, "
                        f"target: {benchmark_info['target_ms']}ms) - consider optimization"
                    )
                elif score >= 95:
                    recommendations.append(
                        f"✅ {operation}: Excellent performance ({avg_duration:.1f}ms avg)"
                    )
            
            # Check memory usage
            memory_stats = stats.get("memory_usage_mb", {})
            if memory_stats.get("max", 0) > 100:
                recommendations.append(
                    f"💾 {operation}: High memory usage ({memory_stats['max']:.1f}MB max) - "
                    "consider memory optimization"
                )
        
        # System-level recommendations
        system = operations.get("system_performance", {}).get("system", {})
        if system:
            if system.get("cpu_percent", 0) > 80:
                recommendations.append("🔥 System: High CPU usage - consider scaling or optimization")
            if system.get("memory_percent", 0) > 85:
                recommendations.append("💾 System: High memory usage - monitor for memory leaks")
            if system.get("disk_percent", 0) > 90:
                recommendations.append("💽 System: Low disk space - cleanup required")
        
        return recommendations if recommendations else ["✅ All operations performing within normal parameters"]


# Global performance monitor instance
performance_monitor = PerformanceMonitor()


def monitor_performance(operation: str, metadata: Optional[Dict[str, Any]] = None):
    """Decorator to monitor function performance."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            with performance_monitor.measure_operation(operation, metadata):
                return await func(*args, **kwargs)
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            with performance_monitor.measure_operation(operation, metadata):
                return func(*args, **kwargs)
        
        # Return appropriate wrapper based on function type
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def get_performance_stats(operation: str = None, time_window_minutes: int = 60) -> Dict[str, Any]:
    """Get performance statistics."""
    if operation:
        return performance_monitor.get_operation_stats(operation, time_window_minutes)
    else:
        return performance_monitor.get_performance_report(time_window_minutes)


def benchmark_operation(operation: str, target_ms: float, warning_ms: float, critical_ms: float, description: str):
    """Set performance benchmark for an operation."""
    performance_monitor.benchmarks[operation] = PerformanceBenchmark(
        operation, target_ms, warning_ms, critical_ms, description
    )


# Database query monitoring
@monitor_performance("database_query")
def monitor_database_query(query_func):
    """Monitor database query performance."""
    return query_func


# API call monitoring  
@monitor_performance("xero_api_call")
def monitor_xero_api_call(api_func):
    """Monitor Xero API call performance."""
    return api_func


# Redis operation monitoring
@monitor_performance("redis_operation")
def monitor_redis_operation(redis_func):
    """Monitor Redis operation performance."""
    return redis_func