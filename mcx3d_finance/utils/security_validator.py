"""
Security configuration validator for MCX3D Finance.
Validates security settings on application startup.
"""
import os
import logging
from typing import Dict, List, Tuple

from ..core.config import get_security_config, get_database_url, settings

logger = logging.getLogger(__name__)


class SecurityValidationError(Exception):
    """Raised when security validation fails."""
    pass


def validate_secret_key(secret_key: str) -> <PERSON><PERSON>[bool, List[str]]:
    """
    Validate JWT secret key meets security requirements.
    
    Args:
        secret_key: The secret key to validate
        
    Returns:
        Tuple of (is_valid, list_of_issues)
    """
    issues = []
    
    if not secret_key:
        issues.append("Secret key is not set")
        return False, issues
    
    if len(secret_key) < 32:
        issues.append(f"Secret key is too short ({len(secret_key)} chars, minimum 32 required)")
    
    # Check for weak patterns
    weak_patterns = [
        "secret", "password", "12345", "admin", "test",
        "your-secret-key", "changeme", "default"
    ]
    
    for pattern in weak_patterns:
        if pattern.lower() in secret_key.lower():
            issues.append(f"Secret key contains weak pattern: '{pattern}'")
    
    # Check complexity
    has_upper = any(c.isupper() for c in secret_key)
    has_lower = any(c.islower() for c in secret_key)
    has_digit = any(c.isdigit() for c in secret_key)
    has_special = any(c in "!@#$%^&*()-_=+[]{}|;:,.<>?" for c in secret_key)
    
    if not all([has_upper, has_lower, has_digit, has_special]):
        missing = []
        if not has_upper:
            missing.append("uppercase letters")
        if not has_lower:
            missing.append("lowercase letters")
        if not has_digit:
            missing.append("digits")
        if not has_special:
            missing.append("special characters")
        issues.append(f"Secret key lacks complexity (missing: {', '.join(missing)})")
    
    return len(issues) == 0, issues


def validate_database_url(database_url: str) -> Tuple[bool, List[str]]:
    """
    Validate database URL doesn't contain hardcoded credentials.
    
    Args:
        database_url: The database URL to validate
        
    Returns:
        Tuple of (is_valid, list_of_issues)
    """
    issues = []
    
    if not database_url:
        issues.append("Database URL is not set")
        return False, issues
    
    # Check for common weak credentials
    weak_credentials = [
        "user:password", "admin:admin", "root:root", 
        "postgres:postgres", "test:test", "default:default"
    ]
    
    for cred in weak_credentials:
        if cred in database_url:
            issues.append(f"Database URL contains weak credentials: '{cred}'")
    
    # Check if using localhost in production
    if not settings.debug and "localhost" in database_url:
        issues.append("Database URL uses localhost in non-debug mode")
    
    return len(issues) == 0, issues


def validate_oauth_transport() -> Tuple[bool, List[str]]:
    """
    Validate OAuth transport security settings.
    
    Returns:
        Tuple of (is_valid, list_of_issues)
    """
    issues = []
    
    # Check if OAUTHLIB_INSECURE_TRANSPORT is set
    if os.environ.get('OAUTHLIB_INSECURE_TRANSPORT') == '1':
        if not settings.debug:
            issues.append("OAUTHLIB_INSECURE_TRANSPORT is enabled in non-debug mode")
        else:
            logger.warning("OAUTHLIB_INSECURE_TRANSPORT is enabled (development mode)")
    
    return len(issues) == 0, issues


def validate_encryption_key(encryption_key: str) -> Tuple[bool, List[str]]:
    """
    Validate Fernet encryption key.
    
    Args:
        encryption_key: The encryption key to validate
        
    Returns:
        Tuple of (is_valid, list_of_issues)
    """
    issues = []
    
    if not encryption_key:
        # Encryption key is optional but recommended
        logger.warning("Encryption key not set - OAuth tokens will use auto-generated key")
        return True, []
    
    try:
        from cryptography.fernet import Fernet
        # Try to create a Fernet instance to validate the key format
        Fernet(encryption_key.encode() if isinstance(encryption_key, str) else encryption_key)
    except Exception as e:
        issues.append(f"Invalid Fernet encryption key format: {str(e)}")
    
    return len(issues) == 0, issues


def validate_security_configuration() -> Dict[str, any]:
    """
    Perform comprehensive security validation.
    
    Returns:
        Dictionary with validation results
        
    Raises:
        SecurityValidationError: If critical security issues are found
    """
    results = {
        "valid": True,
        "errors": [],
        "warnings": []
    }
    
    try:
        # Get security configuration
        security_config = get_security_config()
        
        # Validate secret key
        secret_valid, secret_issues = validate_secret_key(security_config.get("secret_key", ""))
        if not secret_valid:
            results["valid"] = False
            results["errors"].extend([f"SECRET_KEY: {issue}" for issue in secret_issues])
        
        # Validate database URL
        try:
            database_url = get_database_url()
            db_valid, db_issues = validate_database_url(database_url)
            if not db_valid:
                results["valid"] = False
                results["errors"].extend([f"DATABASE_URL: {issue}" for issue in db_issues])
        except ValueError as e:
            results["valid"] = False
            results["errors"].append(f"DATABASE_URL: {str(e)}")
        
        # Validate OAuth transport
        oauth_valid, oauth_issues = validate_oauth_transport()
        if not oauth_valid:
            results["valid"] = False
            results["errors"].extend([f"OAUTH: {issue}" for issue in oauth_issues])
        
        # Validate encryption key
        enc_valid, enc_issues = validate_encryption_key(security_config.get("encryption_key", ""))
        if not enc_valid:
            results["valid"] = False
            results["errors"].extend([f"ENCRYPTION_KEY: {issue}" for issue in enc_issues])
        
        # Additional security checks
        if settings.debug:
            results["warnings"].append("Application is running in DEBUG mode")
        
        if not security_config.get("encryption_key"):
            results["warnings"].append("No encryption key set - using auto-generated key")
        
    except Exception as e:
        results["valid"] = False
        results["errors"].append(f"Validation error: {str(e)}")
    
    # Log results
    if results["valid"]:
        logger.info("✅ Security configuration validation passed")
    else:
        logger.error("❌ Security configuration validation failed")
        for error in results["errors"]:
            logger.error(f"  - {error}")
    
    for warning in results["warnings"]:
        logger.warning(f"  ⚠️  {warning}")
    
    # Raise exception if critical errors found
    if not results["valid"]:
        raise SecurityValidationError(
            "Security validation failed. Please check the errors above and fix your configuration."
        )
    
    return results


def run_security_audit() -> None:
    """
    Run security audit and print results.
    Can be called from command line or during startup.
    """
    print("🔒 MCX3D Finance Security Audit")
    print("=" * 50)
    
    try:
        results = validate_security_configuration()
        
        if results["valid"]:
            print("\n✅ All security checks passed!")
        else:
            print("\n❌ Security issues found:")
            for error in results["errors"]:
                print(f"   - {error}")
        
        if results["warnings"]:
            print("\n⚠️  Warnings:")
            for warning in results["warnings"]:
                print(f"   - {warning}")
        
        print("\n" + "=" * 50)
        
        if not results["valid"]:
            print("\n🔧 How to fix:")
            print("1. Generate secure keys: python -m mcx3d_finance.utils.generate_keys")
            print("2. Set environment variables in .env file")
            print("3. Never commit secrets to version control")
            print("4. Use different keys for each environment")
            
    except Exception as e:
        print(f"\n❌ Security audit failed: {str(e)}")
        raise


if __name__ == "__main__":
    # Run security audit when called directly
    run_security_audit()