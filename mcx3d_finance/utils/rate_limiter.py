"""
Redis-based distributed rate limiting with multiple strategies.
Provides robust rate limiting for API endpoints and user actions.
"""
import logging
import time
from typing import Optional, Dict, Any, Tuple
from datetime import datetime, timezone
from enum import Enum
import redis
import hashlib

from fastapi import HTTPException, Request
from ..core.config import get_security_config

logger = logging.getLogger(__name__)


class RateLimitStrategy(Enum):
    """Rate limiting strategies."""
    FIXED_WINDOW = "fixed_window"
    SLIDING_WINDOW = "sliding_window"
    TOKEN_BUCKET = "token_bucket"
    LEAKY_BUCKET = "leaky_bucket"


class RateLimiter:
    """
    Redis-based distributed rate limiter with multiple strategies.
    
    Features:
    - Multiple rate limiting strategies
    - IP-based and user-based limiting
    - Progressive delays for failed attempts
    - Whitelist/blacklist support
    - Burst handling
    """
    
    def __init__(
        self,
        redis_client: Optional[redis.Redis] = None,
        strategy: RateLimitStrategy = RateLimitStrategy.SLIDING_WINDOW
    ):
        """
        Initialize rate limiter.
        
        Args:
            redis_client: Redis client instance
            strategy: Rate limiting strategy to use
        """
        self.security_config = get_security_config()
        self.strategy = strategy
        
        # Redis connection
        if redis_client:
            self.redis = redis_client
        else:
            redis_url = self.security_config.get("redis_url", "redis://localhost:6379/0")
            self.redis = redis.from_url(redis_url, decode_responses=True)
        
        # Rate limit configurations
        self.limits = {
            "api_default": {"calls": 100, "period": 60},  # 100 calls per minute
            "api_authenticated": {"calls": 1000, "period": 60},  # 1000 calls per minute
            "auth_login": {"calls": 5, "period": 300},  # 5 login attempts per 5 minutes
            "auth_register": {"calls": 3, "period": 3600},  # 3 registrations per hour
            "password_reset": {"calls": 3, "period": 3600},  # 3 resets per hour
            "report_generation": {"calls": 10, "period": 300},  # 10 reports per 5 minutes
            "data_export": {"calls": 5, "period": 3600},  # 5 exports per hour
        }
        
        # Progressive delay configuration for failed auth
        self.auth_delays = [0, 1, 2, 5, 10, 30, 60]  # Seconds
        
        # Load custom limits from config
        custom_limits = self.security_config.get("rate_limits", {})
        self.limits.update(custom_limits)
    
    async def check_rate_limit(
        self,
        key: str,
        limit_type: str = "api_default",
        identifier: Optional[str] = None,
        cost: int = 1
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Check if rate limit is exceeded.
        
        Args:
            key: Rate limit key (e.g., user_id, IP address)
            limit_type: Type of limit to apply
            identifier: Additional identifier for composite keys
            cost: Cost of this request (for weighted limits)
            
        Returns:
            Tuple of (allowed, info) where info contains limit details
        """
        if limit_type not in self.limits:
            limit_type = "api_default"
        
        limit_config = self.limits[limit_type]
        calls_allowed = limit_config["calls"]
        period = limit_config["period"]
        
        # Create composite key if identifier provided
        if identifier:
            key = f"{key}:{identifier}"
        
        # Add limit type to key
        redis_key = f"rate_limit:{limit_type}:{key}"
        
        # Check whitelist/blacklist
        if self._is_whitelisted(key):
            return True, {"whitelisted": True}
        
        if self._is_blacklisted(key):
            return False, {"blacklisted": True, "retry_after": 3600}
        
        # Apply rate limiting based on strategy
        if self.strategy == RateLimitStrategy.SLIDING_WINDOW:
            allowed, info = await self._sliding_window_limit(
                redis_key, calls_allowed, period, cost
            )
        elif self.strategy == RateLimitStrategy.TOKEN_BUCKET:
            allowed, info = await self._token_bucket_limit(
                redis_key, calls_allowed, period, cost
            )
        else:  # Fixed window
            allowed, info = await self._fixed_window_limit(
                redis_key, calls_allowed, period, cost
            )
        
        # Add limit info
        info.update({
            "limit": calls_allowed,
            "period": period,
            "limit_type": limit_type
        })
        
        # Log if limit exceeded
        if not allowed:
            logger.warning(
                f"Rate limit exceeded for {key} on {limit_type}: "
                f"{info.get('used', 0)}/{calls_allowed} in {period}s"
            )
        
        return allowed, info
    
    async def _sliding_window_limit(
        self,
        key: str,
        limit: int,
        period: int,
        cost: int
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Sliding window rate limiting using Redis sorted sets.
        
        Most accurate but slightly more complex implementation.
        """
        now = time.time()
        window_start = now - period
        
        # Lua script for atomic sliding window check
        lua_script = """
        local key = KEYS[1]
        local now = tonumber(ARGV[1])
        local window_start = tonumber(ARGV[2])
        local limit = tonumber(ARGV[3])
        local cost = tonumber(ARGV[4])
        local period = tonumber(ARGV[5])
        
        -- Remove old entries
        redis.call('ZREMRANGEBYSCORE', key, '-inf', window_start)
        
        -- Count current entries
        local current = redis.call('ZCARD', key)
        
        if current + cost <= limit then
            -- Add new entries
            for i=1,cost do
                redis.call('ZADD', key, now, now .. ':' .. i .. ':' .. math.random())
            end
            redis.call('EXPIRE', key, period + 1)
            return {1, current + cost, limit - current - cost}
        else
            return {0, current, 0}
        end
        """
        
        # Execute Lua script
        result = self.redis.eval(
            lua_script,
            1,
            key,
            now,
            window_start,
            limit,
            cost,
            period
        )
        
        allowed = bool(result[0])
        used = result[1]
        remaining = result[2]
        
        # Calculate retry after if not allowed
        retry_after = None
        if not allowed:
            # Get oldest entry to calculate when it expires
            oldest = self.redis.zrange(key, 0, 0, withscores=True)
            if oldest:
                retry_after = int(oldest[0][1] + period - now)
        
        return allowed, {
            "used": used,
            "remaining": remaining,
            "retry_after": retry_after
        }
    
    async def _fixed_window_limit(
        self,
        key: str,
        limit: int,
        period: int,
        cost: int
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Fixed window rate limiting using Redis counters.
        
        Simple and efficient but can have burst issues at window boundaries.
        """
        # Get current window
        window = int(time.time() // period)
        window_key = f"{key}:{window}"
        
        # Increment counter
        try:
            current = self.redis.incr(window_key, cost)
            
            # Set expiration on first increment
            if current == cost:
                self.redis.expire(window_key, period + 1)
            
            allowed = current <= limit
            remaining = max(0, limit - current)
            
            # Calculate retry after
            retry_after = None
            if not allowed:
                current_time = time.time()
                window_end = (window + 1) * period
                retry_after = int(window_end - current_time)
            
            return allowed, {
                "used": current,
                "remaining": remaining,
                "retry_after": retry_after
            }
            
        except redis.RedisError as e:
            logger.error(f"Redis error in rate limiting: {e}")
            # Fail open in case of Redis issues
            return True, {"error": "rate_limit_check_failed"}
    
    async def _token_bucket_limit(
        self,
        key: str,
        capacity: int,
        period: int,
        cost: int
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Token bucket rate limiting.
        
        Allows burst traffic up to bucket capacity.
        """
        refill_rate = capacity / period  # Tokens per second
        
        lua_script = """
        local key = KEYS[1]
        local capacity = tonumber(ARGV[1])
        local cost = tonumber(ARGV[2])
        local refill_rate = tonumber(ARGV[3])
        local now = tonumber(ARGV[4])
        
        local bucket = redis.call('HMGET', key, 'tokens', 'last_refill')
        local tokens = tonumber(bucket[1]) or capacity
        local last_refill = tonumber(bucket[2]) or now
        
        -- Calculate tokens to add
        local elapsed = now - last_refill
        local tokens_to_add = elapsed * refill_rate
        tokens = math.min(capacity, tokens + tokens_to_add)
        
        if tokens >= cost then
            tokens = tokens - cost
            redis.call('HMSET', key, 'tokens', tokens, 'last_refill', now)
            redis.call('EXPIRE', key, 3600)
            return {1, tokens, capacity - tokens}
        else
            redis.call('HMSET', key, 'tokens', tokens, 'last_refill', now)
            redis.call('EXPIRE', key, 3600)
            local wait_time = (cost - tokens) / refill_rate
            return {0, tokens, wait_time}
        end
        """
        
        result = self.redis.eval(
            lua_script,
            1,
            key,
            capacity,
            cost,
            refill_rate,
            time.time()
        )
        
        allowed = bool(result[0])
        tokens_remaining = result[1]
        retry_after = None if allowed else int(result[2])
        
        return allowed, {
            "tokens_remaining": tokens_remaining,
            "capacity": capacity,
            "retry_after": retry_after
        }
    
    def get_progressive_delay(self, key: str, action: str) -> int:
        """
        Get progressive delay for failed authentication attempts.
        
        Args:
            key: User identifier or IP
            action: Action type (e.g., 'login', 'password_reset')
            
        Returns:
            Delay in seconds
        """
        fail_key = f"auth_fails:{action}:{key}"
        
        # Increment failure count
        failures = self.redis.incr(fail_key)
        self.redis.expire(fail_key, 3600)  # Reset after 1 hour
        
        # Get delay based on failure count
        delay_index = min(failures - 1, len(self.auth_delays) - 1)
        delay = self.auth_delays[delay_index]
        
        if delay > 0:
            logger.info(f"Progressive delay of {delay}s for {key} after {failures} failures")
        
        return delay
    
    def reset_progressive_delay(self, key: str, action: str):
        """Reset progressive delay counter after successful action."""
        fail_key = f"auth_fails:{action}:{key}"
        self.redis.delete(fail_key)
    
    def add_to_blacklist(self, key: str, duration: int = 3600):
        """Add key to blacklist for specified duration."""
        blacklist_key = f"blacklist:{key}"
        self.redis.setex(blacklist_key, duration, "1")
        logger.warning(f"Added {key} to blacklist for {duration} seconds")
    
    def add_to_whitelist(self, key: str):
        """Add key to whitelist (permanent unless removed)."""
        self.redis.sadd("whitelist", key)
        logger.info(f"Added {key} to whitelist")
    
    def remove_from_whitelist(self, key: str):
        """Remove key from whitelist."""
        self.redis.srem("whitelist", key)
        logger.info(f"Removed {key} from whitelist")
    
    def _is_blacklisted(self, key: str) -> bool:
        """Check if key is blacklisted."""
        return bool(self.redis.exists(f"blacklist:{key}"))
    
    def _is_whitelisted(self, key: str) -> bool:
        """Check if key is whitelisted."""
        return self.redis.sismember("whitelist", key)
    
    def get_client_ip(self, request: Request) -> str:
        """
        Extract client IP from request, considering proxies.
        
        Args:
            request: FastAPI request object
            
        Returns:
            Client IP address
        """
        # Check X-Forwarded-For header (for proxies/load balancers)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # Take the first IP in the chain
            return forwarded_for.split(",")[0].strip()
        
        # Check X-Real-IP header
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fall back to direct connection
        return request.client.host
    
    def create_composite_key(self, *parts) -> str:
        """
        Create a composite key from multiple parts.
        
        Useful for creating keys like "user:ip" or "org:user:action".
        """
        return ":".join(str(part) for part in parts if part)
    
    async def check_and_raise(
        self,
        key: str,
        limit_type: str = "api_default",
        identifier: Optional[str] = None,
        cost: int = 1
    ):
        """
        Check rate limit and raise HTTPException if exceeded.
        
        Convenience method for use in FastAPI endpoints.
        """
        allowed, info = await self.check_rate_limit(key, limit_type, identifier, cost)
        
        if not allowed:
            retry_after = info.get("retry_after", 60)
            
            raise HTTPException(
                status_code=429,
                detail={
                    "error": "rate_limit_exceeded",
                    "message": f"Rate limit exceeded for {limit_type}",
                    "retry_after": retry_after,
                    "limit": info.get("limit"),
                    "period": info.get("period")
                },
                headers={"Retry-After": str(retry_after)}
            )


# Global rate limiter instances with different strategies
rate_limiter = RateLimiter(strategy=RateLimitStrategy.SLIDING_WINDOW)
api_limiter = RateLimiter(strategy=RateLimitStrategy.TOKEN_BUCKET)
auth_limiter = RateLimiter(strategy=RateLimitStrategy.SLIDING_WINDOW)