# MCX3D LTD Financial Documentation System

## Overview

This system generates comprehensive financial documentation for MCX3D LTD (ModularCX) using real-time data from Xero. It produces professional, NASDAQ-compliant financial reports suitable for investors, board meetings, and regulatory compliance.

## Generated Documents

The system generates the following documents:

### 1. Executive Package
- **Executive Summary**: Company overview, financial highlights, and key metrics
- **Management Discussion & Analysis (MD&A)**: Performance review and strategic outlook
- **Financial Dashboard**: Visual KPIs and trend analysis

### 2. Financial Statements
- **Balance Sheet**: Assets, liabilities, and equity position
- **Income Statement**: Revenue, expenses, and profitability
- **Cash Flow Statement**: Operating, investing, and financing activities
- **Statement of Changes in Equity**: Equity movements

### 3. Supporting Documentation
- **Notes to Financial Statements**: Accounting policies and detailed explanations
- **Financial Ratios Analysis**: Liquidity, profitability, and efficiency metrics
- **Reconciliation Reports**: Data integrity and accuracy verification

### 4. Valuation Reports (Optional)
- **DCF Valuation**: Discounted cash flow analysis
- **SaaS Metrics**: MRR, ARR, churn, CAC, LTV analysis
- **Multiples Valuation**: Industry benchmark comparisons

## Quick Start

### Prerequisites

1. **Docker & Docker Compose** installed
2. **Xero Developer Account** with API credentials
3. **Access Token** for your Xero organization

### Setup

1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-org/mcx3d_financials.git
   cd mcx3d_financials/v2
   ```

2. **Configure Xero credentials**:
   ```bash
   cp .env.example .env.development
   ```
   
   Edit `.env.development` and add your Xero credentials:
   ```
   XERO_CLIENT_ID=your-client-id
   XERO_CLIENT_SECRET=your-client-secret
   XERO_ACCESS_TOKEN=your-access-token
   XERO_REFRESH_TOKEN=your-refresh-token
   XERO_TENANT_ID=your-tenant-id
   ```

3. **Launch the application**:
   ```bash
   ./scripts/launch_with_docs.sh
   ```

4. **Generate financial documents**:
   ```bash
   ./scripts/generate_financial_docs.sh
   ```

## Detailed Usage

### CLI Commands

The system provides a comprehensive CLI for document generation:

```bash
# Generate complete financial package
docker-compose exec web python -m mcx3d_finance.cli financial-docs generate

# Generate with specific options
docker-compose exec web python -m mcx3d_finance.cli financial-docs generate \
  --output-dir /app/reports \
  --formats pdf excel \
  --include-projections \
  --report-date 2024-04-30

# List generated documents
docker-compose exec web python -m mcx3d_finance.cli financial-docs list

# Clean old reports (keep latest 5)
docker-compose exec web python -m mcx3d_finance.cli financial-docs clean --keep-latest 5
```

### API Endpoints

The system also provides RESTful API endpoints:

```bash
# Generate financial package (async)
POST /api/financial-docs/generate-package
{
  "report_date": "2024-04-30",
  "include_projections": true,
  "formats": ["pdf", "excel"]
}

# Check generation status
GET /api/financial-docs/status/{task_id}

# Download generated package
GET /api/financial-docs/download/{task_id}

# Get executive summary (JSON)
GET /api/financial-docs/executive-summary

# Get specific financial statement
GET /api/financial-docs/financial-statements/balance-sheet?format=pdf
GET /api/financial-docs/financial-statements/income-statement?format=excel
GET /api/financial-docs/financial-statements/cash-flow?format=json

# Get financial KPIs
GET /api/financial-docs/analysis/kpis
```

## Output Structure

Generated documents are saved in the `./reports` directory:

```
reports/
├── MCX3D_Financial_Package_20240730.zip
│   ├── executive_summary/
│   │   ├── MCX3D_Executive_Summary_20240730.pdf
│   │   └── MCX3D_KPI_Dashboard_20240730.xlsx
│   ├── financial_statements/
│   │   ├── MCX3D_Balance_Sheet_20240730.pdf
│   │   ├── MCX3D_Income_Statement_20240730.pdf
│   │   ├── MCX3D_Cash_Flow_20240730.pdf
│   │   └── MCX3D_Financial_Statements_20240730.xlsx
│   ├── notes/
│   │   └── MCX3D_Notes_to_Financials_20240730.pdf
│   ├── valuations/
│   │   ├── MCX3D_DCF_Valuation_20240730.pdf
│   │   └── MCX3D_SaaS_Valuation_20240730.pdf
│   └── README.txt
```

## Company Information

**Legal Name**: MCX3D LTD  
**Trading Name**: ModularCX  
**Company Number**: 13325322  
**Registered Office**: 1 Vincent Square, London, SW1P 2PN  
**Financial Year End**: 30 April

## Key Features

- **Real-time Xero Integration**: Fetches live financial data
- **Professional Formatting**: Branded PDFs with ModularCX styling
- **NASDAQ Compliance**: Meets public company reporting standards
- **Multiple Formats**: PDF, Excel, and JSON output
- **Async Processing**: Background generation for large documents
- **Caching**: Redis caching for performance
- **Security**: OAuth2 authentication and encrypted data

## Troubleshooting

### Common Issues

1. **Xero Authentication Failed**:
   - Ensure your access token is valid
   - Check that the tenant ID matches your organization
   - Verify OAuth scopes include financial data access

2. **No Data Retrieved**:
   - Confirm your Xero organization has financial data
   - Check the reporting period matches available data
   - Verify account mappings in the system

3. **Generation Timeout**:
   - Large datasets may take several minutes
   - Check Docker container resources
   - Monitor Celery worker logs

### Logs

View logs for debugging:
```bash
# API logs
docker-compose logs -f web

# Background worker logs
docker-compose logs -f worker

# Database logs
docker-compose logs -f db
```

## Development

### Running Tests
```bash
docker-compose exec web pytest tests/
```

### Code Quality
```bash
docker-compose exec web black .
docker-compose exec web flake8
docker-compose exec web mypy .
```

## Support

For issues or questions:
- Technical Support: <EMAIL>
- Financial Queries: <EMAIL>

## License

© 2024 MCX3D LTD trading as ModularCX. All rights reserved.