"""
Tests for the authentication endpoints.
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from mcx3d_finance.db.models import User

@pytest.mark.asyncio
class TestAuthEndpoints:
    """
    Test suite for the authentication endpoints.
    """

    def test_login_success(self, test_client: TestClient, test_user: User):
        """
        Test successful login.
        """
        response = test_client.post(
            "/api/auth/login",
            json={"email": test_user.email, "password": "password"},
        )
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data

    def test_login_failure(self, test_client: TestClient, test_user: User):
        """
        Test failed login.
        """
        response = test_client.post(
            "/api/auth/login",
            json={"email": test_user.email, "password": "wrong_password"},
        )
        assert response.status_code == 401

    def test_account_lockout(self, test_client: TestClient, test_user: User):
        """
        Test that the account is locked after 5 failed login attempts.
        """
        for _ in range(5):
            test_client.post(
                "/api/auth/login",
                json={"email": test_user.email, "password": "wrong_password"},
            )

        response = test_client.post(
            "/api/auth/login",
            json={"email": test_user.email, "password": "password"},
        )
        assert response.status_code == 403
        assert "Account locked" in response.text

    def test_refresh_token(self, test_client: TestClient, test_user: User):
        """
        Test the refresh token endpoint.
        """
        # Log in to get a refresh token
        response = test_client.post(
            "/api/auth/login",
            json={"email": test_user.email, "password": "password"},
        )
        refresh_token = response.json()["refresh_token"]

        # Use the refresh token to get a new access token
        response = test_client.post(
            "/api/auth/refresh", json={"refresh_token": refresh_token}
        )
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data

    def test_password_change(self, test_client: TestClient, test_user: User):
        """
        Test the password change endpoint.
        """
        # Log in to get an access token
        response = test_client.post(
            "/api/auth/login",
            json={"email": test_user.email, "password": "password"},
        )
        access_token = response.json()["access_token"]

        # Change the password
        response = test_client.post(
            "/api/auth/password/change",
            headers={"Authorization": f"Bearer {access_token}"},
            json={
                "current_password": "password",
                "new_password": "new_password123!",
            },
        )
        assert response.status_code == 200

        # Log in with the new password
        response = test_client.post(
            "/api/auth/login",
            json={"email": test_user.email, "password": "new_password123!"},
        )
        assert response.status_code == 200
