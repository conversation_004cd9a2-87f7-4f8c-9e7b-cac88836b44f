"""
Tests for the AuthMiddleware.
"""
import pytest
from unittest.mock import patch
from jose import jwt
import pyotp

from mcx3d_finance.api.auth_middleware import (
    create_access_token,
    verify_token,
    hash_password,
    verify_password,
    check_account_lockout,
    record_failed_login,
    generate_mfa_secret,
    verify_mfa_token,
    generate_backup_codes,
    verify_backup_code,
)

class TestAuthMiddleware:
    """
    Test suite for the AuthMiddleware.
    """

    def test_jwt_creation_verification(self):
        """
        Test the creation and verification of JWTs.
        """
        data = {"sub": "test_user"}
        token = create_access_token(data)
        payload = verify_token(token)
        assert payload["sub"] == "test_user"

    def test_password_hashing(self):
        """
        Test that passwords are correctly hashed with bcrypt.
        """
        password = "secure_password"
        hashed = hash_password(password)
        assert verify_password(password, hashed)
        assert not verify_password("wrong_password", hashed)

    def test_account_lockout(self, redis_mock):
        """
        Test that an account is locked after 5 failed login attempts.
        """
        user_id = "lockout_user"

        # Simulate 4 failed logins
        for _ in range(4):
            record_failed_login(user_id)

        is_locked, _ = check_account_lockout(user_id)
        assert not is_locked

        # The 5th failed login should lock the account
        record_failed_login(user_id)
        is_locked, remaining = check_account_lockout(user_id)
        assert is_locked
        assert remaining > 0

    def test_mfa(self, redis_mock):
        """
        Test MFA secret generation, TOTP token verification, and backup codes.
        """
        user_id = "mfa_user"

        # Generate MFA secret
        secret = generate_mfa_secret(user_id)
        totp = pyotp.TOTP(secret)

        # Verify a valid token
        assert verify_mfa_token(user_id, totp.now())

        # Verify an invalid token
        assert not verify_mfa_token(user_id, "123456")

        # Generate and verify backup codes
        backup_codes = generate_backup_codes(user_id)
        assert verify_backup_code(user_id, backup_codes[0])
        assert not verify_backup_code(user_id, "000000")

        # The used backup code should no longer be valid
        assert not verify_backup_code(user_id, backup_codes[0])
