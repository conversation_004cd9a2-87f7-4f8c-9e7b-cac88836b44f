# Quick Start Deployment Guide

This guide provides a streamlined deployment process for getting MCX3D Financials v2 up and running quickly.

## Prerequisites

- <PERSON><PERSON> and Docker Compose installed
- PostgreSQL database (local or cloud)
- Xero developer account with OAuth2 credentials

## 1. <PERSON>lone the Repository

```bash
git clone https://github.com/mcx3d/mcx3d_financials.git
cd mcx3d_financials/v2
```

## 2. Configure Environment

Create a `.env` file in the project root:

```bash
# Basic Configuration
ENVIRONMENT=development
SECRET_KEY=your-secret-key-here
DATABASE_URL=postgresql://user:password@localhost:5432/mcx3d_finance

# Xero OAuth2
XERO_CLIENT_ID=your-xero-client-id
XERO_CLIENT_SECRET=your-xero-client-secret
XERO_REDIRECT_URI=http://localhost:8000/api/auth/xero/callback

# Optional: Redis for caching
REDIS_URL=redis://localhost:6379
```

## 3. Start with Docker Compose

### Development Mode

```bash
# Start all services
docker-compose up -d

# Check logs
docker-compose logs -f

# Access the application
# API: http://localhost:8000
# Health check: http://localhost:8000/health/
```

### Production Mode

```bash
# Use production configuration
docker-compose -f docker-compose.production.yml up -d

# Create database tables
docker-compose exec web alembic upgrade head

# Create admin user
docker-compose exec web mcx3d-finance user create --email <EMAIL> --admin
```

## 4. Initial Setup

### Create Organization

```bash
# Using CLI
docker-compose exec web mcx3d-finance org create --name "My Company" --currency USD

# Or via API
curl -X POST http://localhost:8000/api/organizations \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"name": "My Company", "base_currency": "USD"}'
```

### Connect to Xero

1. Navigate to: `http://localhost:8000/api/auth/xero/connect`
2. Authorize the application in Xero
3. Select your organization
4. Complete the connection

### Load Sample Data (Optional)

```bash
# Load comprehensive sample data
docker-compose exec web mcx3d-finance seed sample-data --org-id 1 --months 12

# Or load from JSON files
docker-compose exec web mcx3d-finance seed from-json --org-id 1 --file sample_data/accounts.json --data-type accounts
```

## 5. Verify Installation

### Health Checks

```bash
# Basic health check
curl http://localhost:8000/health/

# Comprehensive health check
curl http://localhost:8000/health/comprehensive

# Performance metrics
curl http://localhost:8000/health/performance
```

### Test API Authentication

```bash
# Login
TOKEN=$(curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "your-password"}' \
  | jq -r '.access_token')

# Test authenticated endpoint
curl -H "Authorization: Bearer $TOKEN" http://localhost:8000/api/organizations
```

## 6. Generate Reports

### Income Statement

```bash
# Generate JSON report
curl -H "Authorization: Bearer $TOKEN" \
  "http://localhost:8000/api/reports/income-statement?organization_id=1&start_date=2024-01-01&end_date=2024-12-31"

# Generate PDF report
curl -H "Authorization: Bearer $TOKEN" \
  "http://localhost:8000/api/reports/income-statement?organization_id=1&start_date=2024-01-01&end_date=2024-12-31&format=pdf" \
  -o income_statement.pdf
```

### Balance Sheet

```bash
curl -H "Authorization: Bearer $TOKEN" \
  "http://localhost:8000/api/reports/balance-sheet?organization_id=1&as_of_date=2024-12-31"
```

## Common Commands

### Database Management

```bash
# Run migrations
docker-compose exec web alembic upgrade head

# Create new migration
docker-compose exec web alembic revision --autogenerate -m "Description"

# Database shell
docker-compose exec db psql -U mcx3d_user -d mcx3d_finance
```

### User Management

```bash
# Create user
docker-compose exec web mcx3d-finance user create --email <EMAIL>

# List users
docker-compose exec web mcx3d-finance user list

# Reset password
docker-compose exec web mcx3d-finance user reset-password --email <EMAIL>
```

### Data Management

```bash
# Sync from Xero
docker-compose exec web mcx3d-finance xero sync --org-id 1

# Export data
docker-compose exec web mcx3d-finance export --org-id 1 --format json --output exports/

# Clear cache
docker-compose exec web mcx3d-finance cache clear
```

## Troubleshooting

### Container Issues

```bash
# View logs
docker-compose logs web
docker-compose logs db

# Restart services
docker-compose restart web

# Rebuild containers
docker-compose build --no-cache
docker-compose up -d
```

### Database Issues

```bash
# Check database connection
docker-compose exec web mcx3d-finance db check

# Reset database (WARNING: Deletes all data)
docker-compose exec web mcx3d-finance db reset --confirm
```

### Performance Issues

```bash
# Check performance metrics
curl http://localhost:8000/health/performance | jq

# Enable debug mode
docker-compose exec web mcx3d-finance --debug

# Check Redis cache
docker-compose exec redis redis-cli ping
```

## Next Steps

1. **Configure Security**
   - Set up SSL/TLS certificates
   - Configure CORS origins
   - Enable rate limiting

2. **Set Up Monitoring**
   - Configure application logs
   - Set up health check monitoring
   - Enable performance tracking

3. **Schedule Tasks**
   - Configure automatic Xero sync
   - Set up report generation schedules
   - Enable backup automation

4. **Customize Settings**
   - Configure email notifications
   - Set up custom report templates
   - Adjust performance settings

For detailed configuration options, see the [full deployment guide](./production-deployment.md).