# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Running the Application
```bash
# Start all services (recommended for development)
docker-compose up --build

# Individual components
docker-compose exec web python -m mcx3d_finance.main                    # Basic API server
docker-compose exec web python -m mcx3d_finance.main_with_monitoring    # Production server with monitoring
```

### Testing
```bash
# All tests with coverage (must maintain ≥85% coverage)
docker-compose exec web pytest

# Specific test categories
docker-compose exec web pytest -m unit              # Fast unit tests
docker-compose exec web pytest -m integration       # Database/external service tests
docker-compose exec web pytest -m e2e               # Full system tests
docker-compose exec web pytest -m performance       # Performance benchmarks
docker-compose exec web pytest tests/core/          # Core business logic only
docker-compose exec web pytest tests/security/      # Security-focused tests
docker-compose exec web pytest --cov-fail-under=85  # Enforce coverage minimum

# Single test file
docker-compose exec web pytest tests/core/test_financial_calculators.py -v

# Specific test function
docker-compose exec web pytest tests/core/test_financial_calculators.py::TestUKFinancialCalculator::test_profit_and_loss_generation -v
```

### Code Quality
```bash
# Format code (required before commits)
docker-compose exec web black mcx3d_finance/

# Lint code
docker-compose exec web flake8 mcx3d_finance/

# Type checking
docker-compose exec web mypy mcx3d_finance/
```

### Database Operations
```bash
# Create new migration
docker-compose exec web alembic revision --autogenerate -m "Description"

# Apply migrations
docker-compose exec web alembic upgrade head

# Reset database (development only)
docker-compose down -v && docker-compose up --build
```

### CLI Operations
```bash
# Financial reports
docker-compose exec web python -m mcx3d_finance.cli.main generate income-statement --organization-id <org_id> --period <period> --format pdf
docker-compose exec web python -m mcx3d_finance.cli.main generate balance-sheet --organization-id <org_id> --date <date> --format excel

# Valuations
docker-compose exec web python -m mcx3d_finance.cli.main valuate dcf --config <path_to_config.json>
docker-compose exec web python -m mcx3d_finance.cli.main valuate multiples --config <path_to_config.json>

# Data synchronization
docker-compose exec web python -m mcx3d_finance.cli.main sync xero --org-id <org_id> --incremental
```

## Architecture Overview

### Application Entry Points

**Two main entry points** with different purposes:
- `mcx3d_finance/main.py`: Basic FastAPI application for development/testing
- `mcx3d_finance/main_with_monitoring.py`: Production-ready application with comprehensive monitoring, metrics, business intelligence, and audit logging

### Core Architecture Pattern

This is a **financial analytics platform** following a layered architecture:

```
API Layer (mcx3d_finance/api/)
├── FastAPI endpoints with comprehensive security middleware
├── Authentication/authorization with JWT + MFA support
├── Multi-format report generation (PDF, Excel, HTML, JSON)
└── Real-time health monitoring (basic/comprehensive/business levels)

Business Logic Layer (mcx3d_finance/core/)
├── UK FRS 102 compliant financial calculations
├── Valuation models (DCF, multiples, SaaS-specific)
├── Data validation with multi-tier quality assurance
├── Account classification and transaction processing
└── Performance optimization with Redis caching

Data Layer (mcx3d_finance/db/)
├── Multi-tenant PostgreSQL schema with SQLAlchemy ORM
├── Organization → Account → Transaction → Invoice hierarchy
├── Bank reconciliation and transaction matching
└── Comprehensive audit trails and sync status tracking

Integration Layer (mcx3d_finance/integrations/)
├── Xero OAuth 2.0 with intelligent rate limiting
├── Real-time webhook processing and data synchronization
├── MCP (Model Control Protocol) for enhanced data processing
└── Fault-tolerant design with circuit breakers and retry logic

Monitoring & Security (mcx3d_finance/monitoring/, mcx3d_finance/security/)
├── Prometheus metrics with business intelligence
├── Correlation ID tracking and structured logging
├── Multi-channel alerting (email, Slack, PagerDuty)
└── Enterprise security (CORS, CSRF, rate limiting, encryption)
```

### Key Design Patterns

**Multi-Tenant Architecture**: All data operations are organization-scoped. The `Organization` model is central with relationships to all financial entities.

**UK Financial Compliance**: The system is built around FRS 102 (UK GAAP) standards. All financial calculations in `core/financial_calculators.py` and account classifications in `core/account_classifications.py` follow UK reporting requirements.

**Async Processing**: Heavy operations (reports, valuations, data imports) use Celery background tasks. Check `tasks/` directory for task definitions.

**External Integration Strategy**: Xero integration follows OAuth 2.0 with sophisticated error handling, rate limiting, and real-time synchronization via webhooks.

**Security-First Design**: Enterprise-grade security with field-level encryption, audit logging, MFA support, and comprehensive middleware stack.

## Critical Business Logic

### Financial Calculations
- **Location**: `core/financial_calculators.py` - `UKFinancialCalculator` class
- **Purpose**: Core financial statement generation and SaaS KPI calculations
- **Key Methods**: `generate_profit_and_loss()`, `calculate_saas_kpis()`
- **Compliance**: Strictly follows UK FRS 102 accounting standards

### Valuation Models
- **DCF**: `core/valuation/dcf.py` - Multi-scenario discounted cash flow analysis
- **Multiples**: `core/valuation/multiples.py` - Industry comparative valuation
- **SaaS**: `core/valuation/saas_valuation.py` - SaaS-specific metrics and models

### Data Validation
- **Engine**: `core/data_validation.py` - Multi-tier validation with quality scoring
- **Types**: Financial integrity, business rules, regulatory compliance, data freshness
- **Integration**: Real-time validation during import and processing

### Xero Integration
- **Flow**: OAuth 2.0 → Data Import → Validation → Storage → Real-time Sync
- **Rate Limiting**: Intelligent queuing in `integrations/xero_rate_limiter.py`
- **Error Handling**: Circuit breaker pattern in `integrations/xero_retry.py`

## Configuration & Environment

### Environment Detection
The application automatically detects environment from `ENVIRONMENT`, `ENV`, or `APP_ENV` variables (development/staging/production/testing) and loads appropriate configuration from `config/` directory.

### Security Configuration
All sensitive values MUST be stored in environment variables. Use `python -m mcx3d_finance.utils.generate_keys` to generate secure keys for JWT, encryption, etc.

### Database Configuration
- **Primary**: PostgreSQL with connection pooling and health monitoring
- **Cache**: Redis for sessions, caching, and Celery task queue
- **Monitoring**: Built-in health checks for all database connections

## Development Guidelines

### Testing Strategy
- **Unit Tests**: Core business logic with isolated component testing
- **Integration Tests**: Database and external service integration
- **E2E Tests**: Complete workflow validation
- **Performance Tests**: Benchmark testing for regression prevention
- **Security Tests**: Authentication, authorization, and security validation

### Data Model Changes
Always use Alembic migrations for database changes. The multi-tenant architecture requires careful consideration of data isolation and foreign key relationships.

### Financial Calculations
When modifying financial calculations, ensure UK FRS 102 compliance. All calculations should be testable with sample data and produce verifiable results.

### External Integrations
Follow the established patterns for fault tolerance: rate limiting, circuit breakers, retry logic, and comprehensive error handling.

### Security Considerations
This system handles sensitive financial data. All new endpoints require authentication, input validation, rate limiting, and audit logging. Field-level encryption is available for sensitive data fields.

### Performance
The system includes comprehensive caching (Redis), query optimization, and background processing. Monitor performance impact of changes using the built-in metrics and profiling tools.

## Monitoring & Observability

The production application (`main_with_monitoring.py`) includes:
- **Metrics Server**: Prometheus metrics on port 8001
- **Structured Logging**: Correlation ID tracking across requests
- **Health Checks**: Multi-tier health monitoring (basic/comprehensive/business)
- **Business Intelligence**: KPI collection and anomaly detection
- **Alerting**: Multi-channel alerts with intelligent deduplication

Monitor business metrics through `/monitoring/business-dashboard` endpoint for KPIs, anomalies, and health scoring.