"""
Test suite for PDF generation fixes.
Tests the enhanced logging, validation, and error recovery mechanisms.
"""

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, MagicMock
from datetime import datetime

from mcx3d_finance.reporting.generator import ReportGenerator
from mcx3d_finance.exceptions import ReportGenerationError


class TestPDFGenerationFixes:
    """Test suite for PDF generation enhancements."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test outputs."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    @pytest.fixture
    def report_generator(self):
        """Create a ReportGenerator instance."""
        return ReportGenerator()
    
    @pytest.fixture
    def sample_income_statement(self):
        """Sample income statement data."""
        return {
            "header": {
                "company_name": "Test Company Inc.",
                "statement_title": "INCOME STATEMENT",
                "period_description": "Three Months Ended December 31, 2023",
                "currency": "USD",
                "amounts_in": "thousands"
            },
            "revenue": {
                "total": 150000,
                "breakdown": {
                    "product_sales": 100000,
                    "service_revenue": 50000
                }
            },
            "cost_of_revenue": {
                "total": 60000
            },
            "gross_profit": {
                "amount": 90000,
                "margin": 0.60
            },
            "operating_expenses": {
                "research_development": 20000,
                "sales_marketing": 15000,
                "general_administrative": 10000,
                "total": 45000
            },
            "operating_income": {
                "amount": 45000,
                "margin": 0.30
            },
            "non_operating_income_expense": {
                "interest_income": 1000,
                "interest_expense": -2000,
                "other": 500,
                "total": -500
            },
            "income_before_taxes": {
                "amount": 44500
            },
            "income_tax_expense": {
                "amount": 11125,
                "rate": 0.25
            },
            "net_income": {
                "amount": 33375,
                "margin": 0.2225
            },
            "earnings_per_share": {
                "basic": 3.34,
                "diluted": 3.30,
                "shares_outstanding_basic": 10000000,
                "shares_outstanding_diluted": 10100000
            }
        }
    
    @pytest.fixture
    def empty_data(self):
        """Empty data for testing validation."""
        return {}
    
    @pytest.fixture
    def invalid_data(self):
        """Invalid data structure for testing."""
        return "This is not a dictionary"
    
    @pytest.fixture
    def minimal_data(self):
        """Minimal valid data with only header."""
        return {
            "header": {
                "company_name": "Minimal Company",
                "statement_title": "TEST STATEMENT"
            }
        }
    
    def test_pdf_generation_with_valid_data(self, report_generator, sample_income_statement, temp_dir):
        """Test PDF generation with valid data produces non-empty file."""
        output_path = os.path.join(temp_dir, "test_income_statement.pdf")
        
        # Generate PDF
        report_generator.generate_income_statement_pdf(sample_income_statement, output_path)
        
        # Verify file exists and has content
        assert Path(output_path).exists()
        file_size = Path(output_path).stat().st_size
        assert file_size > 100  # Should be much larger than 22 bytes
        
        # Verify it's a valid PDF (starts with %PDF)
        with open(output_path, 'rb') as f:
            header = f.read(4)
            assert header == b'%PDF'
    
    def test_pdf_generation_with_empty_data(self, report_generator, empty_data, temp_dir):
        """Test PDF generation with empty data raises appropriate error."""
        output_path = os.path.join(temp_dir, "test_empty.pdf")
        
        with pytest.raises(ValueError, match="income_statement data is empty"):
            report_generator.generate_income_statement_pdf(empty_data, output_path)
        
        # Verify no file was created
        assert not Path(output_path).exists()
    
    def test_pdf_generation_with_invalid_data_type(self, report_generator, invalid_data, temp_dir):
        """Test PDF generation with invalid data type raises appropriate error."""
        output_path = os.path.join(temp_dir, "test_invalid.pdf")
        
        with pytest.raises(ValueError, match="income_statement data must be a dictionary"):
            report_generator.generate_income_statement_pdf(invalid_data, output_path)
        
        # Verify no file was created
        assert not Path(output_path).exists()
    
    def test_pdf_generation_with_missing_header(self, report_generator, temp_dir):
        """Test PDF generation with missing header adds default header."""
        data_without_header = {
            "revenue": {"total": 100000},
            "net_income": {"amount": 50000}
        }
        output_path = os.path.join(temp_dir, "test_no_header.pdf")
        
        # Should succeed with default header
        report_generator.generate_income_statement_pdf(data_without_header, output_path)
        
        # Verify file exists and has content
        assert Path(output_path).exists()
        file_size = Path(output_path).stat().st_size
        assert file_size > 100
    
    def test_pdf_generation_creates_directory(self, report_generator, sample_income_statement, temp_dir):
        """Test PDF generation creates missing directories."""
        nested_path = os.path.join(temp_dir, "reports", "2023", "Q4", "income_statement.pdf")
        
        # Directory doesn't exist yet
        assert not Path(nested_path).parent.exists()
        
        # Generate PDF
        report_generator.generate_income_statement_pdf(sample_income_statement, nested_path)
        
        # Verify directory was created and file exists
        assert Path(nested_path).parent.exists()
        assert Path(nested_path).exists()
    
    @patch('mcx3d_finance.reporting.generator.SimpleDocTemplate')
    def test_pdf_generation_handles_build_error(self, mock_doc_class, report_generator, 
                                               sample_income_statement, temp_dir):
        """Test PDF generation handles doc.build() errors properly."""
        output_path = os.path.join(temp_dir, "test_build_error.pdf")
        
        # Mock doc.build to raise an error
        mock_doc = MagicMock()
        mock_doc.build.side_effect = Exception("PDF build failed")
        mock_doc_class.return_value = mock_doc
        
        # Should raise the error
        with pytest.raises(Exception, match="PDF build failed"):
            report_generator.generate_income_statement_pdf(sample_income_statement, output_path)
        
        # Verify cleanup attempted (file should not exist)
        assert not Path(output_path).exists()
    
    def test_balance_sheet_pdf_generation(self, report_generator, temp_dir):
        """Test balance sheet PDF generation with validation."""
        balance_sheet_data = {
            "header": {
                "company_name": "Test Company Inc.",
                "statement_title": "BALANCE SHEET",
                "reporting_date": "December 31, 2023",
                "currency": "USD"
            },
            "assets": {
                "current_assets": {
                    "cash": 50000,
                    "accounts_receivable": 30000,
                    "total_current_assets": 80000
                },
                "non_current_assets": {
                    "property_plant_equipment": 120000,
                    "total_non_current_assets": 120000
                },
                "total_assets": 200000
            },
            "liabilities": {
                "current_liabilities": {
                    "accounts_payable": 20000,
                    "total_current_liabilities": 20000
                },
                "non_current_liabilities": {
                    "long_term_debt": 50000,
                    "total_non_current_liabilities": 50000
                },
                "total_liabilities": 70000
            },
            "equity": {
                "common_stock": 50000,
                "retained_earnings": 80000,
                "total_equity": 130000
            },
            "total_liabilities_equity": 200000
        }
        
        output_path = os.path.join(temp_dir, "test_balance_sheet.pdf")
        report_generator.generate_balance_sheet_pdf(balance_sheet_data, output_path)
        
        # Verify file exists and has content
        assert Path(output_path).exists()
        file_size = Path(output_path).stat().st_size
        assert file_size > 100
    
    def test_cash_flow_pdf_generation(self, report_generator, temp_dir):
        """Test cash flow PDF generation with validation."""
        cash_flow_data = {
            "header": {
                "company_name": "Test Company Inc.",
                "statement_title": "CASH FLOW STATEMENT",
                "period_end": "December 31, 2023",
                "currency": "USD"
            },
            "operating_activities": {
                "net_income": 33375,
                "adjustments": {
                    "depreciation": 5000,
                    "changes_in_working_capital": -2000
                },
                "net_cash_from_operating": 36375
            },
            "investing_activities": {
                "capital_expenditures": -15000,
                "net_cash_from_investing": -15000
            },
            "financing_activities": {
                "debt_repayment": -5000,
                "dividends_paid": -10000,
                "net_cash_from_financing": -15000
            },
            "net_change_in_cash": 6375,
            "beginning_cash": 18625,
            "ending_cash": 25000
        }
        
        output_path = os.path.join(temp_dir, "test_cash_flow.pdf")
        report_generator.generate_cash_flow_pdf(cash_flow_data, output_path)
        
        # Verify file exists and has content
        assert Path(output_path).exists()
        file_size = Path(output_path).stat().st_size
        assert file_size > 100
    
    @patch('mcx3d_finance.reporting.generator.logger')
    def test_enhanced_logging(self, mock_logger, report_generator, sample_income_statement, temp_dir):
        """Test that enhanced logging is working properly."""
        output_path = os.path.join(temp_dir, "test_logging.pdf")
        
        # Generate PDF
        report_generator.generate_income_statement_pdf(sample_income_statement, output_path)
        
        # Verify logging calls were made
        # Check for data validation logging
        mock_logger.debug.assert_any_call("income_statement data structure: ['header', 'revenue', 'cost_of_revenue', 'gross_profit', 'operating_expenses', 'operating_income', 'non_operating_income_expense', 'income_before_taxes', 'income_tax_expense', 'net_income', 'earnings_per_share']")
        
        # Check for story elements logging
        assert any("PDF story contains" in str(call) for call in mock_logger.debug.call_args_list)
        
        # Check for success logging with file size
        assert any("Income statement PDF generated successfully:" in str(call) 
                  for call in mock_logger.info.call_args_list)
    
    def test_very_small_file_warning(self, report_generator, temp_dir):
        """Test warning for suspiciously small PDF files."""
        # Create minimal data that might produce a small PDF
        minimal_data = {
            "header": {
                "company_name": "X",
                "statement_title": "Y"
            }
        }
        
        output_path = os.path.join(temp_dir, "test_small.pdf")
        
        with patch('mcx3d_finance.reporting.generator.logger') as mock_logger:
            report_generator.generate_income_statement_pdf(minimal_data, output_path)
            
            # Check if file size is logged
            info_calls = [str(call) for call in mock_logger.info.call_args_list]
            assert any("Income statement PDF generated successfully:" in call for call in info_calls)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])