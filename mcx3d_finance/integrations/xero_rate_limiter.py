"""
Xero-specific rate limiting implementation.

Tracks and enforces Xero API rate limits:
- 60 API calls per minute
- 5,000 API calls per day (rolling 24-hour window)
"""

import logging
import time
from typing import Optional, Tu<PERSON>, Dict, Any
from datetime import datetime, timedelta, timezone
import redis
from redis.exceptions import RedisError

from ..utils.redis_client import get_redis_client
from ..exceptions.integration import RateLimitError

logger = logging.getLogger(__name__)


class XeroRateLimiter:
    """
    Xero-specific rate limiter that tracks API usage against Xero's limits.
    
    Xero enforces two rate limits:
    1. 60 API calls per minute (sliding window)
    2. 5,000 API calls per day (rolling 24-hour window)
    
    This implementation uses Redis to track usage across all instances.
    """
    
    # Xero rate limits
    MINUTE_LIMIT = 60
    DAILY_LIMIT = 5000
    
    # Time windows in seconds
    MINUTE_WINDOW = 60
    DAILY_WINDOW = 86400  # 24 hours
    
    # Buffer to avoid hitting exact limits
    MINUTE_BUFFER = 5  # Leave 5 calls as buffer
    DAILY_BUFFER = 100  # Leave 100 calls as buffer
    
    def __init__(self, redis_client: Optional[redis.Redis] = None):
        """
        Initialize Xero rate limiter.
        
        Args:
            redis_client: Redis client instance (optional)
        """
        self.redis = redis_client or get_redis_client()
        
        # Effective limits with buffer
        self.effective_minute_limit = self.MINUTE_LIMIT - self.MINUTE_BUFFER
        self.effective_daily_limit = self.DAILY_LIMIT - self.DAILY_BUFFER
    
    def check_rate_limit(self, tenant_id: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Check if an API call is allowed under Xero rate limits.
        
        This method checks both minute and daily limits without consuming quota.
        
        Args:
            tenant_id: Xero tenant ID
            
        Returns:
            Tuple of (allowed, info) where info contains:
                - minute_used: Calls used in current minute
                - minute_remaining: Calls remaining in current minute
                - daily_used: Calls used in last 24 hours
                - daily_remaining: Calls remaining for the day
                - retry_after: Seconds to wait if rate limited
        """
        try:
            # Get current usage
            minute_count = self._get_minute_count(tenant_id)
            daily_count = self._get_daily_count(tenant_id)
            
            # Check minute limit
            minute_allowed = minute_count < self.effective_minute_limit
            minute_remaining = max(0, self.effective_minute_limit - minute_count)
            
            # Check daily limit
            daily_allowed = daily_count < self.effective_daily_limit
            daily_remaining = max(0, self.effective_daily_limit - daily_count)
            
            # Both limits must be satisfied
            allowed = minute_allowed and daily_allowed
            
            # Calculate retry_after if rate limited
            retry_after = None
            if not allowed:
                if not minute_allowed:
                    # Wait until next minute
                    retry_after = self.MINUTE_WINDOW - (int(time.time()) % self.MINUTE_WINDOW)
                elif not daily_allowed:
                    # Need to wait for oldest call to expire from 24-hour window
                    retry_after = self._get_daily_retry_after(tenant_id)
            
            info = {
                "minute_used": minute_count,
                "minute_remaining": minute_remaining,
                "minute_limit": self.effective_minute_limit,
                "daily_used": daily_count,
                "daily_remaining": daily_remaining,
                "daily_limit": self.effective_daily_limit,
                "retry_after": retry_after
            }
            
            if not allowed:
                reason = "minute limit" if not minute_allowed else "daily limit"
                logger.warning(
                    f"Xero rate limit would be exceeded for tenant {tenant_id}: "
                    f"{reason} - minute: {minute_count}/{self.effective_minute_limit}, "
                    f"daily: {daily_count}/{self.effective_daily_limit}"
                )
            
            return allowed, info
            
        except RedisError as e:
            logger.error(f"Redis error checking Xero rate limits: {e}")
            # Fail open - allow the request but log the error
            return True, {
                "error": "rate_limit_check_failed",
                "minute_used": 0,
                "minute_remaining": self.effective_minute_limit,
                "daily_used": 0,
                "daily_remaining": self.effective_daily_limit
            }
    
    def track_api_call(self, tenant_id: str, count: int = 1) -> bool:
        """
        Track an API call for rate limiting.
        
        This should be called AFTER successfully making an API call.
        
        Args:
            tenant_id: Xero tenant ID
            count: Number of API calls to track (default: 1)
            
        Returns:
            True if tracked successfully, False on error
        """
        try:
            now = time.time()
            
            # Track in both minute and daily windows
            self._track_minute_call(tenant_id, now, count)
            self._track_daily_call(tenant_id, now, count)
            
            # Log current usage periodically
            minute_count = self._get_minute_count(tenant_id)
            if minute_count % 10 == 0:  # Log every 10 calls
                daily_count = self._get_daily_count(tenant_id)
                logger.info(
                    f"Xero API usage for tenant {tenant_id}: "
                    f"minute: {minute_count}/{self.effective_minute_limit}, "
                    f"daily: {daily_count}/{self.effective_daily_limit}"
                )
            
            return True
            
        except RedisError as e:
            logger.error(f"Redis error tracking Xero API call: {e}")
            return False
    
    def check_and_track(self, tenant_id: str) -> None:
        """
        Check rate limit and track API call atomically.
        
        This method combines rate limit checking and tracking in one operation.
        Raises RateLimitError if rate limit would be exceeded.
        
        Args:
            tenant_id: Xero tenant ID
            
        Raises:
            RateLimitError: If rate limit would be exceeded
        """
        # First check if we're within limits
        allowed, info = self.check_rate_limit(tenant_id)
        
        if not allowed:
            # Determine which limit was hit
            if info["minute_remaining"] == 0:
                limit_type = "per-minute"
                current = info["minute_used"]
                limit = self.effective_minute_limit
            else:
                limit_type = "daily"
                current = info["daily_used"]
                limit = self.effective_daily_limit
            
            raise RateLimitError(
                f"Xero {limit_type} rate limit exceeded",
                service_name="Xero",
                limit_type=limit_type,
                retry_after=info.get("retry_after", 60),
                current_usage=current,
                limit_value=limit
            )
        
        # We're within limits, track the call
        self.track_api_call(tenant_id)
    
    def get_usage_stats(self, tenant_id: str) -> Dict[str, Any]:
        """
        Get current usage statistics for a tenant.
        
        Args:
            tenant_id: Xero tenant ID
            
        Returns:
            Dictionary with usage statistics
        """
        try:
            minute_count = self._get_minute_count(tenant_id)
            daily_count = self._get_daily_count(tenant_id)
            
            return {
                "tenant_id": tenant_id,
                "minute": {
                    "used": minute_count,
                    "remaining": max(0, self.effective_minute_limit - minute_count),
                    "limit": self.effective_minute_limit,
                    "percentage": (minute_count / self.effective_minute_limit * 100) if self.effective_minute_limit > 0 else 0
                },
                "daily": {
                    "used": daily_count,
                    "remaining": max(0, self.effective_daily_limit - daily_count),
                    "limit": self.effective_daily_limit,
                    "percentage": (daily_count / self.effective_daily_limit * 100) if self.effective_daily_limit > 0 else 0
                },
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except RedisError as e:
            logger.error(f"Redis error getting usage stats: {e}")
            return {
                "tenant_id": tenant_id,
                "error": "stats_unavailable",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    def reset_tenant_limits(self, tenant_id: str) -> bool:
        """
        Reset rate limit counters for a tenant.
        
        WARNING: This should only be used in exceptional circumstances.
        
        Args:
            tenant_id: Xero tenant ID
            
        Returns:
            True if reset successfully, False on error
        """
        try:
            minute_key = self._get_minute_key(tenant_id)
            daily_key = self._get_daily_key(tenant_id)
            
            self.redis.delete(minute_key, daily_key)
            logger.warning(f"Reset Xero rate limits for tenant {tenant_id}")
            
            return True
            
        except RedisError as e:
            logger.error(f"Redis error resetting rate limits: {e}")
            return False
    
    # Private methods for Redis operations
    
    def _get_minute_key(self, tenant_id: str) -> str:
        """Get Redis key for minute-based rate limiting."""
        current_minute = int(time.time() // 60)
        return f"xero:rate_limit:minute:{tenant_id}:{current_minute}"
    
    def _get_daily_key(self, tenant_id: str) -> str:
        """Get Redis key for daily rate limiting."""
        return f"xero:rate_limit:daily:{tenant_id}"
    
    def _get_minute_count(self, tenant_id: str) -> int:
        """Get API calls in current minute."""
        key = self._get_minute_key(tenant_id)
        count = self.redis.get(key)
        return int(count) if count else 0
    
    def _get_daily_count(self, tenant_id: str) -> int:
        """Get API calls in last 24 hours."""
        key = self._get_daily_key(tenant_id)
        now = time.time()
        window_start = now - self.DAILY_WINDOW
        
        # Remove expired entries
        self.redis.zremrangebyscore(key, '-inf', window_start)
        
        # Count remaining entries
        return self.redis.zcard(key)
    
    def _track_minute_call(self, tenant_id: str, timestamp: float, count: int):
        """Track API call in minute window."""
        key = self._get_minute_key(tenant_id)
        
        # Increment counter
        self.redis.incr(key, count)
        
        # Set expiration on first increment
        ttl = self.redis.ttl(key)
        if ttl == -1:  # No expiration set
            self.redis.expire(key, self.MINUTE_WINDOW + 5)  # Add 5 seconds buffer
    
    def _track_daily_call(self, tenant_id: str, timestamp: float, count: int):
        """Track API call in daily window."""
        key = self._get_daily_key(tenant_id)
        
        # Add entries to sorted set with timestamp as score
        for i in range(count):
            # Add unique member with timestamp score
            member = f"{timestamp}:{i}:{id(timestamp)}"
            self.redis.zadd(key, {member: timestamp})
        
        # Set expiration on key
        self.redis.expire(key, self.DAILY_WINDOW + 300)  # Add 5 minutes buffer
    
    def _get_daily_retry_after(self, tenant_id: str) -> int:
        """Calculate retry_after for daily limit."""
        key = self._get_daily_key(tenant_id)
        now = time.time()
        window_start = now - self.DAILY_WINDOW
        
        # Get oldest entry that would need to expire
        oldest_entries = self.redis.zrangebyscore(
            key, 
            window_start, 
            '+inf', 
            start=0, 
            num=self.DAILY_BUFFER + 1,
            withscores=True
        )
        
        if oldest_entries:
            # Calculate when the oldest entry will be outside the 24-hour window
            oldest_timestamp = oldest_entries[0][1]
            retry_after = int(oldest_timestamp + self.DAILY_WINDOW - now)
            return max(60, retry_after)  # At least 1 minute
        
        return 3600  # Default to 1 hour if can't calculate


# Global Xero rate limiter instance
xero_rate_limiter = XeroRateLimiter()