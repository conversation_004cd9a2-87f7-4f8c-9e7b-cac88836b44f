"""
MCP Bridge Service for Xero Integration

This service manages the synchronization between our stored Xero tokens 
and the MCP server, enabling the MCP Xero tools to access our authenticated sessions.
"""

import os
import json
import logging
import tempfile
from typing import Optional, Dict, Any
from datetime import datetime, timezone

from ..db.session import SessionLocal
from ..db.models import Organization
from ..auth.xero_oauth import XeroAuthManager

logger = logging.getLogger(__name__)


class MCPXeroBridge:
    """Bridge service to connect our Xero authentication with MCP tools."""

    def __init__(self, organization_id: int):
        self.organization_id = organization_id
        self.auth_manager = XeroAuthManager()
        self._token_file_path = None

    def _get_organization_tokens(self) -> Optional[Dict[str, Any]]:
        """Retrieve valid tokens for the organization."""
        try:
            db = SessionLocal()
            organization = db.get(Organization, self.organization_id)
            
            if not organization or not organization.xero_token:
                logger.error(f"No tokens found for organization {self.organization_id}")
                return None
            
            # Get fresh token (this will refresh if needed)
            access_token = self.auth_manager.get_valid_token(self.organization_id)
            
            if not access_token:
                logger.error(f"Failed to get valid token for organization {self.organization_id}")
                return None
            
            # Parse the stored token data
            token_data = json.loads(organization.xero_token)
            
            return {
                'access_token': access_token,
                'token_data': token_data,
                'tenant_id': organization.xero_tenant_id,
                'organization_name': organization.name
            }
            
        except Exception as e:
            logger.error(f"Error retrieving organization tokens: {e}")
            return None
        finally:
            if 'db' in locals():
                db.close()

    def create_mcp_environment(self) -> Optional[Dict[str, str]]:
        """Create environment variables for MCP server authentication."""
        tokens = self._get_organization_tokens()
        
        if not tokens:
            return None
        
        # Create environment variables that the MCP server can use
        env_vars = {
            'XERO_ACCESS_TOKEN': tokens['access_token'],
            'XERO_TENANT_ID': tokens['tenant_id'],
            'XERO_CLIENT_ID': os.getenv('XERO_CLIENT_ID', '475EC4359EA4461DBDB16C4282A67410'),
            'XERO_CLIENT_SECRET': os.getenv('XERO_CLIENT_SECRET', 'PKpFkHUvq536w67hsimfuZdmSmtC-Nmd-ALUbnTJviVghKex'),
            'XERO_REDIRECT_URI': os.getenv('XERO_REDIRECT_URI', 'http://localhost:8000/auth/xero/callback'),
            'XERO_SCOPES': os.getenv('XERO_SCOPES', 'accounting.transactions accounting.contacts accounting.reports.read accounting.settings offline_access'),
        }
        
        # Filter out None values
        env_vars = {k: v for k, v in env_vars.items() if v is not None}
        
        logger.info(f"Created MCP environment for organization: {tokens['organization_name']}")
        return env_vars

    def create_token_file(self) -> Optional[str]:
        """Create a temporary token file for MCP server access."""
        tokens = self._get_organization_tokens()
        
        if not tokens:
            return None
        
        try:
            # Create a temporary file with token information
            token_info = {
                'access_token': tokens['access_token'],
                'tenant_id': tokens['tenant_id'],
                'expires_at': tokens['token_data'].get('expires_at'),
                'refresh_token': tokens['token_data'].get('refresh_token'),
                'organization_name': tokens['organization_name'],
                'created_at': datetime.now(timezone.utc).isoformat()
            }
            
            # Create temp file
            fd, self._token_file_path = tempfile.mkstemp(suffix='.json', prefix='xero_mcp_')
            
            with os.fdopen(fd, 'w') as f:
                json.dump(token_info, f, indent=2)
            
            logger.info(f"Created MCP token file: {self._token_file_path}")
            return self._token_file_path
            
        except Exception as e:
            logger.error(f"Error creating token file: {e}")
            return None

    def cleanup_token_file(self):
        """Clean up temporary token file."""
        if self._token_file_path and os.path.exists(self._token_file_path):
            try:
                os.unlink(self._token_file_path)
                logger.info(f"Cleaned up token file: {self._token_file_path}")
            except Exception as e:
                logger.warning(f"Failed to cleanup token file: {e}")
            finally:
                self._token_file_path = None

    def test_connection(self) -> bool:
        """Test if we can create a valid connection for MCP tools."""
        tokens = self._get_organization_tokens()
        
        if not tokens:
            logger.error("No valid tokens available for MCP connection")
            return False
        
        logger.info(f"MCP connection test successful for: {tokens['organization_name']}")
        logger.info(f"Tenant ID: {tokens['tenant_id']}")
        logger.info(f"Token expires: {tokens['token_data'].get('expires_at', 'Unknown')}")
        
        return True

    def get_connection_info(self) -> Dict[str, Any]:
        """Get connection information for debugging."""
        tokens = self._get_organization_tokens()
        
        if not tokens:
            return {"status": "error", "message": "No tokens available"}
        
        return {
            "status": "success",
            "organization_id": self.organization_id,
            "organization_name": tokens['organization_name'],
            "tenant_id": tokens['tenant_id'],
            "has_access_token": bool(tokens['access_token']),
            "token_expires_at": tokens['token_data'].get('expires_at'),
            "scopes": tokens['token_data'].get('scope', [])
        }

    def __enter__(self):
        """Context manager entry."""
        self.create_token_file()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - cleanup resources."""
        self.cleanup_token_file()


def create_mcp_bridge(organization_id: int = 2) -> MCPXeroBridge:
    """Factory function to create MCP bridge for default organization."""
    return MCPXeroBridge(organization_id)


def test_mcp_bridge():
    """Test function for MCP bridge functionality."""
    print("Testing MCP Bridge Connection...")
    
    bridge = create_mcp_bridge(organization_id=2)  # Modular CX
    
    # Test basic connection
    if bridge.test_connection():
        print("✅ Bridge connection test passed")
        
        # Get connection info
        info = bridge.get_connection_info()
        print(f"Organization: {info.get('organization_name')}")
        print(f"Tenant ID: {info.get('tenant_id')}")
        print(f"Has Token: {info.get('has_access_token')}")
        
        # Test environment creation
        env_vars = bridge.create_mcp_environment()
        if env_vars:
            print("✅ MCP environment variables created")
            print(f"Environment keys: {list(env_vars.keys())}")
        else:
            print("❌ Failed to create MCP environment")
            
    else:
        print("❌ Bridge connection test failed")


if __name__ == "__main__":
    test_mcp_bridge()