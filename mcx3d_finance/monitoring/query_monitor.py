"""
Query Performance Monitoring Module
Tracks database queries to identify N+1 problems and performance issues
"""
import time
import logging
from contextlib import contextmanager
from typing import List, Dict, Any, Optional
from collections import defaultdict
from sqlalchemy import event
from sqlalchemy.engine import Engine
from sqlalchemy.pool import Pool

logger = logging.getLogger(__name__)


class QueryMonitor:
    """Monitor and analyze database query performance."""
    
    def __init__(self):
        self.query_count = 0
        self.total_time = 0.0
        self.queries: List[Dict[str, Any]] = []
        self.query_patterns: Dict[str, int] = defaultdict(int)
        self._monitoring = False
        self._start_time = None
    
    def reset(self) -> None:
        """Reset all monitoring statistics."""
        self.query_count = 0
        self.total_time = 0.0
        self.queries = []
        self.query_patterns.clear()
        self._start_time = None
    
    def _extract_pattern(self, statement: str) -> str:
        """Extract query pattern for N+1 detection."""
        # Normalize the SQL statement to identify patterns
        statement = statement.strip().upper()
        
        # Remove specific values to generalize the pattern
        import re
        # Replace quoted strings
        statement = re.sub(r"'[^']*'", "'?'", statement)
        # Replace numbers
        statement = re.sub(r'\b\d+\b', '?', statement)
        # Replace parameter placeholders
        statement = re.sub(r'%\([^)]+\)s', '?', statement)
        
        return statement
    
    def _detect_n1_pattern(self) -> List[str]:
        """Detect potential N+1 query patterns."""
        n1_patterns = []
        
        # Look for patterns that appear many times (likely N+1)
        for pattern, count in self.query_patterns.items():
            if count > 10 and 'WHERE' in pattern:
                # Common N+1 patterns involve WHERE clauses with changing IDs
                if any(keyword in pattern for keyword in ['CONTACT', 'INVOICE', 'TRANSACTION']):
                    n1_patterns.append(f"{pattern} (executed {count} times)")
        
        return n1_patterns
    
    @contextmanager
    def monitor(self):
        """Context manager to monitor queries within a block."""
        if self._monitoring:
            yield self
            return
            
        self._monitoring = True
        self.reset()
        self._start_time = time.time()
        
        # Store original handlers to restore later
        handlers = []
        
        @event.listens_for(Engine, "before_cursor_execute", once=False)
        def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            context._query_start_time = time.time()
        
        @event.listens_for(Engine, "after_cursor_execute", once=False)
        def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            elapsed = time.time() - context._query_start_time
            self.query_count += 1
            self.total_time += elapsed
            
            # Store query details
            query_info = {
                'statement': statement,
                'time': elapsed,
                'parameters': parameters,
                'executemany': executemany
            }
            self.queries.append(query_info)
            
            # Track query patterns
            pattern = self._extract_pattern(statement)
            self.query_patterns[pattern] += 1
            
            # Log slow queries immediately
            if elapsed > 0.1:
                logger.warning(f"Slow query ({elapsed:.3f}s): {statement[:100]}...")
        
        handlers.extend([before_cursor_execute, after_cursor_execute])
        
        try:
            yield self
        finally:
            # Clean up listeners
            event.remove(Engine, "before_cursor_execute", before_cursor_execute)
            event.remove(Engine, "after_cursor_execute", after_cursor_execute)
            
            self._monitoring = False
            
            # Generate summary report
            self._generate_summary()
    
    def _generate_summary(self) -> None:
        """Generate and log performance summary."""
        if not self._start_time:
            return
            
        elapsed_total = time.time() - self._start_time
        
        logger.info("=" * 80)
        logger.info("Query Performance Summary")
        logger.info("=" * 80)
        logger.info(f"Total Queries: {self.query_count}")
        logger.info(f"Total Query Time: {self.total_time:.2f}s")
        logger.info(f"Total Elapsed Time: {elapsed_total:.2f}s")
        logger.info(f"Query Time Percentage: {(self.total_time/elapsed_total*100):.1f}%")
        
        if self.query_count > 0:
            logger.info(f"Average Query Time: {self.total_time/self.query_count:.3f}s")
            
            # Report slow queries
            slow_queries = [q for q in self.queries if q['time'] > 0.1]
            if slow_queries:
                logger.warning(f"\nFound {len(slow_queries)} slow queries (>100ms):")
                for q in sorted(slow_queries, key=lambda x: x['time'], reverse=True)[:5]:
                    logger.warning(f"  - {q['time']:.3f}s: {q['statement'][:80]}...")
            
            # Detect N+1 patterns
            n1_patterns = self._detect_n1_pattern()
            if n1_patterns:
                logger.error("\n⚠️  Potential N+1 Query Patterns Detected:")
                for pattern in n1_patterns:
                    logger.error(f"  - {pattern}")
                logger.error("\nConsider using batch loading or eager loading to resolve.")
            
            # Report most frequent queries
            logger.info("\nMost Frequent Query Patterns:")
            sorted_patterns = sorted(self.query_patterns.items(), key=lambda x: x[1], reverse=True)[:5]
            for pattern, count in sorted_patterns:
                logger.info(f"  - {count}x: {pattern[:80]}...")
        
        logger.info("=" * 80)
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current monitoring metrics."""
        metrics = {
            'query_count': self.query_count,
            'total_query_time': self.total_time,
            'average_query_time': self.total_time / self.query_count if self.query_count > 0 else 0,
            'slow_queries': len([q for q in self.queries if q['time'] > 0.1]),
            'potential_n1_patterns': len(self._detect_n1_pattern())
        }
        return metrics
    
    def export_report(self, filepath: Optional[str] = None) -> str:
        """Export detailed monitoring report."""
        report_lines = [
            "MCX3D Finance - Database Query Performance Report",
            "=" * 80,
            f"Total Queries: {self.query_count}",
            f"Total Query Time: {self.total_time:.2f}s",
            f"Average Query Time: {self.total_time/self.query_count:.3f}s" if self.query_count > 0 else "N/A",
            "",
            "Query Pattern Analysis:",
            "-" * 40
        ]
        
        # Add pattern analysis
        for pattern, count in sorted(self.query_patterns.items(), key=lambda x: x[1], reverse=True):
            report_lines.append(f"{count:5d}x | {pattern[:100]}")
        
        # Add N+1 detection
        n1_patterns = self._detect_n1_pattern()
        if n1_patterns:
            report_lines.extend([
                "",
                "⚠️  N+1 Query Patterns Detected:",
                "-" * 40
            ])
            report_lines.extend(n1_patterns)
        
        # Add slow queries
        slow_queries = [q for q in self.queries if q['time'] > 0.1]
        if slow_queries:
            report_lines.extend([
                "",
                "Slow Queries (>100ms):",
                "-" * 40
            ])
            for q in sorted(slow_queries, key=lambda x: x['time'], reverse=True)[:10]:
                report_lines.append(f"{q['time']:.3f}s | {q['statement'][:100]}")
        
        report = "\n".join(report_lines)
        
        if filepath:
            with open(filepath, 'w') as f:
                f.write(report)
            logger.info(f"Query report exported to: {filepath}")
        
        return report


# Global monitor instance
query_monitor = QueryMonitor()


def enable_query_logging(engine: Engine, verbose: bool = False) -> None:
    """Enable query logging for debugging."""
    @event.listens_for(engine, "before_cursor_execute")
    def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
        if verbose:
            logger.debug(f"Query: {statement}")
            logger.debug(f"Parameters: {parameters}")


def log_pool_status(pool: Pool) -> None:
    """Log connection pool status."""
    logger.info(f"Pool size: {pool.size()}, Checked out: {pool.checkedout()}")