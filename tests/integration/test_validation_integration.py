"""
Integration tests for data validation module.
Tests the complete validation pipeline with realistic data scenarios.
"""

import pytest
import sys
import os
from datetime import datetime, timedelta
import json

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from mcx3d_finance.core.data_validation import (
    DataValidationEngine,
    ValidationSeverity,
    ValidationCategory
)


class TestValidationIntegration:
    """Integration tests for the complete validation pipeline."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.engine = DataValidationEngine()
    
    def test_complete_validation_pipeline_success(self):
        """Test complete validation pipeline with valid data."""
        # Create comprehensive valid data
        data = {
            "balance_sheet": {
                "report_date": "2024-12-31T00:00:00Z",
                "assets": {
                    "current_assets": {
                        "cash_and_cash_equivalents": 150000,
                        "accounts_receivable": 75000,
                        "inventory": 50000
                    },
                    "non_current_assets": {
                        "property_plant_equipment": 300000,
                        "intangible_assets": 25000
                    },
                    "total_assets": 600000
                },
                "liabilities": {
                    "current_liabilities": {
                        "accounts_payable": 40000,
                        "short_term_debt": 60000
                    },
                    "non_current_liabilities": {
                        "long_term_debt": 200000
                    },
                    "total_liabilities": 300000
                },
                "equity": {
                    "stockholders_equity": {
                        "common_stock": 100000,
                        "retained_earnings": 200000
                    },
                    "total_equity": 300000
                }
            },
            "income_statement": {
                "report_date": "2024-12-31T00:00:00Z",
                "revenue": {
                    "product_revenue": 800000,
                    "service_revenue": 200000,
                    "total_revenue": 1000000
                },
                "expenses": {
                    "cost_of_goods_sold": 400000,
                    "operating_expenses": 350000,
                    "interest_expense": 25000,
                    "total_expenses": 775000
                },
                "net_income": 225000
            },
            "cash_flow": {
                "report_date": "2024-12-31T00:00:00Z",
                "operating_activities": {
                    "net_income": 225000,
                    "depreciation": 50000,
                    "changes_in_working_capital": -25000,
                    "net_cash_from_operating": 250000
                },
                "investing_activities": {
                    "capital_expenditures": -100000,
                    "asset_sales": 20000,
                    "net_cash_from_investing": -80000
                },
                "financing_activities": {
                    "debt_proceeds": 50000,
                    "debt_payments": -30000,
                    "dividends_paid": -40000,
                    "net_cash_from_financing": -20000
                },
                "cash_summary": {
                    "net_change_in_cash": 150000,
                    "beginning_cash": 100000,
                    "ending_cash": 250000
                }
            },
            "transactions": [
                {
                    "id": "txn_001",
                    "date": "2024-12-30",
                    "amount": 5000,
                    "account_id": "acc_cash",
                    "description": "Customer payment",
                    "created_date": "2024-12-30T14:30:00Z",
                    "created_by": "system",
                    "last_modified_date": "2024-12-30T14:30:00Z"
                },
                {
                    "id": "txn_002",
                    "date": "2024-12-29",
                    "amount": -2000,
                    "account_id": "acc_expenses",
                    "description": "Office supplies",
                    "created_date": "2024-12-29T10:15:00Z",
                    "created_by": "user_123",
                    "last_modified_date": "2024-12-29T10:15:00Z"
                }
            ],
            "accounts": [
                {"id": "acc_cash", "name": "Cash Account", "type": "asset"},
                {"id": "acc_expenses", "name": "Operating Expenses", "type": "expense"}
            ],
            "invoices": [
                {"id": "inv_001", "total": 5000, "contact_id": "contact_001", "status": "paid"},
                {"id": "inv_002", "total": 3000, "contact_id": "contact_002", "status": "pending"}
            ],
            "contacts": [
                {"id": "contact_001", "name": "ABC Corporation", "type": "customer"},
                {"id": "contact_002", "name": "XYZ Ltd", "type": "customer"}
            ],
            "payments": [
                {"id": "pay_001", "invoice_id": "inv_001", "amount": 5000, "date": "2024-12-30"}
            ],
            "metadata": {
                "last_sync_date": datetime.utcnow().isoformat(),
                "sync_status": "completed",
                "data_extraction_date": (datetime.utcnow() - timedelta(minutes=30)).isoformat(),
                "organization_id": 1
            }
        }
        
        # Run validation
        report = self.engine.validate_data(1, data)
        
        # Assertions
        assert report.organization_id == 1
        assert report.total_checks > 0
        assert report.success_rate > 80  # Should have high success rate with valid data
        assert not report.has_critical_errors
        
        # Check that all validation categories were tested
        categories_tested = {result.category for result in report.results}
        expected_categories = {
            ValidationCategory.FINANCIAL_INTEGRITY,
            ValidationCategory.BUSINESS_RULES,
            ValidationCategory.CROSS_REFERENCE,
            ValidationCategory.REGULATORY_COMPLIANCE,
            ValidationCategory.DATA_FRESHNESS
        }
        assert expected_categories.issubset(categories_tested)
    
    def test_validation_with_multiple_errors(self):
        """Test validation pipeline with multiple types of errors."""
        # Create data with various errors
        data = {
            "balance_sheet": {
                "report_date": "2024-12-31T00:00:00Z",
                "assets": {
                    "current_assets": {"cash_and_cash_equivalents": -10000},  # Negative cash
                    "total_assets": 500000
                },
                "liabilities": {
                    "total_liabilities": 200000
                },
                "equity": {
                    "total_equity": 250000  # Unbalanced (should be 300000)
                }
            },
            "income_statement": {
                "report_date": "2024-12-31T00:00:00Z",
                "revenue": {"total_revenue": -50000},  # Negative revenue
                "expenses": {"total_expenses": 100000},
                "net_income": -150000
            },
            "cash_flow": {
                "report_date": "2024-12-31T00:00:00Z",
                "operating_activities": {"net_cash_from_operating": 50000},
                "investing_activities": {"net_cash_from_investing": -20000},
                "financing_activities": {"net_cash_from_financing": -10000},
                "cash_summary": {"net_change_in_cash": 50000}  # Inconsistent (should be 20000)
            },
            "transactions": [
                {
                    "id": "txn_1",
                    "date": "2024-12-30",
                    "amount": 0,  # Zero amount transaction
                    "account_id": "nonexistent_account",  # Orphaned transaction
                    "description": "Test"
                }
            ],
            "accounts": [
                {"id": "acc_1", "name": "Test Account"}
            ],
            "invoices": [
                {"id": "inv_1", "total": 1000, "contact_id": "nonexistent_contact"}  # Orphaned invoice
            ],
            "contacts": [],
            "payments": [
                {"invoice_id": "nonexistent_invoice", "amount": 500}  # Orphaned payment
            ],
            "metadata": {
                "last_sync_date": (datetime.utcnow() - timedelta(days=2)).isoformat(),  # Stale data
                "sync_status": "failed",
                "sync_error": "Connection timeout"
            }
        }
        
        # Run validation
        report = self.engine.validate_data(1, data)
        
        # Assertions
        assert report.failed_checks > 0
        assert report.success_rate < 50  # Should have low success rate with many errors
        
        # Check for specific error types
        error_checks = [r.check_name for r in report.results if not r.passed]
        assert "balance_sheet_equation" in error_checks
        assert "negative_revenue" in error_checks
        assert "negative_cash" in error_checks
        assert "cash_flow_consistency" in error_checks
        assert "orphaned_transactions" in error_checks
        assert "orphaned_invoices" in error_checks
        assert "orphaned_payments" in error_checks
        assert "sync_status_failed" in error_checks
    
    def test_validation_report_generation(self):
        """Test validation report generation and export."""
        data = {
            "balance_sheet": {
                "report_date": "2024-12-31T00:00:00Z",
                "assets": {"total_assets": 100000},
                "liabilities": {"total_liabilities": 60000},
                "equity": {"total_equity": 40000}
            },
            "metadata": {
                "last_sync_date": datetime.utcnow().isoformat(),
                "sync_status": "completed"
            }
        }
        
        # Run validation
        report = self.engine.validate_data(1, data)
        
        # Test HTML report generation
        html_report = self.engine.generate_validation_report_html(report)
        assert isinstance(html_report, str)
        assert "Data Validation Report" in html_report
        assert f"Organization ID: {report.organization_id}" in html_report
        
        # Test alert generation
        alert = self.engine.generate_validation_alert(report)
        assert isinstance(alert, dict)
        assert "organization_id" in alert
        assert "alert_level" in alert
        assert "message" in alert
        
        # Test JSON export
        json_export = self.engine.export_validation_results(report, "json")
        assert isinstance(json_export, str)
        exported_data = json.loads(json_export)
        assert exported_data["organization_id"] == 1
        assert "results" in exported_data
        
        # Test CSV export
        csv_export = self.engine.export_validation_results(report, "csv")
        assert isinstance(csv_export, str)
        assert "Check Name,Category,Severity,Passed,Message,Timestamp" in csv_export
    
    def test_performance_with_large_dataset(self):
        """Test validation performance with large dataset."""
        # Create large dataset
        large_transactions = []
        for i in range(1000):
            large_transactions.append({
                "id": f"txn_{i}",
                "date": "2024-12-30",
                "amount": 100 + i,
                "account_id": f"acc_{i % 10}",
                "description": f"Transaction {i}",
                "created_date": "2024-12-30T10:00:00Z",
                "created_by": "system",
                "last_modified_date": "2024-12-30T10:00:00Z"
            })
        
        large_accounts = [{"id": f"acc_{i}", "name": f"Account {i}"} for i in range(10)]
        
        data = {
            "balance_sheet": {
                "report_date": "2024-12-31T00:00:00Z",
                "assets": {"total_assets": 1000000},
                "liabilities": {"total_liabilities": 600000},
                "equity": {"total_equity": 400000}
            },
            "transactions": large_transactions,
            "accounts": large_accounts,
            "metadata": {
                "last_sync_date": datetime.utcnow().isoformat(),
                "sync_status": "completed"
            }
        }
        
        # Measure validation time
        start_time = datetime.utcnow()
        report = self.engine.validate_data(1, data)
        end_time = datetime.utcnow()
        
        validation_time = (end_time - start_time).total_seconds()
        
        # Assertions
        assert report.total_checks > 0
        assert validation_time < 10  # Should complete within 10 seconds
        assert len(report.results) > 0
        
        print(f"Validation completed in {validation_time:.2f} seconds for {len(large_transactions)} transactions")
    
    def test_validation_with_missing_data_sections(self):
        """Test validation behavior with missing data sections."""
        # Minimal data with missing sections
        data = {
            "balance_sheet": {
                "assets": {"total_assets": 100000},
                "liabilities": {"total_liabilities": 60000},
                "equity": {"total_equity": 40000}
            }
            # Missing income_statement, cash_flow, transactions, etc.
        }
        
        # Run validation
        report = self.engine.validate_data(1, data)
        
        # Should still run without errors, but may have warnings about missing data
        assert report.total_checks > 0
        assert isinstance(report.summary, dict)
        
        # Check for missing data warnings
        warning_messages = [r.message for r in report.results if r.severity == ValidationSeverity.WARNING]
        assert len(warning_messages) > 0  # Should have warnings about missing sections


if __name__ == "__main__":
    pytest.main([__file__])
