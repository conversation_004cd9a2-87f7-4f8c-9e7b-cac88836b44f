[mypy]
python_version = 3.9
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True

# Ignore missing imports for third-party libraries without stubs
[mypy-xero_python.*]
ignore_missing_imports = True

[mypy-psutil]
ignore_missing_imports = True

[mypy-plotly.*]
ignore_missing_imports = True

[mypy-reportlab.*]
ignore_missing_imports = True

[mypy-celery.*]
ignore_missing_imports = True

[mypy-redis.*]
ignore_missing_imports = True