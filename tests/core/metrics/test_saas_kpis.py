import pytest
import pandas as pd
from mcx3d_finance.core.metrics.saas_kpis import (
    calculate_mrr,
    calculate_arr,
    calculate_ltv,
    calculate_cac,
    calculate_rule_of_40,
)


@pytest.fixture
def sample_subscription_data():
    """
    Provides a sample DataFrame of subscription data for testing.
    """
    data = {
        "monthly_fee": [10, 20, 30],
        "customers": [100, 50, 25],
    }
    return pd.DataFrame(data)


def test_calculate_mrr_with_sample_data(sample_subscription_data):
    """
    Tests the MRR calculation with a typical set of subscription data.
    """
    mrr = calculate_mrr(sample_subscription_data)
    assert mrr == (10 * 100) + (20 * 50) + (30 * 25)


def test_calculate_mrr_with_empty_data():
    """
    Tests the MRR calculation with an empty DataFrame.
    """
    mrr = calculate_mrr(pd.DataFrame({"monthly_fee": [], "customers": []}))
    assert mrr == 0


def test_calculate_mrr_with_missing_columns():
    """
    Tests that MRR calculation raises a ValueError if columns are missing.
    """
    with pytest.raises(ValueError):
        calculate_mrr(pd.DataFrame({"fees": [10], "users": [100]}))


def test_calculate_arr():
    """
    Tests the ARR calculation.
    """
    assert calculate_arr(1000) == 12000
    assert calculate_arr(0) == 0


def test_calculate_ltv():
    """
    Tests the LTV calculation.
    """
    assert calculate_ltv(100, 12) == 1200
    assert calculate_ltv(0, 12) == 0
    assert calculate_ltv(100, 0) == 0


def test_calculate_cac():
    """
    Tests the CAC calculation.
    """
    assert calculate_cac(1000, 10) == 100
    assert calculate_cac(0, 10) == 0
    assert calculate_cac(1000, 0) == 0


def test_rule_of_40():
    """
    Tests the Rule of 40 calculation.
    """
    assert calculate_rule_of_40(20, 20) is True
    assert calculate_rule_of_40(30, 15) is True
    assert calculate_rule_of_40(10, 20) is False
    assert calculate_rule_of_40(40, 0) is True
    assert calculate_rule_of_40(0, 40) is True
