import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from unittest.mock import MagicMock

from mcx3d_finance.db.models import Base


@pytest.fixture(scope="session")
def db_engine():
    """Fixture for a test database engine."""
    return create_engine("sqlite:///:memory:")


@pytest.fixture(scope="session", autouse=True)
def setup_database(db_engine):
    """Fixture to set up the test database."""
    Base.metadata.create_all(db_engine)
    yield
    Base.metadata.drop_all(db_engine)


@pytest.fixture
def db_session(db_engine) -> Session:
    """Fixture for a test database session."""
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=db_engine)
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()


@pytest.fixture
def mocker():
    """Fixture for mocking."""
    return MagicMock()
