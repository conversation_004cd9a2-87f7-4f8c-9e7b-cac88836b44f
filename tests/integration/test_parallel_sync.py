"""
Integration tests for parallel Xero sync functionality.

Tests the ParallelXeroDataStorageService to ensure concurrent processing
works correctly and provides performance improvements.
"""

import pytest
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone
import threading

from mcx3d_finance.integrations.xero_data_storage_parallel import ParallelXeroDataStorageService
from mcx3d_finance.integrations.xero_data_storage import XeroDataStorageService
from mcx3d_finance.db.models import Organization, Account, Contact, Invoice, BankTransaction


class TestParallelXeroSync:
    """Test parallel sync functionality."""
    
    @pytest.fixture
    def mock_db(self):
        """Create a mock database session."""
        db = Mock()
        db.query = Mock()
        db.add = Mock()
        db.commit = Mock()
        db.rollback = Mock()
        return db
    
    @pytest.fixture
    def sample_data(self):
        """Create sample import data for testing."""
        return {
            "organization": {"Name": "Test Org", "BaseCurrency": "USD"},
            "accounts": [
                {"xero_account_id": f"acc_{i}", "name": f"Account {i}", "type": "EXPENSE"}
                for i in range(100)
            ],
            "contacts": [
                {"xero_contact_id": f"con_{i}", "name": f"Contact {i}"}
                for i in range(100)
            ],
            "invoices": [
                {"xero_invoice_id": f"inv_{i}", "invoice_number": f"INV-{i:04d}", "contact_id": f"con_{i % 100}"}
                for i in range(200)
            ],
            "transactions": [
                {"xero_transaction_id": f"txn_{i}", "amount": 100.0 + i}
                for i in range(150)
            ]
        }
    
    def test_parallel_storage_initialization(self, mock_db):
        """Test parallel storage service initialization."""
        service = ParallelXeroDataStorageService(mock_db, optimize=True, max_workers=4)
        
        assert service.max_workers == 4
        assert service.optimize is True
        assert hasattr(service, '_progress_lock')
        assert hasattr(service, '_stats_lock')
    
    def test_parallel_vs_sequential_performance(self, mock_db, sample_data):
        """Test that parallel processing is faster than sequential."""
        # Mock query responses
        mock_db.query.return_value.filter_by.return_value.first.return_value = None
        mock_db.query.return_value.filter.return_value.all.return_value = []
        mock_db.query.return_value.get.return_value = Mock(name="Test Org")
        
        # Add artificial delay to simulate database operations
        original_add = mock_db.add
        def slow_add(entity):
            time.sleep(0.001)  # 1ms delay per record
            return original_add(entity)
        
        mock_db.add = slow_add
        
        # Test sequential processing
        sequential_service = XeroDataStorageService(mock_db, optimize=True)
        start_time = time.time()
        sequential_result = sequential_service.store_all_data(
            organization_id=1,
            imported_data=sample_data
        )
        sequential_time = time.time() - start_time
        
        # Test parallel processing
        parallel_service = ParallelXeroDataStorageService(mock_db, optimize=True, max_workers=4)
        start_time = time.time()
        parallel_result = parallel_service.store_all_data_parallel(
            organization_id=1,
            imported_data=sample_data
        )
        parallel_time = time.time() - start_time
        
        # Parallel should be faster (in real scenarios, the improvement would be more significant)
        assert parallel_time <= sequential_time * 1.1  # Allow 10% margin for thread overhead
        
        # Both should succeed
        assert sequential_result["success"] is True
        assert parallel_result["success"] is True
    
    def test_thread_safety(self, mock_db, sample_data):
        """Test that parallel processing maintains thread safety."""
        # Track which threads accessed the database
        thread_ids = set()
        
        def track_thread(*args, **kwargs):
            thread_ids.add(threading.current_thread().ident)
            return Mock()
        
        mock_db.query = Mock(side_effect=track_thread)
        mock_db.query.return_value.filter_by.return_value.first.return_value = None
        mock_db.query.return_value.filter.return_value.all.return_value = []
        mock_db.query.return_value.get.return_value = Mock(name="Test Org")
        
        service = ParallelXeroDataStorageService(mock_db, optimize=True, max_workers=4)
        result = service.store_all_data_parallel(
            organization_id=1,
            imported_data=sample_data
        )
        
        # Should have multiple threads (main thread + worker threads)
        assert len(thread_ids) > 1
        assert result["success"] is True
    
    def test_progress_tracking(self, mock_db, sample_data):
        """Test progress tracking across parallel operations."""
        progress_updates = []
        
        def progress_callback(progress, message):
            progress_updates.append((progress, message))
        
        mock_db.query.return_value.filter_by.return_value.first.return_value = None
        mock_db.query.return_value.filter.return_value.all.return_value = []
        mock_db.query.return_value.get.return_value = Mock(name="Test Org")
        
        service = ParallelXeroDataStorageService(mock_db, optimize=True)
        result = service.store_all_data_parallel(
            organization_id=1,
            imported_data=sample_data,
            progress_callback=progress_callback
        )
        
        # Check progress updates
        assert len(progress_updates) > 0
        assert any("organization" in msg.lower() for _, msg in progress_updates)
        assert any(progress == 100 for progress, _ in progress_updates)
    
    def test_error_handling_in_parallel(self, mock_db, sample_data):
        """Test error handling when one entity type fails."""
        # Make invoice processing fail
        def raise_on_invoice(model):
            if model == Invoice:
                raise Exception("Invoice processing failed")
            return Mock().filter_by.return_value.first.return_value
        
        mock_db.query.side_effect = raise_on_invoice
        mock_db.query.return_value.get.return_value = Mock(name="Test Org")
        
        service = ParallelXeroDataStorageService(mock_db, optimize=True)
        result = service.store_all_data_parallel(
            organization_id=1,
            imported_data=sample_data
        )
        
        # Should complete but with errors
        assert result["success"] is False
        assert "errors" in result
        assert any("invoice" in error.lower() for error in result["errors"])
    
    def test_cache_preloading(self, mock_db, sample_data):
        """Test that caches are preloaded correctly in parallel."""
        # Mock contact and account queries
        mock_contacts = [Mock(xero_contact_id=f"con_{i}", id=i) for i in range(100)]
        mock_accounts = [Mock(xero_account_id=f"acc_{i}", id=i) for i in range(100)]
        
        def mock_query(model):
            query_mock = Mock()
            if model == Contact:
                query_mock.filter.return_value.all.return_value = mock_contacts
            elif model == Account:
                query_mock.filter.return_value.all.return_value = mock_accounts
            else:
                query_mock.filter_by.return_value.first.return_value = None
                query_mock.get.return_value = Mock(name="Test Org")
            return query_mock
        
        mock_db.query.side_effect = mock_query
        
        service = ParallelXeroDataStorageService(mock_db, optimize=True)
        
        # Preload caches
        service._preload_caches_parallel(1, sample_data)
        
        # Check caches are populated
        assert len(service._contact_cache) == 100
        assert len(service._account_cache) == 100
    
    def test_statistics_aggregation(self, mock_db, sample_data):
        """Test that statistics are correctly aggregated across threads."""
        mock_db.query.return_value.filter_by.return_value.first.return_value = None
        mock_db.query.return_value.filter.return_value.all.return_value = []
        mock_db.query.return_value.get.return_value = Mock(name="Test Org")
        
        service = ParallelXeroDataStorageService(mock_db, optimize=True)
        result = service.store_all_data_parallel(
            organization_id=1,
            imported_data=sample_data
        )
        
        # Check statistics
        stats = result["stats"]
        assert stats["accounts"]["created"] == 100
        assert stats["contacts"]["created"] == 100
        assert stats["invoices"]["created"] == 200
        assert stats["bank_transactions"]["created"] == 150
        
        # No updates or skips in this test
        assert all(stats[entity]["updated"] == 0 for entity in stats)
        assert all(stats[entity]["skipped"] == 0 for entity in stats)
    
    @pytest.mark.parametrize("use_parallel", [True, False])
    def test_store_all_data_with_parallel_flag(self, mock_db, sample_data, use_parallel):
        """Test store_all_data method with use_parallel flag."""
        mock_db.query.return_value.filter_by.return_value.first.return_value = None
        mock_db.query.return_value.filter.return_value.all.return_value = []
        mock_db.query.return_value.get.return_value = Mock(name="Test Org")
        
        service = ParallelXeroDataStorageService(mock_db, optimize=True)
        
        with patch.object(service, 'store_all_data_parallel') as mock_parallel:
            with patch.object(XeroDataStorageService, 'store_all_data') as mock_sequential:
                mock_parallel.return_value = {"success": True, "parallel": True}
                mock_sequential.return_value = {"success": True, "parallel": False}
                
                result = service.store_all_data(
                    organization_id=1,
                    imported_data=sample_data,
                    use_parallel=use_parallel
                )
                
                if use_parallel:
                    mock_parallel.assert_called_once()
                    mock_sequential.assert_not_called()
                    assert result["parallel"] is True
                else:
                    mock_parallel.assert_not_called()
                    mock_sequential.assert_called_once()
                    assert result["parallel"] is False