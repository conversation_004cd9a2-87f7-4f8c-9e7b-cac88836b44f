"""Add sync_status table

Revision ID: 004_add_sync_status
Revises: 003_remove_enrichment
Create Date: 2025-01-22 15:18:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '004_add_sync_status'
down_revision = '003_remove_enrichment'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create sync_status table
    op.create_table('sync_status',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('organization_id', sa.Integer(), nullable=True),
        sa.Column('sync_type', sa.String(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('records_synced', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sync_status_id'), 'sync_status', ['id'], unique=False)


def downgrade() -> None:
    # Drop sync_status table
    op.drop_index(op.f('ix_sync_status_id'), table_name='sync_status')
    op.drop_table('sync_status')