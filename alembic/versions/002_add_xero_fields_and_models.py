"""Add Xero fields and new models

Revision ID: 002_add_xero_fields
Revises: 71cedea27948
Create Date: 2025-07-22 14:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '002_add_xero_fields'
down_revision = '71cedea27948'
branch_labels = None
depends_on = None


def upgrade():
    # Drop existing foreign key constraints
    op.drop_constraint('transactions_account_id_fkey', 'transactions', type_='foreignkey')
    op.drop_constraint('transactions_contact_id_fkey', 'transactions', type_='foreignkey')
    
    # Add autoincrement id columns where missing
    op.execute('ALTER TABLE accounts DROP CONSTRAINT IF EXISTS accounts_pkey CASCADE')
    op.execute('ALTER TABLE contacts DROP CONSTRAINT IF EXISTS contacts_pkey CASCADE')
    op.execute('ALTER TABLE transactions DROP CONSTRAINT IF EXISTS transactions_pkey CASCADE')
    
    # Recreate accounts table with new schema
    op.drop_table('accounts')
    op.create_table('accounts',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('xero_account_id', sa.String(), nullable=True),
        sa.Column('code', sa.String(), nullable=True),
        sa.Column('name', sa.String(), nullable=True),
        sa.Column('type', sa.String(), nullable=True),
        sa.Column('tax_type', sa.String(), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('class_type', sa.String(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('show_in_expense_claims', sa.Boolean(), nullable=True),
        sa.Column('bank_account_number', sa.String(), nullable=True),
        sa.Column('bank_account_type', sa.String(), nullable=True),
        sa.Column('currency_code', sa.String(), nullable=True),
        sa.Column('reporting_code', sa.String(), nullable=True),
        sa.Column('reporting_code_name', sa.String(), nullable=True),
        sa.Column('has_attachments', sa.Boolean(), nullable=True),
        sa.Column('updated_date_utc', sa.DateTime(), nullable=True),
        sa.Column('add_to_watchlist', sa.Boolean(), nullable=True),
        sa.Column('gaap_classification', sa.String(), nullable=True),
        sa.Column('organization_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_accounts_code'), 'accounts', ['code'], unique=False)
    op.create_index(op.f('ix_accounts_id'), 'accounts', ['id'], unique=False)
    op.create_index(op.f('ix_accounts_name'), 'accounts', ['name'], unique=False)
    op.create_index(op.f('ix_accounts_xero_account_id'), 'accounts', ['xero_account_id'], unique=True)

    # Recreate contacts table with new schema
    op.drop_table('contacts')
    op.create_table('contacts',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('xero_contact_id', sa.String(), nullable=True),
        sa.Column('contact_number', sa.String(), nullable=True),
        sa.Column('account_number', sa.String(), nullable=True),
        sa.Column('contact_status', sa.String(), nullable=True),
        sa.Column('name', sa.String(), nullable=True),
        sa.Column('first_name', sa.String(), nullable=True),
        sa.Column('last_name', sa.String(), nullable=True),
        sa.Column('email_address', sa.String(), nullable=True),
        sa.Column('bank_account_details', sa.String(), nullable=True),
        sa.Column('tax_number', sa.String(), nullable=True),
        sa.Column('accounts_receivable_tax_type', sa.String(), nullable=True),
        sa.Column('accounts_payable_tax_type', sa.String(), nullable=True),
        sa.Column('is_supplier', sa.Boolean(), nullable=True),
        sa.Column('is_customer', sa.Boolean(), nullable=True),
        sa.Column('default_currency', sa.String(), nullable=True),
        sa.Column('updated_date_utc', sa.DateTime(), nullable=True),
        sa.Column('has_attachments', sa.Boolean(), nullable=True),
        sa.Column('has_validation_errors', sa.Boolean(), nullable=True),
        sa.Column('phone_default', sa.String(), nullable=True),
        sa.Column('phone_mobile', sa.String(), nullable=True),
        sa.Column('phone_fax', sa.String(), nullable=True),
        sa.Column('address_street', sa.JSON(), nullable=True),
        sa.Column('address_postal', sa.JSON(), nullable=True),
        sa.Column('business_profile', sa.JSON(), nullable=True),
        sa.Column('enrichment_metadata', sa.JSON(), nullable=True),
        sa.Column('organization_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_contacts_email_address'), 'contacts', ['email_address'], unique=False)
    op.create_index(op.f('ix_contacts_id'), 'contacts', ['id'], unique=False)
    op.create_index(op.f('ix_contacts_name'), 'contacts', ['name'], unique=False)
    op.create_index(op.f('ix_contacts_xero_contact_id'), 'contacts', ['xero_contact_id'], unique=True)

    # Recreate transactions table with new schema
    op.drop_table('transactions')
    op.create_table('transactions',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('xero_transaction_id', sa.String(), nullable=True),
        sa.Column('type', sa.String(), nullable=True),
        sa.Column('date', sa.DateTime(), nullable=True),
        sa.Column('amount', sa.Float(), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('reference', sa.String(), nullable=True),
        sa.Column('is_reconciled', sa.Boolean(), nullable=True),
        sa.Column('currency_code', sa.String(), nullable=True),
        sa.Column('currency_rate', sa.Float(), nullable=True),
        sa.Column('sub_total', sa.Float(), nullable=True),
        sa.Column('total_tax', sa.Float(), nullable=True),
        sa.Column('total', sa.Float(), nullable=True),
        sa.Column('line_items', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('updated_date_utc', sa.DateTime(), nullable=True),
        sa.Column('has_attachments', sa.Boolean(), nullable=True),
        sa.Column('contact_id', sa.Integer(), nullable=True),
        sa.Column('account_id', sa.Integer(), nullable=True),
        sa.Column('organization_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.id'], ),
        sa.ForeignKeyConstraint(['contact_id'], ['contacts.id'], ),
        sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_transactions_date'), 'transactions', ['date'], unique=False)
    op.create_index(op.f('ix_transactions_id'), 'transactions', ['id'], unique=False)
    op.create_index(op.f('ix_transactions_xero_transaction_id'), 'transactions', ['xero_transaction_id'], unique=True)

    # Create invoices table
    op.create_table('invoices',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('xero_invoice_id', sa.String(), nullable=True),
        sa.Column('type', sa.String(), nullable=True),
        sa.Column('contact_id', sa.Integer(), nullable=True),
        sa.Column('date', sa.DateTime(), nullable=True),
        sa.Column('due_date', sa.DateTime(), nullable=True),
        sa.Column('line_amount_types', sa.String(), nullable=True),
        sa.Column('invoice_number', sa.String(), nullable=True),
        sa.Column('reference', sa.String(), nullable=True),
        sa.Column('branding_theme_id', sa.String(), nullable=True),
        sa.Column('url', sa.String(), nullable=True),
        sa.Column('currency_code', sa.String(), nullable=True),
        sa.Column('currency_rate', sa.Float(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('sent_to_contact', sa.Boolean(), nullable=True),
        sa.Column('expected_payment_date', sa.DateTime(), nullable=True),
        sa.Column('planned_payment_date', sa.DateTime(), nullable=True),
        sa.Column('sub_total', sa.Float(), nullable=True),
        sa.Column('total_tax', sa.Float(), nullable=True),
        sa.Column('total', sa.Float(), nullable=True),
        sa.Column('total_discount', sa.Float(), nullable=True),
        sa.Column('has_attachments', sa.Boolean(), nullable=True),
        sa.Column('has_errors', sa.Boolean(), nullable=True),
        sa.Column('is_discounted', sa.Boolean(), nullable=True),
        sa.Column('payments', sa.JSON(), nullable=True),
        sa.Column('amount_due', sa.Float(), nullable=True),
        sa.Column('amount_paid', sa.Float(), nullable=True),
        sa.Column('amount_credited', sa.Float(), nullable=True),
        sa.Column('updated_date_utc', sa.DateTime(), nullable=True),
        sa.Column('line_items', sa.JSON(), nullable=True),
        sa.Column('organization_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['contact_id'], ['contacts.id'], ),
        sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_invoices_date'), 'invoices', ['date'], unique=False)
    op.create_index(op.f('ix_invoices_id'), 'invoices', ['id'], unique=False)
    op.create_index(op.f('ix_invoices_invoice_number'), 'invoices', ['invoice_number'], unique=False)
    op.create_index(op.f('ix_invoices_status'), 'invoices', ['status'], unique=False)
    op.create_index(op.f('ix_invoices_xero_invoice_id'), 'invoices', ['xero_invoice_id'], unique=True)

    # Create bank_transactions table
    op.create_table('bank_transactions',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('xero_transaction_id', sa.String(), nullable=True),
        sa.Column('type', sa.String(), nullable=True),
        sa.Column('contact_id', sa.Integer(), nullable=True),
        sa.Column('line_items', sa.JSON(), nullable=True),
        sa.Column('bank_account', sa.JSON(), nullable=True),
        sa.Column('is_reconciled', sa.Boolean(), nullable=True),
        sa.Column('date', sa.DateTime(), nullable=True),
        sa.Column('reference', sa.String(), nullable=True),
        sa.Column('currency_code', sa.String(), nullable=True),
        sa.Column('currency_rate', sa.Float(), nullable=True),
        sa.Column('url', sa.String(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('line_amount_types', sa.String(), nullable=True),
        sa.Column('sub_total', sa.Float(), nullable=True),
        sa.Column('total_tax', sa.Float(), nullable=True),
        sa.Column('total', sa.Float(), nullable=True),
        sa.Column('updated_date_utc', sa.DateTime(), nullable=True),
        sa.Column('has_attachments', sa.Boolean(), nullable=True),
        sa.Column('organization_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['contact_id'], ['contacts.id'], ),
        sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_bank_transactions_date'), 'bank_transactions', ['date'], unique=False)
    op.create_index(op.f('ix_bank_transactions_id'), 'bank_transactions', ['id'], unique=False)
    op.create_index(op.f('ix_bank_transactions_xero_transaction_id'), 'bank_transactions', ['xero_transaction_id'], unique=True)


def downgrade():
    # Drop new tables
    op.drop_table('bank_transactions')
    op.drop_table('invoices')
    
    # Recreate original tables
    op.drop_table('transactions')
    op.create_table('transactions',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('type', sa.String(), nullable=True),
        sa.Column('date', sa.DateTime(), nullable=True),
        sa.Column('amount', sa.Float(), nullable=True),
        sa.Column('contact_id', sa.String(), nullable=True),
        sa.Column('account_id', sa.String(), nullable=True),
        sa.Column('organization_id', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.id'], ),
        sa.ForeignKeyConstraint(['contact_id'], ['contacts.id'], ),
        sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_transactions_id'), 'transactions', ['id'], unique=False)
    
    op.drop_table('contacts')
    op.create_table('contacts',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('name', sa.String(), nullable=True),
        sa.Column('email', sa.String(), nullable=True),
        sa.Column('is_supplier', sa.String(), nullable=True),
        sa.Column('is_customer', sa.String(), nullable=True),
        sa.Column('organization_id', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_contacts_id'), 'contacts', ['id'], unique=False)
    op.create_index(op.f('ix_contacts_name'), 'contacts', ['name'], unique=False)
    
    op.drop_table('accounts')
    op.create_table('accounts',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('name', sa.String(), nullable=True),
        sa.Column('type', sa.String(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('organization_id', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_accounts_id'), 'accounts', ['id'], unique=False)
    op.create_index(op.f('ix_accounts_name'), 'accounts', ['name'], unique=False)