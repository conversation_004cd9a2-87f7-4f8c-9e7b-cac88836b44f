"""
Test suite for financial chart generation functionality.
"""

import pytest
import tempfile
import os
from typing import Dict, Any

from mcx3d_finance.reporting.generator import ReportGenerator, FinancialChartGenerator


class TestFinancialChartGenerator:
    """Test suite for FinancialChartGenerator class."""
    
    @pytest.fixture
    def chart_generator(self):
        """Create FinancialChartGenerator instance."""
        return FinancialChartGenerator()
    
    @pytest.fixture
    def sample_dcf_data(self):
        """Create sample DCF data for testing."""
        return {
            "organization_id": 1,
            "valuation_date": "2024-01-01",
            "financial_projections": [
                {
                    "year": 1,
                    "revenue": 1000000,
                    "free_cash_flow": 300000,
                    "ebitda": 400000,
                    "revenue_growth_rate": 0.20
                },
                {
                    "year": 2,
                    "revenue": 1200000,
                    "free_cash_flow": 380000,
                    "ebitda": 500000,
                    "revenue_growth_rate": 0.20
                },
                {
                    "year": 3,
                    "revenue": 1440000,
                    "free_cash_flow": 480000,
                    "ebitda": 640000,
                    "revenue_growth_rate": 0.20
                }
            ],
            "sensitivity_analysis": {
                "discount_rate": {
                    "range": [-0.02, -0.01, 0, 0.01, 0.02],
                    "impact": [-15, -8, 0, 8, 15]
                },
                "terminal_growth_rate": {
                    "range": [-0.01, -0.005, 0, 0.005, 0.01],
                    "impact": [-10, -5, 0, 5, 10]
                }
            },
            "monte_carlo_simulation": {
                "num_simulations": 10000,
                "mean_valuation": 50000000,
                "std_deviation": 10000000,
                "percentile_10": 35000000,
                "percentile_90": 65000000
            }
        }
    
    @pytest.fixture
    def sample_saas_data(self):
        """Create sample SaaS data for testing."""
        return {
            "organization_id": 1,
            "valuation_date": "2024-01-01",
            "key_metrics": {
                "arr": 5000000,
                "mrr": 416667,
                "growth_rate": 25,
                "churn_rate": 3,
                "ltv_cac_ratio": 5.2,
                "customer_acquisition_cost": 150,
                "customer_lifetime_value": 780,
                "active_customers": 1000
            },
            "valuation_methods": {
                "arr_multiple": {
                    "adjusted_valuation": 45000000,
                    "base_arr": 5000000,
                    "adjusted_multiple": 9.0
                },
                "revenue_multiple": {
                    "valuation": 40000000,
                    "annual_revenue": 5000000,
                    "revenue_multiple": 8.0
                },
                "saas_dcf": {
                    "enterprise_value": 48000000,
                    "pv_operating_fcf": 35000000,
                    "discount_rate": 0.12
                }
            },
            "weighted_valuation": {
                "weighted_average_valuation": 44000000,
                "valuation_range": {
                    "minimum": 38000000,
                    "maximum": 50000000
                }
            }
        }
    
    def test_chart_generator_initialization(self, chart_generator):
        """Test chart generator initialization."""
        assert chart_generator is not None
        assert hasattr(chart_generator, 'colors')
        assert hasattr(chart_generator, 'default_layout')
        assert hasattr(chart_generator, 'plotly_config')
    
    def test_dcf_revenue_projection_chart(self, chart_generator, sample_dcf_data):
        """Test DCF revenue projection chart generation."""
        projections = sample_dcf_data["financial_projections"]
        fig = chart_generator.create_dcf_revenue_projection_chart(projections)
        
        assert fig is not None
        assert len(fig.data) > 0  # Should have at least one trace
        assert fig.layout.title.text == "Revenue Projections (5-Year Forecast)"
    
    def test_dcf_cash_flow_waterfall_chart(self, chart_generator, sample_dcf_data):
        """Test DCF cash flow waterfall chart generation."""
        projections = sample_dcf_data["financial_projections"]
        fig = chart_generator.create_dcf_cash_flow_waterfall_chart(projections)
        
        assert fig is not None
        assert len(fig.data) > 0
        assert fig.layout.title.text == "Cash Flow Waterfall Analysis"
    
    def test_dcf_sensitivity_tornado_chart(self, chart_generator, sample_dcf_data):
        """Test DCF sensitivity tornado chart generation."""
        sensitivity = sample_dcf_data["sensitivity_analysis"]
        fig = chart_generator.create_dcf_sensitivity_tornado_chart(sensitivity)
        
        assert fig is not None
        assert len(fig.data) >= 2  # Should have at least 2 traces (positive and negative)
        assert fig.layout.title.text == "Sensitivity Analysis - Tornado Diagram"
    
    def test_dcf_monte_carlo_chart(self, chart_generator, sample_dcf_data):
        """Test DCF Monte Carlo distribution chart generation."""
        mc_data = sample_dcf_data["monte_carlo_simulation"]
        fig = chart_generator.create_dcf_monte_carlo_distribution_chart(mc_data)
        
        assert fig is not None
        assert len(fig.data) > 0
        assert fig.layout.title.text == "Monte Carlo Simulation - Valuation Distribution"
    
    def test_saas_arr_growth_chart(self, chart_generator, sample_saas_data):
        """Test SaaS ARR growth chart generation."""
        fig = chart_generator.create_saas_arr_growth_chart(sample_saas_data)
        
        assert fig is not None
        assert len(fig.data) >= 2  # Should have historical and projected traces
        assert fig.layout.title.text == "ARR Growth Trajectory"
    
    def test_saas_unit_economics_chart(self, chart_generator, sample_saas_data):
        """Test SaaS unit economics chart generation."""
        fig = chart_generator.create_saas_unit_economics_chart(sample_saas_data)
        
        assert fig is not None
        assert len(fig.data) > 0
        assert fig.layout.title.text == "SaaS Unit Economics"
    
    def test_saas_valuation_comparison_chart(self, chart_generator, sample_saas_data):
        """Test SaaS valuation methods comparison chart generation."""
        fig = chart_generator.create_saas_valuation_methods_comparison_chart(sample_saas_data)
        
        assert fig is not None
        assert len(fig.data) > 0
        assert fig.layout.title.text == "SaaS Valuation Methods Comparison"
    
    def test_data_validation_dcf(self, chart_generator, sample_dcf_data):
        """Test DCF data validation."""
        validation = chart_generator.validate_dcf_data(sample_dcf_data)
        
        assert validation["has_financial_projections"] is True
        assert validation["projections_valid"] is True
        assert validation["has_sensitivity_analysis"] is True
        assert validation["sensitivity_valid"] is True
        assert validation["has_monte_carlo_data"] is True
        assert validation["monte_carlo_valid"] is True
    
    def test_data_validation_saas(self, chart_generator, sample_saas_data):
        """Test SaaS data validation."""
        validation = chart_generator.validate_saas_data(sample_saas_data)
        
        assert validation["has_key_metrics"] is True
        assert validation["key_metrics_valid"] is True
        assert validation["has_valuation_methods"] is True
        assert validation["valuation_methods_valid"] is True
    
    def test_fallback_chart_creation(self, chart_generator):
        """Test fallback chart creation for invalid data."""
        fig = chart_generator.create_fallback_chart("test_chart", "Test error message")
        
        assert fig is not None
        assert fig.layout.title.text == "Test Chart - Data Not Available"
    
    def test_data_sanitization(self, chart_generator):
        """Test data sanitization functionality."""
        # Test numeric sanitization
        assert chart_generator.sanitize_data(100.5, "numeric") == 100.5
        assert chart_generator.sanitize_data("$1,000.50", "numeric") == 1000.5
        assert chart_generator.sanitize_data(None, "numeric") == 0
        
        # Test string sanitization
        assert chart_generator.sanitize_data("test", "string") == "test"
        assert chart_generator.sanitize_data(None, "string") == "N/A"
        
        # Test list sanitization
        assert chart_generator.sanitize_data([1, 2, 3], "list") == [1, 2, 3]
        assert chart_generator.sanitize_data("not a list", "list") == []

    def test_financial_statement_charts(self, chart_generator):
        """Test financial statement chart generation."""
        # Sample balance sheet data
        balance_sheet_data = {
            "assets": {
                "current_assets": {
                    "cash": 1000000,
                    "accounts_receivable": 500000,
                    "inventory": 300000
                },
                "non_current_assets": {
                    "property_plant_equipment": 2000000,
                    "intangible_assets": 800000
                }
            },
            "liabilities_and_equity": {
                "current_liabilities": {
                    "accounts_payable": 400000,
                    "short_term_debt": 200000
                },
                "total_liabilities": 1500000,
                "total_equity": 3100000
            }
        }

        # Test asset composition chart
        asset_chart = chart_generator.create_balance_sheet_asset_composition_chart(balance_sheet_data)
        assert asset_chart is not None
        assert len(asset_chart.data) > 0

        # Test working capital components chart
        wc_chart = chart_generator.create_working_capital_components_chart(balance_sheet_data)
        assert wc_chart is not None
        assert len(wc_chart.data) > 0

        # Sample income statement data
        income_statement_data = {
            "revenue": {
                "product_sales": 3000000,
                "service_revenue": 2000000,
                "other_revenue": 500000
            }
        }

        # Test revenue breakdown chart
        revenue_chart = chart_generator.create_revenue_breakdown_chart(income_statement_data)
        assert revenue_chart is not None
        assert len(revenue_chart.data) > 0

        # Sample historical data for trend analysis
        historical_data = [
            {
                "period": "2022",
                "balance_sheet": {
                    "liabilities_and_equity": {
                        "total_liabilities": 1000000,
                        "total_equity": 2000000
                    }
                },
                "income_statement": {
                    "revenue": {"total_revenue": 4000000},
                    "gross_profit": 2400000,
                    "operating_income": 1200000,
                    "net_income": 800000
                }
            },
            {
                "period": "2023",
                "balance_sheet": {
                    "liabilities_and_equity": {
                        "total_liabilities": 1200000,
                        "total_equity": 2500000
                    }
                },
                "income_statement": {
                    "revenue": {"total_revenue": 5000000},
                    "gross_profit": 3000000,
                    "operating_income": 1500000,
                    "net_income": 1000000
                }
            }
        ]

        # Test debt-to-equity trend chart
        debt_equity_chart = chart_generator.create_debt_to_equity_trend_chart(historical_data)
        assert debt_equity_chart is not None
        assert len(debt_equity_chart.data) > 0

        # Test margin analysis chart
        margin_chart = chart_generator.create_margin_analysis_chart(historical_data)
        assert margin_chart is not None
        assert len(margin_chart.data) >= 3  # Should have 3 margin traces

    def test_financial_statement_chart_generation(self, chart_generator):
        """Test comprehensive financial statement chart generation."""
        financial_data = {
            "balance_sheet": {
                "assets": {
                    "current_assets": {"cash": 1000000, "inventory": 500000},
                    "non_current_assets": {"equipment": 2000000}
                },
                "liabilities_and_equity": {
                    "current_liabilities": {"payables": 300000},
                    "total_liabilities": 800000,
                    "total_equity": 2700000
                }
            },
            "income_statement": {
                "revenue": {
                    "sales": 3000000,
                    "services": 1000000
                }
            },
            "historical_data": [
                {
                    "period": "2023",
                    "balance_sheet": {
                        "liabilities_and_equity": {
                            "total_liabilities": 800000,
                            "total_equity": 2700000
                        }
                    },
                    "income_statement": {
                        "revenue": {"total_revenue": 4000000},
                        "gross_profit": 2400000,
                        "operating_income": 1200000,
                        "net_income": 800000
                    }
                }
            ]
        }

        charts = chart_generator.generate_financial_statement_charts(financial_data)

        assert isinstance(charts, dict)
        assert "asset_composition" in charts
        assert "working_capital" in charts
        assert "revenue_breakdown" in charts
        assert "debt_to_equity_trend" in charts
        assert "margin_analysis" in charts


class TestReportGeneratorIntegration:
    """Test integration of chart generation with report generator."""
    
    @pytest.fixture
    def report_generator(self):
        """Create ReportGenerator instance."""
        return ReportGenerator()
    
    def test_report_generator_has_chart_generator(self, report_generator):
        """Test that ReportGenerator has chart generator instance."""
        assert hasattr(report_generator, 'chart_generator')
        assert isinstance(report_generator.chart_generator, FinancialChartGenerator)
    
    def test_dcf_pdf_generation_with_charts(self, report_generator, sample_dcf_data):
        """Test DCF PDF generation with charts (basic smoke test)."""
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            try:
                # This should not raise an exception
                report_generator.generate_dcf_valuation_pdf(sample_dcf_data, tmp_file.name)
                
                # Check that file was created
                assert os.path.exists(tmp_file.name)
                assert os.path.getsize(tmp_file.name) > 0
                
            finally:
                # Clean up
                if os.path.exists(tmp_file.name):
                    os.unlink(tmp_file.name)
    
    def test_saas_pdf_generation_with_charts(self, report_generator, sample_saas_data):
        """Test SaaS PDF generation with charts (basic smoke test)."""
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            try:
                # This should not raise an exception
                report_generator.generate_saas_valuation_pdf(sample_saas_data, tmp_file.name)
                
                # Check that file was created
                assert os.path.exists(tmp_file.name)
                assert os.path.getsize(tmp_file.name) > 0
                
            finally:
                # Clean up
                if os.path.exists(tmp_file.name):
                    os.unlink(tmp_file.name)


if __name__ == "__main__":
    pytest.main([__file__])
