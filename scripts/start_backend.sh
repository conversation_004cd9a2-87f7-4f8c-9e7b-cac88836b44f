#!/bin/bash
# start_backend.sh - Quick startup script for MCX3D backend services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo -e "${BLUE}🚀 MCX3D Backend Services Startup${NC}"
echo "=========================================="

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Check Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_status "Docker and Docker Compose are installed"
}

# Check if .env file exists
check_env_file() {
    if [ ! -f "$PROJECT_ROOT/.env" ]; then
        print_warning ".env file not found. Creating from .env.example..."
        cp "$PROJECT_ROOT/.env.example" "$PROJECT_ROOT/.env"
        
        # Generate security keys
        print_info "Generating security keys..."
        SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_urlsafe(32))")
        ENCRYPTION_KEY=$(python3 -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())")
        
        # Update .env file with generated keys
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            sed -i '' "s/SECRET_KEY=/SECRET_KEY=$SECRET_KEY/" "$PROJECT_ROOT/.env"
            sed -i '' "s/ENCRYPTION_KEY=/ENCRYPTION_KEY=$ENCRYPTION_KEY/" "$PROJECT_ROOT/.env"
        else
            # Linux
            sed -i "s/SECRET_KEY=/SECRET_KEY=$SECRET_KEY/" "$PROJECT_ROOT/.env"
            sed -i "s/ENCRYPTION_KEY=/ENCRYPTION_KEY=$ENCRYPTION_KEY/" "$PROJECT_ROOT/.env"
        fi
        
        print_warning "Please update .env file with your Xero credentials and other settings"
        print_warning "Edit: $PROJECT_ROOT/.env"
    else
        print_status ".env file exists"
    fi
}

# Start services based on mode
start_services() {
    local mode="${1:-basic}"
    
    case "$mode" in
        "basic")
            print_info "Starting basic services (PostgreSQL, Redis, Web, Worker)..."
            docker-compose -f "$PROJECT_ROOT/docker-compose.yml" up -d
            ;;
        
        "monitoring")
            print_info "Starting all services including monitoring stack..."
            docker-compose -f "$PROJECT_ROOT/docker-compose.yml" \
                          -f "$PROJECT_ROOT/docker-compose.monitoring.yml" up -d
            ;;
        
        "dev")
            print_info "Starting services in development mode with logs..."
            docker-compose -f "$PROJECT_ROOT/docker-compose.yml" up
            ;;
            
        *)
            print_error "Unknown mode: $mode"
            echo "Usage: $0 [basic|monitoring|dev]"
            exit 1
            ;;
    esac
}

# Wait for services to be healthy
wait_for_services() {
    print_info "Waiting for services to be healthy..."
    
    # Wait for PostgreSQL
    echo -n "  PostgreSQL: "
    while ! docker-compose exec -T db pg_isready -U user -d mcx3d_db &> /dev/null; do
        echo -n "."
        sleep 2
    done
    echo -e " ${GREEN}Ready${NC}"
    
    # Wait for Redis
    echo -n "  Redis: "
    while ! docker-compose exec -T redis redis-cli ping &> /dev/null; do
        echo -n "."
        sleep 2
    done
    echo -e " ${GREEN}Ready${NC}"
    
    # Wait for API
    echo -n "  API: "
    while ! curl -s http://localhost:8000/health/basic &> /dev/null; do
        echo -n "."
        sleep 2
    done
    echo -e " ${GREEN}Ready${NC}"
}

# Run database migrations
run_migrations() {
    print_info "Running database migrations..."
    docker-compose exec -T web alembic upgrade head
    print_status "Migrations completed"
}

# Perform health checks
perform_health_checks() {
    print_info "Performing health checks..."
    
    # Basic health check
    if curl -s http://localhost:8000/health/basic | grep -q "healthy"; then
        print_status "Basic health check passed"
    else
        print_error "Basic health check failed"
    fi
    
    # Comprehensive health check
    if curl -s http://localhost:8000/health/comprehensive | grep -q "healthy"; then
        print_status "Comprehensive health check passed"
    else
        print_warning "Some components may not be fully configured"
    fi
}

# Show service URLs
show_service_urls() {
    echo
    echo -e "${GREEN}🎉 Services are running!${NC}"
    echo "=========================================="
    echo -e "${BLUE}API Documentation:${NC} http://localhost:8000/docs"
    echo -e "${BLUE}Health Status:${NC} http://localhost:8000/health/comprehensive"
    
    if [[ "$1" == "monitoring" ]]; then
        echo -e "${BLUE}Grafana:${NC} http://localhost:3000 (admin/mcx3d_admin_2024)"
        echo -e "${BLUE}Prometheus:${NC} http://localhost:9090"
    fi
    
    echo
    echo -e "${GREEN}Quick Commands:${NC}"
    echo "  View logs:        docker-compose logs -f"
    echo "  Stop services:    docker-compose down"
    echo "  View status:      docker-compose ps"
}

# Main execution
main() {
    local mode="${1:-basic}"
    
    cd "$PROJECT_ROOT"
    
    print_info "Starting MCX3D backend services in '$mode' mode"
    
    # Pre-flight checks
    check_docker
    check_env_file
    
    # Start services
    start_services "$mode"
    
    # Wait for services
    wait_for_services
    
    # Run migrations
    run_migrations
    
    # Health checks
    perform_health_checks
    
    # Show URLs
    show_service_urls "$mode"
    
    print_status "Startup complete!"
}

# Handle script arguments
case "${1:-help}" in
    "basic"|"monitoring"|"dev")
        main "$1"
        ;;
    
    "stop")
        print_info "Stopping all services..."
        cd "$PROJECT_ROOT"
        docker-compose down
        print_status "All services stopped"
        ;;
    
    "status")
        cd "$PROJECT_ROOT"
        docker-compose ps
        ;;
    
    "logs")
        cd "$PROJECT_ROOT"
        docker-compose logs -f
        ;;
    
    "help"|*)
        echo -e "${BLUE}MCX3D Backend Startup Script${NC}"
        echo "Usage: $0 [command]"
        echo
        echo "Commands:"
        echo "  basic       Start core services only (default)"
        echo "  monitoring  Start all services including monitoring"
        echo "  dev         Start in development mode with live logs"
        echo "  stop        Stop all services"
        echo "  status      Show service status"
        echo "  logs        Show service logs"
        echo "  help        Show this help message"
        echo
        echo "Examples:"
        echo "  $0 basic       # Start core services"
        echo "  $0 monitoring  # Start with full monitoring stack"
        echo "  $0 stop        # Stop all services"
        ;;
esac