"""
Dashboard API endpoints for web interface.
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Dict, Any
from datetime import datetime, timedelta
from mcx3d_finance.db.session import get_db
from mcx3d_finance.core.metrics.saas_kpis import SaaSKPICalculator
from mcx3d_finance.core.financials.balance_sheet import BalanceSheetGenerator
from mcx3d_finance.core.financials.income_statement import IncomeStatementGenerator

router = APIRouter()


@router.get("/dashboard/{organization_id}")
async def get_dashboard_data(
    organization_id: int,
    period_months: int = Query(12, description="Number of months to include"),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """Get comprehensive dashboard data."""

    try:
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=period_months * 30)

        # Get KPIs
        kpi_calculator = SaaSKPICalculator()
        kpis = kpi_calculator.calculate_comprehensive_kpis(
            organization_id, start_date, end_date
        )

        # Get financial summary
        balance_sheet_gen = BalanceSheetGenerator(db)
        income_stmt_gen = IncomeStatementGenerator(db)

        latest_balance_sheet = balance_sheet_gen.generate_balance_sheet(
            organization_id, end_date
        )

        latest_income_stmt = income_stmt_gen.generate_income_statement(
            organization_id, start_date, end_date
        )

        return {
            "organization_id": organization_id,
            "period": {
                "start": start_date.isoformat(),
                "end": end_date.isoformat(),
                "months": period_months,
            },
            "kpis": kpis,
            "financial_summary": {
                "balance_sheet": latest_balance_sheet,
                "income_statement": latest_income_stmt,
            },
            "generated_at": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error generating dashboard: {str(e)}"
        )
