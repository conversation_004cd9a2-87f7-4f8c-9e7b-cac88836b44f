# tests/e2e/test_cli_exports.py

import os
import json
import shutil
import tempfile
import subprocess
import sys
from pathlib import Path
from datetime import datetime
import pytest
from click.testing import CliRunner
from decimal import Decimal

from mcx3d_finance.cli.main import cli
from mcx3d_finance.cli.valuation import valuate


@pytest.mark.e2e
@pytest.mark.cli
class TestCLIExports:
    """
    End-to-end tests for CLI export functionality.
    Tests the complete command-line interface for report generation and export.
    """
    
    @pytest.fixture
    def temp_work_dir(self):
        """Create a temporary working directory for CLI tests."""
        temp_dir = tempfile.mkdtemp(prefix="mcx3d_cli_test_")
        original_cwd = os.getcwd()
        os.chdir(temp_dir)
        yield temp_dir
        os.chdir(original_cwd)
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def cli_runner(self):
        """Create a Click CLI runner for testing."""
        return CliRunner()
    
    @pytest.fixture
    def dcf_config_file(self, temp_work_dir):
        """Create a comprehensive DCF configuration file for CLI testing."""
        config_data = {
            "company_name": "CLI Test Corp",
            "valuation_date": "2024-01-20",
            "currency": "USD",
            "industry": "Technology",
            "projections": {
                "revenue": [
                    "4000000",   # $4M Year 1
                    "5200000",   # $5.2M Year 2 (30% growth)
                    "6240000",   # $6.24M Year 3 (20% growth)
                    "7488000",   # $7.488M Year 4 (20% growth)
                    "8985600"    # $8.986M Year 5 (20% growth)
                ],
                "operating_expenses": [
                    "2800000",   # 70% of revenue
                    "3120000",   # 60% of revenue
                    "3432000",   # 55% of revenue
                    "3744000",   # 50% of revenue
                    "4042608"    # 45% of revenue
                ],
                "capex": [
                    "200000",    # 5% of revenue
                    "260000",    # 5% of revenue
                    "312000",    # 5% of revenue
                    "374400",    # 5% of revenue
                    "449280"     # 5% of revenue
                ],
                "depreciation": [
                    "160000",
                    "195000",
                    "234000",
                    "280800",
                    "336960"
                ],
                "working_capital_changes": [
                    "80000",
                    "104000",
                    "124800",
                    "149760",
                    "179712"
                ]
            },
            "assumptions": {
                "discount_rate": "0.12",     # 12% WACC
                "terminal_growth": "0.025",  # 2.5% terminal growth
                "tax_rate": "0.21",          # 21% corporate tax
                "years": 5
            },
            "market_data": {
                "risk_free_rate": "0.035",
                "market_risk_premium": "0.065",
                "beta": "1.2",
                "debt_to_equity": "0.20"
            },
            "export_settings": {
                "output_directory": "./reports/valuations",
                "filename_prefix": "cli_test_dcf",
                "include_sensitivity": True,
                "include_charts": True
            }
        }
        
        config_path = Path(temp_work_dir) / "dcf_config.json"
        with open(config_path, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        return str(config_path)
    
    @pytest.fixture
    def saas_config_file(self, temp_work_dir):
        """Create a comprehensive SaaS configuration file for CLI testing."""
        config_data = {
            "company_name": "CLI SaaS Test Inc",
            "valuation_date": "2024-01-20",
            "currency": "USD",
            "metrics": {
                "arr": [
                    "3000000",   # $3M ARR
                    "4500000",   # $4.5M ARR (50% growth)
                    "6300000",   # $6.3M ARR (40% growth)
                    "8190000",   # $8.19M ARR (30% growth)
                    "10237500"   # $10.24M ARR (25% growth)
                ],
                "monthly_churn_rate": [0.04, 0.035, 0.03, 0.027, 0.024],
                "customer_acquisition_cost": "250",
                "average_revenue_per_user": "625",
                "gross_margin": "0.88",
                "customer_lifetime_value": "3125",
                "net_revenue_retention": "1.22",
                "magic_number": "1.45"
            },
            "assumptions": {
                "discount_rate": "0.13",      # 13% for SaaS
                "terminal_multiple": "10.0",  # 10x ARR terminal
                "growth_efficiency": "0.8"
            },
            "benchmarks": {
                "revenue_multiple_range": [9, 16],
                "growth_rate_median": "0.42",
                "churn_rate_benchmark": "0.03",
                "cac_payback_benchmark": 9
            },
            "export_settings": {
                "output_directory": "./reports/valuations",
                "filename_prefix": "cli_test_saas",
                "include_benchmarks": True,
                "include_quality_score": True
            }
        }
        
        config_path = Path(temp_work_dir) / "saas_config.json"
        with open(config_path, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        return str(config_path)

    @pytest.mark.pdf
    def test_dcf_pdf_export_command(self, cli_runner, dcf_config_file, temp_work_dir):
        """Test DCF PDF export via CLI command."""
        # Test command: python -m mcx3d_finance.cli.main valuate dcf --config config.json --export pdf
        result = cli_runner.invoke(valuate, [
            'dcf',
            '--config', dcf_config_file,
            '--export', 'pdf'
        ])
        
        # Validate command execution
        assert result.exit_code == 0, f"CLI command failed with exit code {result.exit_code}. Output: {result.output}"
        
        # Check for success indicators in output
        success_indicators = [
            "DCF valuation completed",
            "PDF report generated",
            "Export completed successfully",
            ".pdf"
        ]
        
        output_text = result.output
        found_indicators = [indicator for indicator in success_indicators if indicator.lower() in output_text.lower()]
        assert len(found_indicators) >= 1, f"No success indicators found in output: {output_text}"
        
        # Validate output file was created
        reports_dir = Path(temp_work_dir) / "reports" / "valuations"
        
        # Look for generated PDF files
        pdf_files = list(reports_dir.rglob("*.pdf"))
        assert len(pdf_files) >= 1, f"No PDF files found in {reports_dir}. Available files: {list(reports_dir.rglob('*')) if reports_dir.exists() else 'Directory does not exist'}"
        
        # Validate the PDF file
        pdf_file = pdf_files[0]
        assert pdf_file.exists(), f"PDF file does not exist: {pdf_file}"
        assert pdf_file.stat().st_size > 10000, f"PDF file is too small: {pdf_file.stat().st_size} bytes"
        
        # Validate PDF header
        with open(pdf_file, 'rb') as f:
            header = f.read(4)
            assert header == b'%PDF', f"Generated file is not a valid PDF: {pdf_file}"

    @pytest.mark.excel
    def test_saas_excel_export_command(self, cli_runner, saas_config_file, temp_work_dir):
        """Test SaaS Excel export via CLI command."""
        # Test command: python -m mcx3d_finance.cli.main valuate saas --config config.json --export excel
        result = cli_runner.invoke(valuate, [
            'saas',
            '--config', saas_config_file,
            '--export', 'excel'
        ])
        
        # Validate command execution
        assert result.exit_code == 0, f"CLI command failed with exit code {result.exit_code}. Output: {result.output}"
        
        # Check for success indicators in output
        success_indicators = [
            "SaaS valuation completed",
            "Excel report generated",
            "Export completed successfully",
            ".xlsx"
        ]
        
        output_text = result.output
        found_indicators = [indicator for indicator in success_indicators if indicator.lower() in output_text.lower()]
        assert len(found_indicators) >= 1, f"No success indicators found in output: {output_text}"
        
        # Validate output file was created
        reports_dir = Path(temp_work_dir) / "reports" / "valuations"
        
        # Look for generated Excel files
        excel_files = list(reports_dir.rglob("*.xlsx"))
        assert len(excel_files) >= 1, f"No Excel files found in {reports_dir}. Available files: {list(reports_dir.rglob('*')) if reports_dir.exists() else 'Directory does not exist'}"
        
        # Validate the Excel file
        excel_file = excel_files[0]
        assert excel_file.exists(), f"Excel file does not exist: {excel_file}"
        assert excel_file.stat().st_size > 5000, f"Excel file is too small: {excel_file.stat().st_size} bytes"
        
        # Basic Excel file validation (magic bytes)
        with open(excel_file, 'rb') as f:
            header = f.read(4)
            # Excel files start with PK (ZIP format)
            assert header[:2] == b'PK', f"Generated file is not a valid Excel file: {excel_file}"

    def test_export_output_directory_creation(self, cli_runner, dcf_config_file, temp_work_dir):
        """Verify organized directory structure creation: ./reports/valuations/YYYYMMDD/"""
        # Run DCF export command
        result = cli_runner.invoke(valuate, [
            'dcf',
            '--config', dcf_config_file,
            '--export', 'pdf',
            '--export', 'excel'  # Export both formats
        ])
        
        assert result.exit_code == 0, f"CLI command failed: {result.output}"
        
        # Validate directory structure
        reports_base = Path(temp_work_dir) / "reports"
        valuations_dir = reports_base / "valuations"
        
        assert reports_base.exists(), f"Reports base directory not created: {reports_base}"
        assert valuations_dir.exists(), f"Valuations directory not created: {valuations_dir}"
        
        # Look for dated subdirectory (YYYYMMDD format)
        today = datetime.now().strftime("%Y%m%d")
        expected_date_dirs = [
            valuations_dir / today,
            # Also check for slightly different dates in case test runs over midnight
            valuations_dir / datetime.now().strftime("%Y%m%d"),
        ]
        
        date_dir_found = False
        actual_date_dir = None
        
        for date_dir in expected_date_dirs:
            if date_dir.exists():
                date_dir_found = True
                actual_date_dir = date_dir
                break
        
        # If no exact date match, look for any YYYYMMDD pattern
        if not date_dir_found:
            date_dirs = [d for d in valuations_dir.iterdir() if d.is_dir() and len(d.name) == 8 and d.name.isdigit()]
            if date_dirs:
                date_dir_found = True
                actual_date_dir = date_dirs[0]
        
        assert date_dir_found, f"No dated directory found in {valuations_dir}. Contents: {list(valuations_dir.iterdir()) if valuations_dir.exists() else 'Directory does not exist'}"
        
        # Validate files in the dated directory
        pdf_files = list(actual_date_dir.rglob("*.pdf"))
        excel_files = list(actual_date_dir.rglob("*.xlsx"))
        
        assert len(pdf_files) >= 1, f"No PDF files in dated directory: {actual_date_dir}"
        assert len(excel_files) >= 1, f"No Excel files in dated directory: {actual_date_dir}"
        
        # Validate file naming conventions
        for pdf_file in pdf_files:
            assert "dcf" in pdf_file.name.lower(), f"PDF file doesn't follow naming convention: {pdf_file.name}"
        
        for excel_file in excel_files:
            assert "dcf" in excel_file.name.lower(), f"Excel file doesn't follow naming convention: {excel_file.name}"

    def test_cli_export_error_handling(self, cli_runner, temp_work_dir):
        """Test CLI error handling for invalid configs, missing permissions, etc."""
        
        # Test 1: Invalid configuration file
        invalid_config = Path(temp_work_dir) / "invalid_config.json"
        with open(invalid_config, 'w') as f:
            json.dump({"invalid": "data"}, f)
        
        result = cli_runner.invoke(valuate, [
            'dcf',
            '--config', str(invalid_config),
            '--export', 'pdf'
        ])
        
        assert result.exit_code != 0, "CLI should fail with invalid configuration"
        
        error_indicators = ["error", "invalid", "missing", "failed"]
        output_lower = result.output.lower()
        found_error_indicators = [indicator for indicator in error_indicators if indicator in output_lower]
        assert len(found_error_indicators) >= 1, f"No error indicators in output: {result.output}"
        
        # Test 2: Non-existent configuration file
        result = cli_runner.invoke(valuate, [
            'dcf',
            '--config', 'non_existent_config.json',
            '--export', 'pdf'
        ])
        
        assert result.exit_code != 0, "CLI should fail with non-existent configuration file"
        assert "not found" in result.output.lower() or "error" in result.output.lower(), f"No file not found error in output: {result.output}"
        
        # Test 3: Invalid export format
        valid_config = Path(temp_work_dir) / "valid_config.json"
        with open(valid_config, 'w') as f:
            json.dump({
                "company_name": "Test Corp",
                "projections": {"revenue": [1000000]},
                "assumptions": {"discount_rate": "0.1", "terminal_growth": "0.03", "tax_rate": "0.21"}
            }, f)
        
        result = cli_runner.invoke(valuate, [
            'dcf',
            '--config', str(valid_config),
            '--export', 'invalid_format'
        ])
        
        assert result.exit_code != 0, "CLI should fail with invalid export format"
        
        # Test 4: Read-only directory (simulate permission error)
        readonly_dir = Path(temp_work_dir) / "readonly"
        readonly_dir.mkdir()
        
        # Create config pointing to readonly directory
        readonly_config_data = {
            "company_name": "Test Corp",
            "projections": {"revenue": ["1000000"]},
            "assumptions": {"discount_rate": "0.1", "terminal_growth": "0.03", "tax_rate": "0.21"},
            "export_settings": {"output_directory": str(readonly_dir)}
        }
        
        readonly_config = Path(temp_work_dir) / "readonly_config.json"
        with open(readonly_config, 'w') as f:
            json.dump(readonly_config_data, f)
        
        try:
            # Make directory readonly
            readonly_dir.chmod(0o444)
            
            result = cli_runner.invoke(valuate, [
                'dcf',
                '--config', str(readonly_config),
                '--export', 'pdf'
            ])
            
            # Should handle permission error gracefully
            if result.exit_code != 0:
                permission_indicators = ["permission", "access", "readonly", "denied"]
                found_permission_indicators = [ind for ind in permission_indicators if ind in result.output.lower()]
                # We expect either success or a graceful error message
                assert len(found_permission_indicators) >= 1 or "error" in result.output.lower(), \
                    f"No appropriate error message for permission issue: {result.output}"
        
        finally:
            # Restore permissions for cleanup
            try:
                readonly_dir.chmod(0o755)
            except:
                pass

    @pytest.mark.integration
    def test_multiple_export_formats_simultaneously(self, cli_runner, dcf_config_file, temp_work_dir):
        """Test exporting multiple formats in a single command."""
        result = cli_runner.invoke(valuate, [
            'dcf',
            '--config', dcf_config_file,
            '--export', 'pdf',
            '--export', 'excel'
        ])
        
        assert result.exit_code == 0, f"Multi-format export failed: {result.output}"
        
        # Validate both formats were created
        reports_dir = Path(temp_work_dir) / "reports" / "valuations"
        
        pdf_files = list(reports_dir.rglob("*.pdf"))
        excel_files = list(reports_dir.rglob("*.xlsx"))
        
        assert len(pdf_files) >= 1, f"PDF not generated in multi-format export"
        assert len(excel_files) >= 1, f"Excel not generated in multi-format export"
        
        # Validate output mentions both formats
        output_lower = result.output.lower()
        assert "pdf" in output_lower, "PDF export not mentioned in output"
        assert "excel" in output_lower or "xlsx" in output_lower, "Excel export not mentioned in output"

    @pytest.mark.performance
    def test_cli_export_performance_monitoring(self, cli_runner, dcf_config_file, temp_work_dir):
        """Monitor CLI export performance and execution time."""
        import time
        
        start_time = time.time()
        
        result = cli_runner.invoke(valuate, [
            'dcf',
            '--config', dcf_config_file,
            '--export', 'pdf'
        ])
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Validate successful execution
        assert result.exit_code == 0, f"Performance test command failed: {result.output}"
        
        # Performance assertions
        assert execution_time < 30, f"CLI export took too long: {execution_time:.2f} seconds"
        
        # Validate file was created within reasonable time
        reports_dir = Path(temp_work_dir) / "reports" / "valuations"
        pdf_files = list(reports_dir.rglob("*.pdf"))
        assert len(pdf_files) >= 1, "No PDF file created during performance test"
        
        # Validate file size is reasonable for the execution time
        pdf_file = pdf_files[0]
        file_size = pdf_file.stat().st_size
        
        # Rough performance heuristic: should generate at least 1KB per second
        min_expected_size = max(5000, int(execution_time * 1000))  # At least 5KB or 1KB/second
        assert file_size >= min_expected_size, \
            f"File size too small for execution time: {file_size} bytes in {execution_time:.2f}s"

    @pytest.mark.cli
    def test_cli_help_and_documentation(self, cli_runner):
        """Test CLI help system and documentation availability."""
        # Test main CLI help
        result = cli_runner.invoke(cli, ['--help'])
        assert result.exit_code == 0, "Main CLI help command failed"
        assert "valuate" in result.output, "Valuate command not listed in main help"
        
        # Test valuate subcommand help
        result = cli_runner.invoke(valuate, ['--help'])
        assert result.exit_code == 0, "Valuate help command failed"
        
        help_indicators = ["dcf", "saas", "config", "export"]
        found_indicators = [indicator for indicator in help_indicators if indicator in result.output.lower()]
        assert len(found_indicators) >= 3, f"Insufficient help content: {found_indicators}"
        
        # Test DCF subcommand help
        result = cli_runner.invoke(valuate, ['dcf', '--help'])
        assert result.exit_code == 0, "DCF help command failed"
        
        dcf_help_indicators = ["config", "export", "pdf", "excel"]
        found_dcf_indicators = [indicator for indicator in dcf_help_indicators if indicator in result.output.lower()]
        assert len(found_dcf_indicators) >= 2, f"Insufficient DCF help content: {found_dcf_indicators}"

    @pytest.mark.integration
    def test_end_to_end_workflow(self, cli_runner, temp_work_dir):
        """Test complete end-to-end workflow from config creation to report generation."""
        
        # Step 1: Create configuration programmatically
        workflow_config = {
            "company_name": "End-to-End Test Corp",
            "valuation_date": "2024-01-25",
            "currency": "USD",
            "projections": {
                "revenue": ["2000000", "2600000", "3120000", "3744000", "4492800"],
                "operating_expenses": ["1400000", "1560000", "1716000", "1872000", "2021004"],
                "capex": ["100000", "130000", "156000", "187200", "224640"],
                "depreciation": ["80000", "104000", "124800", "149760", "179712"],
                "working_capital_changes": ["40000", "52000", "62400", "74880", "89856"]
            },
            "assumptions": {
                "discount_rate": "0.11",
                "terminal_growth": "0.025",
                "tax_rate": "0.21",
                "years": 5
            },
            "market_data": {
                "risk_free_rate": "0.03",
                "market_risk_premium": "0.06",
                "beta": "1.15",
                "debt_to_equity": "0.15"
            }
        }
        
        config_path = Path(temp_work_dir) / "workflow_config.json"
        with open(config_path, 'w') as f:
            json.dump(workflow_config, f, indent=2)
        
        # Step 2: Execute DCF valuation with multiple export formats
        result = cli_runner.invoke(valuate, [
            'dcf',
            '--config', str(config_path),
            '--export', 'pdf',
            '--export', 'excel'
        ])
        
        assert result.exit_code == 0, f"End-to-end workflow failed: {result.output}"
        
        # Step 3: Validate complete output structure
        reports_dir = Path(temp_work_dir) / "reports" / "valuations"
        assert reports_dir.exists(), "Reports directory not created in workflow"
        
        # Step 4: Validate both report types were generated
        pdf_files = list(reports_dir.rglob("*.pdf"))
        excel_files = list(reports_dir.rglob("*.xlsx"))
        
        assert len(pdf_files) >= 1, "PDF not generated in end-to-end workflow"
        assert len(excel_files) >= 1, "Excel not generated in end-to-end workflow"
        
        # Step 5: Validate file contents and structure
        pdf_file = pdf_files[0]
        excel_file = excel_files[0]
        
        # Basic file validation
        assert pdf_file.stat().st_size > 10000, f"PDF file too small in workflow: {pdf_file.stat().st_size}"
        assert excel_file.stat().st_size > 5000, f"Excel file too small in workflow: {excel_file.stat().st_size}"
        
        # Step 6: Validate output feedback
        output_lines = result.output.strip().split('\n')
        assert len(output_lines) >= 3, "Insufficient output feedback in workflow"
        
        success_keywords = ["completed", "generated", "success", "export"]
        output_text = result.output.lower()
        found_success_keywords = [kw for kw in success_keywords if kw in output_text]
        assert len(found_success_keywords) >= 2, f"Insufficient success indicators in workflow: {found_success_keywords}"
