# Financial Data Download Guide - MCX3D Finance

This guide covers downloading, synchronizing, and exporting financial data from connected accounting systems like Xero.

## Overview

The MCX3D Finance data management system provides:

- **Xero Integration**: Comprehensive Xero API integration with OAuth2 authentication
- **Data Download**: CLI commands to download financial data from connected sources
- **Data Export**: Export data to CSV, JSON, and Excel formats
- **Real-time Sync**: Automated synchronization with rate limiting and error handling
- **Multi-Format Support**: Flexible export options for different use cases

## 🚀 Quick Start

### List Available Organizations

Before downloading data, check which organizations are available and their connection status:

```bash
# Set audit log location
export AUDIT_LOG_FILE="./logs/audit.log"

# List all organizations with connection status
python3 -m mcx3d_finance.cli.main sync list-orgs
```

**Example Output:**
```
🏢 Organizations
================================================================================

🏢 Acme Corporation
   ID: 123
   Status: 🟢 Connected
   Currency: USD
   Xero Tenant: ********-1234-1234-1234-********9012
   Tenant Type: ORGANISATION
   Last Sync: 2024-01-15 14:30:22
   Sync Status: completed
   Created: 2024-01-01 09:00:00

📊 Summary: 1 organizations total, 1 connected to Xero
```

### Download Financial Data

Download all financial data for an organization:

```bash
# Download all data types for organization
python3 -m mcx3d_finance.cli.main sync download --org-id 123

# Download specific data types
python3 -m mcx3d_finance.cli.main sync download \
  --org-id 123 \
  --types accounts,contacts,transactions

# Download data for specific date range
python3 -m mcx3d_finance.cli.main sync download \
  --org-id 123 \
  --start-date 2024-01-01 \
  --end-date 2024-12-31
```

### Export Data to Different Formats

```bash
# Export to CSV (creates directory with multiple CSV files)
python3 -m mcx3d_finance.cli.main sync export \
  --org-id 123 \
  --format csv

# Export to JSON (single file)
python3 -m mcx3d_finance.cli.main sync export \
  --org-id 123 \
  --format json \
  --output financial_data.json

# Export to Excel (single file with multiple sheets)
python3 -m mcx3d_finance.cli.main sync export \
  --org-id 123 \
  --format excel \
  --tables accounts,contacts,transactions
```

## 📋 CLI Commands Reference

### Data Management Commands

#### `sync list-orgs` - List Organizations
Shows all organizations with their connection status and sync information.

**No options required**

**Output includes:**
- Organization ID and name
- Connection status (Connected/Not connected)
- Base currency
- Xero tenant information
- Last sync timestamp and status
- Creation date

#### `sync download` - Download Financial Data
Downloads financial data from connected sources and saves to local files.

**Options:**
- `--org-id` (required): Organization ID to download data from
- `--types`: Comma-separated data types to download
- `--start-date`: Start date for filtering (YYYY-MM-DD format)
- `--end-date`: End date for filtering (YYYY-MM-DD format)
- `--incremental`: Download only new/updated data since last sync
- `--output-dir`: Output directory (default: `./data_downloads`)

**Available Data Types:**
- `accounts`: Chart of accounts with GAAP classifications
- `contacts`: Customers and suppliers with contact details
- `transactions`: Financial transactions and entries
- `invoices`: Sales and purchase invoices
- `bank_transactions`: Bank feeds and reconciliation data

**Examples:**
```bash
# Download all data types
python3 -m mcx3d_finance.cli.main sync download --org-id 123

# Download specific data types
python3 -m mcx3d_finance.cli.main sync download \
  --org-id 123 \
  --types accounts,contacts

# Download with date filtering
python3 -m mcx3d_finance.cli.main sync download \
  --org-id 123 \
  --start-date 2024-01-01 \
  --end-date 2024-03-31

# Incremental download (only changed data)
python3 -m mcx3d_finance.cli.main sync download \
  --org-id 123 \
  --incremental

# Custom output directory
python3 -m mcx3d_finance.cli.main sync download \
  --org-id 123 \
  --output-dir ./company_data
```

#### `sync export` - Export Financial Data
Exports data from the local database to various formats.

**Options:**
- `--org-id` (required): Organization ID to export data from
- `--format`: Export format (`csv`, `json`, `excel`) - default: `csv`
- `--tables`: Comma-separated table names to export
- `--start-date`: Start date for filtering (YYYY-MM-DD format)
- `--end-date`: End date for filtering (YYYY-MM-DD format)
- `--output`: Custom output file path

**Available Tables:**
- `accounts`: Chart of accounts
- `contacts`: Customer and supplier contacts
- `transactions`: Financial transactions
- `invoices`: Sales and purchase invoices
- `bank_transactions`: Bank transaction data

**Examples:**
```bash
# Export all tables to CSV directory
python3 -m mcx3d_finance.cli.main sync export \
  --org-id 123 \
  --format csv

# Export specific tables to JSON
python3 -m mcx3d_finance.cli.main sync export \
  --org-id 123 \
  --format json \
  --tables accounts,contacts \
  --output company_data.json

# Export to Excel with date filtering
python3 -m mcx3d_finance.cli.main sync export \
  --org-id 123 \
  --format excel \
  --start-date 2024-01-01 \
  --end-date 2024-12-31 \
  --output financial_report.xlsx

# Export transactions only
python3 -m mcx3d_finance.cli.main sync export \
  --org-id 123 \
  --format csv \
  --tables transactions
```

#### `sync xero` - Xero Data Synchronization
Synchronizes data directly from Xero accounting system.

**Options:**
- `--org-id` (required): Organization ID to sync
- `--incremental`: Run incremental sync instead of full sync
- `--async-mode`: Run sync asynchronously using Celery
- `--show-progress`: Show progress during sync (default: true)

**Examples:**
```bash
# Full synchronization
python3 -m mcx3d_finance.cli.main sync xero --org-id 123

# Incremental synchronization
python3 -m mcx3d_finance.cli.main sync xero \
  --org-id 123 \
  --incremental

# Asynchronous sync (background task)
python3 -m mcx3d_finance.cli.main sync xero \
  --org-id 123 \
  --async-mode
```

#### `sync status` - Check Sync Status
Checks the status of an asynchronous sync task.

**Arguments:**
- `task_id` (required): Task ID returned by async sync

**Example:**
```bash
python3 -m mcx3d_finance.cli.main sync status abc123-def456-ghi789
```

## 💾 Data Formats and Structure

### JSON Export Format
```json
{
  "accounts": [
    {
      "id": 1,
      "xero_account_id": "********-1234-1234-1234-********9012",
      "code": "1000",
      "name": "Cash at Bank",
      "type": "BANK",
      "class_type": "ASSET",
      "gaap_classification": "Current Assets",
      "organization_id": 123,
      "created_at": "2024-01-01T00:00:00",
      "updated_at": "2024-01-01T00:00:00"
    }
  ],
  "contacts": [
    {
      "id": 1,
      "xero_contact_id": "********-4321-4321-4321-************",
      "name": "ABC Suppliers Ltd",
      "email_address": "<EMAIL>",
      "is_supplier": true,
      "is_customer": false,
      "organization_id": 123
    }
  ]
}
```

### CSV Export Structure
When exporting to CSV, each table is saved as a separate file:
```
export_org_123_20240115_143022/
├── accounts.csv
├── contacts.csv
├── transactions.csv
├── invoices.csv
└── bank_transactions.csv
```

### Excel Export Structure
Excel exports create a single file with multiple sheets:
- Sheet 1: accounts
- Sheet 2: contacts
- Sheet 3: transactions
- Sheet 4: invoices
- Sheet 5: bank_transactions

## 🔄 Data Synchronization Workflow

### Complete Data Download Process

```bash
# 1. Check organization connection status
python3 -m mcx3d_finance.cli.main sync list-orgs

# 2. Download all financial data
python3 -m mcx3d_finance.cli.main sync download \
  --org-id 123 \
  --output-dir ./company_financial_data

# 3. Export specific data for analysis
python3 -m mcx3d_finance.cli.main sync export \
  --org-id 123 \
  --format excel \
  --tables accounts,transactions \
  --output analysis_data.xlsx

# 4. Regular incremental updates
python3 -m mcx3d_finance.cli.main sync download \
  --org-id 123 \
  --incremental
```

### Automated Sync Scheduling

For regular data updates, you can create a script:

```bash
#!/bin/bash
# sync_financial_data.sh

export AUDIT_LOG_FILE="./logs/audit.log"
ORG_ID=123
DATE=$(date +%Y%m%d)

echo "Starting daily financial data sync - $DATE"

# Incremental download
python3 -m mcx3d_finance.cli.main sync download \
  --org-id $ORG_ID \
  --incremental \
  --output-dir "./daily_sync/$DATE"

# Export updated data
python3 -m mcx3d_finance.cli.main sync export \
  --org-id $ORG_ID \
  --format json \
  --output "./exports/financial_data_$DATE.json"

echo "Sync completed - $DATE"
```

## 📊 Available Financial Data

### Chart of Accounts
- **Account codes and names**
- **Account types** (ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE)
- **GAAP classifications** for NASDAQ compliance
- **Tax types and reporting codes**
- **Bank account details** where applicable

### Contacts (Customers & Suppliers)
- **Contact information** (name, email, phone)
- **Addresses** (street and postal)
- **Tax numbers and account numbers**
- **Customer/supplier flags**
- **Payment terms and default currency**

### Financial Transactions
- **Transaction details** (date, amount, description)
- **Account allocations and line items**
- **Currency and exchange rates**
- **Reconciliation status**
- **Reference numbers and attachments**

### Invoices (Sales & Purchase)
- **Invoice details** (number, date, due date)
- **Line items with quantities and amounts**
- **Tax calculations and totals**
- **Payment status and amounts**
- **Customer/supplier information**

### Bank Transactions
- **Bank feed data**
- **Transaction matching and reconciliation**
- **Statement lines and references**
- **Cash flow categorization**

## 🛠 Integration Examples

### Python Integration
```python
import subprocess
import json
from pathlib import Path

def download_financial_data(org_id, output_dir="./data"):
    """Download financial data for an organization."""
    
    # Set environment
    env = {"AUDIT_LOG_FILE": "./logs/audit.log"}
    
    # Download data
    cmd = [
        "python3", "-m", "mcx3d_finance.cli.main",
        "sync", "download",
        "--org-id", str(org_id),
        "--output-dir", output_dir,
        "--format", "json"
    ]
    
    result = subprocess.run(cmd, env=env, capture_output=True, text=True)
    
    if result.returncode == 0:
        print(f"✅ Data downloaded successfully to {output_dir}")
        return True
    else:
        print(f"❌ Download failed: {result.stderr}")
        return False

def export_data_to_excel(org_id, output_file="financial_data.xlsx"):
    """Export financial data to Excel format."""
    
    env = {"AUDIT_LOG_FILE": "./logs/audit.log"}
    
    cmd = [
        "python3", "-m", "mcx3d_finance.cli.main",
        "sync", "export",
        "--org-id", str(org_id),
        "--format", "excel",
        "--output", output_file
    ]
    
    result = subprocess.run(cmd, env=env, capture_output=True, text=True)
    
    if result.returncode == 0:
        print(f"✅ Data exported to {output_file}")
        return output_file
    else:
        print(f"❌ Export failed: {result.stderr}")
        return None

# Usage example
org_id = 123
if download_financial_data(org_id):
    export_data_to_excel(org_id, "company_financials.xlsx")
```

### Data Analysis Workflow
```python
import pandas as pd
import json

def analyze_financial_data(json_file):
    """Analyze exported financial data."""
    
    with open(json_file, 'r') as f:
        data = json.load(f)
    
    # Convert to DataFrames
    accounts_df = pd.DataFrame(data.get('accounts', []))
    transactions_df = pd.DataFrame(data.get('transactions', []))
    
    # Basic analysis
    print("=== Financial Data Analysis ===")
    print(f"Total Accounts: {len(accounts_df)}")
    print(f"Total Transactions: {len(transactions_df)}")
    
    if not transactions_df.empty:
        # Transaction analysis
        transactions_df['date'] = pd.to_datetime(transactions_df['date'])
        transactions_df['amount'] = pd.to_numeric(transactions_df['amount'])
        
        print(f"Date Range: {transactions_df['date'].min()} to {transactions_df['date'].max()}")
        print(f"Total Transaction Value: ${transactions_df['amount'].sum():,.2f}")
        
        # Monthly summary
        monthly = transactions_df.groupby(transactions_df['date'].dt.to_period('M'))['amount'].sum()
        print("\nMonthly Transaction Summary:")
        print(monthly.head())
    
    return accounts_df, transactions_df

# Usage
accounts, transactions = analyze_financial_data("financial_data.json")
```

## 🛠 Troubleshooting

### Common Issues

#### "Organization with ID X not found"
**Solution**: Check available organizations:
```bash
python3 -m mcx3d_finance.cli.main sync list-orgs
```

#### "No active organizations found"
**Solution**: Create user and organization first:
```bash
python3 -m mcx3d_finance.cli.main user create \
  --email <EMAIL> \
  --name "Admin User" \
  --org-name "Your Company"
```

#### "Xero API credentials not configured"
**Solution**: Set environment variables:
```bash
export XERO_CLIENT_ID="your_client_id"
export XERO_CLIENT_SECRET="your_client_secret"
```

#### "pandas and openpyxl are required for Excel export"
**Solution**: Install required packages:
```bash
pip install pandas openpyxl
```

### Data Validation
- **Check data completeness** after download
- **Verify date ranges** match expectations
- **Validate account codes** and classifications
- **Confirm transaction totals** match source system

## 🔐 Security Considerations

### API Security
- **OAuth2 tokens** are encrypted at rest
- **Rate limiting** prevents API abuse
- **Audit logging** tracks all data access
- **Token refresh** handles expired credentials

### Data Protection
- **Local storage** of downloaded data
- **Encrypted database** storage
- **Access controls** by organization
- **Audit trails** for all operations

## 📞 Support

For additional help:
- Check [Xero Integration Guide](./xero-integration.md) for Xero-specific setup
- Review [API Documentation](../api/README.md) for technical details
- See [User Management Guide](./user-management-guide.md) for user setup

---

**Next Steps**: After downloading financial data, you can use it for:
- **Financial Reporting**: Generate P&L, Balance Sheet, and Cash Flow reports
- **Business Valuation**: Run DCF and multiples-based valuation models
- **Analytics**: Calculate SaaS KPIs and business metrics
- **Compliance**: Export data for audit and regulatory requirements