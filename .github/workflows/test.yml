name: MCX3D Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: mcx3d_test
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build test image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: false
        tags: mcx3d-test:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Create test environment file
      run: |
        cat > .env.test << EOF
        DATABASE_URL=postgresql://postgres:postgres@localhost:5432/mcx3d_test
        REDIS_URL=redis://localhost:6379
        ENVIRONMENT=test
        SECRET_KEY=test-secret-key-for-ci
        XERO_CLIENT_ID=test-client-id
        XERO_CLIENT_SECRET=test-client-secret
        XERO_WEBHOOK_KEY=test-webhook-key
        EOF

    - name: Start test containers
      run: |
        docker run -d \
          --name mcx3d-test \
          --network host \
          --env-file .env.test \
          -v ${{ github.workspace }}:/app \
          mcx3d-test:latest \
          tail -f /dev/null

    - name: Wait for services
      run: |
        # Wait for PostgreSQL
        timeout 30 bash -c 'until nc -z localhost 5432; do sleep 1; done'
        # Wait for Redis
        timeout 30 bash -c 'until nc -z localhost 6379; do sleep 1; done'

    - name: Run database migrations
      run: |
        docker exec mcx3d-test alembic upgrade head

    - name: Run unit tests
      run: |
        docker exec mcx3d-test pytest tests/ \
          --maxfail=10 \
          --tb=short \
          -v \
          --cov=mcx3d_finance \
          --cov-report=term-missing \
          --cov-report=xml \
          --cov-fail-under=75 \
          -m "not slow and not e2e" \
          --junitxml=test-results.xml

    - name: Run integration tests
      run: |
        docker exec mcx3d-test pytest tests/ \
          -m "integration and not slow" \
          --maxfail=5 \
          --tb=short \
          -v

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results
        path: test-results.xml

    - name: Cleanup
      if: always()
      run: |
        docker stop mcx3d-test || true
        docker rm mcx3d-test || true

  performance-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: test

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: mcx3d_test
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build test image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: false
        tags: mcx3d-test:latest
        cache-from: type=gha

    - name: Create test environment file
      run: |
        cat > .env.test << EOF
        DATABASE_URL=postgresql://postgres:postgres@localhost:5432/mcx3d_test
        REDIS_URL=redis://localhost:6379
        ENVIRONMENT=test
        SECRET_KEY=test-secret-key-for-ci
        XERO_CLIENT_ID=test-client-id
        XERO_CLIENT_SECRET=test-client-secret
        XERO_WEBHOOK_KEY=test-webhook-key
        EOF

    - name: Start test containers
      run: |
        docker run -d \
          --name mcx3d-perf \
          --network host \
          --env-file .env.test \
          -v ${{ github.workspace }}:/app \
          mcx3d-test:latest \
          tail -f /dev/null

    - name: Wait for services
      run: |
        timeout 30 bash -c 'until nc -z localhost 5432; do sleep 1; done'
        timeout 30 bash -c 'until nc -z localhost 6379; do sleep 1; done'

    - name: Run database migrations
      run: |
        docker exec mcx3d-perf alembic upgrade head

    - name: Establish performance baseline
      run: |
        docker exec mcx3d-perf python scripts/establish_baselines.py

    - name: Run performance tests
      run: |
        docker exec mcx3d-perf pytest tests/ \
          -m "performance and not slow" \
          --maxfail=3 \
          --tb=short \
          -v \
          --benchmark-save=ci-run

    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: |
          performance_baseline.json
          .benchmarks/

    - name: Performance regression check
      run: |
        # Compare current performance with baseline
        docker exec mcx3d-perf python -c "
        import json
        import sys
        
        try:
            with open('performance_baseline.json', 'r') as f:
                data = json.load(f)
            
            print('Performance Summary:')
            for test_name, test_data in data['tests'].items():
                avg_time = test_data['times']['average']
                success_rate = test_data['successful_iterations'] / test_data['iterations'] * 100
                print(f'{test_name}: {avg_time:.3f}s avg, {success_rate:.0f}% success')
                
                # Fail if any test takes longer than 1 second or has < 90% success rate
                if avg_time > 1.0:
                    print(f'ERROR: {test_name} too slow: {avg_time:.3f}s > 1.0s')
                    sys.exit(1)
                if success_rate < 90:
                    print(f'ERROR: {test_name} low success rate: {success_rate:.0f}% < 90%')
                    sys.exit(1)
                    
            print('All performance tests within acceptable limits.')
        except Exception as e:
            print(f'Error checking performance: {e}')
            sys.exit(1)
        "

    - name: Cleanup
      if: always()
      run: |
        docker stop mcx3d-perf || true
        docker rm mcx3d-perf || true

  e2e-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: test
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: mcx3d_test
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build test image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: false
        tags: mcx3d-test:latest
        cache-from: type=gha

    - name: Create test environment file
      run: |
        cat > .env.test << EOF
        DATABASE_URL=postgresql://postgres:postgres@localhost:5432/mcx3d_test
        REDIS_URL=redis://localhost:6379
        ENVIRONMENT=test
        SECRET_KEY=test-secret-key-for-ci
        XERO_CLIENT_ID=test-client-id
        XERO_CLIENT_SECRET=test-client-secret
        XERO_WEBHOOK_KEY=test-webhook-key
        EOF

    - name: Start application stack
      run: |
        docker run -d \
          --name mcx3d-e2e \
          --network host \
          --env-file .env.test \
          -v ${{ github.workspace }}:/app \
          mcx3d-test:latest \
          tail -f /dev/null

    - name: Wait for services
      run: |
        timeout 30 bash -c 'until nc -z localhost 5432; do sleep 1; done'
        timeout 30 bash -c 'until nc -z localhost 6379; do sleep 1; done'

    - name: Run database migrations
      run: |
        docker exec mcx3d-e2e alembic upgrade head

    - name: Run end-to-end tests
      run: |
        docker exec mcx3d-e2e pytest tests/ \
          -m "e2e" \
          --maxfail=3 \
          --tb=short \
          -v

    - name: Run CLI tests
      run: |
        docker exec mcx3d-e2e pytest tests/ \
          -m "cli" \
          --maxfail=3 \
          --tb=short \
          -v

    - name: Cleanup
      if: always()
      run: |
        docker stop mcx3d-e2e || true
        docker rm mcx3d-e2e || true

  security-scan:
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Run Safety check for Python dependencies
      run: |
        pip install safety
        safety check --json --output safety-results.json || true

    - name: Upload security scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-results
        path: |
          trivy-results.sarif
          safety-results.json

  quality-gate:
    runs-on: ubuntu-latest
    needs: [test, performance-tests, security-scan]
    if: always()

    steps:
    - name: Check job results
      run: |
        echo "Test job result: ${{ needs.test.result }}"
        echo "Performance job result: ${{ needs.performance-tests.result }}"
        echo "Security job result: ${{ needs.security-scan.result }}"
        
        if [[ "${{ needs.test.result }}" != "success" ]]; then
          echo "❌ Tests failed"
          exit 1
        fi
        
        if [[ "${{ needs.performance-tests.result }}" != "success" ]]; then
          echo "❌ Performance tests failed"
          exit 1
        fi
        
        if [[ "${{ needs.security-scan.result }}" != "success" ]]; then
          echo "⚠️  Security scan completed with issues - review required"
          # Don't fail the build for security issues, just warn
        fi
        
        echo "✅ All quality gates passed!"

    - name: Create quality report
      run: |
        cat > quality-report.md << EOF
        # Quality Gate Report
        
        ## Test Results
        - Unit Tests: ${{ needs.test.result == 'success' && '✅ PASSED' || '❌ FAILED' }}
        - Performance Tests: ${{ needs.performance-tests.result == 'success' && '✅ PASSED' || '❌ FAILED' }}
        - Security Scan: ${{ needs.security-scan.result == 'success' && '✅ PASSED' || '⚠️ REVIEW REQUIRED' }}
        
        ## Coverage
        Target: 75% minimum
        
        ## Performance
        All report generation operations under 1 second
        
        ## Next Steps
        ${{ needs.test.result == 'success' && needs.performance-tests.result == 'success' && '🚀 Ready for deployment' || '🔧 Review and fix issues before deployment' }}
        EOF

    - name: Upload quality report
      uses: actions/upload-artifact@v3
      with:
        name: quality-report
        path: quality-report.md