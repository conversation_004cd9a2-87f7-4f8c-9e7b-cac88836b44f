#!/bin/bash

# Quick start script for MCX3D Financial Documentation System
# This script provides an interactive setup and document generation

echo "╔══════════════════════════════════════════════════════════╗"
echo "║        MCX3D LTD Financial Documentation System          ║"
echo "║                    Quick Start Guide                     ║"
echo "╚══════════════════════════════════════════════════════════╝"
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to prompt for environment variable
prompt_env_var() {
    local var_name=$1
    local var_description=$2
    local current_value=$(grep "^$var_name=" .env.development 2>/dev/null | cut -d'=' -f2)
    
    if [ -z "$current_value" ]; then
        echo ""
        read -p "Enter $var_description: " value
        echo "$var_name=$value" >> .env.development
    else
        echo "✓ $var_name is already set"
    fi
}

# Check prerequisites
echo "🔍 Checking prerequisites..."

if ! command_exists docker; then
    echo "❌ Docker is not installed. Please install from https://www.docker.com/get-started"
    exit 1
fi

if ! command_exists docker-compose; then
    echo "❌ docker-compose is not installed. Please install docker-compose"
    exit 1
fi

echo "✅ Prerequisites satisfied"

# Setup environment
echo ""
echo "🔧 Setting up environment..."

if [ ! -f ".env.development" ]; then
    echo "Creating .env.development from template..."
    cp .env.example .env.development
    
    echo ""
    echo "📝 Please provide your Xero API credentials:"
    echo "(You can obtain these from https://developer.xero.com/myapps)"
    
    prompt_env_var "XERO_CLIENT_ID" "Xero Client ID"
    prompt_env_var "XERO_CLIENT_SECRET" "Xero Client Secret"
    prompt_env_var "XERO_ACCESS_TOKEN" "Xero Access Token"
    prompt_env_var "XERO_REFRESH_TOKEN" "Xero Refresh Token"
    prompt_env_var "XERO_TENANT_ID" "Xero Tenant/Organization ID"
    
    # Generate secure keys
    echo ""
    echo "🔐 Generating secure keys..."
    SECRET_KEY=$(openssl rand -hex 32)
    ENCRYPTION_KEY=$(python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())")
    
    sed -i.bak "s/^SECRET_KEY=$/SECRET_KEY=$SECRET_KEY/" .env.development
    sed -i.bak "s/^ENCRYPTION_KEY=$/ENCRYPTION_KEY=$ENCRYPTION_KEY/" .env.development
    rm .env.development.bak
    
    echo "✅ Environment configuration complete"
else
    echo "✅ Environment already configured"
fi

# Create necessary directories
mkdir -p reports logs

# Launch application
echo ""
echo "🚀 Launching MCX3D Finance application..."
echo ""

# Build and start containers
docker-compose build
docker-compose up -d

# Wait for services
echo "⏳ Waiting for services to start..."
sleep 15

# Run migrations
echo "🗄️  Setting up database..."
docker-compose exec -T web alembic upgrade head

# Seed data
echo "🌱 Loading initial data..."
docker-compose exec -T web python -m mcx3d_finance.cli seed sample-data

# Main menu
while true; do
    echo ""
    echo "╔══════════════════════════════════════════════════════════╗"
    echo "║                     Main Menu                            ║"
    echo "╚══════════════════════════════════════════════════════════╝"
    echo ""
    echo "1. Generate Financial Documentation Package"
    echo "2. List Generated Documents"
    echo "3. View Application Status"
    echo "4. View Logs"
    echo "5. Stop Application"
    echo "6. Exit"
    echo ""
    read -p "Select an option (1-6): " choice
    
    case $choice in
        1)
            echo ""
            echo "📊 Generating financial documentation..."
            docker-compose exec web python -m mcx3d_finance.cli financial-docs generate
            echo ""
            echo "✅ Documents generated in ./reports/"
            read -p "Press Enter to continue..."
            ;;
        2)
            echo ""
            docker-compose exec web python -m mcx3d_finance.cli financial-docs list
            read -p "Press Enter to continue..."
            ;;
        3)
            echo ""
            echo "🌐 Application Status:"
            echo "   API: http://localhost:8000"
            echo "   API Docs: http://localhost:8000/docs"
            echo "   Health: http://localhost:8000/health/"
            echo ""
            docker-compose ps
            read -p "Press Enter to continue..."
            ;;
        4)
            echo ""
            echo "📋 Viewing logs (press Ctrl+C to stop)..."
            docker-compose logs -f --tail=50
            ;;
        5)
            echo ""
            read -p "Are you sure you want to stop the application? (y/N) " -n 1 -r
            echo ""
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                docker-compose down
                echo "✅ Application stopped"
            fi
            ;;
        6)
            echo ""
            echo "👋 Thank you for using MCX3D Financial Documentation System!"
            exit 0
            ;;
        *)
            echo "❌ Invalid option. Please select 1-6."
            ;;
    esac
done