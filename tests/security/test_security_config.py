"""
Security configuration tests for MCX3D Finance.
"""
import os
import pytest
from unittest.mock import patch, MagicMock

from mcx3d_finance.utils.security_validator import (
    validate_secret_key,
    validate_database_url,
    validate_oauth_transport,
    validate_encryption_key,
    validate_security_configuration,
    SecurityValidationError
)
from mcx3d_finance.utils.generate_keys import (
    generate_secret_key,
    generate_fernet_key,
    validate_secret_key as key_validator
)


class TestSecretKeyValidation:
    """Test secret key validation."""
    
    def test_valid_secret_key(self):
        """Test validation of a valid secret key."""
        # Generate a valid key
        key = generate_secret_key(64)
        valid, issues = validate_secret_key(key)
        assert valid is True
        assert len(issues) == 0
    
    def test_empty_secret_key(self):
        """Test validation of empty secret key."""
        valid, issues = validate_secret_key("")
        assert valid is False
        assert "Secret key is not set" in issues
    
    def test_short_secret_key(self):
        """Test validation of short secret key."""
        valid, issues = validate_secret_key("short_key")
        assert valid is False
        assert any("too short" in issue for issue in issues)
    
    def test_weak_secret_key(self):
        """Test detection of weak secret keys."""
        weak_keys = [
            "your-secret-key-here-minimum-32-characters-long",
            "password123456789012345678901234",
            "admin12345678901234567890123456789",
            "test_secret_key_with_32_characters"
        ]
        
        for key in weak_keys:
            valid, issues = validate_secret_key(key)
            assert valid is False
            assert any("weak pattern" in issue for issue in issues)
    
    def test_simple_secret_key(self):
        """Test detection of simple secret keys lacking complexity."""
        simple_keys = [
            "a" * 32,  # Only lowercase
            "A" * 32,  # Only uppercase
            "1" * 32,  # Only digits
            "abcdefghijklmnopqrstuvwxyz123456"  # No special chars
        ]
        
        for key in simple_keys:
            valid, issues = validate_secret_key(key)
            assert valid is False
            assert any("lacks complexity" in issue for issue in issues)


class TestDatabaseURLValidation:
    """Test database URL validation."""
    
    def test_valid_database_url(self):
        """Test validation of valid database URLs."""
        valid_urls = [
            "postgresql://myuser:<EMAIL>:5432/mydb",
            "***************************************************/production_db"
        ]
        
        for url in valid_urls:
            valid, issues = validate_database_url(url)
            assert valid is True
            assert len(issues) == 0
    
    def test_empty_database_url(self):
        """Test validation of empty database URL."""
        valid, issues = validate_database_url("")
        assert valid is False
        assert "Database URL is not set" in issues
    
    def test_weak_credentials(self):
        """Test detection of weak database credentials."""
        weak_urls = [
            "postgresql://user:password@localhost:5432/db",
            "postgresql://admin:admin@localhost:5432/db",
            "postgresql://root:root@localhost:5432/db",
            "postgresql://postgres:postgres@localhost:5432/db"
        ]
        
        for url in weak_urls:
            valid, issues = validate_database_url(url)
            assert valid is False
            assert any("weak credentials" in issue for issue in issues)
    
    @patch('mcx3d_finance.core.config.settings')
    def test_localhost_in_production(self, mock_settings):
        """Test detection of localhost in production mode."""
        mock_settings.debug = False
        
        valid, issues = validate_database_url("postgresql://user:pass@localhost:5432/db")
        assert valid is False
        assert any("localhost in non-debug mode" in issue for issue in issues)


class TestOAuthTransportValidation:
    """Test OAuth transport validation."""
    
    def test_secure_transport(self):
        """Test validation when insecure transport is not set."""
        # Ensure the env var is not set
        os.environ.pop('OAUTHLIB_INSECURE_TRANSPORT', None)
        
        valid, issues = validate_oauth_transport()
        assert valid is True
        assert len(issues) == 0
    
    @patch('mcx3d_finance.core.config.settings')
    def test_insecure_transport_in_debug(self, mock_settings):
        """Test insecure transport allowed in debug mode."""
        mock_settings.debug = True
        os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'
        
        valid, issues = validate_oauth_transport()
        assert valid is True  # Should be valid in debug mode
        assert len(issues) == 0
        
        # Clean up
        os.environ.pop('OAUTHLIB_INSECURE_TRANSPORT', None)
    
    @patch('mcx3d_finance.core.config.settings')
    def test_insecure_transport_in_production(self, mock_settings):
        """Test insecure transport detected in production mode."""
        mock_settings.debug = False
        os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'
        
        valid, issues = validate_oauth_transport()
        assert valid is False
        assert any("non-debug mode" in issue for issue in issues)
        
        # Clean up
        os.environ.pop('OAUTHLIB_INSECURE_TRANSPORT', None)


class TestEncryptionKeyValidation:
    """Test encryption key validation."""
    
    def test_valid_fernet_key(self):
        """Test validation of valid Fernet key."""
        key = generate_fernet_key()
        valid, issues = validate_encryption_key(key)
        assert valid is True
        assert len(issues) == 0
    
    def test_empty_encryption_key(self):
        """Test that empty encryption key is allowed but warned."""
        valid, issues = validate_encryption_key("")
        assert valid is True  # Optional key
        assert len(issues) == 0
    
    def test_invalid_fernet_key(self):
        """Test detection of invalid Fernet key format."""
        invalid_keys = [
            "not-a-valid-fernet-key",
            "short",
            "invalid_base64_@#$%",
        ]
        
        for key in invalid_keys:
            valid, issues = validate_encryption_key(key)
            assert valid is False
            assert any("Invalid Fernet encryption key format" in issue for issue in issues)


class TestSecurityConfigurationValidation:
    """Test comprehensive security validation."""
    
    @patch('mcx3d_finance.core.config.get_security_config')
    @patch('mcx3d_finance.core.config.get_database_url')
    @patch('mcx3d_finance.core.config.settings')
    def test_valid_configuration(self, mock_settings, mock_db_url, mock_sec_config):
        """Test validation of valid security configuration."""
        mock_settings.debug = False
        mock_db_url.return_value = "postgresql://user:<EMAIL>:5432/mydb"
        mock_sec_config.return_value = {
            "secret_key": generate_secret_key(64),
            "encryption_key": generate_fernet_key(),
            "algorithm": "HS256",
            "access_token_expire_minutes": 30
        }
        
        results = validate_security_configuration()
        assert results["valid"] is True
        assert len(results["errors"]) == 0
    
    @patch('mcx3d_finance.core.config.get_security_config')
    def test_invalid_configuration(self, mock_sec_config):
        """Test validation fails with invalid configuration."""
        mock_sec_config.return_value = {
            "secret_key": "weak_key",
            "encryption_key": "invalid_key"
        }
        
        with pytest.raises(SecurityValidationError):
            validate_security_configuration()


class TestPasswordHashing:
    """Test password hashing functionality."""
    
    def test_password_hash_and_verify(self):
        """Test password hashing and verification."""
        from mcx3d_finance.api.auth_middleware import hash_password, verify_password
        
        # Test password hashing
        password = "MySecurePassword123!"
        hashed = hash_password(password)
        
        # Verify the hash is different from the password
        assert hashed != password
        assert len(hashed) > 50  # bcrypt hashes are typically 60 chars
        
        # Verify correct password
        assert verify_password(password, hashed) is True
        
        # Verify incorrect password
        assert verify_password("WrongPassword", hashed) is False
        
        # Verify similar password fails
        assert verify_password("MySecurePassword123", hashed) is False
    
    def test_password_hash_uniqueness(self):
        """Test that same password produces different hashes."""
        from mcx3d_finance.api.auth_middleware import hash_password
        
        password = "TestPassword123!"
        hash1 = hash_password(password)
        hash2 = hash_password(password)
        
        # Same password should produce different hashes due to salt
        assert hash1 != hash2
    
    def test_verify_password_error_handling(self):
        """Test password verification error handling."""
        from mcx3d_finance.api.auth_middleware import verify_password
        
        # Test with invalid hash format
        assert verify_password("password", "invalid_hash") is False
        
        # Test with None values
        assert verify_password("password", None) is False
        assert verify_password(None, "$2b$12$valid_hash") is False


class TestKeyGeneration:
    """Test key generation utilities."""
    
    def test_secret_key_generation(self):
        """Test secret key generation."""
        # Test default length
        key = generate_secret_key()
        assert len(key) == 64
        assert key_validator(key) is True
        
        # Test custom length
        key = generate_secret_key(128)
        assert len(key) == 128
        assert key_validator(key) is True
        
        # Test minimum length
        key = generate_secret_key(32)
        assert len(key) == 32
        assert key_validator(key) is True
    
    def test_fernet_key_generation(self):
        """Test Fernet key generation."""
        from cryptography.fernet import Fernet
        
        key = generate_fernet_key()
        # Verify it's a valid Fernet key
        fernet = Fernet(key.encode())
        
        # Test encryption/decryption works
        test_data = b"test data"
        encrypted = fernet.encrypt(test_data)
        decrypted = fernet.decrypt(encrypted)
        assert decrypted == test_data