"""
Celery tasks for data synchronization and processing.
"""

from mcx3d_finance.tasks.celery_app import celery_app
from mcx3d_finance.auth.xero_oauth import XeroAuthManager
from mcx3d_finance.integrations.xero_client import XeroClient
from mcx3d_finance.integrations.xero_sync import XeroSyncEngine
from mcx3d_finance.integrations.xero_data_import import XeroDataImportService
from mcx3d_finance.integrations.xero_data_storage import XeroDataStorageService
from mcx3d_finance.core.data_processors import UKDataProcessor
from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization, SyncStatus
from datetime import datetime, timedelta, timezone
import logging
from typing import Dict, Any
import json
from enum import Enum

logger = logging.getLogger(__name__)


def make_json_serializable(obj: Any) -> Any:
    """Convert non-serializable objects to JSON-serializable format."""
    if isinstance(obj, Enum):
        return obj.value
    elif isinstance(obj, dict):
        return {k: make_json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [make_json_serializable(item) for item in obj]
    elif isinstance(obj, (datetime,)):
        return obj.isoformat()
    else:
        return obj


@celery_app.task(bind=True, max_retries=3, default_retry_delay=300)
def sync_xero_data(self, org_id: int, incremental: bool = True) -> Dict[str, Any]:
    """
    Sync data from Xero API for a specific organization.

    Args:
        org_id: Organization ID (internal database ID)
        incremental: Whether to perform incremental sync

    Returns:
        Dict with sync results
    """
    db = SessionLocal()
    try:
        # Update task status
        self.update_state(
            state="PROGRESS", 
            meta={
                "status": "Initializing sync...",
                "progress": 0,
                "org_id": org_id
            }
        )

        # Get organization
        organization = db.query(Organization).get(org_id)
        if not organization:
            raise ValueError(f"Organization {org_id} not found")

        # Initialize components
        auth_manager = XeroAuthManager()
        
        # Get access token for organization
        self.update_state(
            state="PROGRESS", 
            meta={
                "status": "Authenticating with Xero...",
                "progress": 5,
                "org_id": org_id
            }
        )
        
        token = auth_manager.get_valid_token(org_id)
        if not token:
            raise Exception(f"No valid token found for organization {org_id}")

        # Initialize import and storage services
        import_service = XeroDataImportService(org_id)
        storage_service = XeroDataStorageService(db)

        # Progress callback for nested services
        def import_progress_callback(progress, message):
            # Scale import progress from 10-70%
            scaled_progress = 10 + int(progress * 0.6)
            self.update_state(
                state="PROGRESS",
                meta={
                    "status": f"Import: {message}",
                    "progress": scaled_progress,
                    "org_id": org_id
                }
            )
        
        def storage_progress_callback(progress, message):
            # Scale storage progress from 70-95%
            scaled_progress = 70 + int(progress * 0.25)
            self.update_state(
                state="PROGRESS",
                meta={
                    "status": f"Storage: {message}",
                    "progress": scaled_progress,
                    "org_id": org_id
                }
            )

        try:
            # Step 1: Import data from Xero
            if incremental and organization.last_sync_at:
                # Incremental sync
                import_result = import_service.import_incremental_data(
                    since_date=organization.last_sync_at
                )
            else:
                # Full sync
                import_result = import_service.import_all_data(
                    progress_callback=import_progress_callback
                )
            
            if not import_result["success"]:
                raise Exception(f"Import failed: {import_result.get('error', 'Unknown error')}")
            
            # Step 2: Store data in database
            storage_result = storage_service.store_all_data(
                organization_id=org_id,
                imported_data=import_result["processed_data"],
                progress_callback=storage_progress_callback
            )
            
            if not storage_result["success"]:
                raise Exception(f"Storage failed: {storage_result.get('error', 'Unknown error')}")
            
            # Combine statistics - ensure JSON serializable
            sync_results: Dict[str, Any] = make_json_serializable({
                "import_stats": import_result["stats"],
                "storage_stats": storage_result["stats"],
                "processing_results": import_result["processed_data"].get("processing_results", {}),
                "errors": []
            })
            
            # Check for any errors in processing
            if import_result.get("stats", {}).get("errors"):
                sync_results["errors"].extend(import_result["stats"]["errors"])
            
            # Update organization sync status
            organization.last_sync_at = datetime.now(timezone.utc)
            organization.sync_status = "completed"
            db.commit()

            self.update_state(
                state="SUCCESS",
                meta={
                    "status": "Sync completed successfully",
                    "progress": 100,
                    "org_id": org_id,
                    "results": sync_results
                }
            )

            return {
                "success": True,
                "org_id": org_id,
                "sync_type": "incremental" if incremental else "full",
                "results": sync_results,
                "message": "Sync completed successfully"
            }

        except Exception as sync_error:
            logger.error(f"Error during sync process: {sync_error}")
            sync_results["errors"].append(str(sync_error))
            
            # Update organization sync status
            organization.sync_status = "failed"
            db.commit()
            
            # Create failed sync status record
            sync_status = SyncStatus(
                organization_id=org_id,
                sync_type="incremental" if incremental else "full",
                status="failed",
                records_synced=sync_results,
                started_at=datetime.now(timezone.utc) - timedelta(minutes=5),
                completed_at=datetime.now(timezone.utc),
                error_message=str(sync_error)
            )
            db.add(sync_status)
            db.commit()
            
            # Retry the task if retries are available
            raise self.retry(exc=sync_error, countdown=300)

    except Exception as e:
        logger.error(f"Error in Xero sync task: {e}")
        self.update_state(
            state="FAILURE",
            meta={
                "error": str(e),
                "org_id": org_id,
                "status": "Sync failed"
            }
        )
        return {
            "success": False,
            "error": str(e),
            "org_id": org_id
        }
    finally:
        db.close()


@celery_app.task
def generate_financial_report(
    org_id: str, report_type: str, period: str
) -> Dict[str, Any]:
    """
    Generate financial report asynchronously.

    Args:
        org_id: Organization ID
        report_type: Type of report (income_statement, balance_sheet, etc.)
        period: Reporting period

    Returns:
        Dict with report generation results
    """
    try:
        logger.info(
            f"Generating {report_type} report for org {org_id}, period {period}"
        )

        # Implementation would integrate with financial calculators
        # and report generators

        return {
            "success": True,
            "report_type": report_type,
            "org_id": org_id,
            "period": period,
            "file_path": f"/reports/{org_id}_{report_type}_{period}.pdf",
        }

    except Exception as e:
        logger.error(f"Error generating report: {e}")
        return {"success": False, "error": str(e)}
