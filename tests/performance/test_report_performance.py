# tests/performance/test_report_performance.py

import os
import gc
import time
import tempfile
import shutil
import threading
from pathlib import Path
from decimal import Decimal
from concurrent.futures import ThreadPoolExecutor, as_completed
import pytest

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

try:
    import memory_profiler
    MEMORY_PROFILER_AVAILABLE = True
except ImportError:
    MEMORY_PROFILER_AVAILABLE = False

from mcx3d_finance.reporting.generator import ReportGenerator
from mcx3d_finance.core.valuation.dcf import DCFValuation
from mcx3d_finance.core.valuation.saas_valuation import SaaSValuation


@pytest.mark.performance
@pytest.mark.slow
class TestReportPerformance:
    """
    Performance tests for report generation functionality.
    Tests large datasets, memory usage, concurrency, and establishes performance baselines.
    """
    
    @pytest.fixture
    def temp_output_dir(self):
        """Create a temporary directory for performance test outputs."""
        temp_dir = tempfile.mkdtemp(prefix="mcx3d_perf_test_")
        yield temp_dir
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def report_generator(self):
        """Create a ReportGenerator instance for performance testing."""
        return ReportGenerator()
    
    @pytest.fixture
    def large_dcf_dataset(self):
        """Generate a large DCF dataset for performance testing."""
        # Simulate a complex 10-year projection with detailed monthly breakdowns
        years = 10
        base_revenue = Decimal("10000000")  # $10M starting revenue
        
        # Generate realistic growth patterns with seasonal variations
        revenue_projections = []
        expense_projections = []
        capex_projections = []
        depreciation_projections = []
        wc_projections = []
        
        for year in range(years):
            # Decreasing growth rate over time (40% -> 10%)
            growth_rate = max(0.10, 0.40 - (year * 0.03))
            
            year_revenue = base_revenue * (Decimal(str(1 + growth_rate)) ** year)
            
            # Add seasonal variation (±15%)
            monthly_revenues = []
            for month in range(12):
                seasonal_factor = 1 + (0.15 * ((month % 6 - 2.5) / 2.5))  # Peaks in summer/winter
                monthly_revenue = year_revenue * Decimal(str(seasonal_factor)) / 12
                monthly_revenues.append(monthly_revenue)
            
            # Operating expenses: 60-70% of revenue with economies of scale
            expense_rate = max(0.60, 0.75 - (year * 0.015))  # Decreasing expense ratio
            year_expenses = year_revenue * Decimal(str(expense_rate))
            
            # CapEx: 8-12% of revenue with cyclical investment
            capex_rate = 0.08 + (0.04 * abs((year % 4 - 2) / 2))  # Cyclical CapEx
            year_capex = year_revenue * Decimal(str(capex_rate))
            
            # Depreciation: 5-year straight line on historical CapEx
            year_depreciation = sum(capex_projections[-4:]) / 5 if len(capex_projections) >= 4 else year_capex * Decimal("0.2")
            
            # Working capital: 5% of revenue growth
            if revenue_projections:
                wc_change = (year_revenue - revenue_projections[-1]) * Decimal("0.05")
            else:
                wc_change = year_revenue * Decimal("0.05")
            
            revenue_projections.append(year_revenue)
            expense_projections.append(year_expenses)
            capex_projections.append(year_capex)
            depreciation_projections.append(year_depreciation)
            wc_projections.append(wc_change)
        
        return {
            "company_name": "Large Scale Performance Test Corp",
            "valuation_date": "2024-01-30",
            "currency": "USD",
            "industry": "Technology - Enterprise",
            "projections": {
                "revenue": revenue_projections,
                "operating_expenses": expense_projections,
                "capex": capex_projections,
                "depreciation": depreciation_projections,
                "working_capital_changes": wc_projections
            },
            "assumptions": {
                "discount_rate": Decimal("0.09"),      # 9% WACC
                "terminal_growth": Decimal("0.025"),   # 2.5% terminal growth
                "tax_rate": Decimal("0.21"),           # 21% corporate tax
                "years": years
            },
            "market_data": {
                "risk_free_rate": Decimal("0.035"),
                "market_risk_premium": Decimal("0.055"),
                "beta": Decimal("1.1"),
                "debt_to_equity": Decimal("0.35")
            },
            "detailed_breakdown": {
                "monthly_revenue_detail": True,
                "quarterly_reporting": True,
                "segment_analysis": True,
                "scenario_modeling": True
            }
        }
    
    @pytest.fixture
    def large_saas_dataset(self):
        """Generate a large SaaS dataset for performance testing."""
        years = 8
        base_arr = Decimal("5000000")  # $5M starting ARR
        
        # Generate complex SaaS metrics with cohort analysis
        arr_projections = []
        churn_rates = []
        
        for year in range(years):
            # SaaS growth patterns: high initial growth, stabilizing
            if year < 3:
                growth_rate = 0.60 - (year * 0.15)  # 60% -> 30%
            else:
                growth_rate = max(0.15, 0.30 - ((year - 3) * 0.05))  # 30% -> 15%
            
            year_arr = base_arr * (Decimal(str(1 + growth_rate)) ** year)
            
            # Churn improvement over time
            monthly_churn = max(0.02, 0.07 - (year * 0.007))  # 7% -> 2%
            
            arr_projections.append(year_arr)
            churn_rates.append(monthly_churn)
        
        return {
            "company_name": "Large Scale SaaS Performance Test Inc",
            "valuation_date": "2024-01-30",
            "currency": "USD",
            "metrics": {
                "arr": arr_projections,
                "monthly_churn_rate": churn_rates,
                "customer_acquisition_cost": Decimal("350"),
                "average_revenue_per_user": Decimal("850"),
                "gross_margin": Decimal("0.89"),
                "customer_lifetime_value": Decimal("4250"),
                "net_revenue_retention": Decimal("1.25"),
                "magic_number": Decimal("1.6")
            },
            "assumptions": {
                "discount_rate": Decimal("0.12"),      # 12% for SaaS
                "terminal_multiple": Decimal("12.0"),  # 12x ARR terminal
                "growth_efficiency": Decimal("0.85")
            },
            "benchmarks": {
                "revenue_multiple_range": (10, 18),
                "growth_rate_median": Decimal("0.45"),
                "churn_rate_benchmark": Decimal("0.025"),
                "cac_payback_benchmark": 8
            },
            "detailed_metrics": {
                "cohort_analysis": True,
                "unit_economics": True,
                "growth_accounting": True,
                "competitive_benchmarking": True
            }
        }

    @pytest.mark.benchmark
    def test_large_dataset_report_generation(self, report_generator, large_dcf_dataset, temp_output_dir):
        """Test report generation performance with large datasets (10-year projections)."""
        
        # Performance tracking
        start_time = time.time()
        initial_memory = 0
        peak_memory = 0
        
        if PSUTIL_AVAILABLE:
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        try:
            # Generate DCF valuation with large dataset
            dcf_valuation = DCFValuation(
                projections=large_dcf_dataset["projections"],
                assumptions=large_dcf_dataset["assumptions"]
            )
            
            # Measure calculation time
            calc_start = time.time()
            dcf_result = dcf_valuation.calculate()
            calc_time = time.time() - calc_start
            
            if PSUTIL_AVAILABLE:
                current_memory = process.memory_info().rss / 1024 / 1024
                peak_memory = max(peak_memory, current_memory)
            
            # Generate both PDF and Excel reports
            pdf_path = Path(temp_output_dir) / "large_dataset_dcf.pdf"
            excel_path = Path(temp_output_dir) / "large_dataset_dcf.xlsx"
            
            # PDF generation timing
            pdf_start = time.time()
            pdf_result = report_generator.generate_dcf_valuation_pdf(
                dcf_result=dcf_result,
                company_data=large_dcf_dataset,
                output_path=str(pdf_path)
            )
            pdf_time = time.time() - pdf_start
            
            if PSUTIL_AVAILABLE:
                current_memory = process.memory_info().rss / 1024 / 1024
                peak_memory = max(peak_memory, current_memory)
            
            # Excel generation timing
            excel_start = time.time()
            excel_result = report_generator.generate_dcf_valuation_excel(
                dcf_result=dcf_result,
                company_data=large_dcf_dataset,
                output_path=str(excel_path)
            )
            excel_time = time.time() - excel_start
            
            end_time = time.time()
            total_time = end_time - start_time
            
            if PSUTIL_AVAILABLE:
                final_memory = process.memory_info().rss / 1024 / 1024
                peak_memory = max(peak_memory, final_memory)
            
            # Performance assertions
            assert total_time < 120, f"Large dataset processing took too long: {total_time:.2f}s"
            assert calc_time < 30, f"DCF calculation took too long: {calc_time:.2f}s"
            assert pdf_time < 60, f"PDF generation took too long: {pdf_time:.2f}s"
            assert excel_time < 45, f"Excel generation took too long: {excel_time:.2f}s"
            
            # Memory usage assertions (if available)
            if PSUTIL_AVAILABLE and peak_memory > 0:
                memory_increase = peak_memory - initial_memory
                assert memory_increase < 1000, f"Excessive memory usage: {memory_increase:.2f}MB increase"
                
                # Memory efficiency: should process at least 1 year per 50MB
                memory_per_year = memory_increase / large_dcf_dataset["assumptions"]["years"]
                assert memory_per_year < 50, f"Inefficient memory usage: {memory_per_year:.2f}MB per year"
            
            # File size and quality assertions
            assert os.path.exists(pdf_result), "PDF not generated for large dataset"
            assert os.path.exists(excel_result), "Excel not generated for large dataset"
            
            pdf_size = os.path.getsize(pdf_result)
            excel_size = os.path.getsize(excel_result)
            
            # Large datasets should produce substantial files
            assert pdf_size > 50000, f"PDF too small for large dataset: {pdf_size} bytes"
            assert excel_size > 25000, f"Excel too small for large dataset: {excel_size} bytes"
            
            # But not excessively large
            assert pdf_size < 10000000, f"PDF too large: {pdf_size} bytes (>10MB)"
            assert excel_size < 5000000, f"Excel too large: {excel_size} bytes (>5MB)"
            
            # Performance benchmarks for reporting
            performance_metrics = {
                "total_time": total_time,
                "calculation_time": calc_time,
                "pdf_generation_time": pdf_time,
                "excel_generation_time": excel_time,
                "pdf_size": pdf_size,
                "excel_size": excel_size,
                "years_processed": large_dcf_dataset["assumptions"]["years"],
                "projections_count": len(large_dcf_dataset["projections"]["revenue"])
            }
            
            if PSUTIL_AVAILABLE:
                performance_metrics.update({
                    "initial_memory_mb": initial_memory,
                    "peak_memory_mb": peak_memory,
                    "memory_increase_mb": peak_memory - initial_memory
                })
            
            # Log performance metrics for analysis
            print(f"\nLarge Dataset Performance Metrics:")
            for metric, value in performance_metrics.items():
                if isinstance(value, float):
                    print(f"  {metric}: {value:.2f}")
                else:
                    print(f"  {metric}: {value}")
                    
        finally:
            # Cleanup
            gc.collect()

    @pytest.mark.skipif(not PSUTIL_AVAILABLE, reason="psutil not available for memory monitoring")
    def test_memory_usage_during_generation(self, report_generator, large_dcf_dataset, temp_output_dir):
        """Monitor memory consumption and detect potential memory leaks."""
        
        process = psutil.Process()
        memory_samples = []
        generation_count = 5
        
        # Baseline memory measurement
        gc.collect()  # Force garbage collection
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Generate multiple reports while monitoring memory
        for i in range(generation_count):
            iteration_start_memory = process.memory_info().rss / 1024 / 1024
            
            # Generate DCF valuation
            dcf_valuation = DCFValuation(
                projections=large_dcf_dataset["projections"],
                assumptions=large_dcf_dataset["assumptions"]
            )
            dcf_result = dcf_valuation.calculate()
            
            mid_iteration_memory = process.memory_info().rss / 1024 / 1024
            
            # Generate reports
            pdf_path = Path(temp_output_dir) / f"memory_test_dcf_{i}.pdf"
            excel_path = Path(temp_output_dir) / f"memory_test_dcf_{i}.xlsx"
            
            pdf_result = report_generator.generate_dcf_valuation_pdf(
                dcf_result=dcf_result,
                company_data={
                    **large_dcf_dataset,
                    "company_name": f"Memory Test Corp {i}"
                },
                output_path=str(pdf_path)
            )
            
            excel_result = report_generator.generate_dcf_valuation_excel(
                dcf_result=dcf_result,
                company_data={
                    **large_dcf_dataset,
                    "company_name": f"Memory Test Corp {i}"
                },
                output_path=str(excel_path)
            )
            
            # Measure memory after generation
            post_generation_memory = process.memory_info().rss / 1024 / 1024
            
            # Force cleanup
            del dcf_valuation
            del dcf_result
            gc.collect()
            
            iteration_end_memory = process.memory_info().rss / 1024 / 1024
            
            memory_samples.append({
                "iteration": i,
                "start_memory": iteration_start_memory,
                "mid_memory": mid_iteration_memory,
                "post_generation_memory": post_generation_memory,
                "end_memory": iteration_end_memory,
                "pdf_size": os.path.getsize(pdf_result),
                "excel_size": os.path.getsize(excel_result)
            })
            
            # Validate files were created
            assert os.path.exists(pdf_result), f"PDF not created in iteration {i}"
            assert os.path.exists(excel_result), f"Excel not created in iteration {i}"
            
            # Memory growth check per iteration
            iteration_growth = iteration_end_memory - iteration_start_memory
            assert iteration_growth < 200, f"Excessive memory growth in iteration {i}: {iteration_growth:.2f}MB"
        
        # Analyze memory patterns
        final_memory = process.memory_info().rss / 1024 / 1024
        total_memory_growth = final_memory - baseline_memory
        
        # Memory leak detection
        assert total_memory_growth < 300, f"Potential memory leak detected: {total_memory_growth:.2f}MB total growth"
        
        # Analyze memory efficiency
        avg_memory_per_iteration = total_memory_growth / generation_count
        assert avg_memory_per_iteration < 60, f"High memory usage per iteration: {avg_memory_per_iteration:.2f}MB"
        
        # Check for consistent memory patterns (no runaway growth)
        memory_growths = [
            sample["end_memory"] - memory_samples[0]["start_memory"] 
            for sample in memory_samples
        ]
        
        # Memory growth should stabilize (not increase linearly)
        if len(memory_growths) >= 3:
            early_growth = memory_growths[1]
            late_growth = memory_growths[-1]
            growth_acceleration = late_growth - early_growth
            
            assert growth_acceleration < 150, f"Memory growth acceleration detected: {growth_acceleration:.2f}MB"
        
        # Report memory analysis
        print(f"\nMemory Usage Analysis:")
        print(f"  Baseline Memory: {baseline_memory:.2f}MB")
        print(f"  Final Memory: {final_memory:.2f}MB")
        print(f"  Total Growth: {total_memory_growth:.2f}MB")
        print(f"  Average per Iteration: {avg_memory_per_iteration:.2f}MB")
        print(f"  Peak Memory per Sample: {max(s['post_generation_memory'] for s in memory_samples):.2f}MB")

    def test_concurrent_user_report_requests(self, report_generator, large_dcf_dataset, large_saas_dataset, temp_output_dir):
        """Simulate multiple users requesting reports simultaneously."""
        
        concurrent_users = 8
        reports_per_user = 2
        total_reports = concurrent_users * reports_per_user
        
        results = []
        errors = []
        start_time = time.time()
        
        def generate_user_reports(user_id):
            """Generate reports for a single user."""
            user_results = []
            
            try:
                for report_num in range(reports_per_user):
                    report_start = time.time()
                    
                    # Alternate between DCF and SaaS reports
                    if report_num % 2 == 0:
                        # DCF Report
                        dcf_valuation = DCFValuation(
                            projections=large_dcf_dataset["projections"],
                            assumptions=large_dcf_dataset["assumptions"]
                        )
                        dcf_result = dcf_valuation.calculate()
                        
                        output_path = Path(temp_output_dir) / f"concurrent_user_{user_id}_dcf_{report_num}.pdf"
                        result_path = report_generator.generate_dcf_valuation_pdf(
                            dcf_result=dcf_result,
                            company_data={
                                **large_dcf_dataset,
                                "company_name": f"Concurrent Test Corp User {user_id}"
                            },
                            output_path=str(output_path)
                        )
                        report_type = "DCF"
                        
                    else:
                        # SaaS Report
                        saas_valuation = SaaSValuation(
                            metrics=large_saas_dataset["metrics"],
                            assumptions=large_saas_dataset["assumptions"]
                        )
                        saas_result = saas_valuation.calculate()
                        
                        output_path = Path(temp_output_dir) / f"concurrent_user_{user_id}_saas_{report_num}.xlsx"
                        result_path = report_generator.generate_saas_valuation_excel(
                            saas_result=saas_result,
                            company_data={
                                **large_saas_dataset,
                                "company_name": f"Concurrent SaaS Corp User {user_id}"
                            },
                            output_path=str(output_path)
                        )
                        report_type = "SaaS"
                    
                    report_end = time.time()
                    generation_time = report_end - report_start
                    
                    user_results.append({
                        "user_id": user_id,
                        "report_num": report_num,
                        "report_type": report_type,
                        "path": result_path,
                        "generation_time": generation_time,
                        "file_size": os.path.getsize(result_path) if os.path.exists(result_path) else 0,
                        "success": True
                    })
                    
                    # Validate file was created
                    assert os.path.exists(result_path), f"Report not created: {result_path}"
                    
            except Exception as e:
                errors.append({
                    "user_id": user_id,
                    "error": str(e),
                    "error_type": type(e).__name__
                })
                user_results.append({
                    "user_id": user_id,
                    "success": False,
                    "error": str(e)
                })
            
            return user_results
        
        # Execute concurrent report generation
        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = {
                executor.submit(generate_user_reports, user_id): user_id 
                for user_id in range(concurrent_users)
            }
            
            for future in as_completed(futures):
                user_results = future.result()
                results.extend(user_results)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Analyze concurrent performance
        successful_reports = [r for r in results if r.get("success", False)]
        failed_reports = [r for r in results if not r.get("success", True)]
        
        # Performance assertions
        assert len(successful_reports) >= total_reports * 0.95, \
            f"Too many failed reports: {len(failed_reports)}/{total_reports}"
        
        assert len(errors) == 0, f"Errors during concurrent generation: {errors}"
        
        # Timing assertions
        assert total_time < 300, f"Concurrent generation took too long: {total_time:.2f}s"
        
        # Individual report timing
        generation_times = [r["generation_time"] for r in successful_reports if "generation_time" in r]
        if generation_times:
            avg_generation_time = sum(generation_times) / len(generation_times)
            max_generation_time = max(generation_times)
            
            assert avg_generation_time < 60, f"Average report time too slow: {avg_generation_time:.2f}s"
            assert max_generation_time < 120, f"Slowest report took too long: {max_generation_time:.2f}s"
        
        # File integrity checks
        for result in successful_reports:
            if "path" in result and "file_size" in result:
                assert result["file_size"] > 5000, f"Generated file too small: {result['path']}"
        
        # Concurrency efficiency
        theoretical_sequential_time = sum(generation_times) if generation_times else total_time
        efficiency = theoretical_sequential_time / total_time if total_time > 0 else 1
        
        # Should achieve at least 2x speedup with 8 concurrent users
        assert efficiency >= 2.0, f"Poor concurrency efficiency: {efficiency:.2f}x speedup"
        
        # Report concurrent performance metrics
        print(f"\nConcurrent Performance Analysis:")
        print(f"  Total Time: {total_time:.2f}s")
        print(f"  Successful Reports: {len(successful_reports)}/{total_reports}")
        print(f"  Average Generation Time: {avg_generation_time:.2f}s")
        print(f"  Max Generation Time: {max_generation_time:.2f}s")
        print(f"  Concurrency Efficiency: {efficiency:.2f}x")
        print(f"  Reports per Second: {len(successful_reports)/total_time:.2f}")

    @pytest.mark.benchmark
    def test_report_generation_benchmarks(self, report_generator, large_dcf_dataset, temp_output_dir):
        """Establish baseline performance metrics for regression testing."""
        
        # Benchmark configuration
        benchmark_iterations = 3
        benchmark_results = []
        
        for iteration in range(benchmark_iterations):
            iteration_start = time.time()
            
            # Memory tracking (if available)
            initial_memory = 0
            peak_memory = 0
            
            if PSUTIL_AVAILABLE:
                process = psutil.Process()
                initial_memory = process.memory_info().rss / 1024 / 1024
                peak_memory = initial_memory
            
            # DCF Calculation Benchmark
            calc_start = time.time()
            dcf_valuation = DCFValuation(
                projections=large_dcf_dataset["projections"],
                assumptions=large_dcf_dataset["assumptions"]
            )
            dcf_result = dcf_valuation.calculate()
            calc_time = time.time() - calc_start
            
            if PSUTIL_AVAILABLE:
                current_memory = process.memory_info().rss / 1024 / 1024
                peak_memory = max(peak_memory, current_memory)
            
            # PDF Generation Benchmark
            pdf_path = Path(temp_output_dir) / f"benchmark_dcf_{iteration}.pdf"
            pdf_start = time.time()
            pdf_result = report_generator.generate_dcf_valuation_pdf(
                dcf_result=dcf_result,
                company_data={
                    **large_dcf_dataset,
                    "company_name": f"Benchmark Corp {iteration}"
                },
                output_path=str(pdf_path)
            )
            pdf_time = time.time() - pdf_start
            
            if PSUTIL_AVAILABLE:
                current_memory = process.memory_info().rss / 1024 / 1024
                peak_memory = max(peak_memory, current_memory)
            
            # Excel Generation Benchmark
            excel_path = Path(temp_output_dir) / f"benchmark_dcf_{iteration}.xlsx"
            excel_start = time.time()
            excel_result = report_generator.generate_dcf_valuation_excel(
                dcf_result=dcf_result,
                company_data={
                    **large_dcf_dataset,
                    "company_name": f"Benchmark Corp {iteration}"
                },
                output_path=str(excel_path)
            )
            excel_time = time.time() - excel_start
            
            iteration_end = time.time()
            total_time = iteration_end - iteration_start
            
            if PSUTIL_AVAILABLE:
                final_memory = process.memory_info().rss / 1024 / 1024
                peak_memory = max(peak_memory, final_memory)
            
            # Collect benchmark data
            benchmark_data = {
                "iteration": iteration,
                "total_time": total_time,
                "calculation_time": calc_time,
                "pdf_generation_time": pdf_time,
                "excel_generation_time": excel_time,
                "pdf_size": os.path.getsize(pdf_result) if os.path.exists(pdf_result) else 0,
                "excel_size": os.path.getsize(excel_result) if os.path.exists(excel_result) else 0,
                "years_processed": large_dcf_dataset["assumptions"]["years"]
            }
            
            if PSUTIL_AVAILABLE:
                benchmark_data.update({
                    "initial_memory_mb": initial_memory,
                    "peak_memory_mb": peak_memory,
                    "memory_usage_mb": peak_memory - initial_memory
                })
            
            benchmark_results.append(benchmark_data)
            
            # Validate files were created
            assert os.path.exists(pdf_result), f"PDF benchmark {iteration} not created"
            assert os.path.exists(excel_result), f"Excel benchmark {iteration} not created"
            
            # Cleanup for next iteration
            gc.collect()
        
        # Analyze benchmark results
        avg_total_time = sum(r["total_time"] for r in benchmark_results) / len(benchmark_results)
        avg_calc_time = sum(r["calculation_time"] for r in benchmark_results) / len(benchmark_results)
        avg_pdf_time = sum(r["pdf_generation_time"] for r in benchmark_results) / len(benchmark_results)
        avg_excel_time = sum(r["excel_generation_time"] for r in benchmark_results) / len(benchmark_results)
        
        # Performance benchmarks (these will serve as regression baselines)
        performance_benchmarks = {
            "avg_total_time": avg_total_time,
            "avg_calculation_time": avg_calc_time,
            "avg_pdf_generation_time": avg_pdf_time,
            "avg_excel_generation_time": avg_excel_time,
            "max_total_time": max(r["total_time"] for r in benchmark_results),
            "min_total_time": min(r["total_time"] for r in benchmark_results),
            "time_std_deviation": (
                sum((r["total_time"] - avg_total_time) ** 2 for r in benchmark_results) 
                / len(benchmark_results)
            ) ** 0.5,
            "avg_pdf_size": sum(r["pdf_size"] for r in benchmark_results) / len(benchmark_results),
            "avg_excel_size": sum(r["excel_size"] for r in benchmark_results) / len(benchmark_results)
        }
        
        if PSUTIL_AVAILABLE:
            avg_memory_usage = sum(r.get("memory_usage_mb", 0) for r in benchmark_results) / len(benchmark_results)
            performance_benchmarks["avg_memory_usage_mb"] = avg_memory_usage
        
        # Benchmark assertions (these establish acceptable performance baselines)
        assert avg_total_time < 90, f"Average total time baseline exceeded: {avg_total_time:.2f}s"
        assert avg_calc_time < 25, f"Average calculation time baseline exceeded: {avg_calc_time:.2f}s"
        assert avg_pdf_time < 45, f"Average PDF time baseline exceeded: {avg_pdf_time:.2f}s"
        assert avg_excel_time < 35, f"Average Excel time baseline exceeded: {avg_excel_time:.2f}s"
        
        # Consistency check (low standard deviation indicates consistent performance)
        time_coefficient_of_variation = performance_benchmarks["time_std_deviation"] / avg_total_time
        assert time_coefficient_of_variation < 0.25, f"High performance variability: {time_coefficient_of_variation:.2f}"
        
        # Output benchmark results for regression testing
        print(f"\nPerformance Benchmarks (Baseline Metrics):")
        for metric, value in performance_benchmarks.items():
            if isinstance(value, float):
                print(f"  {metric}: {value:.3f}")
            else:
                print(f"  {metric}: {value}")
        
        print(f"\nDetailed Benchmark Results:")
        for i, result in enumerate(benchmark_results):
            print(f"  Iteration {i+1}: Total={result['total_time']:.2f}s, "
                  f"Calc={result['calculation_time']:.2f}s, "
                  f"PDF={result['pdf_generation_time']:.2f}s, "
                  f"Excel={result['excel_generation_time']:.2f}s")
        
        # Store benchmarks for potential CI/CD integration
        return performance_benchmarks

    @pytest.mark.performance
    def test_memory_efficiency_with_cleanup(self, report_generator, temp_output_dir):
        """Test memory cleanup and efficiency during report generation cycles."""
        
        if not PSUTIL_AVAILABLE:
            pytest.skip("psutil not available for memory monitoring")
        
        process = psutil.Process()
        cleanup_cycles = 3
        reports_per_cycle = 4
        
        # Simple dataset for memory efficiency testing
        simple_dcf_data = {
            "company_name": "Memory Efficiency Test Corp",
            "projections": {
                "revenue": [Decimal("1000000") * (1.2 ** i) for i in range(5)],
                "operating_expenses": [Decimal("700000") * (1.15 ** i) for i in range(5)],
                "capex": [Decimal("50000") * (1.1 ** i) for i in range(5)],
                "depreciation": [Decimal("40000") for _ in range(5)],
                "working_capital_changes": [Decimal("20000") for _ in range(5)]
            },
            "assumptions": {
                "discount_rate": Decimal("0.10"),
                "terminal_growth": Decimal("0.03"),
                "tax_rate": Decimal("0.21"),
                "years": 5
            }
        }
        
        baseline_memory = process.memory_info().rss / 1024 / 1024
        cycle_memories = []
        
        for cycle in range(cleanup_cycles):
            cycle_start_memory = process.memory_info().rss / 1024 / 1024
            
            # Generate multiple reports in this cycle
            for report in range(reports_per_cycle):
                dcf_valuation = DCFValuation(
                    projections=simple_dcf_data["projections"],
                    assumptions=simple_dcf_data["assumptions"]
                )
                dcf_result = dcf_valuation.calculate()
                
                # Generate PDF
                pdf_path = Path(temp_output_dir) / f"efficiency_cycle_{cycle}_report_{report}.pdf"
                pdf_result = report_generator.generate_dcf_valuation_pdf(
                    dcf_result=dcf_result,
                    company_data=simple_dcf_data,
                    output_path=str(pdf_path)
                )
                
                assert os.path.exists(pdf_result), f"PDF not created: cycle {cycle}, report {report}"
            
            # Explicit cleanup
            del dcf_valuation
            del dcf_result
            gc.collect()
            
            cycle_end_memory = process.memory_info().rss / 1024 / 1024
            cycle_memory_growth = cycle_end_memory - cycle_start_memory
            
            cycle_memories.append({
                "cycle": cycle,
                "start_memory": cycle_start_memory,
                "end_memory": cycle_end_memory,
                "growth": cycle_memory_growth
            })
            
            # Memory growth per cycle should be reasonable
            assert cycle_memory_growth < 100, f"Excessive memory growth in cycle {cycle}: {cycle_memory_growth:.2f}MB"
        
        final_memory = process.memory_info().rss / 1024 / 1024
        total_growth = final_memory - baseline_memory
        
        # Overall memory efficiency
        assert total_growth < 150, f"Total memory growth too high: {total_growth:.2f}MB"
        
        # Memory growth should not be linear (indicating proper cleanup)
        memory_growths = [cm["growth"] for cm in cycle_memories]
        if len(memory_growths) >= 2:
            # Later cycles should not have significantly higher growth than earlier ones
            early_avg = sum(memory_growths[:2]) / 2
            late_avg = sum(memory_growths[-2:]) / 2
            growth_increase = late_avg - early_avg
            
            # Allow some growth but not excessive
            assert growth_increase < 50, f"Memory growth acceleration: {growth_increase:.2f}MB"
        
        print(f"\nMemory Efficiency Analysis:")
        print(f"  Baseline: {baseline_memory:.2f}MB")
        print(f"  Final: {final_memory:.2f}MB")
        print(f"  Total Growth: {total_growth:.2f}MB")
        print(f"  Average Growth per Cycle: {sum(memory_growths)/len(memory_growths):.2f}MB")
        
        for cycle_mem in cycle_memories:
            print(f"  Cycle {cycle_mem['cycle']}: {cycle_mem['growth']:.2f}MB growth")
