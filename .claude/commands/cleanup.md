# /cleanup - Intelligent Project Cleanup & Organization

## Purpose
Systematically clean up code repositories by removing AI artifacts, organizing misplaced files, and maintaining proper project structure across different project types.

## Usage
```
/cleanup [options] [target]
```

## Arguments & Options
- `$ARGUMENTS` - Supports multiple patterns:
  - No arguments: Intelligent full cleanup with safe defaults
  - `--dry-run`: Preview changes without executing
  - `--type artifacts`: Only remove AI artifacts and temporary files
  - `--type organization`: Only fix file organization and structure
  - `--type all`: Complete cleanup (default)
  - `--safe`: Conservative cleanup (default)
  - `--aggressive`: More thorough cleanup with higher risk tolerance
  - Specific targets: `root`, `tests`, `docs`, `scripts`

## Execution Framework

You are an intelligent project cleanup specialist. Your mission is to identify and resolve common organizational issues while preserving all legitimate project files. Follow this systematic approach:

### Phase 1: Discovery & Analysis (MANDATORY)

1. **Create Progress Tracking**
   Use TodoWrite to create a comprehensive task list covering:
   - Project analysis and type detection
   - AI artifact identification
   - File organization assessment
   - Cleanup execution
   - Verification and reporting

2. **Project Type Detection**
   Use LS and Glob tools to identify project type(s):
   - Python: Look for `pyproject.toml`, `setup.py`, `requirements.txt`, `__init__.py` files
   - Node.js: Look for `package.json`, `node_modules/`, `.npmrc`
   - Mixed/Hybrid: Projects with multiple technology stacks
   - Custom: Projects with unique structures

3. **Directory Structure Analysis**
   Systematically analyze the root directory and immediate subdirectories:
   - Catalog all files using LS tool for root directory
   - Identify existing organizational directories (`tests/`, `docs/`, `scripts/`, etc.)
   - Map current file locations vs. conventional locations

### Phase 2: AI Artifact & Misplacement Detection

4. **AI Artifact Identification Patterns**
   Use Glob and selective Read to identify:
   - **Demo/Example Files**: `demo_*`, `example_*`, `sample_*`, `test_*` in root
   - **Issue Documentation**: `ISSUE_*`, `FIX_*`, `CHANGES_*`, `*_fixes_*`, `*_summary.*`
   - **Temporary Files**: Files with timestamps, UUIDs, or temporary naming patterns
   - **Generated Scripts**: Files with AI signatures, systematic naming, or demonstration purposes
   - **Misplaced Tests**: Test files outside proper test directories

5. **Content-Based Analysis** (when files are ambiguous)
   Use Read tool selectively to analyze file contents for:
   - AI-generated demonstration code
   - Temporary implementation notes
   - Issue-specific documentation that should be moved
   - Configuration files in wrong locations

### Phase 3: Intelligent Organization Planning

6. **Project-Specific Organization Rules**
   Apply conventions based on detected project type:
   
   **Python Projects**:
   - Tests: `tests/` directory or `test_*.py` pattern
   - Documentation: `docs/` with subdirectories
   - Scripts: `scripts/` or `bin/`
   - Configuration: Root or `config/`
   
   **Node.js Projects**:
   - Tests: `test/`, `__tests__/`, or `*.test.js` pattern
   - Documentation: `docs/`
   - Scripts: `scripts/` or `bin/`
   - Source: `src/` or `lib/`
   
   **General Rules**:
   - Move misplaced test files to appropriate test directories
   - Organize documentation in `docs/` hierarchy (technical-reports, user-guide, etc.)
   - Move configuration files to conventional locations
   - Remove obvious AI artifacts and demonstration files

7. **Conflict Resolution Strategy**
   Plan for potential conflicts:
   - Check if target files already exist before moving
   - Generate alternative names if conflicts exist
   - Preserve existing file hierarchy when possible

### Phase 4: Safe Execution

8. **Pre-Execution Safety Checks**
   - Verify target directories exist or can be created
   - Confirm no critical files would be lost
   - Log all planned operations for potential rollback

9. **Execution with Progress Tracking**
   Execute cleanup operations using appropriate tools:
   - Use Bash for file movements with proper error handling
   - Update TodoWrite progress after each major operation
   - Use MultiEdit for batch operations when appropriate
   - Maintain operation log for audit trail

10. **Operations Priority Order**:
    1. Move misplaced legitimate files to proper locations
    2. Organize documentation into appropriate subdirectories
    3. Remove identified AI artifacts (with confirmation if not `--aggressive`)
    4. Clean up empty directories
    5. Verify all operations completed successfully

### Phase 5: Verification & Reporting

11. **Post-Execution Verification**
    - Use LS to verify final directory structure
    - Confirm all intended moves completed successfully
    - Check that no legitimate files were lost

12. **Comprehensive Reporting**
    Provide detailed summary including:
    - Files moved and their new locations
    - AI artifacts removed
    - Directory structure improvements
    - Any issues encountered and resolutions
    - Rollback instructions if needed

## Safety Principles (CRITICAL)

- **NEVER delete files without clear identification as AI artifacts**
- **ALWAYS preserve legitimate project files**
- **ALWAYS provide clear logging of all operations**
- **RESPECT existing .gitignore patterns**
- **CREATE rollback information for all operations**
- **STOP immediately if any operation seems unsafe**

## Error Handling

- Gracefully handle permission issues
- Provide clear error messages with suggested solutions
- Offer manual intervention steps when automated cleanup fails
- Maintain partial operation logs even if cleanup is interrupted

## Integration Features

- **Version Control Aware**: Respect existing Git patterns and ignore rules
- **Project Tool Integration**: Work harmoniously with existing linters, formatters
- **Cross-Platform Compatibility**: Handle different path separators and file systems
- **Batch Processing**: Optimize for large projects with hundreds of files

Execute this comprehensive cleanup workflow systematically, maintaining transparency through TodoWrite progress tracking and providing detailed reporting of all changes made.