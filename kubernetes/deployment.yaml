apiVersion: apps/v1
kind: Deployment
metadata:
  name: mcx3d-finance-api
  labels:
    app: mcx3d-finance
spec:
  replicas: 3
  selector:
    matchLabels:
      app: mcx3d-finance
  template:
    metadata:
      labels:
        app: mcx3d-finance
    spec:
      containers:
      - name: api
        image: mcx3d/finance-api:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: mcx3d-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: mcx3d-secrets
              key: redis-url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"