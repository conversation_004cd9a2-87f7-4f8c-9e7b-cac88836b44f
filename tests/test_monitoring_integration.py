"""
Integration tests for MCX3D Financial Platform Monitoring System

Tests all monitoring components including health checks, metrics collection,
alerting, and configuration management.
"""

import pytest
import asyncio
import time
import json
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from datetime import datetime, timezone, timedelta

from mcx3d_finance.monitoring.health_checker import <PERSON><PERSON>hecker, HealthStatus
from mcx3d_finance.monitoring.metrics import (
    update_celery_metrics, record_celery_queue_metrics, 
    record_external_service_metrics, CELERY_WORKER_COUNT
)
from mcx3d_finance.monitoring.alerting import <PERSON><PERSON><PERSON>anager, AlertSeverity
from mcx3d_finance.monitoring.config_loader import MonitoringConfigLoader


class TestHealthChecker:
    """Test the comprehensive health checking system."""
    
    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        mock_session = Mock()
        mock_session.execute.return_value.scalar.return_value = 1
        mock_session.execute.return_value.fetchone.return_value = Mock(
            active_connections=10,
            max_connections=100,
            database_size_bytes=1024*1024*1024  # 1GB
        )
        return mock_session
    
    @pytest.fixture
    def mock_redis_client(self):
        """Mock Redis client."""
        mock_redis = Mock()
        mock_redis.ping.return_value = True
        mock_redis.info.return_value = {
            'connected_clients': 5,
            'keyspace_hits': 1000,
            'keyspace_misses': 100
        }
        mock_redis.info.side_effect = lambda section=None: {
            'memory': {
                'used_memory': 50*1024*1024,  # 50MB
                'maxmemory': 100*1024*1024    # 100MB
            }
        } if section == 'memory' else {
            'connected_clients': 5,
            'keyspace_hits': 1000,
            'keyspace_misses': 100
        }
        mock_redis.llen.return_value = 5
        mock_redis.keys.return_value = [f'celery-task-meta-{i}' for i in range(10)]
        mock_redis.get.return_value = json.dumps({'status': 'SUCCESS'})
        return mock_redis
    
    @pytest.fixture
    def health_checker(self, mock_db_session, mock_redis_client):
        """Create health checker with mocked dependencies."""
        return HealthChecker(db_session=mock_db_session, redis_client=mock_redis_client)
    
    @pytest.mark.asyncio
    async def test_comprehensive_health_check(self, health_checker):
        """Test comprehensive health check execution."""
        with patch('psutil.cpu_percent', return_value=50.0), \
             patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.disk_usage') as mock_disk, \
             patch('psutil.process_iter', return_value=[]):
            
            mock_memory.return_value = Mock(percent=60.0, available=1024*1024*1024)
            mock_disk.return_value = Mock(percent=70.0, free=10*1024*1024*1024)
            
            result = await health_checker.get_comprehensive_health()
            
            assert 'timestamp' in result
            assert 'overall_status' in result
            assert 'components' in result
            assert 'summary' in result
            assert 'recommendations' in result
            
            # Check that all components are present
            expected_components = [
                'database', 'redis', 'system_resources', 
                'celery_workers', 'external_services', 
                'application', 'file_system'
            ]
            
            for component in expected_components:
                assert component in result['components']
                assert 'status' in result['components'][component]
                assert 'response_time_ms' in result['components'][component]
    
    @pytest.mark.asyncio
    async def test_database_health_check(self, health_checker):
        """Test database health check specifically."""
        result = await health_checker._check_database_health()
        
        assert result['status'] == HealthStatus.HEALTHY
        assert 'response_time_ms' in result
        assert 'metrics' in result
        assert result['metrics']['active_connections'] == 10
        assert result['metrics']['max_connections'] == 100
    
    @pytest.mark.asyncio
    async def test_redis_health_check(self, health_checker):
        """Test Redis health check specifically."""
        result = await health_checker._check_redis_health()
        
        assert result['status'] == HealthStatus.HEALTHY
        assert 'response_time_ms' in result
        assert 'metrics' in result
        assert result['metrics']['connected_clients'] == 5
        assert result['metrics']['hit_ratio'] == 90.91  # 1000/(1000+100)*100
    
    @pytest.mark.asyncio
    async def test_celery_worker_health_check(self, health_checker):
        """Test Celery worker health check with real metrics."""
        with patch('mcx3d_finance.tasks.celery_app.celery_app') as mock_celery:
            mock_inspect = Mock()
            mock_inspect.ping.return_value = {'worker1': 'pong', 'worker2': 'pong'}
            mock_inspect.active.return_value = {'worker1': [], 'worker2': [{'id': 'task1'}]}
            mock_inspect.stats.return_value = {}
            mock_celery.control.inspect.return_value = mock_inspect
            
            result = await health_checker._check_celery_workers()
            
            assert result['status'] == HealthStatus.HEALTHY
            assert result['metrics']['worker_count'] == 2
            assert result['metrics']['active_tasks'] == 1
            assert 'total_queue_length' in result['metrics']
            assert 'failed_tasks' in result['metrics']
    
    @pytest.mark.asyncio
    async def test_external_services_health_check(self, health_checker):
        """Test external services health check."""
        with patch('aiohttp.ClientSession') as mock_session:
            mock_response = AsyncMock()
            mock_response.status = 401  # Expected for unauthenticated Xero requests
            mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response
            
            with patch('socket.gethostbyname') as mock_dns:
                mock_dns.return_value = '*******'
                
                result = await health_checker._check_external_services()
                
                assert 'status' in result
                assert 'services' in result
                assert 'xero_api' in result['services']
                assert 'internet_connectivity' in result['services']
                assert 'dns_resolution' in result['services']
    
    @pytest.mark.asyncio
    async def test_configurable_thresholds(self, mock_db_session, mock_redis_client):
        """Test that health checker uses configurable thresholds."""
        with patch('mcx3d_finance.monitoring.config_loader.get_monitoring_config') as mock_config:
            mock_loader = Mock()
            mock_loader.environment = 'testing'
            mock_loader.get_health_thresholds.return_value = {
                'database': {
                    'max_response_time_ms': 50,
                    'critical_response_time_ms': 200
                },
                'celery': {
                    'max_queue_length': 10,
                    'max_failed_tasks': 2
                }
            }
            mock_config.return_value = mock_loader
            
            health_checker = HealthChecker(db_session=mock_db_session, redis_client=mock_redis_client)
            
            assert health_checker.health_thresholds['database']['max_response_time_ms'] == 50
            assert health_checker.health_thresholds['celery']['max_queue_length'] == 10


class TestMetricsCollection:
    """Test the metrics collection system."""
    
    def test_update_celery_metrics(self):
        """Test Celery metrics update."""
        # Clear any existing metrics state
        CELERY_WORKER_COUNT._value._value = 0
        
        update_celery_metrics(
            worker_count=3,
            active_tasks=5,
            queue_length=10,
            failed_tasks=2
        )
        
        # Verify metrics were updated
        assert CELERY_WORKER_COUNT._value._value == 3
    
    def test_record_celery_queue_metrics(self):
        """Test detailed Celery queue metrics recording."""
        queue_metrics = {
            'total_queue_length': 15,
            'failed_tasks': 3,
            'queues': {
                'celery': 10,
                'high_priority': 5
            }
        }
        
        record_celery_queue_metrics(queue_metrics)
        
        # This test verifies the function runs without error
        # In a real scenario, you'd check the actual metric values
    
    def test_record_external_service_metrics(self):
        """Test external service metrics recording."""
        record_external_service_metrics(
            service_name='xero_api',
            endpoint='https://api.xero.com',
            is_healthy=True,
            response_time_seconds=0.5
        )
        
        # This test verifies the function runs without error


class TestAlertManager:
    """Test the alerting system."""
    
    @pytest.fixture
    def alert_manager(self):
        """Create alert manager with mocked configuration."""
        with patch('mcx3d_finance.monitoring.config_loader.get_monitoring_config') as mock_config:
            mock_loader = Mock()
            mock_loader.environment = 'testing'
            mock_loader.get_alert_thresholds.return_value = {
                'email_alerts': ['CRITICAL'],
                'slack_alerts': ['ERROR', 'CRITICAL'],
                'pagerduty_alerts': ['CRITICAL']
            }
            mock_loader.get_rate_limit_config.return_value = {
                'max_alerts_per_window': 5,
                'rate_limit_window_minutes': 1,
                'deduplication_minutes': 0
            }
            mock_config.return_value = mock_loader
            
            return AlertManager()
    
    @pytest.mark.asyncio
    async def test_alert_triggering(self, alert_manager):
        """Test alert triggering with different severities."""
        with patch.object(alert_manager, '_log_alert') as mock_log, \
             patch.object(alert_manager, '_send_email') as mock_email, \
             patch.object(alert_manager, '_send_slack') as mock_slack:
            
            # Test INFO alert (should only log)
            await alert_manager.trigger_alert(
                AlertSeverity.INFO,
                'Test Info Alert',
                'This is a test info alert',
                {'component': 'test'}
            )
            
            mock_log.assert_called()
            mock_email.assert_not_called()
            mock_slack.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_alert_rate_limiting(self, alert_manager):
        """Test alert rate limiting functionality."""
        alert_key = 'test_alert'
        
        # Send multiple alerts quickly
        for i in range(10):
            await alert_manager.trigger_alert(
                AlertSeverity.WARNING,
                'Test Rate Limited Alert',
                f'Alert number {i}',
                {'component': 'test'},
                alert_key=alert_key
            )
        
        # Verify rate limiting is working (alerts after the limit should be suppressed)
        assert len(alert_manager.recent_alerts) > 0
    
    def test_alert_configuration_loading(self, alert_manager):
        """Test that alert manager loads configuration correctly."""
        assert alert_manager.alert_config['email_alerts'] == ['CRITICAL']
        assert alert_manager.alert_config['slack_alerts'] == ['ERROR', 'CRITICAL']


class TestMonitoringConfigLoader:
    """Test the monitoring configuration loader."""
    
    def test_config_loading(self):
        """Test configuration loading for different environments."""
        with patch('mcx3d_finance.core.config.detect_environment', return_value='testing'):
            loader = MonitoringConfigLoader()
            config = loader.load_config()
            
            assert 'health_thresholds' in config
            assert 'alert_thresholds' in config
            assert 'monitoring_intervals' in config
            assert config['_metadata']['environment'] == 'testing'
    
    def test_threshold_retrieval(self):
        """Test specific threshold retrieval."""
        with patch('mcx3d_finance.core.config.detect_environment', return_value='production'):
            loader = MonitoringConfigLoader()
            
            # Test specific threshold retrieval
            db_response_time = loader.get_threshold('database', 'max_response_time_ms', 100)
            assert isinstance(db_response_time, int)
            
            # Test non-existent threshold with default
            non_existent = loader.get_threshold('nonexistent', 'nonexistent', 'default_value')
            assert non_existent == 'default_value'
    
    def test_alert_configuration(self):
        """Test alert configuration methods."""
        with patch('mcx3d_finance.core.config.detect_environment', return_value='production'):
            loader = MonitoringConfigLoader()
            
            # Test alert sending decision
            should_send_email_critical = loader.should_send_alert('email_alerts', 'CRITICAL')
            should_send_email_info = loader.should_send_alert('email_alerts', 'INFO')
            
            # These assertions depend on the actual config in monitoring.yml
            assert isinstance(should_send_email_critical, bool)
            assert isinstance(should_send_email_info, bool)
    
    def test_config_validation(self):
        """Test configuration validation."""
        with patch('mcx3d_finance.core.config.detect_environment', return_value='testing'):
            loader = MonitoringConfigLoader()
            validation_result = loader.validate_config()
            
            assert 'valid' in validation_result
            assert 'issues' in validation_result
            assert 'warnings' in validation_result
            assert validation_result['environment'] == 'testing'


class TestIntegrationScenarios:
    """Test complete integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_complete_monitoring_workflow(self):
        """Test complete monitoring workflow from health check to alerting."""
        # Mock all dependencies
        mock_db = Mock()
        mock_db.execute.return_value.scalar.return_value = 1
        mock_db.execute.return_value.fetchone.return_value = Mock(
            active_connections=95,  # High connection count to trigger degraded state
            max_connections=100,
            database_size_bytes=1024*1024*1024
        )
        
        mock_redis = Mock()
        mock_redis.ping.return_value = True
        mock_redis.info.return_value = {'connected_clients': 5}
        mock_redis.info.side_effect = lambda section=None: {
            'memory': {'used_memory': 50*1024*1024, 'maxmemory': 100*1024*1024}
        } if section == 'memory' else {'connected_clients': 5}
        mock_redis.llen.return_value = 0
        mock_redis.keys.return_value = []
        
        with patch('psutil.cpu_percent', return_value=50.0), \
             patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.disk_usage') as mock_disk, \
             patch('psutil.process_iter', return_value=[]):
            
            mock_memory.return_value = Mock(percent=60.0, available=1024*1024*1024)
            mock_disk.return_value = Mock(percent=70.0, free=10*1024*1024*1024)
            
            # Create health checker
            health_checker = HealthChecker(db_session=mock_db, redis_client=mock_redis)
            
            # Run comprehensive health check
            health_result = await health_checker.get_comprehensive_health()
            
            # Verify results
            assert health_result['overall_status'] in [HealthStatus.HEALTHY, HealthStatus.DEGRADED]
            assert len(health_result['components']) >= 5
            
            # Test that metrics were updated (this would be verified by checking Prometheus metrics)
            # In a real test, you'd verify that the appropriate metrics were recorded
            
    @pytest.mark.asyncio
    async def test_performance_requirements(self):
        """Test that health checks meet performance requirements (<100ms)."""
        mock_db = Mock()
        mock_db.execute.return_value.scalar.return_value = 1
        mock_db.execute.return_value.fetchone.return_value = Mock(
            active_connections=10, max_connections=100, database_size_bytes=1024*1024*1024
        )
        
        mock_redis = Mock()
        mock_redis.ping.return_value = True
        mock_redis.info.return_value = {'connected_clients': 5}
        mock_redis.llen.return_value = 0
        mock_redis.keys.return_value = []
        
        health_checker = HealthChecker(db_session=mock_db, redis_client=mock_redis)
        
        start_time = time.time()
        result = await health_checker._check_database_health()
        duration_ms = (time.time() - start_time) * 1000
        
        # Verify response time is under 100ms (generous for unit tests)
        assert duration_ms < 1000  # 1 second for unit test environment
        assert result['response_time_ms'] < 1000


if __name__ == '__main__':
    pytest.main([__file__, '-v'])