# Financial Chart Generation Guide

## Overview

The MCX3D Financial Chart Generation system provides professional-quality charts and visualizations for financial valuation reports. The system integrates seamlessly with existing PDF and Excel report generation, adding sophisticated charts that enhance the value and clarity of financial analysis.

## Features

### Chart Types

#### DCF Valuation Charts
- **Revenue Projection Chart**: 5-year revenue forecast with growth annotations
- **Cash Flow Waterfall Chart**: Operating to free cash flow breakdown
- **Sensitivity Tornado Diagram**: Impact analysis of key variables
- **Monte Carlo Distribution**: Valuation probability distribution

#### SaaS Valuation Charts
- **ARR Growth Trajectory**: Historical and projected Annual Recurring Revenue
- **Unit Economics Chart**: CAC, LTV, and payback period visualization
- **Valuation Methods Comparison**: Side-by-side comparison of different valuation approaches
- **Cohort Retention Heatmap**: Customer retention analysis over time

#### Financial Statement Charts
- **Asset Composition**: Pie chart of balance sheet assets
- **Revenue Breakdown**: Segmented revenue analysis
- **Margin Analysis**: Profitability trends over time

### Output Formats

- **PDF Integration**: High-resolution PNG images embedded in ReportLab documents
- **Excel Integration**: Native Excel chart objects using OpenPyXL
- **Standalone Charts**: Plotly figures for web display or further customization

## Usage Examples

### Basic Chart Generation

```python
from mcx3d_finance.reporting.generator import FinancialChartGenerator

# Initialize chart generator
chart_gen = FinancialChartGenerator()

# DCF Revenue Projection Chart
dcf_data = {
    "financial_projections": [
        {"year": 1, "revenue": 1000000, "free_cash_flow": 300000},
        {"year": 2, "revenue": 1200000, "free_cash_flow": 380000},
        {"year": 3, "revenue": 1440000, "free_cash_flow": 480000}
    ]
}

revenue_chart = chart_gen.create_dcf_revenue_projection_chart(
    dcf_data["financial_projections"]
)

# SaaS ARR Growth Chart
saas_data = {
    "key_metrics": {
        "arr": 5000000,
        "growth_rate": 25
    }
}

arr_chart = chart_gen.create_saas_arr_growth_chart(saas_data)
```

### Integrated Report Generation

```python
from mcx3d_finance.reporting.generator import ReportGenerator

# Initialize report generator (includes chart generator)
report_gen = ReportGenerator()

# Generate DCF PDF with charts
dcf_data = {
    "organization_id": 1,
    "valuation_date": "2024-01-01",
    "financial_projections": [...],
    "sensitivity_analysis": {...},
    "monte_carlo_simulation": {...}
}

report_gen.generate_dcf_valuation_pdf(dcf_data, "dcf_report_with_charts.pdf")

# Generate SaaS Excel with charts
saas_data = {
    "organization_id": 1,
    "key_metrics": {...},
    "valuation_methods": {...}
}

report_gen.generate_saas_valuation_excel(saas_data, "saas_report_with_charts.xlsx")
```

### Custom Styling and Themes

```python
# Apply different themes
chart = chart_gen.create_dcf_revenue_projection_chart(projections)
chart = chart_gen.apply_financial_theme(chart, "professional")  # Default
chart = chart_gen.apply_financial_theme(chart, "dark")          # Dark theme
chart = chart_gen.apply_financial_theme(chart, "minimal")       # Minimal theme

# Add financial annotations
key_insights = {"cagr": 0.15}  # 15% CAGR
chart = chart_gen.add_financial_annotations(chart, "dcf_revenue", key_insights)
```

### Error Handling and Validation

```python
# Validate data before chart generation
dcf_validation = chart_gen.validate_dcf_data(dcf_data)
if dcf_validation["projections_valid"]:
    chart = chart_gen.create_dcf_revenue_projection_chart(
        dcf_data["financial_projections"]
    )
else:
    chart = chart_gen.create_fallback_chart("revenue_projection", "Invalid data")

# Safe chart generation with automatic fallback
chart = chart_gen.safe_chart_generation(
    chart_gen.create_dcf_revenue_projection_chart,
    dcf_data["financial_projections"]
)
```

## Data Structure Requirements

### DCF Data Structure

```python
dcf_data = {
    "financial_projections": [
        {
            "year": int,                    # Required
            "revenue": float,               # Required
            "free_cash_flow": float,        # Required
            "ebitda": float,               # Optional
            "revenue_growth_rate": float,   # Optional
            "operating_expenses": float,    # Optional for waterfall
            "taxes": float,                # Optional for waterfall
            "capex": float,                # Optional for waterfall
            "working_capital_change": float # Optional for waterfall
        }
    ],
    "sensitivity_analysis": {
        "variable_name": {
            "range": [float, ...],         # Variable range
            "impact": [float, ...]         # Impact on valuation (%)
        }
    },
    "monte_carlo_simulation": {
        "num_simulations": int,
        "mean_valuation": float,
        "std_deviation": float,
        "percentile_10": float,           # Optional
        "percentile_90": float,           # Optional
        "simulation_results": [float, ...] # Optional raw results
    }
}
```

### SaaS Data Structure

```python
saas_data = {
    "key_metrics": {
        "arr": float,                     # Annual Recurring Revenue
        "mrr": float,                     # Monthly Recurring Revenue
        "growth_rate": float,             # Growth rate (%)
        "churn_rate": float,              # Customer churn rate (%)
        "ltv_cac_ratio": float,           # LTV/CAC ratio
        "customer_acquisition_cost": float,
        "customer_lifetime_value": float,
        "active_customers": int
    },
    "valuation_methods": {
        "arr_multiple": {
            "adjusted_valuation": float,
            "base_arr": float,
            "adjusted_multiple": float
        },
        "revenue_multiple": {
            "valuation": float,
            "annual_revenue": float,
            "revenue_multiple": float
        },
        "saas_dcf": {
            "enterprise_value": float,
            "pv_operating_fcf": float,
            "discount_rate": float
        }
    },
    "weighted_valuation": {
        "weighted_average_valuation": float,
        "valuation_range": {
            "minimum": float,
            "maximum": float
        }
    }
}
```

## Configuration Options

### Chart Styling

```python
# Professional color palette
colors = {
    "primary": "#1f77b4",      # Professional blue
    "secondary": "#ff7f0e",    # Orange
    "success": "#2ca02c",      # Green
    "danger": "#d62728",       # Red
    "warning": "#ff7f0e",      # Orange
    "info": "#17a2b8",         # Cyan
    "dark": "#343a40",         # Dark gray
    "neutral": "#6c757d"       # Medium gray
}

# Chart dimensions for PDF
pdf_chart_size = {
    "width": 6 * inch,         # 6 inches
    "height": 4 * inch         # 4 inches
}

# Chart dimensions for Excel
excel_chart_size = {
    "height": 10,              # 10 cm
    "width": 16                # 16 cm
}
```

### Performance Optimization

- Charts are generated with 2x scale for high DPI output
- Static plots are used for PDF embedding (no interactivity)
- Excel charts use native OpenPyXL objects for better performance
- Fallback mechanisms prevent report generation failures

## Best Practices

1. **Data Validation**: Always validate data before chart generation
2. **Error Handling**: Use safe chart generation methods for production
3. **Styling Consistency**: Use the professional theme for client reports
4. **Performance**: Generate charts only when needed for large datasets
5. **Testing**: Test chart generation with various data scenarios

## Troubleshooting

### Common Issues

1. **Missing Data**: Charts will show fallback message if required data is missing
2. **Invalid Data Types**: Data sanitization handles most type conversion issues
3. **Memory Usage**: Large Monte Carlo datasets may require sampling
4. **PDF Size**: High-resolution charts increase PDF file size

### Error Messages

- "Chart Unavailable - Insufficient data": Missing required data fields
- "Error in chart generation": Exception during chart creation
- "Invalid data format": Data structure doesn't match requirements

## Dependencies

- `plotly`: Chart generation and visualization
- `reportlab`: PDF integration
- `openpyxl`: Excel chart integration
- `pandas`: Data manipulation
- `numpy`: Numerical operations

## Future Enhancements

- Interactive dashboard generation
- Real-time data integration
- Additional chart types (scatter plots, box plots)
- Custom color scheme configuration
- Export to additional formats (SVG, HTML)
