#!/bin/bash
# production_deploy.sh - Production deployment script for MCX3D Finance
# Updated with lessons learned from successful deployment - January 2025
#
# KEY LESSONS LEARNED FROM DEPLOYMENT:
# 1. Database migrations MUST run inside containers (docker-compose exec web alembic upgrade head)
# 2. Required environment variables include XERO_CLIENT_ID, XERO_CLIENT_SECRET, ENVIRONMENT
# 3. Health check endpoint is /health/detailed (not /health/comprehensive)
# 4. Environment file must avoid shell expansions like $(date) - use static values
# 5. Docker Compose health checks are critical for proper service startup order
# 6. Production deployment validated: 4 services, all healthy, performance benchmarks met

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKUP_DIR="$PROJECT_ROOT/backups"
LOG_FILE="$PROJECT_ROOT/logs/deployment_$(date +%Y%m%d_%H%M%S).log"
HEALTH_CHECK_RETRIES=30
HEALTH_CHECK_DELAY=5

# Ensure log directory exists
mkdir -p "$(dirname "$LOG_FILE")"
mkdir -p "$BACKUP_DIR"

# Logging functions
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

print_status() {
    echo -e "${GREEN}✓${NC} $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}✗${NC} $1" | tee -a "$LOG_FILE"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1" | tee -a "$LOG_FILE"
}

# Pre-deployment checks
pre_deployment_checks() {
    log "Starting pre-deployment checks..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check environment file
    if [ ! -f "$PROJECT_ROOT/.env" ]; then
        print_error ".env file not found"
        exit 1
    fi
    
    # Validate required environment variables (updated based on deployment experience)
    local required_vars=(
        "DATABASE_URL"
        "REDIS_URL"
        "SECRET_KEY"
        "ENCRYPTION_KEY"
        "XERO_CLIENT_ID"
        "XERO_CLIENT_SECRET"
        "ENVIRONMENT"
    )
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^${var}=..*" "$PROJECT_ROOT/.env"; then
            print_error "Missing required environment variable: $var"
            exit 1
        fi
    done
    
    # Check disk space
    local available_space=$(df -BG "$PROJECT_ROOT" | awk 'NR==2 {print $4}' | sed 's/G//')
    if [ "$available_space" -lt 5 ]; then
        print_error "Insufficient disk space. At least 5GB required, ${available_space}GB available"
        exit 1
    fi
    
    print_status "Pre-deployment checks passed"
}

# Backup current data
backup_data() {
    log "Creating backup..."
    
    local backup_name="backup_$(date +%Y%m%d_%H%M%S)"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    # Create backup directory
    mkdir -p "$backup_path"
    
    # Backup database if running
    if docker-compose ps db | grep -q "Up"; then
        print_info "Backing up database..."
        docker-compose exec -T db pg_dump -U user mcx3d_db > "$backup_path/database.sql"
        print_status "Database backed up to $backup_path/database.sql"
    fi
    
    # Backup environment file
    cp "$PROJECT_ROOT/.env" "$backup_path/.env.backup"
    
    # Create backup manifest
    cat > "$backup_path/manifest.json" <<EOF
{
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "version": "$(git describe --tags --always 2>/dev/null || echo 'unknown')",
    "services": $(docker-compose ps --format json 2>/dev/null || echo '[]')
}
EOF
    
    print_status "Backup completed: $backup_path"
}

# Pull latest images
pull_images() {
    log "Pulling latest images..."
    
    # Pull base images
    docker pull postgres:13
    docker pull redis:6.2
    
    # Build application image
    print_info "Building application image..."
    docker-compose build --no-cache
    
    print_status "Images updated"
}

# Deploy services
deploy_services() {
    local deployment_mode="${1:-rolling}"
    
    log "Deploying services (mode: $deployment_mode)..."
    
    case "$deployment_mode" in
        "rolling")
            # Rolling deployment - minimal downtime
            print_info "Performing rolling deployment..."
            
            # Update database first
            docker-compose up -d db redis
            wait_for_service "db" "pg_isready -U user -d mcx3d_db"
            wait_for_service "redis" "redis-cli ping"
            
            # Run migrations (lesson learned: must run inside container)
            print_info "Running database migrations inside container..."
            docker-compose exec web alembic upgrade head || docker-compose run --rm web alembic upgrade head
            
            # Update application services
            docker-compose up -d web worker
            ;;
            
        "blue-green")
            # Blue-green deployment - zero downtime
            print_info "Performing blue-green deployment..."
            
            # Start new services with different names
            docker-compose -p mcx3d_green up -d
            
            # Wait for new services to be healthy
            wait_for_health_check "http://localhost:8001/health/comprehensive"
            
            # Switch traffic (would need load balancer configuration)
            print_info "Switching traffic to new deployment..."
            
            # Stop old services
            docker-compose -p mcx3d_blue down
            ;;
            
        "recreate")
            # Recreate deployment - full restart
            print_info "Performing recreate deployment..."
            docker-compose down
            docker-compose up -d
            ;;
            
        *)
            print_error "Unknown deployment mode: $deployment_mode"
            exit 1
            ;;
    esac
    
    print_status "Services deployed"
}

# Wait for service to be ready
wait_for_service() {
    local service_name="$1"
    local health_command="$2"
    local retries=$HEALTH_CHECK_RETRIES
    
    echo -n "  Waiting for $service_name: "
    while [ $retries -gt 0 ]; do
        if docker-compose exec -T "$service_name" sh -c "$health_command" &> /dev/null; then
            echo -e " ${GREEN}Ready${NC}"
            return 0
        fi
        echo -n "."
        sleep $HEALTH_CHECK_DELAY
        retries=$((retries - 1))
    done
    
    echo -e " ${RED}Failed${NC}"
    return 1
}

# Wait for health check endpoint
wait_for_health_check() {
    local health_url="$1"
    local retries=$HEALTH_CHECK_RETRIES
    
    echo -n "  Waiting for health check: "
    while [ $retries -gt 0 ]; do
        if curl -sf "$health_url" &> /dev/null; then
            echo -e " ${GREEN}Healthy${NC}"
            return 0
        fi
        echo -n "."
        sleep $HEALTH_CHECK_DELAY
        retries=$((retries - 1))
    done
    
    echo -e " ${RED}Unhealthy${NC}"
    return 1
}

# Post-deployment validation
post_deployment_validation() {
    log "Running post-deployment validation..."
    
    local all_healthy=true
    
    # Check basic health (updated endpoint based on deployment experience)
    if ! curl -sf http://localhost:8000/health > /dev/null; then
        print_error "Basic health check failed"
        all_healthy=false
    else
        print_status "Basic health check passed"
    fi
    
    # Check comprehensive health (updated based on actual available endpoints)
    local health_response=$(curl -sf http://localhost:8000/health/detailed || echo "{}")
    if echo "$health_response" | grep -q '"status"'; then
        print_status "Detailed health check completed"
        # Show health summary
        echo "$health_response" | python3 -c "import sys, json; data=json.load(sys.stdin); print(f\"Status: {data.get('status', 'unknown')}\"); [print(f\"  {k}: {v.get('status', v)}\" if isinstance(v, dict) else f\"  {k}: {v}\") for k,v in data.get('components', {}).items()]" 2>/dev/null || true
    else
        print_warning "Detailed health check unavailable"
    fi
    
    # Check database connectivity
    if docker-compose exec -T db pg_isready -U user -d mcx3d_db &> /dev/null; then
        print_status "Database connectivity verified"
    else
        print_error "Database connectivity failed"
        all_healthy=false
    fi
    
    # Check Redis connectivity
    if docker-compose exec -T redis redis-cli ping | grep -q "PONG"; then
        print_status "Redis connectivity verified"
    else
        print_error "Redis connectivity failed"
        all_healthy=false
    fi
    
    # Check Celery workers
    if docker-compose exec -T worker celery -A mcx3d_finance.tasks.celery_app inspect ping &> /dev/null; then
        print_status "Celery workers responsive"
    else
        print_warning "Celery workers not responding"
    fi
    
    if [ "$all_healthy" = true ]; then
        print_status "Post-deployment validation passed"
        return 0
    else
        print_error "Post-deployment validation failed"
        return 1
    fi
}

# Setup monitoring
setup_monitoring() {
    log "Setting up monitoring..."
    
    # Start monitoring stack
    docker-compose -f docker-compose.monitoring.yml up -d
    
    # Wait for Prometheus
    wait_for_health_check "http://localhost:9090/-/healthy"
    
    # Wait for Grafana
    wait_for_health_check "http://localhost:3000/api/health"
    
    print_status "Monitoring stack deployed"
    print_info "Grafana: http://localhost:3000 (admin/mcx3d_admin_2024)"
    print_info "Prometheus: http://localhost:9090"
}

# Rollback deployment
rollback_deployment() {
    local backup_name="$1"
    
    if [ -z "$backup_name" ]; then
        # Find latest backup
        backup_name=$(ls -t "$BACKUP_DIR" | head -1)
    fi
    
    local backup_path="$BACKUP_DIR/$backup_name"
    
    if [ ! -d "$backup_path" ]; then
        print_error "Backup not found: $backup_path"
        exit 1
    fi
    
    log "Rolling back to backup: $backup_name"
    
    # Stop current services
    docker-compose down
    
    # Restore environment file
    if [ -f "$backup_path/.env.backup" ]; then
        cp "$backup_path/.env.backup" "$PROJECT_ROOT/.env"
        print_status "Environment file restored"
    fi
    
    # Restore database
    if [ -f "$backup_path/database.sql" ]; then
        # Start only database service
        docker-compose up -d db
        wait_for_service "db" "pg_isready -U user -d mcx3d_db"
        
        # Restore database
        docker-compose exec -T db psql -U user mcx3d_db < "$backup_path/database.sql"
        print_status "Database restored"
    fi
    
    # Start all services
    docker-compose up -d
    
    print_status "Rollback completed"
}

# Show deployment status
show_deployment_status() {
    echo
    echo -e "${CYAN}=== Deployment Status ===${NC}"
    
    # Show service status
    docker-compose ps
    
    # Show health status (updated based on available endpoints)
    echo
    echo -e "${CYAN}Health Status:${NC}"
    curl -s http://localhost:8000/health/detailed | python3 -m json.tool 2>/dev/null || echo "Health check unavailable"
    
    # Show recent logs
    echo
    echo -e "${CYAN}Recent Logs:${NC}"
    docker-compose logs --tail=20
}

# Main deployment function
main() {
    local action="${1:-deploy}"
    
    echo -e "${BLUE}🚀 MCX3D Production Deployment${NC}"
    echo "=========================================="
    log "Starting deployment action: $action"
    
    cd "$PROJECT_ROOT"
    
    case "$action" in
        "deploy")
            pre_deployment_checks
            backup_data
            pull_images
            deploy_services "${2:-rolling}"
            
            if post_deployment_validation; then
                print_status "Deployment successful!"
                show_deployment_status
            else
                print_error "Deployment validation failed"
                print_warning "Consider rolling back with: $0 rollback"
                exit 1
            fi
            ;;
            
        "deploy-with-monitoring")
            pre_deployment_checks
            backup_data
            pull_images
            deploy_services "${2:-rolling}"
            setup_monitoring
            
            if post_deployment_validation; then
                print_status "Deployment with monitoring successful!"
                show_deployment_status
            else
                print_error "Deployment validation failed"
                exit 1
            fi
            ;;
            
        "rollback")
            rollback_deployment "$2"
            post_deployment_validation
            ;;
            
        "validate")
            post_deployment_validation
            ;;
            
        "backup")
            backup_data
            ;;
            
        "status")
            show_deployment_status
            ;;
            
        "help"|*)
            echo -e "${BLUE}MCX3D Production Deployment Script${NC}"
            echo "Usage: $0 [action] [options]"
            echo
            echo "Actions:"
            echo "  deploy [mode]              Deploy services (modes: rolling, blue-green, recreate)"
            echo "  deploy-with-monitoring     Deploy with full monitoring stack"
            echo "  rollback [backup_name]     Rollback to previous deployment"
            echo "  validate                   Validate current deployment"
            echo "  backup                     Create backup of current state"
            echo "  status                     Show deployment status"
            echo "  help                       Show this help message"
            echo
            echo "Examples:"
            echo "  $0 deploy rolling          # Rolling deployment (default)"
            echo "  $0 deploy blue-green       # Blue-green deployment"
            echo "  $0 rollback                # Rollback to latest backup"
            echo "  $0 rollback backup_20240120_120000  # Rollback to specific backup"
            ;;
    esac
    
    log "Deployment action completed: $action"
}

# Execute main function
main "$@"